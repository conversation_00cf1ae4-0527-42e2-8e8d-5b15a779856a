/*
Up to this point we saved player personal info in Player.
This migration adds it also to PlayerTeamProfile.

This allows us to anonymize player data when we remove a player from a team or organization.
But it also allows us not to delete the player profile

E.g. <PERSON> is removed from Barcelona and joins PSG, but we still want to keep the data associated with his records
We go to his Barcelona profile and change his name to "Remo<PERSON> (1)" but his PSG profile will still have his personal info as <PERSON>
*/

module.exports = {
  async up(db, client) {
    const players = await db.collection("players").find({});

    console.log("players", players);

    while (await players.hasNext()) {
      const player = await players.next();

      console.log("player", player);

      const email = player.email ? player.email : player.guardian?.email;
      const phone = player.phone ? player.phone : player.guardian?.phone;

      const personalInfo = {
        firstName: player.firstName,
        lastName: player.lastName,
        ...(email ? { email } : {}),
        ...(phone ? { phone } : {}),
        dob: player.dob,
        medicalConditions: player.medicalConditions,
        gender: player.gender,
        playingExperience: player.playingExperience,
        playingExperienceDescription: player.playingExperienceDescription,
        address: player.address,
        guardian: player.guardian,
        documents: player.documents,
      };

      console.log("UPDATING WITH personalInfo", personalInfo);

      await db.collection("player-team-profiles").updateMany(
        { playerId: player.id },
        {
          $set: {
            firstName: personalInfo.firstName,
            lastName: personalInfo.lastName,
            ...(personalInfo.email ? { email: personalInfo.email } : {}),
            ...(personalInfo.phone ? { phone: personalInfo.phone } : {}),
            dob: personalInfo.dob,
            medicalConditions: personalInfo.medicalConditions,

            gender: personalInfo.gender,
            playingExperience: personalInfo.playingExperience,
            playingExperienceDescription: personalInfo.playingExperienceDescription,
            address: personalInfo.address,
            ...(personalInfo.documents ? { documents: personalInfo.documents } : {}),
            ...(personalInfo.guardian ? { guardian: personalInfo.guardian } : {}),
          },
        },
      );

      console.log("UPDATED");
    }
  },

  async down(db, client) {},
};
