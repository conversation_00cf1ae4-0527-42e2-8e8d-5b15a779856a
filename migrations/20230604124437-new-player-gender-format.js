/*
Up to this point Player.gender was = PlayerGenders.
This migration changes it to a new format: { name: PlayerGenders, description: string }
*/

module.exports = {
  async up(db, client) {
    const players = await db.collection("players").find({
      gender: { $type: "string" },
    });

    while (await players.hasNext()) {
      const player = await players.next();
      const gender = player.gender;
      const newFields = {};

      if (gender === "other") {
        newFields.name = "other";
        newFields.description = "N/A";
      } else {
        newFields.name = gender;
      }

      await db.collection("players").updateOne(
        { _id: player._id },
        {
          $set: {
            ...player,
            gender: newFields,
          },
        },
      );
    }
  },

  async down(db, client) {},
};
