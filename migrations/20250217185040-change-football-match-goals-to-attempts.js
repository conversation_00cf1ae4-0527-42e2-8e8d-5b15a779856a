module.exports = {
  async up(db, client) {
    // Rename collections
    await db.collection("football-match-goals-scored").rename("football-match-attempts-for");
    await db.collection("football-match-goals-conceded").rename("football-match-attempts-against");

    // Update field names in attempts-for collection
    await db
      .collection("football-match-attempts-for")
      .updateMany({ scorer: { $exists: true } }, { $rename: { scorer: "shooter" } });

    // Update field names in attempts-against collection
    await db
      .collection("football-match-attempts-against")
      .updateMany({ scorer: { $exists: true } }, { $rename: { scorer: "shooter" } });

    // Update type field values in attempts-for collection
    await db
      .collection("football-match-attempts-for")
      .updateMany({ type: "scored" }, { $set: { type: "goal" } });

    // Update type field values in attempts-against collection
    await db
      .collection("football-match-attempts-against")
      .updateMany({ type: "conceded" }, { $set: { type: "goal" } });
  },

  async down(db, client) {
    // Revert collection names
    await db.collection("football-match-attempts-for").rename("football-match-goals-scored");
    await db.collection("football-match-attempts-against").rename("football-match-goals-conceded");

    // Revert field names in goals-scored collection
    await db
      .collection("football-match-goals-scored")
      .updateMany({ shooter: { $exists: true } }, { $rename: { shooter: "scorer" } });

    // Revert field names in goals-conceded collection
    await db
      .collection("football-match-goals-conceded")
      .updateMany({ shooter: { $exists: true } }, { $rename: { shooter: "scorer" } });

    // Revert type field values in goals-scored collection
    await db
      .collection("football-match-goals-scored")
      .updateMany({ type: "goal" }, { $set: { type: "scored" } });

    // Revert type field values in goals-conceded collection
    await db
      .collection("football-match-goals-conceded")
      .updateMany({ type: "goal" }, { $set: { type: "conceded" } });
  },
};
