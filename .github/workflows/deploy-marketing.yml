name: Deploy Marketing to production

on:
  workflow_dispatch:

jobs:
  # This job doesn't do anything: it's here only to run `npm ci` so that the dependencies are cached
  # for all the other jobs.
  cache-dependencies:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          # By default, only the last commit is fetched by GitHub Actions. Nx needs the whole git
          # history to properly figure out the affected projects so we use the `fetch-depth` option
          # to fetch everything.
          # https://stackoverflow.com/a/67201604
          fetch-depth: 0

      - uses: ./.github/actions/setup

  deploy-marketing:
    runs-on: ubuntu-latest
    needs: [cache-dependencies]
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true
      - name: Build And Deploy
        id: marketing
        uses: azure/static-web-apps-deploy@latest
        with:
          azure_static_web_apps_api_token: ${{ secrets.API_TOKEN_MARKETING }}
          repo_token: ${{ secrets.GITHUB_TOKEN }} # Used for GitHub integrations (i.e. PR comments)
          action: "upload"
          app_location: "/" # App source code path
          output_location: "dist/apps/marketing/exported"
          app_build_command: "npm run build-marketing"
        env: # Add environment variables here
          is_static_export: true
