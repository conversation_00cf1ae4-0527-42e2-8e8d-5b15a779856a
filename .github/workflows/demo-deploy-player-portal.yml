name: Deploy Demo Player Portal

on:
  workflow_dispatch:

jobs:
  # This job doesn't do anything: it's here only to run `npm ci` so that the dependencies are cached
  # for all the other jobs.
  cache-dependencies:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          # By default, only the last commit is fetched by GitHub Actions. Nx needs the whole git
          # history to properly figure out the affected projects so we use the `fetch-depth` option
          # to fetch everything.
          # https://stackoverflow.com/a/67201604
          fetch-depth: 0

      - uses: ./.github/actions/setup

  deploy-player-portal:
    runs-on: ubuntu-latest
    needs: [cache-dependencies]
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true
      - name: Build And Deploy
        id: builddeploy
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ secrets.API_TOKEN_DEMO_PLAYER_PORTAL }}
          repo_token: ${{ secrets.GITHUB_TOKEN }} # Used for Github integrations (i.e. PR comments)
          action: "upload"
          ###### Repository/Build Configurations - These values can be configured to match your app requirements. ######
          # For more information regarding Static Web App workflow configurations, please visit: https://aka.ms/swaworkflowconfig
          app_location: "/" # App source code path
          output_location: "dist/apps/player-portal" # Built app content directory - optional
          app_build_command: "npm run build-demo-player-portal"
          ###### End of Repository/Build Configurations ######
