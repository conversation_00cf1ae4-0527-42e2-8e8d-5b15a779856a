name: Deploy Coach portal to production

on:
  workflow_dispatch:

jobs:
  # This job doesn't do anything: it's here only to run `npm ci` so that the dependencies are cached
  # for all the other jobs.
  cache-dependencies:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          # By default, only the last commit is fetched by GitHub Actions. Nx needs the whole git
          # history to properly figure out the affected projects so we use the `fetch-depth` option
          # to fetch everything.
          # https://stackoverflow.com/a/67201604
          fetch-depth: 0

      - uses: ./.github/actions/setup

  deploy-coach-portal:
    runs-on: ubuntu-latest
    needs: [cache-dependencies]
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true
      - uses: ./.github/actions/setup
      - name: Build
        run: npm run build-client
      - name: Deploy
        id: builddeploy
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ secrets.API_TOKEN_PROD_CLIENT }}
          repo_token: ${{ secrets.GITHUB_TOKEN }} # Used for Github integrations (i.e. PR comments)
          action: "upload"
          app_location: "/dist/apps/client"
          skip_app_build: true
          skip_api_build: true
