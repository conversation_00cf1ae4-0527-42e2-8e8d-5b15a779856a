name: Deploy API to production

on:
  workflow_dispatch:

jobs:
  # This job doesn't do anything: it's here only to run `npm ci` so that the dependencies are cached
  # for all the other jobs.
  cache-dependencies:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          # By default, only the last commit is fetched by GitHub Actions. Nx needs the whole git
          # history to properly figure out the affected projects so we use the `fetch-depth` option
          # to fetch everything.
          # https://stackoverflow.com/a/67201604
          fetch-depth: 0

      - uses: ./.github/actions/setup

  deploy-api:
    runs-on: ubuntu-latest
    needs: [cache-dependencies]
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/actions/setup

      - uses: azure/docker-login@v1
        with:
          login-server: ${{ secrets.ACR_PROD_LOGINSERVER }}
          username: ${{ secrets.ACR_PROD_USERNAME }}
          password: ${{ secrets.ACR_PROD_PASSWORD }}

      - name: Build image
        run: |
          npx nx run api:docker --release=${{ github.sha }}
          IMAGE_ID=$(docker images -q | head -1)
          docker tag $IMAGE_ID ${{ secrets.ACR_PROD_LOGINSERVER }}/coach-center:latest
          docker tag $IMAGE_ID ${{ secrets.ACR_PROD_LOGINSERVER }}/coach-center:${{ github.sha }}
          docker tag $IMAGE_ID ${{ secrets.ACR_PROD_LOGINSERVER }}/coach-center:${{ github.run_id }}

      - name: Deploy image
        run: docker push ${{ secrets.ACR_PROD_LOGINSERVER }}/coach-center --all-tags
