name: "Setup"
description: "Setup Node and install npm dependencies"
inputs:
  npm-ignore-scripts:
    description: "If `npm ci` should be called with the `ignore-scripts` flag"
    default: false
runs:
  using: "composite"
  steps:
    # Set up your GitHub Actions workflow with a the node.js version specified in the .nvmrc file
    - name: Setup Node
      uses: actions/setup-node@v4
      with:
        node-version-file: ".nvmrc"
        cache: "npm"

    # Install the dependencies specified in package.json
    - name: NPM install
      run: |
        npm ci --ignore-scripts=${{ inputs.npm-ignore-scripts }}
      shell: bash
      env:
        CI: true
