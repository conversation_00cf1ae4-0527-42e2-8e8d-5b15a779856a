# Player Controller Unit Testing Plan

## Overview

This plan outlines the comprehensive testing strategy for the `PlayerController` class, including test structure, mocking patterns, and specific test cases for each controller method.

## Test Structure Analysis

Based on the existing test patterns in the codebase, the following structure should be used:

### 1. Test File Organization

- **File**: `apps/api/src/app/player/player.controller.spec.ts`
- **Pattern**: Unit tests (not e2e) using NestJS Testing module
- **Framework**: Jest with NestJS testing utilities

### 2. Test Bed Configuration

```typescript
import { Test, TestingModule } from "@nestjs/testing";
import { EventEmitter2 } from "@nestjs/event-emitter";

import { PlayerController } from "./player.controller";
import { PlayerService } from "./player.service";
import { mockDep, passingGuard } from "../../test";
import {
  JwtAuthGuard,
  PlayerUserGuard,
  PlayerManageGuard,
  ProfileGuard,
  PermissionsGuard,
  ApiKeyGuard,
  PlayerOwnsImageGuard,
} from "../guards"; // Import paths may vary
```

### 3. Mock Dependencies

#### Core Service Mocks

- **PlayerService**: Mock all methods used by controller
- **EventEmitter2**: Use real instance or mock with emit method

#### Guard Mocks

All guards should be overridden with `passingGuard` for unit tests:

- `JwtAuthGuard`
- `PlayerUserGuard`
- `PlayerManageGuard`
- `ProfileGuard`
- `PermissionsGuard`
- `ApiKeyGuard`
- `PlayerOwnsImageGuard`

## Required Factories and Test Data

### 1. Entity Factories Needed

- `playerFactory` - Already exists
- `organizationFactory` - Already exists
- `teamFactory` - Already exists
- `profileFactory` - Already exists
- `coachUserFactory` - Already exists
- `playerUserFactory` - Need to create
- `imageAssetFactory` - Need to create

### 2. Additional Test Utilities

- `populatedPlayerFactory` - For PopulatedPlayer entities
- `playerTeamProfileFactory` - For PlayerTeamProfile entities
- `paginatedPlayersFactory` - For paginated results

## Controller Methods to Test

### 1. Document Management Methods

#### `getPlayerDocumentImages(playerId: PlayerId)`

- **Guards**: JwtAuthGuard, PlayerUserGuard, PlayerManageGuard
- **Success Cases**:
  - Returns array of ImageAsset objects
- **Error Cases**:
  - DomainError → BadRequestException
  - ParsingError → InternalServerErrorException
  - UnexpectedError → InternalServerErrorException

#### `submitPlayerDocuments(playerId: PlayerId)`

- **Guards**: JwtAuthGuard, PlayerUserGuard, PlayerManageGuard
- **Success Cases**:
  - Successfully marks documents as submitted
- **Error Cases**:
  - DomainError → BadRequestException
  - UnexpectedError → InternalServerErrorException
  - ParsingError → InternalServerErrorException

#### `addPlayerPhoto(playerId: PlayerId, photoId: ImageId)`

- **Guards**: JwtAuthGuard, PlayerUserGuard, PlayerManageGuard, PlayerOwnsImageGuard
- **Success Cases**:
  - Returns updated Player with photo
- **Error Cases**:
  - DomainError → BadRequestException
  - Other errors → InternalServerErrorException

#### `removePlayerPhoto(playerId: PlayerId)`

- **Guards**: JwtAuthGuard, PlayerUserGuard, PlayerManageGuard
- **Success Cases**:
  - Returns updated Player without photo
- **Error Cases**:
  - DomainError → BadRequestException
  - Other errors → InternalServerErrorException

#### `addPlayerImageDocument(playerId: PlayerId, imageId: ImageId)`

- **Guards**: JwtAuthGuard, PlayerUserGuard, PlayerManageGuard, PlayerOwnsImageGuard
- **Success Cases**:
  - Returns updated Player with image document
- **Error Cases**:
  - DomainError → BadRequestException
  - Other errors → InternalServerErrorException

#### `removePlayerImageDocument(playerId: PlayerId, imageId: ImageId)`

- **Guards**: JwtAuthGuard, PlayerUserGuard, PlayerManageGuard
- **Success Cases**:
  - Returns updated Player without image document
- **Error Cases**:
  - DomainError → BadRequestException
  - Other errors → InternalServerErrorException

### 2. Player Management Methods

#### `createApplicant(dto: CreatePlayerDto)`

- **Guards**: None (public endpoint with validation pipe)
- **Success Cases**:
  - Creates new player and returns Player entity
  - Emits NewApplication event with correct email data
- **Error Cases**:
  - DomainError → BadRequestException
  - Other errors → InternalServerErrorException

#### `applyToOrganization(organizationId: OrganizationId, playerId: PlayerId)`

- **Guards**: JwtAuthGuard, PlayerUserGuard, PlayerManageGuard
- **Success Cases**:
  - Successfully applies player to organization
- **Error Cases**:
  - DomainError → BadRequestException
  - Other errors → InternalServerErrorException

#### `demoCreateApplicant(organizationId: OrganizationId)`

- **Guards**: ApiKeyGuard
- **Success Cases**:
  - Creates demo player for organization
- **Error Cases**:
  - DomainError → BadRequestException
  - Other errors → InternalServerErrorException

#### `update(playerUser: PlayerUser, dto: Player)`

- **Guards**: JwtAuthGuard, PlayerUserGuard, PlayerManageGuard
- **Success Cases**:
  - Updates player and returns updated Player entity
- **Error Cases**:
  - DomainError → BadRequestException
  - UnexpectedError → InternalServerErrorException
  - Other errors → InternalServerErrorException

### 3. Team Management Methods

#### `assignNewTeam(dto: AssignTeamDto, profile: Profile)`

- **Guards**: JwtAuthGuard, ProfileGuard, PermissionsGuard
- **Permissions**: ManagePlayers
- **Success Cases**:
  - Assigns player to team and returns PopulatedPlayer
- **Error Cases**:
  - DomainError → BadRequestException
  - ParsingError → InternalServerErrorException
  - Other errors → InternalServerErrorException

#### `changeStatus(dto: ChangeStatusDto, profile: Profile)`

- **Guards**: JwtAuthGuard, ProfileGuard, PermissionsGuard
- **Permissions**: ManageReviews, ManageEvents
- **Success Cases**:
  - Changes player status and returns PopulatedPlayer
- **Error Cases**:
  - DomainError → BadRequestException
  - ParsingError → InternalServerErrorException
  - Other errors → InternalServerErrorException

#### `removeFromOrganization(organizationId: OrganizationId, playerId: PlayerId)`

- **Guards**: JwtAuthGuard, ProfileGuard, PermissionsGuard
- **Permissions**: ManagePlayers
- **Success Cases**:
  - Removes player from organization and returns playerId
- **Error Cases**:
  - DomainError → BadRequestException
  - ParsingError → InternalServerErrorException
  - Other errors → InternalServerErrorException

#### `removeFromTeam(teamId: TeamId, organizationId: OrganizationId, playerId: PlayerId, profile: Profile)`

- **Guards**: JwtAuthGuard, ProfileGuard, PermissionsGuard
- **Permissions**: ManagePlayers
- **Success Cases**:
  - Removes player from team and returns PopulatedPlayer
- **Error Cases**:
  - DomainError → BadRequestException
  - ParsingError → InternalServerErrorException
  - Other errors → InternalServerErrorException

### 4. Query Methods

#### `playerExists(dto: GetPlayerDto)`

- **Guards**: None (public endpoint with validation pipe)
- **Success Cases**:
  - Returns true if player exists
  - Returns false if player doesn't exist
- **Error Cases**:
  - Any error → InternalServerErrorException

#### `isWithinOrganization(orgId: OrganizationId, playerId: PlayerId)`

- **Guards**: JwtAuthGuard
- **Success Cases**:
  - Returns true if player is in organization
  - Returns false if player is not in organization
- **Error Cases**:
  - DomainError → BadRequestException
  - Other errors → InternalServerErrorException

#### `searchPlayers(orgId: OrganizationId, query: PlayerQueryDto)`

- **Guards**: JwtAuthGuard, PermissionsGuard
- **Permissions**: ManagePlayers
- **Success Cases**:
  - Returns PaginatedPopulatedPlayers
- **Error Cases**:
  - Any error → InternalServerErrorException

#### `getTeamPlayers(teamId: TeamId, orgId: OrganizationId, query: PlayerQueryDto)`

- **Guards**: JwtAuthGuard
- **Permissions**: ManageReviews, ManageEvents
- **Success Cases**:
  - Returns PopulatedPlayer array
- **Error Cases**:
  - Any error → InternalServerErrorException

#### `getCurrentPlayers(user: PlayerUser)`

- **Guards**: JwtAuthGuard, PlayerUserGuard
- **Success Cases**:
  - Returns array of related players
- **Error Cases**:
  - Any error → InternalServerErrorException

## Test Implementation Strategy

### 1. Test Structure Pattern

Each method should have:

- Describe block for the method
- Success case tests
- Error case tests for each error type
- Guard verification (implicitly tested through module setup)

### 2. Mock Setup Pattern

```typescript
const module: TestingModule = await Test.createTestingModule({
  controllers: [PlayerController],
  providers: [
    mockDep(PlayerService, {
      methodName: async () => expectedResult,
    }),
    mockDep(EventEmitter2, {
      emit: jest.fn(),
    }),
  ],
})
  .overrideGuard(JwtAuthGuard)
  .useValue(passingGuard)
  .overrideGuard(PlayerUserGuard)
  .useValue(passingGuard)
  .overrideGuard(PlayerManageGuard)
  .useValue(passingGuard)
  .overrideGuard(ProfileGuard)
  .useValue(passingGuard)
  .overrideGuard(PermissionsGuard)
  .useValue(passingGuard)
  .overrideGuard(ApiKeyGuard)
  .useValue(passingGuard)
  .overrideGuard(PlayerOwnsImageGuard)
  .useValue(passingGuard)
  .compile();
```

### 3. Error Testing Pattern

```typescript
it("throws BadRequestException when service returns DomainError", async () => {
  const module = await createTestModule({
    serviceMock: {
      methodName: async () => new DomainError("Test error"),
    },
  });

  const controller = module.get<PlayerController>(PlayerController);

  await expect(controller.methodName(params)).rejects.toThrow(BadRequestException);
});
```

## Additional Testing Utilities Needed

### 1. New Factory Functions

- `playerUserFactory` - For PlayerUser entities
- `imageAssetFactory` - For ImageAsset entities
- `populatedPlayerFactory` - For PopulatedPlayer entities
- `paginatedResultFactory` - For paginated responses

### 2. Test Helper Functions

- `createTestModule(options)` - Standardized module creation
- `createMockPlayerService(overrides)` - Standardized service mocking
- `expectBadRequest(promise)` - Standardized error assertion
- `expectInternalServerError(promise)` - Standardized error assertion

### 3. Test Data Generators

- Valid DTOs for each endpoint
- Invalid DTOs for validation testing
- Error instances for error case testing

## Implementation Priority

1. **Phase 1**: Document management methods (6 methods)
2. **Phase 2**: Player management methods (4 methods)
3. **Phase 3**: Team management methods (4 methods)
4. **Phase 4**: Query methods (5 methods)

Each phase should include both success and error cases for complete coverage.

## Detailed Implementation Examples

### Example Test Structure

```typescript
describe("PlayerController", () => {
  let controller: PlayerController;
  let playerService: jest.Mocked<PlayerService>;
  let eventEmitter: jest.Mocked<EventEmitter2>;

  const createTestModule = async (serviceOverrides: Partial<PlayerService> = {}) => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PlayerController],
      providers: [
        mockDep(PlayerService, {
          getPlayerImageDocuments: jest.fn(),
          markDocumentsAsSubmitted: jest.fn(),
          addPhoto: jest.fn(),
          removePhoto: jest.fn(),
          addImageDocument: jest.fn(),
          removeImageDocument: jest.fn(),
          createApplicant: jest.fn(),
          applyToOrganization: jest.fn(),
          demoCreateApplicant: jest.fn(),
          updatePlayer: jest.fn(),
          assignNewTeam: jest.fn(),
          changeStatus: jest.fn(),
          removeFromOrganization: jest.fn(),
          softRemoveFromTeam: jest.fn(),
          findExistingPlayer: jest.fn(),
          isWithinOrganization: jest.fn(),
          searchPlayers: jest.fn(),
          searchTeamPlayers: jest.fn(),
          findRelatedPlayers: jest.fn(),
          ...serviceOverrides,
        }),
        mockDep(EventEmitter2, {
          emit: jest.fn(),
        }),
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue(passingGuard)
      .overrideGuard(PlayerUserGuard)
      .useValue(passingGuard)
      .overrideGuard(PlayerManageGuard)
      .useValue(passingGuard)
      .overrideGuard(ProfileGuard)
      .useValue(passingGuard)
      .overrideGuard(PermissionsGuard)
      .useValue(passingGuard)
      .overrideGuard(ApiKeyGuard)
      .useValue(passingGuard)
      .overrideGuard(PlayerOwnsImageGuard)
      .useValue(passingGuard)
      .compile();

    controller = module.get<PlayerController>(PlayerController);
    playerService = module.get(PlayerService);
    eventEmitter = module.get(EventEmitter2);

    return { controller, playerService, eventEmitter };
  };

  describe("getPlayerDocumentImages", () => {
    const playerId = UUID.generate<PlayerId>();
    const mockImages = [
      { id: UUID.generate(), url: "test-url-1.jpg" },
      { id: UUID.generate(), url: "test-url-2.jpg" },
    ] as Assets.Image.ImageAsset[];

    it("returns player document images successfully", async () => {
      const { controller } = await createTestModule({
        getPlayerImageDocuments: jest.fn().mockResolvedValue(mockImages),
      });

      const result = await controller.getPlayerDocumentImages(playerId);

      expect(result).toEqual(mockImages);
      expect(playerService.getPlayerImageDocuments).toHaveBeenCalledWith(playerId);
    });

    it("throws BadRequestException when service returns DomainError", async () => {
      const domainError = new DomainError("Test domain error");
      const { controller } = await createTestModule({
        getPlayerImageDocuments: jest.fn().mockResolvedValue(domainError),
      });

      await expect(controller.getPlayerDocumentImages(playerId)).rejects.toThrow(
        BadRequestException,
      );
    });

    it("throws InternalServerErrorException when service returns UnexpectedError", async () => {
      const unexpectedError = new UnexpectedError("Test unexpected error");
      const { controller } = await createTestModule({
        getPlayerImageDocuments: jest.fn().mockResolvedValue(unexpectedError),
      });

      await expect(controller.getPlayerDocumentImages(playerId)).rejects.toThrow(
        InternalServerErrorException,
      );
    });
  });

  describe("createApplicant", () => {
    const createPlayerDto = Player.toCreateDtoOrThrow({
      organizationId: UUID.generate<OrganizationId>(),
      firstName: "John",
      lastName: "Doe",
      email: "<EMAIL>",
      phone: "+**********",
      dob: CustomDate.subYears(15),
      gender: { name: PlayerGenders.Male },
      playingExperience: PlayingExperience.Beginner,
      playingExperienceDescription: "New to the sport",
      medicalConditions: "None",
      address: {
        postcode: "SW1A 1AA",
        address: "123 Test Street, London, UK",
      },
      acceptedTerms: true,
    });

    const mockPlayer = playerFactory.create(createPlayerDto.organizationId);

    it("creates applicant and emits event successfully", async () => {
      const { controller, eventEmitter } = await createTestModule({
        createApplicant: jest.fn().mockResolvedValue(mockPlayer),
      });

      const result = await controller.createApplicant(createPlayerDto);

      expect(result).toEqual(mockPlayer);
      expect(playerService.createApplicant).toHaveBeenCalledWith(createPlayerDto);
      expect(eventEmitter.emit).toHaveBeenCalledWith(Events.Type.NewApplication, {
        emails: [createPlayerDto.email],
      });
    });

    it("includes guardian email in event when guardian exists", async () => {
      const dtoWithGuardian = {
        ...createPlayerDto,
        guardian: {
          email: "<EMAIL>",
          firstName: "Jane",
          lastName: "Doe",
          phone: "+1234567891",
        },
      };

      const { controller, eventEmitter } = await createTestModule({
        createApplicant: jest.fn().mockResolvedValue(mockPlayer),
      });

      await controller.createApplicant(dtoWithGuardian);

      expect(eventEmitter.emit).toHaveBeenCalledWith(Events.Type.NewApplication, {
        emails: [dtoWithGuardian.email, dtoWithGuardian.guardian.email],
      });
    });
  });
});
```

## Entity Relationship Testing Considerations

### 1. Player-Organization Relationships

- Test player assignment to organizations
- Test player removal from organizations
- Test organization membership validation

### 2. Player-Team Relationships

- Test team assignment and removal
- Test team status changes
- Test team-specific queries

### 3. Player-Guardian Relationships

- Test guardian creation and updates
- Test guardian-player associations
- Test guardian permissions

### 4. Player-Document Relationships

- Test document uploads and associations
- Test document removal
- Test document submission workflows

## Mock Data Patterns

### 1. Consistent ID Generation

```typescript
const testIds = {
  playerId: UUID.generate<PlayerId>(),
  organizationId: UUID.generate<OrganizationId>(),
  teamId: UUID.generate<TeamId>(),
  profileId: UUID.generate<ProfileId>(),
  imageId: UUID.generate<Assets.Image.ImageId>(),
};
```

### 2. Factory Usage Patterns

```typescript
const mockPlayer = playerFactory.create(testIds.organizationId);
const mockOrganization = organizationFactory.create();
const mockTeam = teamFactory.create(testIds.organizationId);
const mockProfile = profileFactory.create(UUID.generate<CoachUserId>());
```

### 3. Error Instance Creation

```typescript
const mockErrors = {
  domain: new DomainError("Test domain error"),
  unexpected: new UnexpectedError("Test unexpected error"),
  parsing: new ParsingError("Test parsing error"),
};
```

## Testing Best Practices

### 1. Test Isolation

- Each test should be independent
- Use fresh mocks for each test
- Clear any shared state between tests

### 2. Comprehensive Coverage

- Test all success paths
- Test all error paths
- Test edge cases and boundary conditions

### 3. Meaningful Assertions

- Verify return values
- Verify service method calls
- Verify event emissions
- Verify error types and messages

### 4. Readable Test Names

- Use descriptive test names
- Follow the pattern: "should [expected behavior] when [condition]"
- Group related tests in describe blocks

## Performance Considerations

### 1. Mock Efficiency

- Reuse mock instances where possible
- Avoid creating unnecessary mock data
- Use minimal mock implementations

### 2. Test Execution Speed

- Keep tests focused and fast
- Avoid real database operations
- Use synchronous mocks where possible

### 3. Memory Management

- Clean up large mock objects after tests
- Avoid memory leaks in test setup/teardown
