### Feature description

This is a brief summary of the feature we want to build and why we need it

### User perspective

- as a user, I should be able to...
- as a user I should see...
- as {another persona} I should be able to...

### Architecture notes

- we should consider this and this when building the feature, in accordance to our existing codebase patterns
- we should introduce this new pattern we're not using yet, because...
- we should be careful with this in order not to break that
- we should leverage this helper or method we already have to do that...

### Task list

- [ ] refactor this area
- [ ] create a new folder with the following files
- [ ] handle business logic concern n1
- [ ] handle business logic concern n2 ...
- [ ] check for type or lint errors, check tests
- [ ] write brief new tests for the basic stuff

### Closing notes

- any notes, conclusions, recommendations, observations or just a summary of the current feature work.
- Suggestions for updating the files inside `ai-assets/docs` folder to keep them up to date with recent work.
