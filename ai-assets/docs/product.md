# Product Overview

## Main Applications

### Coach Center API

- **Type**: Backend Service
- **Purpose**: Central API powering all Coach Center applications
- **Functionalities**:
  - User authentication and authorization
  - Team and player management
  - Match and training session data
  - Payment processing and financial integration
  - Organization and permissions management
- **Technical Details**: Built with NestJS, Docker support, environment-specific configurations

### Coach Center Marketing

- **Type**: Marketing Website
- **Purpose**: Public-facing landing page for Coach Center
- **Functionalities**:
  - Product information and features showcase
  - Marketing content and calls-to-action
  - Static site generation for performance
- **Technical Details**: Next.js application with export capability

### Coach Center Client

- **Type**: Web Application
- **Purpose**: Coach-facing platform for team management
- **Functionalities**:
  - Team and player administration
  - Match planning and review
  - Training session management
  - Player statistics and analytics
  - Organization settings and permissions
- **Technical Details**: React application with Webpack, hot module replacement

### Player Portal

- **Type**: Web Application
- **Purpose**: Player-focused interface for engagement
- **Functionalities**:
  - Player profile management
  - Event and training session information
  - Document management
  - Payment processing
- **Technical Details**: React application with Webpack, hot module replacement

### Match Mode

- **Type**: Progressive Web App
- **Purpose**: Match-day functionality for coaches
- **Functionalities**:
  - Real-time match tracking
  - Player performance monitoring
  - Match statistics and analytics
  - Offline capabilities
- **Technical Details**: React PWA built with Vite

## Shared Components

### Helpers Library

- **Purpose**: Cross-platform utility functions and types
- **Functionalities**:
  - Common utility functions
  - Shared type definitions
  - Test utilities

### UI Library

- **Purpose**: Shared React components based on Material UI
- **Functionalities**:
  - Reusable UI components
  - Design system implementation
  - Component documentation via Storybook
