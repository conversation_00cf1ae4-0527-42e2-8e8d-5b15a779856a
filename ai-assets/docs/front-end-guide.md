# Front-End Guide

This document outlines the high-level architectural patterns found in the React-based front-end applications (`apps/client`, `apps/player-portal`) and the shared UI library (`libs/ui`).

1.  **Component-Based Architecture:** The applications are built using React components, as evidenced by the numerous `.tsx` files within `/apps/client` and `/apps/player-portal`, and the `/libs/ui` library which likely contains reusable UI components.

2.  **Routing:** The presence of files like `/apps/client/src/app/routes.tsx` and `/apps/player-portal/src/app/routes.tsx` indicates the use of a routing library (likely React Router) to manage navigation between different pages or views.

3.  **State Management:** While the specific state management solution isn't immediately obvious from the file names, the presence of directories like `/libs/ui/src/lib/state` and files like `/libs/ui/src/lib/state/useAuth.ts`, `/libs/ui/src/lib/state/usePlayers.ts`, etc., suggests the use of React Hooks and potentially a context API or a state management library like Redux or Zustand to manage application state.

4.  **API Interaction (SDK):** The `/libs/ui/src/lib/sdk` directory indicates a software development kit (SDK) or a set of functions responsible for interacting with the back-end API. This promotes a centralized and organized way of fetching and sending data.

5.  **Shared UI Library (`libs/ui`):** The existence of the `/libs/ui` library suggests a focus on building reusable UI components, styles, and hooks that can be shared across different front-end applications (like `apps/client` and `apps/player-portal`). This promotes consistency and reduces code duplication.

6.  **Forms and Validation:** Files like `/apps/client/src/app/seasons/upsertForm.tsx`, `/apps/client/src/app/organization-settings/useOrganizationForm.tsx`, and those within `/libs/ui/src/lib/misc/forms` suggest the use of a form management library (possibly React Hook Form) and a validation library (like Zod, as hinted by `/libs/ui/src/lib/misc/forms/zod-hook-form.ts`).

7.  **Internationalization (i18n):** The presence of files like `/apps/client/src/i18n.ts` and directories like `/apps/client/src/locales/en` and `/apps/client/src/locales/es` indicates that the applications support multiple languages.

8.  **Authentication and Authorization (Front-end):** Similar to the back-end, the front-end applications handle authentication and authorization, as seen in the `/apps/client/src/app/auth` and `/apps/player-portal/src/app/auth` directories and files like `/apps/client/src/app/auth.guards.tsx` and `/apps/client/src/app/permissions/canAccess.tsx`.

9.  **Layouts and Styling:** Directories like `/apps/client/src/app/layouts` and `/apps/player-portal/src/app/layouts` and files related to styling (e.g., `.css` files, `/apps/client/src/app/global.styles.tsx`) suggest a structured approach to defining the visual structure and appearance of the applications.

10. **Hooks:** The extensive use of custom hooks (files starting with `use`, like `/apps/player-portal/src/app/application-form/useApplicationForm.ts`, `/apps/player-portal/src/app/application-form/usePlayerEligibility.tsx`, etc.) is a strong pattern in these React applications, promoting code reusability and logic encapsulation.
