### Backend, nest.js

- use `apps/api/src/app/auth/auth.spec.ts` as an example

- try to write more end-to-end style tests rather than ones with shallow mocking as it provides more realism. In the recommended example file you can see how we use in-memory mongodb to facilitate that. - we call an endpoint, let the logic go all the way down ot the database and then assert the DB entries themselves.

- if the previous rule is hard to implement for a given situation we want to test - call it out and ask the human for help and further guidance.

### Front end

We use the notion of an `SDKProvider` wrapping all our react components. It passes down functions that the rest of the code can use to fetch or mutate server data. That's the right point for you to apply stubbing or mocking when testing any logic that includes http calls.
