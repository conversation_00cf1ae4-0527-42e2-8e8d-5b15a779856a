### Creating and iterating on the plan

- use the `ai-assets/plans/example-plan.md` to deduct the expected structure and format of the planning doc. You MUST either create a doc and name it either at your assumption or use any user-provided hint, OR use an existing plan and continue from where it was left off, if the human tells you to.
- you're highly encouraged to ask follow up questions from the human and refine the plan as much as possible so that AI can do the coding with it later with ease and with as little effort/ambiguity as possible.

### Execution of the plan

- report which task you're working on if possible in the cli or other applicable way
- tick the checkboxes whenever you complete a task, feel free to leave any additional comments if needed, e.g. - notes about how you resolved it, anything we should consider and so on. Including if you failed to complete a step - do leave any helpful tips for the human reviewing your work.

### After we're done implementing

- analyze whether we need to update the `./ai-assets/docs` directory and any file inside. Maybe we introduced a new pattern - we should keep the doc files up to date.
