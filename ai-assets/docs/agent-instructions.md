- You can find general information about the product, tech, coding style guide and architecture inside `./ai-assets/docs` folder. You MUST always take a look there for context before you do any serious amount of work.

- Depending on the kind of task you're doing, be it front or back-end, you can find specialized guides in the `./ai-assets/docs` folder (front-end-guide.md, back-end-guide.md) - they go deeper on the specific areas. Otherwise `./ai-assets/docs/style-guide.md` contains the general style guide which should always be valid.

- The `./ai-assets/docs/repository.md` file contains general information about the current nx monorepo and high level apps and libs overview.

- If the user asks you to use "feature planning" as part of your workflow you must take a look at `./ai-assets/docs/feature-planning.md` which contains instructions on how to plan features and strictly follow.

- do not attempt writing tests unless the user asks you explicitly to do it.

- do try to fix any existing tests we break with our new work, but if it proves hard - limit it to just a few retries and give up. Leave a note for help in the planning doc or/and respond to the human, include any useful info about the issue. Do ask follow up clarifying questions if you believe this can help.

- if you absolutely have to type cast to `any` or type cast at all - make sure to leave a comment explaining why

-
