{"name": "coach-center", "version": "0.0.0", "engines": {"node": "22.1.0"}, "scripts": {"start": "nx run-many --target=serve --all --parallel", "start:api": "nx serve api", "build": "nx run-many --target=build --all --parallel", "test": "nx run-many --target=test --all --parallel", "e2e": "nx run-many --target=e2e --all --parallel", "lint": "nx run-many --target=lint --all --parallel", "type-check": "nx run-many --target=type-check --all", "format:check": "nx format:check --all", "format:write": "nx format:write --all", "prepare": "husky install", "api:docker": "nx run api:docker", "build-client": "nx run client:build --configuration=production", "build-staging-client": "nx run client:build --configuration=staging", "build-demo-client": "nx run client:build --configuration=demo", "build-player-portal": "nx run player-portal:build --configuration=production", "build-staging-player-portal": "nx run player-portal:build --configuration=staging", "build-demo-player-portal": "nx run player-portal:build --configuration=demo", "build-marketing": "nx run marketing:build --configuration=production && nx run marketing:export", "create-migration": "migrate-mongo create -- ", "perform-migrations": "migrate-mongo up", "status-migrations": "migrate-mongo status", "analyze:client": "webpack-bundle-analyzer dist/apps/client/stats.json"}, "private": true, "devDependencies": {"@babel/core": "^7.20.12", "@babel/preset-react": "^7.14.5", "@babel/preset-typescript": "7.12.13", "@emotion/babel-plugin": "11.11.0", "@nestjs/schematics": "10.2.3", "@nestjs/testing": "10.4.15", "@nrwl/js": "19.8.9", "@nx/cypress": "20.3.0", "@nx/eslint": "20.3.0", "@nx/eslint-plugin": "20.3.0", "@nx/jest": "20.3.0", "@nx/js": "20.3.0", "@nx/nest": "20.3.0", "@nx/next": "20.3.0", "@nx/node": "20.3.0", "@nx/react": "20.3.0", "@nx/vite": "20.3.0", "@nx/web": "20.3.0", "@nx/webpack": "20.3.0", "@nx/workspace": "20.3.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.7", "@svgr/webpack": "^6.1.2", "@swc/cli": "~0.3.12", "@swc/jest": "^0.2.38", "@testing-library/react": "15.0.6", "@types/bcrypt": "^5.0.2", "@types/concat-stream": "^2.0.0", "@types/draftjs-to-html": "^0.8.1", "@types/html-to-draftjs": "^1.4.0", "@types/jest": "29.5.14", "@types/lodash": "^4.14.191", "@types/multer": "^1.4.7", "@types/node": "22.15.3", "@types/passport-jwt": "^4.0.1", "@types/react": "18.3.1", "@types/react-dom": "18.3.0", "@types/react-draft-wysiwyg": "^1.13.4", "@types/react-router-dom": "5.3.3", "@types/supertest": "^6.0.2", "@types/uuid": "^9.0.0", "@typescript-eslint/eslint-plugin": "8.19.0", "@typescript-eslint/parser": "8.19.0", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/coverage-v8": "^1.0.4", "@vitest/ui": "^1.3.1", "autoprefixer": "10.4.13", "babel-jest": "29.7.0", "babel-loader": "^9.1.2", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.4.0", "cypress": "13.17.0", "eslint": "9.17.0", "eslint-config-next": "15.1.3", "eslint-config-prettier": "9.1.0", "eslint-plugin-cypress": "4.1.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-react": "7.37.3", "eslint-plugin-react-hooks": "5.0.0", "fake-indexeddb": "^6.0.0", "husky": "^8.0.3", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "jsdom": "~22.1.0", "lint-staged": "^13.1.0", "migrate-mongo": "^10.0.0", "mongodb-memory-server": "^10.1.3", "nx": "20.3.0", "postcss": "8.4.38", "prettier": "2.8.1", "react-refresh": "^0.14.0", "style-loader": "^3.3.0", "stylus": "0.59.0", "stylus-loader": "^7.1.0", "supertest": "^7.0.0", "swc-loader": "^0.2.6", "tailwindcss": "3.4.3", "testdouble": "^3.20.2", "ts-jest": "29.1.0", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "5.6.3", "url-loader": "^4.1.1", "vite": "^5.1.4", "vite-plugin-pwa": "^0.21.1", "vitest": "^1.3.1", "webpack": "5.97.1", "webpack-bundle-analyzer": "^4.9.0", "webpack-merge": "^5.8.0"}, "dependencies": {"@azure/storage-blob": "^12.15.0", "@date-io/date-fns": "^2.16.0", "@emotion/react": "11.11.1", "@emotion/styled": "11.11.0", "@faker-js/faker": "^7.6.0", "@mui/base": "^5.0.0-beta.58", "@mui/icons-material": "^5.11.0", "@mui/lab": "^5.0.0-alpha.53", "@mui/material": "^5.11.4", "@mui/utils": "^5.11.2", "@mui/x-charts": "^7.22.1", "@mui/x-data-grid": "^7.21.0", "@mui/x-date-pickers": "^5.0.13", "@nestjs/common": "10.4.15", "@nestjs/config": "3.3.0", "@nestjs/core": "10.4.15", "@nestjs/event-emitter": "^2.1.1", "@nestjs/jwt": "^10.0.1", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "10.4.15", "@sendgrid/mail": "^7.7.0", "@sentry/integrations": "^7.30.0", "@sentry/node": "^7.30.0", "@sentry/tracing": "^7.30.0", "@swc/helpers": "^0.5.17", "@swc/core": "~1.5.7", "@swc-node/register": "~1.9.1", "@tanstack/react-query": "^4.22.0", "@tanstack/react-query-devtools": "^4.22.0", "@testing-library/user-event": "^14.6.1", "axios": "1.7.9", "bcrypt": "^5.1.1", "concat-stream": "^2.0.0", "core-js": "3.36.1", "crypto": "^1.0.1", "date-fns": "^2.29.3", "dexie": "^4.0.11", "dexie-react-hooks": "^1.1.7", "draft-js": "^0.11.7", "draftjs-to-html": "^0.9.1", "exhaustive": "^1.1.1", "gocardless-nodejs": "^3.9.0", "html-to-draftjs": "^1.5.0", "i18next": "^24.2.1", "i18next-browser-languagedetector": "^8.0.2", "immer": "^10.1.1", "lodash": "^4.17.21", "mongodb": "^5.9.2", "multer": "^1.4.5-lts.1", "next": "14.2.3", "normalize.css": "^8.0.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "react": "18.3.1", "react-dom": "18.3.1", "react-draft-wysiwyg": "^1.15.0", "react-hook-form": "^7.41.5", "react-i18next": "^15.4.0", "react-icons": "^4.8.0", "react-is": "18.3.1", "react-number-format": "^5.2.2", "react-router-dom": "6.11.2", "react-use": "^17.4.0", "recharts": "^2.13.3", "reflect-metadata": "^0.1.13", "regenerator-runtime": "0.13.7", "rxjs": "^7.0.0", "string-strip-html": "^13.0.6", "stripe": "^12.13.0", "ts-pattern": "^5.7.0", "tslib": "^2.3.0", "unique-names-generator": "^4.7.1", "uuid": "^9.0.0", "zod": "^3.20.2"}, "lint-staged": {"*": "prettier --write --ignore-unknown", "*{js,jsx,ts,tsx}": "eslint --fix"}}