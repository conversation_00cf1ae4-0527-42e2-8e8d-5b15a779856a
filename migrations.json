{"migrations": [{"version": "20.0.0-beta.7", "description": "Migration for v20.0.0-beta.7", "implementation": "./src/migrations/update-20-0-0/move-use-daemon-process", "package": "nx", "name": "move-use-daemon-process"}, {"version": "20.0.1", "description": "Set `useLegacyCache` to true for migrating workspaces", "implementation": "./src/migrations/update-20-0-1/use-legacy-cache", "x-repair-skip": true, "package": "nx", "name": "use-legacy-cache"}, {"cli": "nx", "version": "20.0.0-beta.5", "description": "replace getJestProjects with getJestProjectsAsync", "implementation": "./src/migrations/update-20-0-0/replace-getJestProjects-with-getJestProjectsAsync", "package": "@nx/jest", "name": "replace-getJestProjects-with-getJestProjectsAsync"}, {"version": "20.2.0-beta.5", "description": "Update TypeScript ESLint packages to v8.13.0 if they are already on v8", "implementation": "./src/migrations/update-20-2-0/update-typescript-eslint-v8-13-0", "package": "@nx/eslint", "name": "update-typescript-eslint-v8.13.0"}, {"version": "20.3.0-beta.1", "description": "Update ESLint flat config to include .cjs, .mjs, .cts, and .mts files in overrides (if needed)", "implementation": "./src/migrations/update-20-3-0/add-file-extensions-to-overrides", "package": "@nx/eslint", "name": "add-file-extensions-to-overrides"}, {"cli": "nx", "version": "20.2.0-beta.2", "description": "Update the ModuleFederationConfig import use @nx/module-federation.", "factory": "./src/migrations/update-20-2-0/migrate-mf-imports-to-new-package", "package": "@nx/react", "name": "update-20-2-0-update-module-federation-config-import"}, {"cli": "nx", "version": "20.2.0-beta.2", "description": "Update the withModuleFederation import use @nx/module-federation/webpack.", "factory": "./src/migrations/update-20-2-0/migrate-with-mf-import-to-new-package", "package": "@nx/react", "name": "update-20-2-0-update-with-module-federation-import"}, {"cli": "nx", "version": "20.3.0-beta.2", "description": "If workspace includes Module Federation projects, ensure the new @nx/module-federation package is installed.", "factory": "./src/migrations/update-20-3-0/ensure-nx-module-federation-package", "package": "@nx/react", "name": "ensure-nx-module-federation-package"}]}