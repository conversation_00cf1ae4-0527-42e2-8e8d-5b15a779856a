import { DeepMap, DeepPartial, <PERSON>Error, ResolverR<PERSON>ult, FieldValues } from "react-hook-form";
import { pickBy, get, isPlainObject, mapValues } from "lodash/fp";
import { Dictionary, set } from "lodash";
import { GenericRecord, ParsingError } from "@mio/helpers";
import { ErrorFormatter } from "./map-errors";

export type FieldErrorsNested<TFieldValues> = DeepMap<DeepPartial<TFieldValues>, FieldError>;

const toFieldErrors = <T extends GenericRecord>(
  data: T | unknown[],
  formatter: typeof ErrorFormatter,
): FieldErrorsNested<T> => {
  const result = {};

  const mapNestedErrors = (value: any, key: string): void => {
    if (isPlainObject(value)) {
      // Recursively handle nested objects
      mapValues(
        (nestedValue: unknown, nestedKey: string) =>
          mapNestedErrors(nestedValue, `${key}.${nestedKey}`),
        value,
      );
    } else {
      set(result, key, {
        message: formatter.format(get(0, value), key),
        type: "validation",
      });
    }
  };

  Object.entries(data)
    .sort(([key1], [key2]) => key1.length - key2.length)
    .forEach(([key, value]) => {
      mapNestedErrors(value, key);
    });

  return result as FieldErrorsNested<T>;
};

export const useFormResolverNested = <T extends FieldValues, E extends FieldValues = T>(
  parse: (data: unknown) => T | ParsingError,
  formatter = ErrorFormatter,
  filter: (data: Dictionary<unknown>) => Dictionary<unknown> = (data) => data,
) => {
  return (data: GenericRecord): ResolverResult<E> => {
    // Sanitize values, removing empty fields
    const sanitizedValues = pickBy((value) => value !== "", data || {});

    const filteredValues = filter(sanitizedValues);
    const parsed = parse(filteredValues);

    if (parsed instanceof ParsingError) {
      const mappedErrors = toFieldErrors(parsed.errors, formatter);

      return {
        values: {} as E, // Empty values object if errors are present
        errors: mappedErrors as FieldErrorsNested<E>, // Return mapped errors
      };
    } else {
      return {
        values: parsed as unknown as E, // First convert to 'unknown', then to 'E'
        errors: {} as FieldErrorsNested<E>, // No errors
      };
    }
  };
};
