import {
  CustomEnumErrors,
  DateErrors,
  EmailErrors,
  PhoneErrors,
  StringErrors,
  UUIDErrors,
} from "@mio/helpers";

/* Those functions help us use consistent error messages across the app.
     If they are too generic for some cases we can override them.
  */

const commonFormatters = {
  invalidField: () => "Invalid",
  required: () => "Required",
};

const stringFormatters = {
  tooShort: () => "Too short",
  tooLong: () => "Too long",
};

type FormattingFunction = (errorMessage?: unknown, fieldName?: string) => string;

export const ErrorFormatter = {
  format: (...[errorMessage]: Parameters<FormattingFunction>): string => {
    if (!errorMessage) {
      return "";
    }

    switch (errorMessage) {
      case StringErrors.Required:
        return commonFormatters.required();
      case StringErrors.NotAString:
        return commonFormatters.invalidField();
      case StringErrors.TooShort:
        return stringFormatters.tooShort();
      case StringErrors.TooLong:
        return stringFormatters.tooLong();
      default:
        return `Invalid or missing.`;
    }
  },

  extendFormatter: (...cases: FormattingFunction[]) => {
    return {
      format: (...[errorMessage, fieldName]: Parameters<FormattingFunction>) => {
        const result = cases.reduce((_total, formattingFunction) => {
          return formattingFunction(errorMessage, fieldName) || "";
        }, "");

        return result || ErrorFormatter.format(errorMessage);
      },

      extendFormatter: ErrorFormatter.extendFormatter,
    };
  },
};

export const dateFormatters = {
  notPast: (fieldName: string) => `${fieldName} must be in the past`,
  notFuture: (fieldName: string) => `${fieldName} must be in the future`,
  notPresent: (fieldName: string) => `${fieldName} must be in the present`,
  notPastOrPresent: (fieldName: string) => `${fieldName} must be in the past or present`,
};

export const mapStringError = (error: StringErrors) => {
  switch (error) {
    case StringErrors.Required:
      return commonFormatters.required();
    case StringErrors.NotAString:
      return commonFormatters.invalidField();
    case StringErrors.TooShort:
      return stringFormatters.tooShort();
    case StringErrors.TooLong:
      return stringFormatters.tooLong();
  }
};

export const mapDateError = (error: DateErrors, fieldName: string) => {
  switch (error) {
    case DateErrors.Required:
      return commonFormatters.required();
    case DateErrors.InvalidDate:
    case DateErrors.InvalidTime:
      return commonFormatters.invalidField();
    case DateErrors.NotFuture:
      return dateFormatters.notFuture(fieldName);
    case DateErrors.NotPast:
      return dateFormatters.notPast(fieldName);
    case DateErrors.NotPresent:
      return dateFormatters.notPresent(fieldName);
    case DateErrors.NotPastOrPresent:
      return dateFormatters.notPastOrPresent(fieldName);
    case DateErrors.TooSmall:
    case DateErrors.TooBig:
      /* not very generic error, should be overriden in the specific form instead*/
      return "";
  }
};

export const mapEnumError = (error: CustomEnumErrors) => {
  switch (error) {
    case CustomEnumErrors.Required:
      return commonFormatters.required();
    case CustomEnumErrors.InvalidValue:
      return commonFormatters.invalidField();
    default:
      return commonFormatters.required();
  }
};

export const mapEmailErrors = (error: EmailErrors) => {
  switch (error) {
    case EmailErrors.Required:
      return commonFormatters.required();
    case EmailErrors.InvalidEmail:
      return commonFormatters.invalidField();
    case EmailErrors.SameGuardianPlayerEmail:
      return "Parent/guardian and player emails cannot be the same.";
  }
};

export const mapPhoneErrors = (error: PhoneErrors) => {
  switch (error) {
    case PhoneErrors.Required:
      return commonFormatters.required();
    case PhoneErrors.InvalidPhone:
      return commonFormatters.invalidField();
    case PhoneErrors.TooShort:
      return stringFormatters.tooShort();
    case PhoneErrors.TooLong:
      return stringFormatters.tooLong();
  }
};

export const mapUUIDErrors = (error: UUIDErrors) => {
  switch (error) {
    case UUIDErrors.NotAString:
      return commonFormatters.invalidField();
    case UUIDErrors.InvalidUUID:
      return commonFormatters.invalidField();
  }
};
