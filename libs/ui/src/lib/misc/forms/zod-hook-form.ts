/* makes the connection between ParsingError and react-hook-form errors */

import { DeepMap, DeepPartial, FieldError } from "react-hook-form";
import { pickBy, get } from "lodash/fp";
import { set } from "lodash";

import { GenericRecord, ParsingError } from "@mio/helpers";
import { ErrorFormatter } from "./map-errors";

export type FieldErrors<TFieldValues> = DeepMap<DeepPartial<TFieldValues>, FieldError>;

const toFieldErrors = <T extends GenericRecord>(
  data: T | unknown[],
  formatter: typeof ErrorFormatter,
) => {
  const result = {};

  Object.entries(data)
    .sort(([key1], [key2]) => key1.length - key2.length)
    .forEach(([key, value]) => {
      set(result, key, {
        message: formatter.format(get(0, value), key),
        type: "validation",
      });
    });

  return result as FieldErrors<T>;
};

export const useFormResolver = <T, E = T>(
  parse: (data: unknown) => T | ParsingError,
  formatter = ErrorFormatter,
) => {
  return (data: GenericRecord) => {
    const sanitizedValues = pickBy((value) => value !== "", data || {});

    const parsed = parse(sanitizedValues);

    if (parsed instanceof ParsingError) {
      const mappedErrors = toFieldErrors(parsed.errors, formatter);

      return {
        values: {},
        errors: mappedErrors as FieldErrors<E>,
      };
    } else {
      return {
        values: parsed,
        errors: {} as FieldErrors<E>,
      };
    }
  };
};
