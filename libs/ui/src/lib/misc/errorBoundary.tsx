import { Component, PropsWithChildren, ReactNode } from "react";

type Props = PropsWithChildren<{
  fallback: ReactNode;
  catchWhen: (err: unknown) => boolean;
}>;

type State = {
  error?: unknown;
};

export class ErrorBoundary extends Component<Props, State> {
  state = { error: undefined };

  static getDerivedStateFromError(error: unknown) {
    // Update state so the next render will show the fallback UI.
    return { hasError: error };
  }

  componentDidCatch(error: unknown, errorInfo: unknown) {
    //TODO: log
  }

  render() {
    if (this.props.catchWhen(this.state.error)) {
      return this.props.fallback;
    }

    return this.props.children;
  }
}
