import { FC, ReactElement, PropsWithChildren } from "react";
import { Navigate } from "react-router-dom";

import { PageLoader } from "../../compositions";
import { AuthFunction } from "./types";
import { useReturnUrl } from "./urlSearchParams";

type Props = PropsWithChildren<{
  redirectTo: string;
  authFunction: AuthFunction;
}>;

export const PublicRoute: FC<Props> = ({ children, authFunction, redirectTo }) => {
  const { isLoading, isLogged } = authFunction();

  const returnUrl = useReturnUrl();

  if (isLoading) {
    return <PageLoader message="Loggin in..." />;
  }

  if (isLogged && returnUrl) {
    return <Navigate to={returnUrl} />;
  }

  if (isLogged) {
    return <Navigate to={redirectTo} />;
  }

  return children as ReactElement;
};
