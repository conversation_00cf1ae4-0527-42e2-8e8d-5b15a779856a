import { FC, ReactElement, PropsWithChildren } from "react";
import { Navigate } from "react-router-dom";

import { useCreateReturnUrl } from "./urlSearchParams";
import { AuthFunction } from "./types";
import { PageLoader } from "../../compositions";

type Props = PropsWithChildren<{
  redirectTo: string;
  authFunction: AuthFunction;
}>;

export const PrivateRoute: FC<Props> = ({ children, authFunction, redirectTo }) => {
  const { isLoading, isLogged } = authFunction();

  const url = useCreateReturnUrl(redirectTo);

  if (isLoading) {
    return <PageLoader message="Loggin in..." />;
  }

  if (!isLogged) {
    return <Navigate to={url} />;
  }

  return children as ReactElement;
};
