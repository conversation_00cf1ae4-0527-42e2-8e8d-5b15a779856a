import { FC, PropsWithChildren, ReactElement, useEffect } from "react";

import { OrganizationId } from "@mio/helpers";

import { useOrganization, useOrganizationId, useProvidedCurrentProfile } from "../../state";
import { useUrlSearchParams } from "../guards";
import { Alert } from "../../primitives";

type Props = {
  redirect: (orgId: OrganizationId) => void;
  children?: never;
};

export const CurrentOrganizationProvider: FC<Props> = ({ redirect }) => {
  const targetOrganization = useOrganization();
  const organizationId = useOrganizationId();

  useEffect(() => {
    if (targetOrganization && !organizationId) {
      redirect(targetOrganization.id);
    }
  }, [targetOrganization, organizationId, redirect]);

  return null;
};

export const usePersistedActiveOrganization = () => {
  const key = "activeOrganizationId";

  const savedOrganizationId = localStorage.getItem(key);

  const setActiveOrganization = (newOrganizationId: OrganizationId, redirectTo = "") => {
    localStorage.setItem(key, newOrganizationId);

    /* reload the window to start with fresh state */
    window.location.assign(redirectTo);
  };

  return { activeOrganizationId: savedOrganizationId, setActiveOrganization };
};

export const OrganizationResolver: FC<PropsWithChildren> = ({ children }) => {
  const profile = useProvidedCurrentProfile();
  const organizationId = useOrganizationId();
  const existingSearchParams = useUrlSearchParams();
  const { setActiveOrganization } = usePersistedActiveOrganization();

  const activeOrganization = useOrganization();

  useEffect(() => {
    if (activeOrganization && organizationId !== activeOrganization.id) {
      /* preserve any existing url search params */
      const baseUrl = `/${activeOrganization.id}`;

      const redirectUrl = existingSearchParams ? baseUrl + "?" + existingSearchParams : baseUrl;

      setActiveOrganization(activeOrganization.id, redirectUrl);
    }
  }, [activeOrganization, organizationId, setActiveOrganization, existingSearchParams]);

  if (profile.organizations.length === 0) {
    return <Alert severity="warning">You're not a part of any organization yet.</Alert>;
  }

  return children as ReactElement;
};
