import { FC, PropsWithChildren } from "react";

import { isStateError, StateErrors, useCurrentUser } from "../../state";
import { PageLoader, PageError } from "../../compositions";
import { ErrorBoundary } from "../errorBoundary";

export const CurrentUserProvider: FC<PropsWithChildren> = ({ children }) => {
  const { status } = useCurrentUser();

  if (status === "loading") {
    return <PageLoader message="Loading user..." />;
  }

  if (status === "error") {
    return <PageError message="Failed to load user." />;
  }

  if (status === "success") {
    return (
      <ErrorBoundary
        fallback={<PageError message="Failed to load user." />}
        catchWhen={(err) => isStateError(err) && err.message === StateErrors.UserNotFound}
      >
        {children}
      </ErrorBoundary>
    );
  }

  return <PageError message="User not found." />;
};
