import { FC, PropsWithChildren } from "react";
import { QueryClientProvider, QueryClient } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";

const queryClient = new QueryClient();

type Props = PropsWithChildren<{
  queryTools?: boolean;
}>;

export const StateProvider: FC<Props> = ({ children, queryTools = false }) => {
  return (
    <QueryClientProvider client={queryClient}>
      <>
        {children}
        {queryTools && <ReactQueryDevtools />}
      </>
    </QueryClientProvider>
  );
};
