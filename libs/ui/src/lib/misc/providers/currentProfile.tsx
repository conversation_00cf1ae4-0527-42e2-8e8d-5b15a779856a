import { FC, PropsWithChildren } from "react";

import { isStateError, StateErrors, useCurrentProfile } from "../../state";
import { PageLoader, PageError } from "../../compositions";
import { ErrorBoundary } from "../errorBoundary";

export const CurrentProfileProvider: FC<PropsWithChildren> = ({ children }) => {
  const query = useCurrentProfile();

  if (query.status === "loading") {
    return <PageLoader message="Loading profile..." />;
  }

  if (query.status === "error") {
    return <PageError message="Failed to load profile." />;
  }

  if (query.status === "success" && query.data) {
    return (
      <ErrorBoundary
        fallback={<PageError message="Profile not found." />}
        catchWhen={(err) => isStateError(err) && err.message === StateErrors.ProfileNotFound}
      >
        {children}
      </ErrorBoundary>
    );
  }

  return <PageError message="Profile not found." />;
};
