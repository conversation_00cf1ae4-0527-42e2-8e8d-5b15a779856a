import { FC, PropsWithChildren, useEffect, useState } from "react";

import { DomainError, ErrorMessages } from "@mio/helpers";

import { SDK, SDKContext } from "../../state";
import { PageLoader } from "../../compositions";

type Props = PropsWithChildren<{
  sdk: Promise<SDK>;
}>;

export const SDKProvider: FC<Props> = ({ sdk, children }) => {
  const [resolvedSdk, setResolvedSdk] = useState<undefined | SDK>(undefined);

  useEffect(() => {
    sdk
      .then((result) => {
        setResolvedSdk(result);
      })
      .catch((err) => {
        throw new DomainError(ErrorMessages.SDKUninitialized, { err });
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (!resolvedSdk) {
    return <PageLoader message="Loading sdk..." />;
  }

  return <SDKContext.Provider value={resolvedSdk}>{children}</SDKContext.Provider>;
};
