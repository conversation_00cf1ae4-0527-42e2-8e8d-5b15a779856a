import { FC, PropsWithChildren } from "react";

import { isStateError, StateErrors, useLoadPermissions, useOrganization } from "../../state";
import { PageLoader, PageError } from "../../compositions";
import { ErrorBoundary } from "../errorBoundary";

export const CurrentPermissionsProvider: FC<PropsWithChildren> = ({ children }) => {
  const organization = useOrganization();
  const query = useLoadPermissions(organization.id);

  if (query.status === "loading") {
    return <PageLoader message="Loading permissions..." />;
  }

  if (query.status === "error") {
    return <PageError message="Failed to load user permissions." />;
  }

  if (query.status === "success" && query.data.length > 0) {
    return (
      <ErrorBoundary
        fallback={<PageError message="Permissions not found." />}
        catchWhen={(err) => isStateError(err) && err.message === StateErrors.MissingPermissions}
      >
        {children}
      </ErrorBoundary>
    );
  }

  return <PageError message={`You don't have any permissions to ${organization.displayName}.`} />;
};
