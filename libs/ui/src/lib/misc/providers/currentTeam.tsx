import { FC, useEffect } from "react";
import { useNavigate } from "react-router-dom";

import { useGetTeams, useOrganization, useTeamId } from "../../state";

type Props = {
  children: JSX.Element;
};

export const CurrentTeamProvider: FC<Props> = ({ children }) => {
  const organization = useOrganization();
  const teams = useGetTeams(organization.id);
  const selectedTeam = useTeamId() || "";
  const navigate = useNavigate();

  useEffect(() => {
    if (teams.isSuccess && teams.data.length === 1 && !selectedTeam) {
      const activeTeam = teams.data[0];
      const url = `/${organization.id}/teams/${activeTeam.id}`;
      navigate(url);
    }
  }, [teams.isSuccess, teams.data, navigate, organization.id, selectedTeam]);

  return children;
};
