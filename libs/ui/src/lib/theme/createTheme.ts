import { createTheme } from "@mui/material/styles";

declare module "@mui/material/styles" {
  interface Palette {
    other: Record<"purple" | "blue" | "lightGrey", string>;
  }

  interface PaletteOptions {
    other: Record<"purple" | "blue" | "lightGrey", string>;
  }
}

export const createCustomTheme = () => {
  return createTheme({
    palette: {
      primary: {
        light: "#00655b",
        main: "#009688",
        dark: "#00695f",
      },
      secondary: {
        main: "#6B4759",
      },
      other: {
        purple: "#2F3899",
        blue: "#4287f5",
        lightGrey: "#e2e2e2",
      },
    },
    components: {
      MuiButton: {
        styleOverrides: {
          root: {
            textTransform: "none",
          },
        },
      },
    },
  });
};
