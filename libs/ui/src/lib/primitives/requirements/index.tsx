import { FC } from "react";
import styled from "@emotion/styled";

import Tick from "../../../assets/tick.svg";
import Alert from "../../../assets/alert.svg";
import { HiddenSpan } from "../../misc";
import { Box, Typography } from "..";

type Requirement = {
  displayLabel: string;
  readableLabel: string;
  valid: boolean;
};

export type RequirementsProps = {
  value: string;
  name: string; // e.g.: "Password"
  requirements: Requirement[];
  readableLabelId: string;
};

type ScreenReaderTextProps = {
  value: string;
  name: string; // e.g.: "Password"
  requirements: Requirement[];
  allRequirementsMet: boolean;
  readableLabelId: string;
};

const StatusIcon = styled.img`
  width: 1.1em;
`;

const RequirementItem = styled.li`
  display: flex;
  margin-bottom: 6px;
`;

const ScreenReaderText: FC<ScreenReaderTextProps> = ({
  value,
  requirements,
  allRequirementsMet,
  readableLabelId,
  name,
}) => {
  const readableText = requirements.map((req) => req.readableLabel).join(". ");

  // Requirements sorted in order for screen readers to read aloud
  // the requirements that have been met first.
  const sortedRequirements = [...requirements].sort((a, b) => {
    if (a.valid && !b.valid) {
      return -1;
    }
    if (!a.valid && b.valid) {
      return 1;
    }
    return 0;
  });

  return (
    <>
      <HiddenSpan role={value ? "alert" : ""} aria-hidden={!value}>
        {allRequirementsMet && `All ${name} requirements are met. `}
        {sortedRequirements.map(
          (req) =>
            `${req.valid ? "Requirement met" : "Requirement not met"}: ${req.readableLabel}. `,
        )}
      </HiddenSpan>
      <HiddenSpan id={readableLabelId}>
        {name} must meet the following requirements. {readableText}.
      </HiddenSpan>
    </>
  );
};

export const Requirements: FC<RequirementsProps> = ({
  value,
  requirements,
  readableLabelId,
  name,
  ...props
}) => {
  const currentValue = value || "";
  const allRequirementsMet = requirements.every((req) => req.valid);
  return (
    <>
      <Box {...props} component="ul" pl={0}>
        {requirements.map((req) => (
          <RequirementItem key={req.displayLabel}>
            {req.valid ? (
              <Box
                component={StatusIcon}
                src={Tick}
                aria-hidden="true"
                alt="Requirement met icon"
                mr={1}
              />
            ) : (
              <Box
                component={StatusIcon}
                src={Alert}
                aria-hidden="true"
                alt="Requirement not met icon"
                mr={1}
              />
            )}
            <Typography
              aria-label={`${req.valid ? "Requirement met" : "Requirement not met"}: ${
                req.readableLabel
              }`}
            >
              {req.displayLabel}
            </Typography>
          </RequirementItem>
        ))}
      </Box>

      <ScreenReaderText
        value={currentValue}
        requirements={requirements}
        allRequirementsMet={allRequirementsMet}
        readableLabelId={readableLabelId}
        name={name}
      />
    </>
  );
};

export default Requirements;
