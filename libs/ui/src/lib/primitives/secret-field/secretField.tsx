import { FC, useState } from "react";
import { omit } from "lodash/fp";
import InputAdornment from "@mui/material/InputAdornment";
import Visibility from "@mui/icons-material/Visibility";
import VisibilityOff from "@mui/icons-material/VisibilityOff";
import IconButton from "@mui/material/IconButton";
import TextField, { TextFieldProps } from "@mui/material/TextField";

type SecretFieldProps = TextFieldProps & {
  buttonLabel?: string;
  onToggle?: (isVisible: boolean) => void;
};

export const SecretField: FC<SecretFieldProps> = (props) => {
  const initialType = props.type || "text";
  const [isVisible, setVisible] = useState(false);
  const currentType = isVisible ? initialType : "password";

  /* prevent DOM warnings about unknown props */
  const ommittedProps = omit("buttonLabel", props);

  return (
    <TextField
      {...ommittedProps}
      type={currentType}
      InputProps={{
        endAdornment: (
          <InputAdornment position="end">
            <IconButton
              aria-label={props.buttonLabel || "toggle visibility"}
              onClick={() => {
                setVisible((state) => !state);
                if (props.onToggle) {
                  props.onToggle(!isVisible);
                }
              }}
              edge="end"
            >
              {isVisible ? <VisibilityOff /> : <Visibility />}
            </IconButton>
          </InputAdornment>
        ),
      }}
    />
  );
};

export default SecretField;
export { type SecretFieldProps };
