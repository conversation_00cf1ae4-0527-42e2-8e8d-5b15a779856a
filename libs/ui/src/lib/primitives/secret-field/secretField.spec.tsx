import { render, fireEvent } from "@testing-library/react";

import { SecretField } from "./secretField";

describe(SecretField.displayName, () => {
  it("input value is hidden by default", () => {
    const { getByLabelText } = render(<SecretField id="password" label="Password" />);
    const input = getByLabelText("Password");
    expect(input).toHaveProperty("type", "password");
  });

  it("input visibility can be toggled", () => {
    const { getByLabelText, getByRole } = render(
      <SecretField id="password" label="Password" buttonLabel="toggle visibility" type="text" />,
    );
    const input = getByLabelText("Password");
    const toggleButton = getByRole("button", { name: "toggle visibility" });

    expect(input).toHaveProperty("type", "password");

    fireEvent.click(toggleButton);
    expect(input).toHaveProperty("type", "text");

    fireEvent.click(toggleButton);
    expect(input).toHaveProperty("type", "password");
  });
});
