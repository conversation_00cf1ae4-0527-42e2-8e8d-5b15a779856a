import { AxiosInstance } from "axios";

import {
  apiUrls,
  buildUrlWithParams,
  UrlParams,
  isError,
  OrganizationId,
  FootballMatchAttemptFor,
  TeamId,
  FootballMatchPlan,
  UrlQuery,
} from "@mio/helpers";

import { applyGenericErrorHandling } from "../genericErrorHandling";

export const makeGetAttemptsFor =
  (axiosClient: AxiosInstance) =>
  (organizationId: OrganizationId, teamId: TeamId, footballMatchPlanId: FootballMatchPlan.Id) =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.footballMatchAttemptsFor, {
          [UrlParams.OrganizationId]: organizationId,
          [UrlParams.TeamId]: teamId,
          [UrlParams.FootballMatchPlanId]: footballMatchPlanId,
        }),
      )
      .then((res) => {
        const result = FootballMatchAttemptFor.Entity.toEntities(res.data);

        if (isError(result)) {
          throw result;
        }
        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeGetExtraAttemptsFor =
  (axiosClient: AxiosInstance) =>
  (organizationId: OrganizationId, teamId: TeamId, footballMatchPlanId: FootballMatchPlan.Id) =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.footballMatchAttemptsForExtra, {
          [UrlParams.OrganizationId]: organizationId,
          [UrlParams.TeamId]: teamId,
          [UrlParams.FootballMatchPlanId]: footballMatchPlanId,
        }),
      )
      .then((res) => {
        const result = FootballMatchAttemptFor.Entity.toEntities(res.data);

        if (isError(result)) {
          throw result;
        }
        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeGetAttemptFor =
  (axiosClient: AxiosInstance) =>
  (
    organizationId: OrganizationId,
    teamId: TeamId,
    footballMatchAttemptForId: FootballMatchAttemptFor.Id,
  ) =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.footballMatchAttemptFor, {
          [UrlParams.OrganizationId]: organizationId,
          [UrlParams.TeamId]: teamId,
          [UrlParams.FootballMatchAttemptForId]: footballMatchAttemptForId,
        }),
      )
      .then((res) => {
        const result = FootballMatchAttemptFor.Entity.toEntity(res.data);

        if (isError(result)) {
          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeCreateAttemptFor =
  (axiosClient: AxiosInstance) => (footballMatchAttemptFor: FootballMatchAttemptFor.CreateDto) =>
    axiosClient
      .post(
        buildUrlWithParams(apiUrls.footballMatchAttemptsFor, {
          [UrlParams.OrganizationId]: footballMatchAttemptFor.organizationId,
          [UrlParams.TeamId]: footballMatchAttemptFor.teamId,
          [UrlParams.FootballMatchPlanId]: footballMatchAttemptFor.footballMatchPlanId,
        }),
        footballMatchAttemptFor,
      )
      .then((res) => {
        const result = FootballMatchAttemptFor.Entity.toEntity(res.data);

        if (isError(result)) {
          result.addContext({
            service: makeCreateAttemptFor.name,
            method: axiosClient.post.name,
            operation: FootballMatchAttemptFor.Entity.toEntity.name,
            input: res.data,
          });

          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeUpdateAttemptFor =
  (axiosClient: AxiosInstance) => (footballMatchAttemptFor: FootballMatchAttemptFor.AttemptFor) =>
    axiosClient
      .patch(
        buildUrlWithParams(apiUrls.footballMatchAttemptFor, {
          [UrlParams.OrganizationId]: footballMatchAttemptFor.organizationId,
          [UrlParams.TeamId]: footballMatchAttemptFor.teamId,
          [UrlParams.FootballMatchAttemptForId]: footballMatchAttemptFor.id,
        }),
        footballMatchAttemptFor,
      )
      .then((res) => {
        const result = FootballMatchAttemptFor.Entity.toEntity(res.data);

        if (isError(result)) {
          result.addContext({
            service: makeUpdateAttemptFor.name,
            method: axiosClient.put.name,
            operation: FootballMatchAttemptFor.Entity.toEntity.name,
            input: res.data,
          });

          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeDeleteAttemptFor =
  (axiosClient: AxiosInstance) => (footballMatchAttemptFor: FootballMatchAttemptFor.AttemptFor) =>
    axiosClient
      .delete(
        buildUrlWithParams(apiUrls.footballMatchAttemptFor, {
          [UrlParams.OrganizationId]: footballMatchAttemptFor.organizationId,
          [UrlParams.TeamId]: footballMatchAttemptFor.teamId,
          [UrlParams.FootballMatchAttemptForId]: footballMatchAttemptFor.id,
        }),
      )
      .then((res) => {
        const result = FootballMatchAttemptFor.Entity.toEntity(res.data);

        if (isError(result)) {
          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);
