import { AxiosInstance } from "axios";

import {
  apiUrls,
  buildUrlWithParams,
  UrlParams,
  isError,
  OrganizationId,
  FootballMatchPlan,
  TeamId,
} from "@mio/helpers";

import { applyGenericErrorHandling } from "../genericErrorHandling";

export const makeGetPlans =
  (axiosClient: AxiosInstance) => (organizationId: OrganizationId, teamId: TeamId) =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.footballMatchPlans, {
          [UrlParams.OrganizationId]: organizationId,
          [UrlParams.TeamId]: teamId,
        }),
      )
      .then((res) => {
        const result = FootballMatchPlan.Entity.toEntities(res.data);

        if (isError(result)) {
          throw result;
        }
        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeGetPlan =
  (axiosClient: AxiosInstance) =>
  (organizationId: OrganizationId, teamId: TeamId, footballMatchPlanId: FootballMatchPlan.Id) =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.footballMatchPlan, {
          [UrlParams.OrganizationId]: organizationId,
          [UrlParams.TeamId]: teamId,
          [UrlParams.FootballMatchPlanId]: footballMatchPlanId,
        }),
      )
      .then((res) => {
        const result = FootballMatchPlan.Entity.toEntity(res.data);

        if (isError(result)) {
          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeCreatePlan =
  (axiosClient: AxiosInstance) => (footballMatchPlan: FootballMatchPlan.CreateDto) =>
    axiosClient
      .post(
        buildUrlWithParams(apiUrls.footballMatchPlans, {
          [UrlParams.OrganizationId]: footballMatchPlan.organizationId,
          [UrlParams.TeamId]: footballMatchPlan.teamId,
        }),
        footballMatchPlan,
      )
      .then((res) => {
        const result = FootballMatchPlan.Entity.toEntity(res.data);

        if (isError(result)) {
          result.addContext({
            service: makeCreatePlan.name,
            method: axiosClient.post.name,
            operation: FootballMatchPlan.Entity.toEntity.name,
            input: res.data,
          });

          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeUpdatePlan =
  (axiosClient: AxiosInstance) => (footballMatchPlan: FootballMatchPlan.CompletePlan) =>
    axiosClient
      .patch(
        buildUrlWithParams(apiUrls.footballMatchPlan, {
          [UrlParams.OrganizationId]: footballMatchPlan.organizationId,
          [UrlParams.TeamId]: footballMatchPlan.teamId,
          [UrlParams.FootballMatchPlanId]: footballMatchPlan.id,
        }),
        footballMatchPlan,
      )
      .then((res) => {
        const result = FootballMatchPlan.Entity.toEntity(res.data);

        if (isError(result)) {
          result.addContext({
            service: makeUpdatePlan.name,
            method: axiosClient.put.name,
            operation: FootballMatchPlan.Entity.toEntity.name,
            input: res.data,
          });

          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);
