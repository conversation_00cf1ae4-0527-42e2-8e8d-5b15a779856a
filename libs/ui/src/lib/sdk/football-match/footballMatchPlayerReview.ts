import { AxiosInstance } from "axios";

import {
  apiUrls,
  buildUrlWithParams,
  UrlParams,
  isError,
  OrganizationId,
  TeamId,
  FootballMatchPlan,
  FootballMatchPlayerReview,
} from "@mio/helpers";

import { applyGenericErrorHandling } from "../genericErrorHandling";

export const makeGetPlayerReviews =
  (axiosClient: AxiosInstance) =>
  (organizationId: OrganizationId, teamId: TeamId, footballMatchPlanId: FootballMatchPlan.Id) =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.footballMatchPlayersReviews, {
          [UrlParams.OrganizationId]: organizationId,
          [UrlParams.TeamId]: teamId,
          [UrlParams.FootballMatchPlanId]: footballMatchPlanId,
        }),
      )
      .then((res) => {
        const result = FootballMatchPlayerReview.Entity.toEntities(res.data);

        if (isError(result)) {
          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeGetAverageMatchPlayerReviews =
  (axiosClient: AxiosInstance) => (organizationId: OrganizationId, teamId: TeamId) =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.footballMatchPlayersAverageReviews, {
          [UrlParams.OrganizationId]: organizationId,
          [UrlParams.TeamId]: teamId,
        }),
      )
      .then((res) => {
        const result = FootballMatchPlayerReview.Entity.toAverageRatingEntities(res.data);

        if (isError(result)) {
          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeUpsertPlayerReviews =
  (axiosClient: AxiosInstance) =>
  (
    footballMatchPlayerReviews: FootballMatchPlayerReview.CreateDto[],
    organizationId: OrganizationId,
    teamId: TeamId,
    footballMatchPlanId: FootballMatchPlan.Id,
  ) =>
    axiosClient
      .post(
        buildUrlWithParams(apiUrls.footballMatchPlayersReviews, {
          [UrlParams.OrganizationId]: organizationId,
          [UrlParams.TeamId]: teamId,
          [UrlParams.FootballMatchPlanId]: footballMatchPlanId,
        }),
        footballMatchPlayerReviews,
      )
      .then((res) => {
        const result = FootballMatchPlayerReview.Entity.toEntities(res.data);

        if (isError(result)) {
          result.addContext({
            service: makeUpsertPlayerReviews.name,
            method: axiosClient.post.name,
            operation: FootballMatchPlayerReview.Entity.toEntity.name,
            input: res.data,
          });

          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeUpdatePlayerReviews =
  (axiosClient: AxiosInstance) =>
  (
    footballMatchReviews: FootballMatchPlayerReview.CompletePlayerReview[],
    organizationId: OrganizationId,
    teamId: TeamId,
    footballMatchPlanId: FootballMatchPlan.Id,
  ) =>
    axiosClient
      .patch(
        buildUrlWithParams(apiUrls.footballMatchReview, {
          [UrlParams.OrganizationId]: organizationId,
          [UrlParams.TeamId]: teamId,
          [UrlParams.FootballMatchPlanId]: footballMatchPlanId,
        }),
        footballMatchReviews,
      )
      .then((res) => {
        const result = FootballMatchPlayerReview.Entity.toEntities(res.data);

        if (isError(result)) {
          result.addContext({
            service: makeUpdatePlayerReviews.name,
            method: axiosClient.put.name,
            operation: FootballMatchPlayerReview.Entity.toEntity.name,
            input: res.data,
          });

          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);
