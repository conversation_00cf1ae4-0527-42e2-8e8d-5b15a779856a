import { AxiosInstance } from "axios";
import { isNil } from "lodash/fp";

import {
  apiUrls,
  buildUrlWithParams,
  UrlParams,
  isError,
  OrganizationId,
  FootballMatchReview,
  TeamId,
  FootballMatchPlan,
} from "@mio/helpers";

import { applyGenericErrorHandling } from "../genericErrorHandling";

export const makeGetReviews =
  (axiosClient: AxiosInstance) => (organizationId: OrganizationId, teamId: TeamId) =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.footballMatchReviews, {
          [UrlParams.OrganizationId]: organizationId,
          [UrlParams.TeamId]: teamId,
        }),
      )
      .then((res) => {
        const result = FootballMatchReview.Entity.toEntities(res.data);

        if (isError(result)) {
          throw result;
        }
        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeGetReview =
  (axiosClient: AxiosInstance) =>
  (organizationId: OrganizationId, teamId: TeamId, footballMatchPlanId: FootballMatchPlan.Id) =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.footballMatchReview, {
          [UrlParams.OrganizationId]: organizationId,
          [UrlParams.TeamId]: teamId,
          [UrlParams.FootballMatchPlanId]: footballMatchPlanId,
        }),
      )
      .then((res) => {
        if (isNil(res.data) || res.data === "") {
          return null;
        }

        const result = FootballMatchReview.Entity.toEntity(res.data);

        if (isError(result)) {
          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeCreateReview =
  (axiosClient: AxiosInstance) => (footballMatchReview: FootballMatchReview.CreateDto) =>
    axiosClient
      .post(
        buildUrlWithParams(apiUrls.footballMatchReviews, {
          [UrlParams.OrganizationId]: footballMatchReview.organizationId,
          [UrlParams.TeamId]: footballMatchReview.teamId,
        }),
        footballMatchReview,
      )
      .then((res) => {
        const result = FootballMatchReview.Entity.toEntity(res.data);

        if (isError(result)) {
          result.addContext({
            service: makeCreateReview.name,
            method: axiosClient.post.name,
            operation: FootballMatchReview.Entity.toEntity.name,
            input: res.data,
          });

          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeUpdateReview =
  (axiosClient: AxiosInstance) => (footballMatchReview: FootballMatchReview.CompleteReview) =>
    axiosClient
      .patch(
        buildUrlWithParams(apiUrls.footballMatchReview, {
          [UrlParams.OrganizationId]: footballMatchReview.organizationId,
          [UrlParams.TeamId]: footballMatchReview.teamId,
          [UrlParams.FootballMatchPlanId]: footballMatchReview.footballMatchPlanId,
        }),
        footballMatchReview,
      )
      .then((res) => {
        const result = FootballMatchReview.Entity.toEntity(res.data);

        if (isError(result)) {
          result.addContext({
            service: makeUpdateReview.name,
            method: axiosClient.put.name,
            operation: FootballMatchReview.Entity.toEntity.name,
            input: res.data,
          });

          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);
