import { AxiosInstance } from "axios";

import {
  apiUrls,
  buildUrlWithParams,
  UrlParams,
  isError,
  OrganizationId,
  FootballMatchAttemptAgainst,
  TeamId,
  FootballMatchPlan,
} from "@mio/helpers";

import { applyGenericErrorHandling } from "../genericErrorHandling";

export const makeGetAttemptsAgainst =
  (axiosClient: AxiosInstance) =>
  (organizationId: OrganizationId, teamId: TeamId, footballMatchPlanId: FootballMatchPlan.Id) =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.footballMatchAttemptsAgainst, {
          [UrlParams.OrganizationId]: organizationId,
          [UrlParams.TeamId]: teamId,
          [UrlParams.FootballMatchPlanId]: footballMatchPlanId,
        }),
      )
      .then((res) => {
        const result = FootballMatchAttemptAgainst.Entity.toEntities(res.data);

        if (isError(result)) {
          throw result;
        }
        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeGetExtraAttemptsAgainst =
  (axiosClient: AxiosInstance) =>
  (organizationId: OrganizationId, teamId: TeamId, footballMatchPlanId: FootballMatchPlan.Id) =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.footballMatchAttemptsAgainstExtra, {
          [UrlParams.OrganizationId]: organizationId,
          [UrlParams.TeamId]: teamId,
          [UrlParams.FootballMatchPlanId]: footballMatchPlanId,
        }),
      )
      .then((res) => {
        const result = FootballMatchAttemptAgainst.Entity.toEntities(res.data);

        if (isError(result)) {
          throw result;
        }
        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeGetAttemptAgainst =
  (axiosClient: AxiosInstance) =>
  (
    organizationId: OrganizationId,
    teamId: TeamId,
    footballMatchAttemptAgainstId: FootballMatchAttemptAgainst.Id,
  ) =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.footballMatchAttemptAgainst, {
          [UrlParams.OrganizationId]: organizationId,
          [UrlParams.TeamId]: teamId,
          [UrlParams.FootballMatchAttemptAgainstId]: footballMatchAttemptAgainstId,
        }),
      )
      .then((res) => {
        const result = FootballMatchAttemptAgainst.Entity.toEntity(res.data);

        if (isError(result)) {
          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeCreateAttemptAgainst =
  (axiosClient: AxiosInstance) =>
  (footballMatchAttemptsAgainst: FootballMatchAttemptAgainst.CreateDto) =>
    axiosClient
      .post(
        buildUrlWithParams(apiUrls.footballMatchAttemptsAgainst, {
          [UrlParams.OrganizationId]: footballMatchAttemptsAgainst.organizationId,
          [UrlParams.TeamId]: footballMatchAttemptsAgainst.teamId,
          [UrlParams.FootballMatchPlanId]: footballMatchAttemptsAgainst.footballMatchPlanId,
        }),
        footballMatchAttemptsAgainst,
      )
      .then((res) => {
        const result = FootballMatchAttemptAgainst.Entity.toEntity(res.data);

        if (isError(result)) {
          result.addContext({
            service: makeCreateAttemptAgainst.name,
            method: axiosClient.post.name,
            operation: FootballMatchAttemptAgainst.Entity.toEntity.name,
            input: res.data,
          });

          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeUpdateAttemptAgainst =
  (axiosClient: AxiosInstance) =>
  (footballMatchAttemptAgainst: FootballMatchAttemptAgainst.AttemptAgainst) =>
    axiosClient
      .patch(
        buildUrlWithParams(apiUrls.footballMatchAttemptAgainst, {
          [UrlParams.OrganizationId]: footballMatchAttemptAgainst.organizationId,
          [UrlParams.TeamId]: footballMatchAttemptAgainst.teamId,
          [UrlParams.FootballMatchAttemptAgainstId]: footballMatchAttemptAgainst.id,
        }),
        footballMatchAttemptAgainst,
      )
      .then((res) => {
        const result = FootballMatchAttemptAgainst.Entity.toEntity(res.data);

        if (isError(result)) {
          result.addContext({
            service: makeUpdateAttemptAgainst.name,
            method: axiosClient.put.name,
            operation: FootballMatchAttemptAgainst.Entity.toEntity.name,
            input: res.data,
          });

          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeDeleteAttemptAgainst =
  (axiosClient: AxiosInstance) =>
  (footballMatchAttemptAgainst: FootballMatchAttemptAgainst.AttemptAgainst) =>
    axiosClient
      .delete(
        buildUrlWithParams(apiUrls.footballMatchAttemptAgainst, {
          [UrlParams.OrganizationId]: footballMatchAttemptAgainst.organizationId,
          [UrlParams.TeamId]: footballMatchAttemptAgainst.teamId,
          [UrlParams.FootballMatchAttemptAgainstId]: footballMatchAttemptAgainst.id,
        }),
      )
      .then((res) => {
        console.log("res", res);
        const result = FootballMatchAttemptAgainst.Entity.toEntity(res.data);

        if (isError(result)) {
          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);
