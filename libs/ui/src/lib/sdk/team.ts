import { AxiosInstance } from "axios";

import {
  apiUrls,
  buildUrlWithParams,
  UrlParams,
  CreateTeamDto,
  isError,
  OrganizationId,
  Team,
  TeamId,
  UpdateTeamDto,
} from "@mio/helpers";
import { applyGenericErrorHandling } from "./genericErrorHandling";

export const makeCreateTeam = (axiosClient: AxiosInstance) => (payload: CreateTeamDto) =>
  axiosClient
    .post(
      buildUrlWithParams(apiUrls.createTeam, {
        [UrlParams.OrganizationId]: payload.organizationId,
      }),
      payload,
    )
    .then(() => undefined)
    .catch(applyGenericErrorHandling);

export const makeUpdateTeam = (axiosClient: AxiosInstance) => (payload: UpdateTeamDto) =>
  axiosClient
    .patch(
      buildUrlWithParams(apiUrls.updateTeam, {
        [UrlParams.TeamId]: payload.id,
        [UrlParams.OrganizationId]: payload.organizationId,
      }),
      payload,
    )
    .then((response) => {
      const parsed = Team.toEntity(response.data);

      if (isError(parsed)) {
        throw parsed;
      }
      return parsed;
    })
    .catch(applyGenericErrorHandling);

export const makeDeleteTeam = (axiosClient: AxiosInstance) => (payload: TeamId) =>
  axiosClient
    .delete(buildUrlWithParams(apiUrls.deleteTeam, { [UrlParams.TeamId]: payload }))
    .then(() => undefined)
    .catch(applyGenericErrorHandling);

export const makeGetTeams = (axiosClient: AxiosInstance) => (payload: { id: OrganizationId }) =>
  axiosClient
    .get(buildUrlWithParams(apiUrls.getTeams, { organizationId: payload.id }))
    .then((res) => {
      const result = Team.toEntities(res.data);

      if (isError(result)) {
        throw result;
      }
      return result;
    })
    .catch(applyGenericErrorHandling);

export const makeGetAdminTeams =
  (axiosClient: AxiosInstance) => (payload: { id: OrganizationId }) =>
    axiosClient
      .get(buildUrlWithParams(apiUrls.getAdminTeams, { organizationId: payload.id }))
      .then((res) => {
        const result = Team.toEntities(res.data);

        if (isError(result)) {
          throw result;
        }
        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeGetTeam =
  (axiosClient: AxiosInstance) => (orgId: OrganizationId, teamId: TeamId) =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.getTeam, {
          [UrlParams.TeamId]: teamId,
          [UrlParams.OrganizationId]: orgId,
        }),
      )
      .then((res) => {
        const result = Team.toEntity(res.data);

        if (isError(result)) {
          throw result;
        }
        return result;
      })
      .catch(applyGenericErrorHandling);
