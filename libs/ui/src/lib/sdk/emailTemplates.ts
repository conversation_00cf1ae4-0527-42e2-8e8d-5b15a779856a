import { AxiosInstance } from "axios";

import {
  apiUrls,
  buildUrlWithParams,
  UrlParams,
  isError,
  OrganizationId,
  EmailTemplate,
  CreateEmailTemplateDto,
  EmailTemplateId,
} from "@mio/helpers";

import { applyGenericErrorHandling } from "./genericErrorHandling";

export const makeGetEmailTemplates =
  (axiosClient: AxiosInstance) => (organizationId: OrganizationId) =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.emailTemplates, {
          [UrlParams.OrganizationId]: organizationId,
        }),
      )
      .then((res) => {
        const result = EmailTemplate.toEntities(res.data);

        if (isError(result)) {
          throw result;
        }
        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeCreateEmailTemplate =
  (axiosClient: AxiosInstance) => (template: CreateEmailTemplateDto) =>
    axiosClient
      .post(
        buildUrlWithParams(apiUrls.emailTemplates, {
          [UrlParams.OrganizationId]: template.organizationId,
        }),
        template,
      )
      .then(() => {
        return undefined;
      })
      .catch(applyGenericErrorHandling);

export const makeUpdateEmailTemplate = (axiosClient: AxiosInstance) => (template: EmailTemplate) =>
  axiosClient
    .put(
      buildUrlWithParams(apiUrls.emailTemplate, {
        [UrlParams.OrganizationId]: template.organizationId,
        [UrlParams.EmailTemplateId]: template.id,
      }),
      template,
    )
    .then(() => {
      return undefined;
    })
    .catch(applyGenericErrorHandling);

type DeleteEmailTemplate = {
  templateId: EmailTemplateId;
  organizationId: OrganizationId;
};
export const makeDeleteEmailTemplate =
  (axiosClient: AxiosInstance) =>
  ({ organizationId, templateId }: DeleteEmailTemplate) =>
    axiosClient
      .delete(
        buildUrlWithParams(apiUrls.emailTemplate, {
          [UrlParams.OrganizationId]: organizationId,
          [UrlParams.EmailTemplateId]: templateId,
        }),
      )
      .then(() => {
        return undefined;
      })
      .catch(applyGenericErrorHandling);
