import { AxiosInstance } from "axios";

import { apiUrls, isError, <PERSON><PERSON><PERSON>ode<PERSON>to, PlayerUser } from "@mio/helpers";
import { applyGenericErrorHandling } from "./genericErrorHandling";

export const makeLoginEndpoint = (axiosClient: AxiosInstance) => (payload: LoginCodeDto) =>
  axiosClient
    .post(apiUrls.playerLogin, payload)
    .then((res) => {
      const result = PlayerUser.Entity.toAccessTokenDto(res.data);

      if (isError(result)) {
        throw result;
      }
      return result;
    })
    .catch(applyGenericErrorHandling);

export const makeRegisterEndpoint =
  (axiosClient: AxiosInstance) => (payload: PlayerUser.SignInDto) =>
    axiosClient
      .post(apiUrls.playerCode, payload)
      .then(() => undefined)
      .catch(applyGenericErrorHandling);
