import { AxiosInstance } from "axios";

import {
  apiUrls,
  buildUrlWithParams,
  UrlParams,
  isError,
  OrganizationId,
  PermissionsModule,
} from "@mio/helpers";
import { applyGenericErrorHandling } from "./genericErrorHandling";

export const makeCreateRole =
  (axiosClient: AxiosInstance) => (payload: PermissionsModule.Role.SaveDto) =>
    axiosClient
      .post(
        buildUrlWithParams(apiUrls.roles, {
          [UrlParams.OrganizationId]: payload.organizationId,
        }),
        payload,
      )
      .then((response) => {
        const parsed = PermissionsModule.Role.Role.toEntity(response.data);

        if (isError(parsed)) {
          throw parsed;
        }

        return parsed;
      })
      .catch(applyGenericErrorHandling);

export type UpdateRoleParams = {
  roleId: PermissionsModule.Role.RoleId;
  organizationId: OrganizationId;
  dto: PermissionsModule.Role.SaveDto;
};

export const makeUpdateRole = (axiosClient: AxiosInstance) => (payload: UpdateRoleParams) =>
  axiosClient
    .patch(
      buildUrlWithParams(apiUrls.role, {
        [UrlParams.RoleId]: payload.roleId,
        [UrlParams.OrganizationId]: payload.organizationId,
      }),
      payload.dto,
    )
    .then((response) => {
      const parsed = PermissionsModule.Role.Role.toEntity(response.data);

      if (isError(parsed)) {
        throw parsed;
      }
      return parsed;
    })
    .catch(applyGenericErrorHandling);

export const makeGetRoles = (axiosClient: AxiosInstance) => (orgId: OrganizationId) =>
  axiosClient
    .get(buildUrlWithParams(apiUrls.roles, { [UrlParams.OrganizationId]: orgId }))
    .then((res) => {
      const result = PermissionsModule.Role.Role.toEntities(res.data);

      if (isError(result)) {
        throw result;
      }
      return result;
    })
    .catch(applyGenericErrorHandling);

export type DeleteRoleParams = {
  organizationId: OrganizationId;
  roleId: PermissionsModule.Role.RoleId;
};

export const makeDeleteRole = (axiosClient: AxiosInstance) => (payload: DeleteRoleParams) =>
  axiosClient
    .delete(
      buildUrlWithParams(apiUrls.role, {
        [UrlParams.RoleId]: payload.roleId,
        [UrlParams.OrganizationId]: payload.organizationId,
      }),
    )
    .then(() => {
      return undefined;
    })
    .catch(applyGenericErrorHandling);
