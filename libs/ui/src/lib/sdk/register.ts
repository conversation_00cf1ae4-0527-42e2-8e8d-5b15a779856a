import { AxiosInstance } from "axios";

import { apiUrls, InviteRegistrationDto } from "@mio/helpers";
import { applyGenericErrorHandling } from "./genericErrorHandling";

export const makeRegisterEndpoint =
  (axiosClient: AxiosInstance) => (payload: InviteRegistrationDto) =>
    axiosClient
      .post(apiUrls.registerWithInvite, payload)
      .then(() => undefined)
      .catch(applyGenericErrorHandling);
