import { AxiosInstance } from "axios";

import {
  apiUrls,
  buildUrlWithParams,
  GCSubscription,
  isError,
  OrganizationId,
  UrlParams,
} from "@mio/helpers";

import { applyGenericErrorHandling } from "./genericErrorHandling";

export const makeGetGCSubscriptions =
  (axiosClient: AxiosInstance) => async (id: OrganizationId) => {
    return axiosClient
      .get(
        buildUrlWithParams(apiUrls.generalGoCardlessSubscriptions, {
          [UrlParams.OrganizationId]: id,
        }),
      )
      .then((res) => {
        const result = GCSubscription.toEntities(res.data);

        if (isError(result)) {
          throw result;
        }
        return result;
      })
      .catch(applyGenericErrorHandling);
  };
