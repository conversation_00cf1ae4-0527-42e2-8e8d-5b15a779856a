import { AxiosInstance } from "axios";
import { omit } from "lodash/fp";

import {
  UrlParams,
  apiUrls,
  buildUrlWithParams,
  isError,
  Organization,
  OrganizationId,
  OrganizationSlug,
  PublicOrganization,
  UpdateOrganizationDto,
  InviteId,
} from "@mio/helpers";
import { applyGenericErrorHandling } from "./genericErrorHandling";

/* Get extended Organization data */
export const makeGetOrganization = (axiosClient: AxiosInstance) => (organization: OrganizationId) =>
  axiosClient
    .get(buildUrlWithParams(apiUrls.getOrganization, { [UrlParams.OrganizationId]: organization }))
    .then((res) => {
      const result = Organization.toEntity(res.data);

      if (isError(result)) {
        throw result;
      }
      return result;
    })
    .catch(applyGenericErrorHandling);

export const makeGetOrganizationBySlug = (axiosClient: AxiosInstance) => (slug: OrganizationSlug) =>
  axiosClient
    .get(buildUrlWithParams(apiUrls.getOrganizationBySlug, { [UrlParams.OrganizationSlug]: slug }))
    .then((res) => {
      const result = Organization.toPublicFromUnknown(res.data);

      if (isError(result)) {
        throw result;
      }
      return result as PublicOrganization;
    })
    .catch(applyGenericErrorHandling);

export const makeUpdateOrganization =
  (axiosClient: AxiosInstance) =>
  (data: UpdateOrganizationDto & { organizationId: OrganizationId }) =>
    axiosClient
      .patch(
        buildUrlWithParams(apiUrls.updateOrganization, {
          [UrlParams.OrganizationId]: data.organizationId,
        }),
        omit("organizationId", data),
      )
      .then(() => {
        return undefined;
      })
      .catch(applyGenericErrorHandling);

export const makeJoinOrganization = (axiosClient: AxiosInstance) => (inviteId: InviteId) =>
  axiosClient
    .patch(
      buildUrlWithParams(apiUrls.joinOrganization, {
        [UrlParams.InviteId]: inviteId,
      }),
    )
    .then(() => {
      return undefined;
    })
    .catch(applyGenericErrorHandling);
