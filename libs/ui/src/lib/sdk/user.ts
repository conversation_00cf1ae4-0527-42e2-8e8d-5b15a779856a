import { AxiosInstance } from "axios";

import {
  apiUrls,
  buildUrlWithParams,
  UrlParams,
  OrganizationId,
  isError,
  PasswordResetActionDto,
  PasswordResetInitiationDto,
  Coach,
  CoachUser,
} from "@mio/helpers";
import { applyGenericErrorHandling } from "./genericErrorHandling";

export const makeCurrentUserEndpoint = (axiosClient: AxiosInstance) => () =>
  axiosClient
    .get(apiUrls.currentUser)
    .then((res) => {
      const result = CoachUser.publicFromUnknown(res.data);

      if (isError(result)) {
        throw result;
      }
      return result;
    })
    .catch(applyGenericErrorHandling);

export const makeGetOrganizationUsersEndpoint =
  (axiosClient: AxiosInstance) =>
  (orgId: OrganizationId): Promise<Coach<"populated">[]> =>
    axiosClient
      .get(buildUrlWithParams(apiUrls.organizationUsers, { [UrlParams.OrganizationId]: orgId }))
      .then((res) => {
        const result = CoachUser.toPopulatedEntitiesFromUnknown(res.data);

        if (isError(result)) {
          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeInitiatePasswordReset =
  (axiosClient: AxiosInstance) => (data: PasswordResetInitiationDto) =>
    axiosClient
      .post(apiUrls.passwordResetInitiation, data)
      .then(() => {
        return undefined;
      })
      .catch(applyGenericErrorHandling);

export const makeResetPassword = (axiosClient: AxiosInstance) => (data: PasswordResetActionDto) =>
  axiosClient
    .post(apiUrls.passwordResetAction, data)
    .then(() => {
      return undefined;
    })
    .catch(applyGenericErrorHandling);
