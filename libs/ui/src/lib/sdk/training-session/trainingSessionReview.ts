import { AxiosInstance } from "axios";

import {
  apiUrls,
  buildUrlWithParams,
  UrlParams,
  isError,
  OrganizationId,
  TrainingSessionReview,
  TeamId,
} from "@mio/helpers";

import { applyGenericErrorHandling } from "../genericErrorHandling";

export const makeGetReview =
  (axiosClient: AxiosInstance) =>
  (organizationId: OrganizationId, teamId: TeamId, reviewId: TrainingSessionReview.ReviewId) =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.trainingSessionReview, {
          [UrlParams.OrganizationId]: organizationId,
          [UrlParams.TeamId]: teamId,
          [UrlParams.TrainingSessionReviewId]: reviewId,
        }),
      )
      .then((res) => {
        const result = TrainingSessionReview.Entity.toExtendedEntity(res.data);

        if (isError(result)) {
          throw result;
        }
        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeGetReviews =
  (axiosClient: AxiosInstance) => (organizationId: OrganizationId, teamId: TeamId) =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.trainingSessionReviews, {
          [UrlParams.OrganizationId]: organizationId,
          [UrlParams.TeamId]: teamId,
        }),
      )
      .then((res) => {
        const result = TrainingSessionReview.Entity.toEntities(res.data);

        if (isError(result)) {
          throw result;
        }
        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeCreateReview =
  (axiosClient: AxiosInstance) => (trainingSessionReview: TrainingSessionReview.CreateDto) =>
    axiosClient
      .post(
        buildUrlWithParams(apiUrls.trainingSessionReviews, {
          [UrlParams.OrganizationId]: trainingSessionReview.organizationId,
          [UrlParams.TeamId]: trainingSessionReview.teamId,
        }),
        trainingSessionReview,
      )
      .then((res) => {
        const result = TrainingSessionReview.Entity.toEntity(res.data);

        if (isError(result)) {
          result.addContext({
            service: makeCreateReview.name,
            method: axiosClient.post.name,
            operation: TrainingSessionReview.Entity.toEntity.name,
            input: res.data,
          });

          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeUpdateReview =
  (axiosClient: AxiosInstance) => (trainingSessionReview: TrainingSessionReview.Review) =>
    axiosClient
      .patch(
        buildUrlWithParams(apiUrls.trainingSessionReview, {
          [UrlParams.OrganizationId]: trainingSessionReview.organizationId,
          [UrlParams.TeamId]: trainingSessionReview.teamId,
          [UrlParams.TrainingSessionReviewId]: trainingSessionReview.id,
        }),
        trainingSessionReview,
      )
      .then((res) => {
        const result = TrainingSessionReview.Entity.toEntity(res.data);

        if (isError(result)) {
          result.addContext({
            service: makeUpdateReview.name,
            method: axiosClient.put.name,
            operation: TrainingSessionReview.Entity.toEntity.name,
            input: res.data,
          });

          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);
