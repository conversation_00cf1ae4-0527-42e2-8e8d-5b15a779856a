import { AxiosInstance } from "axios";

import {
  apiUrls,
  buildUrlWithParams,
  UrlParams,
  isError,
  OrganizationId,
  TrainingSessionPlayerReview,
  TeamId,
} from "@mio/helpers";

import { applyGenericErrorHandling } from "../genericErrorHandling";

export const makeGetPlayerReviews =
  (axiosClient: AxiosInstance) =>
  (organizationId: OrganizationId, teamId: TeamId, query: TrainingSessionPlayerReview.QueryDto) =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.trainingSessionPlayerReviews, {
          [UrlParams.OrganizationId]: organizationId,
          [UrlParams.TeamId]: teamId,
        }),
        { params: query },
      )
      .then((res) => {
        const result = TrainingSessionPlayerReview.Entity.toExtendedEntities(res.data);

        if (isError(result)) {
          throw result;
        }
        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeCreatePlayerReview =
  (axiosClient: AxiosInstance) =>
  (trainingSessionPlayerReview: TrainingSessionPlayerReview.CreateDto) =>
    axiosClient
      .post(
        buildUrlWithParams(apiUrls.trainingSessionPlayerReviews, {
          [UrlParams.OrganizationId]: trainingSessionPlayerReview.organizationId,
          [UrlParams.TeamId]: trainingSessionPlayerReview.teamId,
        }),
        trainingSessionPlayerReview,
      )
      .then((res) => {
        const result = TrainingSessionPlayerReview.Entity.toEntity(res.data);

        if (isError(result)) {
          result.addContext({
            service: makeCreatePlayerReview.name,
            method: axiosClient.post.name,
            operation: TrainingSessionPlayerReview.Entity.toEntity.name,
            input: res.data,
          });

          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeUpdatePlayerReview =
  (axiosClient: AxiosInstance) =>
  (trainingSessionPlayerReview: TrainingSessionPlayerReview.PlayerReview) =>
    axiosClient
      .patch(
        buildUrlWithParams(apiUrls.trainingSessionPlayerReview, {
          [UrlParams.OrganizationId]: trainingSessionPlayerReview.organizationId,
          [UrlParams.TeamId]: trainingSessionPlayerReview.teamId,
          [UrlParams.TrainingSessionPlayerReviewId]: trainingSessionPlayerReview.id,
        }),
        trainingSessionPlayerReview,
      )
      .then((res) => {
        const result = TrainingSessionPlayerReview.Entity.toEntity(res.data);

        if (isError(result)) {
          result.addContext({
            service: makeUpdatePlayerReview.name,
            method: axiosClient.put.name,
            operation: TrainingSessionPlayerReview.Entity.toEntity.name,
            input: res.data,
          });

          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);
