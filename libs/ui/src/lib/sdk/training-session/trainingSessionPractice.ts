import { AxiosInstance } from "axios";

import {
  apiUrls,
  buildUrlWithParams,
  UrlParams,
  isError,
  OrganizationId,
  TrainingSessionPractice,
  TeamId,
} from "@mio/helpers";

import { applyGenericErrorHandling } from "../genericErrorHandling";

export const makeGetPractices =
  (axiosClient: AxiosInstance) => (organizationId: OrganizationId, teamId: TeamId) =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.trainingSessionPractices, {
          [UrlParams.OrganizationId]: organizationId,
          [UrlParams.TeamId]: teamId,
        }),
      )
      .then((res) => {
        const result = TrainingSessionPractice.Entity.toEntities(res.data);

        if (isError(result)) {
          throw result;
        }
        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeCreatePractice =
  (axiosClient: AxiosInstance) => (trainingSessionPractice: TrainingSessionPractice.CreateDto) =>
    axiosClient
      .post(
        buildUrlWithParams(apiUrls.trainingSessionPractices, {
          [UrlParams.OrganizationId]: trainingSessionPractice.organizationId,
          [UrlParams.TeamId]: trainingSessionPractice.teamId,
        }),
        trainingSessionPractice,
      )
      .then((res) => {
        const result = TrainingSessionPractice.Entity.toEntity(res.data);

        if (isError(result)) {
          result.addContext({
            service: makeCreatePractice.name,
            method: axiosClient.post.name,
            operation: TrainingSessionPractice.Entity.toEntity.name,
            input: res.data,
          });

          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeUpdatePractice =
  (axiosClient: AxiosInstance) => (trainingSessionPractice: TrainingSessionPractice.Practice) =>
    axiosClient
      .put(
        buildUrlWithParams(apiUrls.trainingSessionPractice, {
          [UrlParams.OrganizationId]: trainingSessionPractice.organizationId,
          [UrlParams.TeamId]: trainingSessionPractice.teamId,
        }),
        trainingSessionPractice,
      )
      .then((res) => {
        const result = TrainingSessionPractice.Entity.toEntity(res.data);

        if (isError(result)) {
          result.addContext({
            service: makeUpdatePractice.name,
            method: axiosClient.put.name,
            operation: TrainingSessionPractice.Entity.toEntity.name,
            input: res.data,
          });

          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);
