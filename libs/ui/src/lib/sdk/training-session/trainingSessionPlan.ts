import { AxiosInstance } from "axios";

import {
  apiUrls,
  buildUrlWithParams,
  UrlParams,
  isError,
  OrganizationId,
  TrainingSessionPlan,
  TeamId,
} from "@mio/helpers";

import { applyGenericErrorHandling } from "../genericErrorHandling";

export const makeGetPlans =
  (axiosClient: AxiosInstance) => (organizationId: OrganizationId, teamId: TeamId) =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.trainingSessionPlans, {
          [UrlParams.OrganizationId]: organizationId,
          [UrlParams.TeamId]: teamId,
        }),
      )
      .then((res) => {
        const result = TrainingSessionPlan.Entity.toEntities(res.data);

        if (isError(result)) {
          throw result;
        }
        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeCreatePlan =
  (axiosClient: AxiosInstance) => (trainingSessionPlan: TrainingSessionPlan.CreateDto) =>
    axiosClient
      .post(
        buildUrlWithParams(apiUrls.trainingSessionPlans, {
          [UrlParams.OrganizationId]: trainingSessionPlan.organizationId,
          [UrlParams.TeamId]: trainingSessionPlan.teamId,
        }),
        trainingSessionPlan,
      )
      .then((res) => {
        const result = TrainingSessionPlan.Entity.toEntity(res.data);

        if (isError(result)) {
          result.addContext({
            service: makeCreatePlan.name,
            method: axiosClient.post.name,
            operation: TrainingSessionPlan.Entity.toEntity.name,
            input: res.data,
          });

          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeUpdatePlan =
  (axiosClient: AxiosInstance) => (trainingSessionPlan: TrainingSessionPlan.Plan) =>
    axiosClient
      .put(
        buildUrlWithParams(apiUrls.trainingSessionPlan, {
          [UrlParams.OrganizationId]: trainingSessionPlan.organizationId,
          [UrlParams.TeamId]: trainingSessionPlan.teamId,
        }),
        trainingSessionPlan,
      )
      .then((res) => {
        const result = TrainingSessionPlan.Entity.toEntity(res.data);

        if (isError(result)) {
          result.addContext({
            service: makeUpdatePlan.name,
            method: axiosClient.put.name,
            operation: TrainingSessionPlan.Entity.toEntity.name,
            input: res.data,
          });

          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);
