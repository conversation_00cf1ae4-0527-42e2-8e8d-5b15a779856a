import { AxiosInstance } from "axios";

import {
  apiUrls,
  buildUrlWithParams,
  UrlParams,
  isError,
  OrganizationId,
  SeasonPlan,
  TeamId,
} from "@mio/helpers";

import { applyGenericErrorHandling } from "../genericErrorHandling";

export const makeGetSeasonPlans =
  (axiosClient: AxiosInstance) => (organizationId: OrganizationId, teamId: TeamId) =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.seasonPlans, {
          [UrlParams.OrganizationId]: organizationId,
          [UrlParams.TeamId]: teamId,
        }),
      )
      .then((res) => {
        const result = SeasonPlan.Entity.toEntities(res.data);

        if (isError(result)) {
          throw result;
        }
        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeCreateSeasonPlan =
  (axiosClient: AxiosInstance) => (seasonPlan: SeasonPlan.CreateDto) =>
    axiosClient
      .post(
        buildUrlWithParams(apiUrls.seasonPlans, {
          [UrlParams.OrganizationId]: seasonPlan.organizationId,
          [UrlParams.TeamId]: seasonPlan.teamId,
        }),
        seasonPlan,
      )
      .then((res) => {
        const result = SeasonPlan.Entity.toEntity(res.data);

        if (isError(result)) {
          result.addContext({
            service: makeCreateSeasonPlan.name,
            method: axiosClient.post.name,
            operation: SeasonPlan.Entity.toEntity.name,
            input: res.data,
          });

          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeUpdateSeasonPlan =
  (axiosClient: AxiosInstance) => (seasonPlan: SeasonPlan.SeasonPlan) =>
    axiosClient
      .put(
        buildUrlWithParams(apiUrls.seasonPlan, {
          [UrlParams.OrganizationId]: seasonPlan.organizationId,
          [UrlParams.TeamId]: seasonPlan.teamId,
        }),
        seasonPlan,
      )
      .then((res) => {
        const result = SeasonPlan.Entity.toEntity(res.data);

        if (isError(result)) {
          result.addContext({
            service: makeUpdateSeasonPlan.name,
            method: axiosClient.put.name,
            operation: SeasonPlan.Entity.toEntity.name,
            input: res.data,
          });

          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);
