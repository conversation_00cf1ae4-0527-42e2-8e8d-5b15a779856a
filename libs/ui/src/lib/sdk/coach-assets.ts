import { AxiosInstance } from "axios";

import {
  Assets,
  CoachUserId,
  CustomUrl,
  OrganizationId,
  UrlParams,
  apiUrls,
  buildUrlWithParams,
  isError,
} from "@mio/helpers";

import { applyGenericErrorHandling } from "./genericErrorHandling";

export type UploadImageParams = {
  coachId: CoachUserId;
  organizationId: OrganizationId;
  dto: Assets.Image.ClientCreateDto;
};

export const makeUploadImageCloudOnly =
  (axiosClient: AxiosInstance) => (payload: UploadImageParams) => {
    const formData = new FormData();
    formData.append("file", payload.dto.file);
    formData.append("description", payload.dto.description ?? "");

    return axiosClient
      .post(
        buildUrlWithParams(apiUrls.assets.coach_images, {
          [UrlParams.CoachId]: payload.coachId,
          [UrlParams.OrganizationId]: payload.organizationId,
        }),
        formData,
      )
      .then((res) => {
        const parsed = CustomUrl.parser.parse(res.data);

        if (isError(parsed)) {
          parsed.addContext({
            service: makeUploadImageCloudOnly.name,
            method: axiosClient.post.name,
            operation: Assets.Image.Entity.toEntity.name,
            params: { coachId: payload.coachId, dto: payload.dto },
          });

          throw parsed;
        }

        return parsed;
      })
      .catch((err) => {
        return applyGenericErrorHandling(err);
      });
  };
