import { AxiosInstance } from "axios";

import {
  apiUrls,
  buildUrlWithParams,
  isError,
  OrganizationId,
  Profile,
  TeamId,
  UrlParams,
} from "@mio/helpers";
import { applyGenericErrorHandling } from "./genericErrorHandling";

export const makeGetProfileEndpoint = (axiosClient: AxiosInstance) => () =>
  axiosClient
    .get(apiUrls.currentProfile)
    .then((res) => {
      const result = Profile.toPublicEntity(res.data);

      if (isError(result)) {
        throw result;
      }
      return result;
    })
    .catch(applyGenericErrorHandling);

export const makeGetTeamCoachesEndpoint =
  (axiosClient: AxiosInstance) => (teamId: TeamId, orgId: OrganizationId) =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.teamCoaches, {
          [UrlParams.OrganizationId]: orgId,
          [UrlParams.TeamId]: teamId,
        }),
      )
      .then((res) => {
        const result = Profile.toEntities(res.data);

        if (isError(result)) {
          throw result;
        }
        return result;
      })
      .catch(applyGenericErrorHandling);
