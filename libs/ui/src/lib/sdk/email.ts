import { AxiosInstance } from "axios";

import {
  apiUrls,
  buildUrlWithParams,
  EmailDto,
  OrganizationId,
  MarketingEmailDto,
  UrlParams,
} from "@mio/helpers";

import { applyGenericErrorHandling } from "./genericErrorHandling";

export const makeSendEmail =
  (axiosClient: AxiosInstance) => (payload: EmailDto, orgId: OrganizationId) =>
    axiosClient
      .post(
        buildUrlWithParams(apiUrls.sendEmail, {
          [UrlParams.OrganizationId]: orgId,
        }),
        payload,
      )
      .then(() => undefined)
      .catch(applyGenericErrorHandling);

export const makeSendMarketingEmail =
  (axiosClient: AxiosInstance) => (payload: MarketingEmailDto) =>
    axiosClient
      .post(apiUrls.sendMarketingEmail, payload)
      .then(() => undefined)
      .catch(applyGenericErrorHandling);
