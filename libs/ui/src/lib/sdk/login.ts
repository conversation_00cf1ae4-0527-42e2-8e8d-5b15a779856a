import { AxiosInstance } from "axios";

import { apiUrls, isError, CredentialsDto, CoachUser } from "@mio/helpers";
import { applyGenericErrorHandling } from "./genericErrorHandling";

export const makeLoginEndpoint = (axiosClient: AxiosInstance) => (payload: CredentialsDto) =>
  axiosClient
    .post(apiUrls.login, payload)
    .then((res) => {
      const result = CoachUser.toAccessTokenDto(res.data);

      if (isError(result)) {
        throw result;
      }
      return result;
    })
    .catch(applyGenericErrorHandling);
