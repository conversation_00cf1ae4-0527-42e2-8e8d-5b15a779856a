import { AxiosInstance } from "axios";

import { Assets, PlayerId, UrlParams, apiUrls, buildUrlWithParams, isError } from "@mio/helpers";

import { applyGenericErrorHandling } from "./genericErrorHandling";

export type UploadImageParams = {
  playerId: PlayerId;
  dto: Assets.Image.ClientCreateDto;
};

export const makeUploadImage = (axiosClient: AxiosInstance) => (payload: UploadImageParams) => {
  const formData = new FormData();
  formData.append("file", payload.dto.file);
  formData.append("description", payload.dto.description ?? "");

  return axiosClient
    .post(
      buildUrlWithParams(apiUrls.assets.player_images, {
        [UrlParams.PlayerId]: payload.playerId,
      }),
      formData,
    )
    .then((res) => {
      const parsed = Assets.Image.Entity.toEntity(res.data);

      if (isError(parsed)) {
        parsed.addContext({
          service: makeUploadImage.name,
          method: axiosClient.post.name,
          operation: Assets.Image.Entity.toEntity.name,
          params: { playerId: payload.playerId, dto: payload.dto },
        });

        throw parsed;
      }

      return parsed;
    })
    .catch(applyGenericErrorHandling);
};

export type DeleteImageParams = {
  playerId: PlayerId;
  imageId: Assets.Image.ImageId;
};

export const makeDeleteImage = (axiosClient: AxiosInstance) => (payload: DeleteImageParams) =>
  axiosClient
    .delete(
      buildUrlWithParams(apiUrls.assets.player_image, {
        [UrlParams.PlayerId]: payload.playerId,
        [UrlParams.PlayerImageId]: payload.imageId,
      }),
    )
    .then(() => undefined)
    .catch(applyGenericErrorHandling);

export type GetImageParams = DeleteImageParams;

export const makeGetImage =
  (axiosClient: AxiosInstance) =>
  async (params: GetImageParams): Promise<Assets.Image.ImageAsset> => {
    return axiosClient
      .get(
        buildUrlWithParams(apiUrls.assets.player_image, {
          [UrlParams.PlayerImageId]: params.imageId,
          [UrlParams.PlayerId]: params.playerId,
        }),
      )
      .then((res) => {
        const parsed = Assets.Image.Entity.toEntity(res.data);

        if (isError(parsed)) {
          parsed.addContext({
            service: makeGetImage.name,
            method: axiosClient.get.name,
            operation: Assets.Image.Entity.toEntity.name,
            params,
          });

          throw parsed;
        }

        return parsed;
      })
      .catch(applyGenericErrorHandling);
  };
