import { AxiosInstance } from "axios";

import {
  apiUrls,
  buildUrlWithParams,
  UrlParams,
  isError,
  OrganizationId,
  TeamId,
  FootballMatchStats,
  StatsCategory,
} from "@mio/helpers";

import { applyGenericErrorHandling } from "../genericErrorHandling";

export const makeGetPlayerMatchStats =
  (axiosClient: AxiosInstance) =>
  (organizationId: OrganizationId, teamId: TeamId, category: StatsCategory) =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.footballMatchPlayerStats, {
          [UrlParams.OrganizationId]: organizationId,
          [UrlParams.TeamId]: teamId,
        }),
        { params: { category } },
      )
      .then((res) => {
        const result = FootballMatchStats.Entity.toPlayersGoalsStatsEntities(res.data);

        if (isError(result)) {
          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeGetTeamAttemptsForStats =
  (axiosClient: AxiosInstance) => (organizationId: OrganizationId, teamId: TeamId) =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.statsTeamAttemptsFor, {
          [UrlParams.OrganizationId]: organizationId,
          [UrlParams.TeamId]: teamId,
        }),
      )
      .then((res) => res.data);

export const makeGetTeamAttemptsAgainstStats =
  (axiosClient: AxiosInstance) => (organizationId: OrganizationId, teamId: TeamId) =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.statsTeamAttemptsAgainst, {
          [UrlParams.OrganizationId]: organizationId,
          [UrlParams.TeamId]: teamId,
        }),
      )
      .then((res) => res.data);
