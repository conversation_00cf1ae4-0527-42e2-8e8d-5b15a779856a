import { AxiosInstance } from "axios";

import {
  apiUrls,
  buildUrlWithParams,
  UrlParams,
  isError,
  OrganizationId,
  CreateSeasonDto,
  Season,
  SeasonId,
  UpdateSeasonDto,
} from "@mio/helpers";

import { applyGenericErrorHandling } from "./genericErrorHandling";

export const makeCreateSeason = (axiosClient: AxiosInstance) => (payload: CreateSeasonDto) =>
  axiosClient
    .post(
      buildUrlWithParams(apiUrls.createSeason, {
        [UrlParams.OrganizationId]: payload.organizationId,
      }),
      payload,
    )
    .then(() => undefined)
    .catch(applyGenericErrorHandling);

export const makeUpdateSeason = (axiosClient: AxiosInstance) => (payload: UpdateSeasonDto) =>
  axiosClient
    .patch(
      buildUrlWithParams(apiUrls.updateSeason, {
        [UrlParams.SeasonId]: payload.id,
        [UrlParams.OrganizationId]: payload.organizationId,
      }),
      payload,
    )
    .then((response) => {
      const parsed = Season.toEntity(response.data);

      if (isError(parsed)) {
        throw parsed;
      }
      return parsed;
    })
    .catch(applyGenericErrorHandling);

export const makeGetSeasons = (axiosClient: AxiosInstance) => (payload: { id: OrganizationId }) =>
  axiosClient
    .get(buildUrlWithParams(apiUrls.findSeasons, { organizationId: payload.id }))
    .then((res) => {
      const result = Season.toEntities(res.data);

      if (isError(result)) {
        throw result;
      }
      return result;
    })
    .catch(applyGenericErrorHandling);

export const makeGetSeason =
  (axiosClient: AxiosInstance) => (seasonId: SeasonId, orgId: OrganizationId) =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.getSeason, {
          [UrlParams.SeasonId]: seasonId,
          [UrlParams.OrganizationId]: orgId,
        }),
      )
      .then((res) => {
        const result = Season.toEntity(res.data);

        if (isError(result)) {
          throw result;
        }
        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeGetCurrentSeason = (axiosClient: AxiosInstance) => (orgId: OrganizationId) =>
  axiosClient
    .get(
      buildUrlWithParams(apiUrls.currentSeason, {
        [UrlParams.OrganizationId]: orgId,
      }),
    )
    .then((res) => {
      if (!res.data) {
        return null;
      }

      const result = Season.toEntity(res.data);

      if (isError(result)) {
        throw result;
      }
      return result;
    })
    .catch(applyGenericErrorHandling);
