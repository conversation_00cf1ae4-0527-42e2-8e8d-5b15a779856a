import { AxiosInstance } from "axios";

import {
  apiUrls,
  buildUrlWithParams,
  UrlParams,
  isError,
  PlayerId,
  PaymentRequest,
} from "@mio/helpers";

export const makeGetPlayerPayments =
  (axiosClient: AxiosInstance) =>
  (playerId: PlayerId): Promise<PaymentRequest.Entity[]> =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.payments.my_payments, {
          [UrlParams.PlayerId]: playerId,
        }),
      )
      .then((response) => {
        const payments = PaymentRequest.Entity.toEntities(response.data);

        if (isError(payments)) {
          payments.addContext({
            service: makeGetPlayerPayments.name,
            method: axiosClient.get.name,
            operation: PaymentRequest.Entity.toEntities.name,
            params: { playerId },
          });
          throw payments;
        }

        return payments;
      });

export const makeCreateCheckoutSession =
  (axiosClient: AxiosInstance) => (paymentRequestId: PaymentRequest.Id) =>
    axiosClient
      .post(
        buildUrlWithParams(apiUrls.payments.create_checkout_session, {
          [UrlParams.PaymentRequestId]: paymentRequestId,
        }),
      )
      .then((response) => {
        if (isError(response)) {
          response.addContext({
            service: makeGetPlayerPayments.name,
            method: axiosClient.get.name,
            operation: PaymentRequest.Entity.toEntities.name,
            params: { paymentRequestId },
          });

          throw response;
        }

        return response;
      });
