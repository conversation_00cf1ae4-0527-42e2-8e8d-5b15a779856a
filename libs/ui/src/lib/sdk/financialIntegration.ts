import { AxiosInstance } from "axios";

import {
  apiUrls,
  buildUrlWithParams,
  UrlParams,
  isError,
  OrganizationId,
  CreateFinancialIntegrationDto,
  UpdateFinancialIntegrationDto,
  FinancialIntegration,
  FinancialIntegrationId,
} from "@mio/helpers";

import { applyGenericErrorHandling } from "./genericErrorHandling";

export const makeCreateFinancialIntegration =
  (axiosClient: AxiosInstance) => (payload: CreateFinancialIntegrationDto) =>
    axiosClient
      .post(
        buildUrlWithParams(apiUrls.generalFinancialIntegration, {
          [UrlParams.OrganizationId]: payload.organizationId,
        }),
        payload,
      )
      .then(() => undefined)
      .catch(applyGenericErrorHandling);

export const makeUpdateFinancialIntegration =
  (axiosClient: AxiosInstance) =>
  (payload: {
    organizationId: OrganizationId;
    updatedFinancialIntegration: UpdateFinancialIntegrationDto;
  }) =>
    axiosClient
      .patch(
        buildUrlWithParams(apiUrls.identifiedFinancialIntegration, {
          [UrlParams.FinancialIntegrationId]: payload.updatedFinancialIntegration.id,
          [UrlParams.OrganizationId]: payload.organizationId,
        }),
        payload.updatedFinancialIntegration,
      )
      .then((response) => {
        const parsed = FinancialIntegration.toEntity(response.data);

        if (isError(parsed)) {
          throw parsed;
        }

        return parsed;
      })
      .catch(applyGenericErrorHandling);

export const makeDeleteFinancialIntegration =
  (axiosClient: AxiosInstance) =>
  (payload: { organizationId: OrganizationId; financialIntegrationId: FinancialIntegrationId }) =>
    axiosClient
      .delete(
        buildUrlWithParams(apiUrls.identifiedFinancialIntegration, {
          [UrlParams.FinancialIntegrationId]: payload.financialIntegrationId,
          [UrlParams.OrganizationId]: payload.organizationId,
        }),
      )
      .then(() => undefined)
      .catch(applyGenericErrorHandling);

export const makeFindFinancialIntegrations =
  (axiosClient: AxiosInstance) => (payload: { id: OrganizationId }) =>
    axiosClient
      .get(buildUrlWithParams(apiUrls.generalFinancialIntegration, { organizationId: payload.id }))
      .then((res) => {
        const result = FinancialIntegration.toEntities(res.data);

        if (isError(result)) {
          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeGetFinancialIntegration =
  (axiosClient: AxiosInstance) =>
  (financialIntegrationId: FinancialIntegrationId, orgId: OrganizationId) =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.identifiedFinancialIntegration, {
          [UrlParams.FinancialIntegrationId]: financialIntegrationId,
          [UrlParams.OrganizationId]: orgId,
        }),
      )
      .then((res) => {
        const result = FinancialIntegration.toEntity(res.data);

        if (isError(result)) {
          throw result;
        }

        return result;
      })
      .catch(applyGenericErrorHandling);
