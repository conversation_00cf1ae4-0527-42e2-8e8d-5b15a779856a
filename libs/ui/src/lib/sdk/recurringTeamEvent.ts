import { AxiosInstance } from "axios";

import { apiUrls, buildUrlWithParams, isError, RecurringTeamEvent, UrlParams } from "@mio/helpers";

import { applyGenericErrorHandling } from "./genericErrorHandling";

export const makeCreateRecurringTeamEvent =
  (axiosClient: AxiosInstance) => (payload: RecurringTeamEvent.CreateDto) =>
    axiosClient
      .post(
        buildUrlWithParams(apiUrls.recurringTeamEvents, {
          [UrlParams.OrganizationId]: payload.organizationId,
          [UrlParams.TeamId]: payload.teamId,
        }),
        payload,
      )
      .then((response) => {
        const parsed = RecurringTeamEvent.Entity.toEntity(response.data);

        if (isError(parsed)) {
          throw parsed;
        }

        return parsed;
      })
      .catch(applyGenericErrorHandling);

export const makeUpdateRecurringTeamEvent =
  (axiosClient: AxiosInstance) => (payload: RecurringTeamEvent.UpdateDto) =>
    axiosClient
      .patch(
        buildUrlWithParams(apiUrls.recurringTeamEvent, {
          [UrlParams.RecurringTeamEventId]: payload.id,
          [UrlParams.TeamId]: payload.teamId,
          [UrlParams.OrganizationId]: payload.organizationId,
        }),
        payload,
      )
      .then((response) => {
        const parsed = RecurringTeamEvent.Entity.toEntity(response.data);

        if (isError(parsed)) {
          throw parsed;
        }

        return parsed;
      })
      .catch(applyGenericErrorHandling);
