import { AxiosInstance } from "axios";

import { apiUrls, isError, PlayerUser } from "@mio/helpers";

import { applyGenericErrorHandling } from "./genericErrorHandling";

export const makeGetCurrentPlayerUser = (axiosClient: AxiosInstance) => () =>
  axiosClient
    .get(apiUrls.currentPlayerUser)
    .then((res) => {
      const result = PlayerUser.Entity.toEntity(res.data);

      if (isError(result)) {
        result.addContext({
          service: makeGetCurrentPlayerUser.name,
          method: axiosClient.get.name,
          operation: PlayerUser.Entity.toEntity.name,
          input: res.data,
        });

        throw result;
      }

      return result;
    })
    .catch(applyGenericErrorHandling);
