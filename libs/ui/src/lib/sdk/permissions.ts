import { AxiosInstance } from "axios";

import {
  apiUrls,
  buildUrlWithParams,
  UrlParams,
  isError,
  OrganizationId,
  PermissionsModule,
} from "@mio/helpers";
import { applyGenericErrorHandling } from "./genericErrorHandling";

export const makeCreatePermission =
  (axiosClient: AxiosInstance) => (payload: PermissionsModule.PermissionEntity.CreateCustomDto) =>
    axiosClient
      .post(
        buildUrlWithParams(apiUrls.permissions, {
          [UrlParams.OrganizationId]: payload.organizationId,
        }),
        payload,
      )
      .then((response) => {
        const parsed = PermissionsModule.PermissionEntity.Permission.toEntity(response.data);

        if (isError(parsed)) {
          throw parsed;
        }

        return parsed;
      })
      .catch(applyGenericErrorHandling);

export type UpdatePermissionParams = {
  permissionId: PermissionsModule.PermissionEntity.PermissionId;
  organizationId: OrganizationId;
  dto: PermissionsModule.PermissionEntity.UpdateCustomDto;
};

export const makeUpdatePermission =
  (axiosClient: AxiosInstance) => (payload: UpdatePermissionParams) =>
    axiosClient
      .patch(
        buildUrlWithParams(apiUrls.permission, {
          [UrlParams.PermissionId]: payload.permissionId,
          [UrlParams.OrganizationId]: payload.organizationId,
        }),
        payload.dto,
      )
      .then((response) => {
        const parsed = PermissionsModule.PermissionEntity.Permission.toEntity(response.data);

        if (isError(parsed)) {
          throw parsed;
        }
        return parsed;
      })
      .catch(applyGenericErrorHandling);

export const makeGetPermissions = (axiosClient: AxiosInstance) => (orgId: OrganizationId) =>
  axiosClient
    .get(buildUrlWithParams(apiUrls.currentPermissions, { [UrlParams.OrganizationId]: orgId }))
    .then((res) => {
      const result = PermissionsModule.PermissionEntity.Permission.toPublicEntities(res.data);

      if (isError(result)) {
        throw result;
      }
      return result;
    })
    .catch(applyGenericErrorHandling);

export type DeletePermissionParams = {
  organizationId: OrganizationId;
  permissionId: PermissionsModule.PermissionEntity.PermissionId;
};

export const makeDeletePermission =
  (axiosClient: AxiosInstance) => (payload: DeletePermissionParams) =>
    axiosClient
      .delete(
        buildUrlWithParams(apiUrls.permission, {
          [UrlParams.PermissionId]: payload.permissionId,
          [UrlParams.OrganizationId]: payload.organizationId,
        }),
      )
      .then(() => {
        return undefined;
      })
      .catch(applyGenericErrorHandling);
