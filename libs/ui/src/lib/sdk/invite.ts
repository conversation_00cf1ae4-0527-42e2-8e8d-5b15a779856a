import { AxiosInstance } from "axios";

import {
  ExpiredInvite,
  Invite,
  CreateInviteDto,
  InviteId,
  isError,
  OrganizationId,
  apiUrls,
  buildUrlWithParams,
  UrlParams,
  PendingInvite,
} from "@mio/helpers";
import { applyGenericErrorHandling } from "./genericErrorHandling";

export const makeGetInviteEndpoint = (axiosClient: AxiosInstance) => (payload: { id: InviteId }) =>
  axiosClient
    .get(buildUrlWithParams(apiUrls.getInvite, { inviteId: payload.id }))
    .then((res) => {
      const result = Invite.toEntity(res.data);

      if (isError(result)) {
        throw result;
      }
      return result;
    })
    .catch(applyGenericErrorHandling);

export const makeGetUnredeemedInvites =
  (axiosClient: AxiosInstance) =>
  (orgId: OrganizationId): Promise<Array<PendingInvite | ExpiredInvite>> =>
    axiosClient
      .get(buildUrlWithParams(apiUrls.getInvites, { [UrlParams.OrganizationId]: orgId }))
      .then((res) => {
        const result = Invite.toEntities(res.data);

        if (isError(result)) {
          throw result;
        }

        return Invite.filterNonRedeemed(result);
      })
      .catch(applyGenericErrorHandling);

export const makeDeleteInvite =
  (axiosClient: AxiosInstance) =>
  (params: { orgId: OrganizationId; inviteId: InviteId }): Promise<void> =>
    axiosClient
      .delete(
        buildUrlWithParams(apiUrls.inviteDetails, {
          [UrlParams.OrganizationId]: params.orgId,
          [UrlParams.InviteId]: params.inviteId,
        }),
      )
      .then(() => {
        return undefined;
      })
      .catch(applyGenericErrorHandling);

export const makeCreateInvite =
  (axiosClient: AxiosInstance) =>
  (dto: CreateInviteDto): Promise<void> =>
    axiosClient
      .post(
        buildUrlWithParams(apiUrls.createInvite, {
          [UrlParams.OrganizationId]: dto.organization,
        }),
        dto,
      )
      .then(() => {
        return undefined;
      })
      .catch(applyGenericErrorHandling);
