import { AxiosInstance } from "axios";

import {
  apiUrls,
  buildUrlWithParams,
  isError,
  OrganizationId,
  PlayerId,
  TeamEvent,
  TeamId,
  UrlParams,
} from "@mio/helpers";

import { applyGenericErrorHandling } from "./genericErrorHandling";

export const makeGetTeamEvents =
  (axiosClient: AxiosInstance) =>
  (orgId: OrganizationId, teamId: TeamId, query: TeamEvent.QueryDto) =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.teamEvents, {
          [UrlParams.OrganizationId]: orgId,
          [UrlParams.TeamId]: teamId,
        }),
        { params: query },
      )
      .then((res) => {
        const result = TeamEvent.Entity.toExtendedEntities(res.data);

        if (isError(result)) {
          result.addContext({
            service: makeGetTeamEvents.name,
            operation: TeamEvent.Entity.toExtendedEntities.name,
            data: res.data,
          });

          throw result;
        }
        return result;
      })
      .catch(applyGenericErrorHandling);

export const makeCreateTeamEvent =
  (axiosClient: AxiosInstance) => (payload: TeamEvent.CreateSingleDto) =>
    axiosClient
      .post(
        buildUrlWithParams(apiUrls.teamEvents, {
          [UrlParams.OrganizationId]: payload.organizationId,
          [UrlParams.TeamId]: payload.teamId,
        }),
        payload,
      )
      .then((response) => {
        const parsed = TeamEvent.Entity.toSingleEventEntity(response.data);

        if (isError(parsed)) {
          throw parsed;
        }

        return parsed;
      })
      .catch(applyGenericErrorHandling);

export const makeGetPlayerTeamEvents =
  (axiosClient: AxiosInstance) => (playerId: PlayerId, query: TeamEvent.QueryDto) => {
    return axiosClient
      .get(
        buildUrlWithParams(apiUrls.playerEvents, {
          [UrlParams.PlayerId]: playerId,
        }),
        { params: query },
      )
      .then((res) => {
        const result = TeamEvent.Entity.toPlayerEventEntities(res.data);

        if (isError(result)) {
          result.addContext({
            service: makeGetPlayerTeamEvents.name,
            operation: TeamEvent.Entity.toPlayerEventEntities.name,
            data: res.data,
          });

          throw result;
        }
        return result;
      })
      .catch(applyGenericErrorHandling);
  };

export type PlayerEventResponseParams = TeamEvent.AttendanceDto & {
  playerId: PlayerId;
  teamEventId: TeamEvent.EventId;
};

export const makeRespondToPlayerEvent =
  (axiosClient: AxiosInstance) =>
  async (payload: PlayerEventResponseParams): Promise<TeamEvent.PlayerTeamEvent> => {
    return axiosClient
      .patch(
        buildUrlWithParams(apiUrls.playerTeamEvent, {
          [UrlParams.PlayerId]: payload.playerId,
          [UrlParams.TeamEventId]: payload.teamEventId,
        }),
        payload,
      )
      .then((res) => {
        const result = TeamEvent.Entity.toPlayerEventEntity(res.data);

        if (isError(result)) {
          result.addContext({
            service: makeRespondToPlayerEvent.name,
            operation: TeamEvent.Entity.toPlayerEventEntity.name,
            data: res.data,
          });

          throw result;
        }
        return result;
      })
      .catch(applyGenericErrorHandling);
  };
