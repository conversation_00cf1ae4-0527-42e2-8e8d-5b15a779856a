import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useParams } from "react-router-dom";

import { useSdk } from "./useSdk";
import {
  APIError,
  CreateTeamDto,
  OrganizationId,
  Team,
  TeamId,
  UUID,
  UrlParams,
  isError,
  UpdateTeamDto,
} from "@mio/helpers";
import { StateError, StateErrors } from "./errors";

const teamsKey = ["teams"];
const adminTeamsKey = ["admin-teams"];

export const useGetTeams = (orgId: OrganizationId) => {
  const sdk = useSdk();

  return useQuery<Team[], APIError>(teamsKey, () => sdk.getTeams({ id: orgId }), {
    refetchOnWindowFocus: false,
  });
};

export const useGetAdminTeams = (orgId: OrganizationId) => {
  const sdk = useSdk();

  return useQuery<Team[], APIError>(adminTeamsKey, () => sdk.getAdminTeams({ id: orgId }), {
    refetchOnWindowFocus: false,
  });
};

export const useCreateTeam = () => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<undefined, APIError, CreateTeamDto>(sdk.createTeam, {
    onSuccess: () => {
      queryClient.refetchQueries(teamsKey);
    },
  });
};

export const useUpdateTeam = () => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<Team, APIError, UpdateTeamDto>(sdk.updateTeam, {
    onSuccess: () => {
      queryClient.refetchQueries(teamsKey);
    },
  });
};

export const useDeleteTeam = () => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<undefined, APIError, TeamId>(
    (teamId: TeamId) => {
      return sdk.deleteTeam(teamId);
    },
    {
      onSuccess: () => {
        queryClient.refetchQueries(teamsKey);
      },
    },
  );
};

export const useGetTeam = (orgId: OrganizationId, teamId: TeamId) => {
  const sdk = useSdk();

  return useQuery<Team, APIError>(["teams", teamId], () => sdk.getTeam(orgId, teamId), {
    refetchOnWindowFocus: false,
  });
};

export const useTeamId = () => {
  const params = useParams();

  const validated = UUID.parse<TeamId>(params[UrlParams.TeamId]);

  return isError(validated) ? undefined : validated;
};

export const useRequiredTeamId = () => {
  const params = useParams();

  const validated = UUID.parse<TeamId>(params[UrlParams.TeamId]);

  if (isError(validated)) {
    throw new StateError(StateErrors.InvalidTeamId);
  }

  return validated;
};

export const useRequiredTeam = () => {
  const teamId = useRequiredTeamId();
  const client = useQueryClient();

  const teams = client.getQueryData<Team[]>(teamsKey);

  const activeTeam = teams?.find((team) => team.id === teamId);

  if (!activeTeam) {
    throw new StateError(StateErrors.TeamNotFound);
  }

  return activeTeam;
};
