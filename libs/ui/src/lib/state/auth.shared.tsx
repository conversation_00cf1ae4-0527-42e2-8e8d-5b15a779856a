import { noop } from "lodash/fp";
import { createContext, FC, useState, useCallback, useContext } from "react";

import { AccessToken } from "@mio/helpers";

export type AuthParams = Partial<{
  onRegisterSuccess: () => void;
}>;

type TokenContextProps = {
  token?: AccessToken;
  setToken: (token: AccessToken) => void;
  deleteToken: () => void;
};

const TokenContext = createContext<TokenContextProps>({
  token: undefined,
  setToken: noop,
  deleteToken: noop,
});

export const TokenContextProvider: FC<{ children: JSX.Element }> = ({ children }) => {
  const [token, setLocalToken] = useState(
    (localStorage.getItem("token") as AccessToken) || undefined,
  );

  const setToken = (newToken: AccessToken) => {
    localStorage.setItem("token", newToken);
    setLocalToken(newToken);
  };

  const deleteToken = () => {
    localStorage.removeItem("token");
    setLocalToken(undefined);
  };

  return (
    <TokenContext.Provider value={{ token, setToken, deleteToken }}>
      {children}
    </TokenContext.Provider>
  );
};

/* we don't want to use "?returnUrl=..." when the user has logged out manually */
export const useLogoutContext = () => {
  const key = "didLogout";

  const didLogoutRaw = localStorage.getItem(key);
  const didLogout = didLogoutRaw ? didLogoutRaw === "true" : false;

  const setLogoutMode = useCallback((value: boolean) => {
    localStorage.setItem(key, String(value));
  }, []);

  return { didLogout, setLogoutMode };
};

export const useAuthToken = () => {
  return useContext<TokenContextProps>(TokenContext);
};
