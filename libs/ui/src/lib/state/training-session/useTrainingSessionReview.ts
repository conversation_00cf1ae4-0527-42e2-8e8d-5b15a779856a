import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useParams } from "react-router-dom";

import {
  APIError,
  TrainingSessionReview,
  UrlParams,
  isError,
  UUID,
  TrainingSessionPlayerReview,
  TeamId,
} from "@mio/helpers";

import { useSdk } from "../useSdk";
import { StateError, StateErrors } from "../errors";
import { useOrganization } from "../useOrganization";
import { useRequiredTeamId } from "../useTeam";

export const useRequiredReviewId = () => {
  const params = useParams();

  const validated = UUID.parse<TrainingSessionReview.ReviewId>(
    params[UrlParams.TrainingSessionReviewId],
  );

  if (isError(validated)) {
    throw new StateError(StateErrors.InvalidTrainingSessionReviewId);
  }

  return validated;
};

const createCacheKey = (teamId: TeamId, reviewId: TrainingSessionReview.ReviewId) => [
  "training-session-reviews",
  teamId,
  reviewId,
];

export const useGetExtendedTrainingSessionReview = () => {
  const sdk = useSdk();
  const organization = useOrganization();
  const teamId = useRequiredTeamId();
  const reviewId = useRequiredReviewId();
  const client = useQueryClient();

  const key = createCacheKey(teamId, reviewId);

  const query = useQuery<TrainingSessionReview.ExtendedReview, APIError>(
    key,
    () => sdk.trainingSession.getReview(organization.id, teamId, reviewId),
    {
      refetchOnWindowFocus: false,
    },
  );

  const addPlayerReview = (
    review: TrainingSessionReview.ExtendedReview,
    playerReview: TrainingSessionPlayerReview.PlayerReview,
  ) => {
    const updatedReview = TrainingSessionReview.Entity.addPlayerReview(review, playerReview);

    client.setQueryData(key, updatedReview);
  };

  const editPlayerReview = (
    review: TrainingSessionReview.ExtendedReview,
    playerReview: TrainingSessionPlayerReview.PlayerReview,
  ) => {
    const updatedReview = TrainingSessionReview.Entity.editPlayerReview(review, playerReview);

    client.setQueryData(key, updatedReview);
  };

  return { query, addPlayerReview, editPlayerReview };
};

export const useCreateTrainingSessionReview = () => {
  const sdk = useSdk();

  return useMutation<TrainingSessionReview.Review, APIError, TrainingSessionReview.CreateDto>(
    (dto) => sdk.trainingSession.createReview(dto),
  );
};

export const useUpdateTrainingSessionReview = () => {
  const sdk = useSdk();

  return useMutation<TrainingSessionReview.Review, APIError, TrainingSessionReview.Review>((dto) =>
    sdk.trainingSession.updateReview(dto),
  );
};
