import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { OrganizationId, APIError, SeasonPlan, TeamId } from "@mio/helpers";

import { useSdk } from "../useSdk";

const cacheKey = ["season-plans"];

export const useGetSeasonPlans = (orgId: OrganizationId, teamId: TeamId) => {
  const sdk = useSdk();

  return useQuery<SeasonPlan.SeasonPlan[], APIError>(
    cacheKey,
    () => sdk.trainingSession.getSeasonPlans(orgId, teamId),
    {
      refetchOnWindowFocus: false,
    },
  );
};

export const useCreateSeasonPlan = () => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<SeasonPlan.SeasonPlan, APIError, SeasonPlan.CreateDto>(
    (dto) => sdk.trainingSession.createSeasonPlan(dto),
    {
      onSuccess: () => {
        queryClient.refetchQueries(cacheKey);
      },
    },
  );
};

export const useUpdateSeasonPlan = () => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<SeasonPlan.SeasonPlan, APIError, SeasonPlan.SeasonPlan>(
    (dto) => sdk.trainingSession.updateSeasonPlan(dto),
    {
      onSuccess: () => {
        queryClient.refetchQueries(cacheKey);
      },
    },
  );
};
