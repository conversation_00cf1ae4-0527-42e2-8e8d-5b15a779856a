import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { OrganizationId, APIError, TrainingSessionPlan, TeamId } from "@mio/helpers";

import { useSdk } from "../useSdk";

const cacheKey = ["training-session-plans"];

export const useGetTrainingSessionPlans = (orgId: OrganizationId, teamId: TeamId) => {
  const sdk = useSdk();

  return useQuery<TrainingSessionPlan.Plan[], APIError>(
    cacheKey,
    () => sdk.trainingSession.getPlans(orgId, teamId),
    {
      refetchOnWindowFocus: false,
    },
  );
};

export const useCreateTrainingSessionPlan = () => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<TrainingSessionPlan.Plan, APIError, TrainingSessionPlan.CreateDto>(
    (dto) => sdk.trainingSession.createPlan(dto),
    {
      onSuccess: () => {
        queryClient.refetchQueries(cacheKey);
      },
    },
  );
};

export const useUpdateTrainingSessionPlan = () => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<TrainingSessionPlan.Plan, APIError, TrainingSessionPlan.Plan>(
    (dto) => sdk.trainingSession.updatePlan(dto),
    {
      onSuccess: () => {
        queryClient.refetchQueries(cacheKey);
      },
    },
  );
};
