import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { OrganizationId, APIError, TrainingSessionPractice, TeamId } from "@mio/helpers";

import { useSdk } from "../useSdk";

const cacheKey = ["training-session-practices"];

export const useGetTrainingSessionPractices = (orgId: OrganizationId, teamId: TeamId) => {
  const sdk = useSdk();

  return useQuery<TrainingSessionPractice.Practice[], APIError>(
    cacheKey,
    () => sdk.trainingSession.getPractices(orgId, teamId),
    {
      refetchOnWindowFocus: false,
    },
  );
};

export const useCreateTrainingSessionPractice = () => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<TrainingSessionPractice.Practice, APIError, TrainingSessionPractice.CreateDto>(
    (dto) => sdk.trainingSession.createPractice(dto),
    {
      onSuccess: () => {
        queryClient.refetchQueries(cacheKey);
      },
    },
  );
};

export const useUpdateTrainingSessionPractice = () => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<TrainingSessionPractice.Practice, APIError, TrainingSessionPractice.Practice>(
    (dto) => sdk.trainingSession.updatePractice(dto),
    {
      onSuccess: () => {
        queryClient.refetchQueries(cacheKey);
      },
    },
  );
};
