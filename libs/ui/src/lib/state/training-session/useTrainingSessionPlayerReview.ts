import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { APIError, TrainingSessionPlayerReview } from "@mio/helpers";

import { useSdk } from "../useSdk";
import { useOrganization } from "../useOrganization";
import { useRequiredTeamId } from "../useTeam";

const cacheKey = ["training-session-player-reviews"];

export const useGetTrainingSessionPlayerReviews = (query: TrainingSessionPlayerReview.QueryDto) => {
  const sdk = useSdk();
  const organization = useOrganization();
  const teamId = useRequiredTeamId();

  return useQuery<TrainingSessionPlayerReview.ExtendedReview[], APIError>(
    [...cacheKey, teamId, query.startDate?.toISOString(), query.endDate?.toISOString()],
    () => sdk.trainingSession.getPlayerReviews(organization.id, teamId, query),
    {
      refetchOnWindowFocus: false,
    },
  );
};

export const useCreateTrainingSessionPlayerReview = () => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<
    TrainingSessionPlayerReview.PlayerReview,
    APIError,
    TrainingSessionPlayerReview.CreateDto
  >((dto) => sdk.trainingSession.createPlayerReview(dto), {
    onSuccess: () => {
      queryClient.refetchQueries(cacheKey);
    },
  });
};

export const useUpdateTrainingSessionPlayerReview = () => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<
    TrainingSessionPlayerReview.PlayerReview,
    APIError,
    TrainingSessionPlayerReview.PlayerReview
  >((dto) => sdk.trainingSession.updatePlayerReview(dto), {
    onSuccess: () => {
      queryClient.refetchQueries(cacheKey);
    },
  });
};
