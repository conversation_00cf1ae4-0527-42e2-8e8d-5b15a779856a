import { useQuery, useQueryClient, useMutation } from "@tanstack/react-query";

import { APIError, OrganizationId, PermissionsModule } from "@mio/helpers";
import { StateError, StateErrors } from "./errors";
import { DeletePermissionParams, UpdatePermissionParams } from "../sdk";
import { useSdk } from "./useSdk";

const createKey = (organizationId: OrganizationId) => [organizationId, "currentPermissions"];

export const useLoadPermissions = (orgId: OrganizationId) => {
  const sdk = useSdk();

  return useQuery<PermissionsModule.PermissionEntity.PublicPermission[], APIError>(
    createKey(orgId),
    () => sdk.getCurrentPermissions(orgId),
    {
      refetchOnWindowFocus: false,
    },
  );
};

export const useRequiredPermissions = (orgId: OrganizationId) => {
  const client = useQueryClient();

  const cachedData = client.getQueryData<PermissionsModule.PermissionEntity.PublicPermission[]>(
    createKey(orgId),
  );

  if (!cachedData) {
    throw new StateError(StateErrors.MissingPermissions);
  }

  return cachedData;
};

export const useCreatePermission = () => {
  const sdk = useSdk();

  return useMutation<
    PermissionsModule.PermissionEntity.Permission,
    APIError,
    PermissionsModule.PermissionEntity.CreateCustomDto
  >(sdk.createPermission);
};

export const useUpdatePermission = () => {
  const sdk = useSdk();

  return useMutation<
    PermissionsModule.PermissionEntity.Permission,
    APIError,
    UpdatePermissionParams
  >(sdk.updatePermission);
};

export const useDeletePermission = () => {
  const sdk = useSdk();

  return useMutation<void, APIError, DeletePermissionParams>(sdk.deletePermission);
};
