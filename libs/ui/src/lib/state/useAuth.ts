import { useCallback } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";

import { useSdk } from "./useSdk";
import { APIError, AccessTokenDto, CredentialsDto, InviteRegistrationDto } from "@mio/helpers";
import { useAuthToken, useLogoutContext } from "./auth.shared";

type Options = {
  delayLogin?: number;
};

export const useAuth = (options?: Options) => {
  const sdk = useSdk();
  const client = useQueryClient();
  const { setLogoutMode } = useLogoutContext();
  const { token, setToken, deleteToken } = useAuthToken();

  const loginState = useMutation<AccessTokenDto, APIError, CredentialsDto>(sdk.login, {
    onSuccess: (tokenDto) => {
      if (options?.delayLogin) {
        setTimeout(() => {
          client.removeQueries();
          setLogoutMode(false);
          setToken(tokenDto.token);
        }, options.delayLogin);
      } else {
        client.removeQueries();
        setLogoutMode(false);
        setToken(tokenDto.token);
      }
    },
  });

  const registerState = useMutation<void, APIError, InviteRegistrationDto>(sdk.register);

  const onLogout = useCallback(() => {
    client.removeQueries();
    setLogoutMode(true);
    deleteToken();
  }, [deleteToken, client, setLogoutMode]);

  return {
    login: loginState,
    register: registerState,
    isLogged: !!token,
    token,
    logout: onLogout,
  };
};
