import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { useSdk } from "./useSdk";

import {
  OrganizationId,
  APIError,
  FinancialIntegration,
  CreateFinancialIntegrationDto,
  UpdateFinancialIntegrationDto,
  FinancialIntegrationId,
} from "@mio/helpers";

const financialIntegrationsKey = ["financial-integrations"];

export const useCreateFinancialIntegration = () => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<undefined, APIError, CreateFinancialIntegrationDto>(
    sdk.createFinancialIntegration,
    {
      onSuccess: () => {
        queryClient.refetchQueries(financialIntegrationsKey);
      },
    },
  );
};

export const useUpdateFinancialIntegration = () => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<
    FinancialIntegration,
    APIError,
    { organizationId: OrganizationId; updatedFinancialIntegration: UpdateFinancialIntegrationDto }
  >(sdk.updateFinancialIntegration, {
    onSuccess: () => {
      queryClient.refetchQueries(financialIntegrationsKey);
    },
  });
};

export const useDeleteFinancialIntegration = () => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<
    undefined,
    APIError,
    { organizationId: OrganizationId; financialIntegrationId: FinancialIntegrationId }
  >(sdk.deleteFinancialIntegration, {
    onSuccess: () => {
      queryClient.refetchQueries(financialIntegrationsKey);
    },
  });
};

export const useFindFinancialIntegrations = (orgId: OrganizationId) => {
  const sdk = useSdk();

  return useQuery<FinancialIntegration[], APIError>(
    financialIntegrationsKey,
    () => sdk.findFinancialIntegrations({ id: orgId }),
    {
      refetchOnWindowFocus: false,
    },
  );
};

export const useGetFinancialIntegration = (
  orgId: OrganizationId,
  financialIntegrationId: FinancialIntegrationId,
) => {
  const sdk = useSdk();

  return useQuery<FinancialIntegration, APIError>(
    ["financial-integrations", financialIntegrationId],
    () => sdk.getFinancialIntegration(financialIntegrationId, orgId),
    {
      refetchOnWindowFocus: false,
    },
  );
};
