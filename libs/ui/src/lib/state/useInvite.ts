import { useMutation, useQuery } from "@tanstack/react-query";

import {
  CreateInviteDto,
  ExpiredInvite,
  Invite,
  InviteId,
  OrganizationId,
  PendingInvite,
  APIError,
} from "@mio/helpers";
import { useSdk } from "./useSdk";

export const useInvite = (data: { id: InviteId | undefined }) => {
  const sdk = useSdk();

  const makeApiCallGivenCorrectParams = () =>
    data.id ? sdk.getInvite({ id: data.id }) : Promise.resolve(undefined);

  const query = useQuery<Invite | undefined, APIError>(["invite"], makeApiCallGivenCorrectParams, {
    refetchOnWindowFocus: false,
  });

  return query;
};

export const useUnredeemedInvites = (orgId: OrganizationId) => {
  const sdk = useSdk();

  const query = useQuery<Array<PendingInvite | ExpiredInvite>, APIError>(
    ["invites", orgId],
    () => sdk.getUnredeemedInvites(orgId),
    {
      refetchOnWindowFocus: false,
    },
  );

  return query;
};

export const useDeleteInvite = () => {
  const sdk = useSdk();

  return useMutation<void, APIError, { orgId: OrganizationId; inviteId: InviteId }>(
    sdk.deleteInvite,
  );
};

export const useCreateInvite = () => {
  const sdk = useSdk();

  return useMutation<void, APIError, CreateInviteDto>(sdk.createInvite);
};
