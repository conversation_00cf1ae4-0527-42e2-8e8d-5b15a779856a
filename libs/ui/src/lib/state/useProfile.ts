import { useQuery } from "@tanstack/react-query";

import { PublicProfile, APIError, Profile } from "@mio/helpers";
import { useSdk } from "./useSdk";
import { StateError, StateErrors } from "./errors";
import { useOrganization } from "./useOrganization";
import { useRequiredTeam } from "./useTeam";

const cacheKey = ["currentProfile"];

/* Does an HTTP request to fetch the profile */
export const useCurrentProfile = () => {
  const sdk = useSdk();

  const query = useQuery<PublicProfile, APIError>(cacheKey, sdk.getCurrentProfile, {
    refetchOnWindowFocus: false,
  });

  return query;
};

/* Doesn't do any requests, should be used in tandem with CurrentProfileProvider
which either provides the Profile or catches the error we throw here */
export const useProvidedCurrentProfile = () => {
  const sdk = useSdk();

  const query = useQuery<PublicProfile, APIError>(cacheKey, sdk.getCurrentProfile, {
    enabled: false,
  });

  if (query.status !== "success" || !query.data) {
    throw new StateError(StateErrors.UserNotFound);
  }

  return query.data;
};

export const useTeamCoaches = () => {
  const sdk = useSdk();
  const team = useRequiredTeam();
  const organization = useOrganization();

  const query = useQuery<Profile[], APIError>([organization.id, "team-coaches", team.id], () =>
    sdk.getTeamCoaches(team.id, organization.id),
  );

  return query;
};
