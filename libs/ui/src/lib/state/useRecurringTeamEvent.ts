import { useMutation } from "@tanstack/react-query";

import { APIError, RecurringTeamEvent } from "@mio/helpers";

import { useSdk } from "./useSdk";

export const useCreateRecurringTeamEvent = () => {
  const sdk = useSdk();

  return useMutation<RecurringTeamEvent.RecurringTeamEvent, APIError, RecurringTeamEvent.CreateDto>(
    sdk.createRecurringTeamEvent,
  );
};

export const useUpdateRecurringTeamEvent = () => {
  const sdk = useSdk();

  return useMutation<RecurringTeamEvent.RecurringTeamEvent, APIError, RecurringTeamEvent.UpdateDto>(
    sdk.updateRecurringTeamEvent,
  );
};
