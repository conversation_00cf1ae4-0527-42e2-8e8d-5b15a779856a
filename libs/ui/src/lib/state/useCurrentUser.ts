import { useEffect } from "react";
import { useQuery, useQueryClient, useMutation } from "@tanstack/react-query";

import { StateError, StateErrors } from "./errors";
import {
  APIError,
  PasswordResetActionDto,
  PasswordResetInitiationDto,
  Coach,
  PublicCoachUser,
  OrganizationId,
  PermissionsModule,
} from "@mio/helpers";
import { useAuth } from "./useAuth";
import { useSdk } from "./useSdk";

const cacheKey = ["currentUser"];

/* does an HTTP request ot fetch the User */
export const useCurrentUser = (onSuccess?: (data: PublicCoachUser) => void) => {
  const sdk = useSdk();
  const { isLogged } = useAuth();

  const query = useQuery<PublicCoachUser, APIError>(cacheKey, sdk.getCurrentUser, {
    onSuccess,
    refetchOnWindowFocus: false,
  });

  useEffect(() => {
    if (isLogged) {
      query.refetch();
    } else {
      query.remove();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLogged]);

  return query;
};

/* Doesn't do any requests, should be used in tandem with CurrentUserProvider
which either provides the User or catches the error we throw here */
export const useProvidedCurrentUser = () => {
  const sdk = useSdk();

  const query = useQuery<PublicCoachUser, APIError>(cacheKey, sdk.getCurrentUser, {
    enabled: false,
  });

  if (query.status !== "success" || !query.data) {
    throw new StateError(StateErrors.UserNotFound);
  }

  return query.data;
};

const createOrgUsersKey = (orgId: OrganizationId) => ["organizations", orgId, "users"];

export const useOrganizationUsers = (orgId: OrganizationId) => {
  const sdk = useSdk();

  const query = useQuery<Coach<"populated">[], APIError>(
    createOrgUsersKey(orgId),
    () => sdk.getOrganizationUsers(orgId),
    {
      refetchOnWindowFocus: false,
    },
  );

  return { query };
};

export const useOrganizationUsersCrud = (orgId: OrganizationId) => {
  const client = useQueryClient();

  const key = createOrgUsersKey(orgId);
  const data = client.getQueryData<Coach<"populated">[]>(key) || [];

  const addPermission = (
    user: Coach<"populated">,
    permission: PermissionsModule.PermissionEntity.Permission,
  ) => {
    const updatedUser = {
      ...user,
      permissions: [...user.permissions, permission],
    };

    const result = data.map((elem) => {
      return elem.id === updatedUser.id ? updatedUser : elem;
    });

    client.setQueryData(key, result);
  };

  const updatePermission = (
    user: Coach<"populated">,
    updatedPermission: PermissionsModule.PermissionEntity.Permission,
  ) => {
    const updatedUser = {
      ...user,
      permissions: user.permissions.map((elem) =>
        elem.id === updatedPermission.id ? updatedPermission : elem,
      ),
    };

    const result = data.map((elem) => {
      return elem.id === updatedUser.id ? updatedUser : elem;
    });

    client.setQueryData(key, result);
  };

  const removePermission = (
    user: Coach<"populated">,
    permissionId: PermissionsModule.PermissionEntity.PermissionId,
  ) => {
    const updatedUser = {
      ...user,
      permissions: user.permissions.filter((elem) => elem.id !== permissionId),
    };

    const result = data.map((elem) => {
      return elem.id === updatedUser.id ? updatedUser : elem;
    });

    client.setQueryData(key, result);
  };

  return { addPermission, removePermission, updatePermission };
};

export const useInitiatePasswordReset = () => {
  const sdk = useSdk();

  return useMutation<undefined, APIError, PasswordResetInitiationDto>(sdk.initiatePasswordReset);
};

export const useResetPassword = () => {
  const sdk = useSdk();

  return useMutation<undefined, APIError, PasswordResetActionDto>(sdk.resetPassword);
};
