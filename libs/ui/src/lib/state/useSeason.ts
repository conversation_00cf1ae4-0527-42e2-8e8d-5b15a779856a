import { isError, useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useParams } from "react-router-dom";

import {
  CreateSeasonDto,
  OrganizationId,
  Season,
  SeasonId,
  UpdateSeasonDto,
  UUID,
  APIError,
  UrlParams,
} from "@mio/helpers";

import { useSdk } from "./useSdk";
import { StateError, StateErrors } from "./errors";
import { useOrganization } from "./useOrganization";

const seasonsKey = ["seasons"];

export const useGetSeasons = (orgId: OrganizationId) => {
  const sdk = useSdk();

  return useQuery<Season[], APIError>(seasonsKey, () => sdk.getSeasons({ id: orgId }), {
    refetchOnWindowFocus: false,
  });
};

export const useCreateSeason = () => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<undefined, APIError, CreateSeasonDto>(sdk.createSeason, {
    onSuccess: () => {
      queryClient.refetchQueries(seasonsKey);
    },
  });
};

export const useUpdateSeason = () => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<Season, APIError, UpdateSeasonDto>(sdk.updateSeason, {
    onSuccess: () => {
      queryClient.refetchQueries(seasonsKey);
    },
  });
};

export const useGetSeason = (orgId: OrganizationId, seasonId: SeasonId) => {
  const sdk = useSdk();

  return useQuery<Season, APIError>(["seasons", seasonId], () => sdk.getSeason(seasonId, orgId), {
    refetchOnWindowFocus: false,
  });
};

export const useRequiredSeasonId = () => {
  const params = useParams();

  const validated = UUID.parse<SeasonId>(params[UrlParams.SeasonId]);

  if (isError(validated)) {
    throw new StateError(StateErrors.InvalidSeasonId);
  }

  return validated;
};

const currentSeasonKey = ["currentSeason"];

export const useGetCurrentSeason = () => {
  const organization = useOrganization();
  const sdk = useSdk();

  return useQuery<Season | null, APIError>(currentSeasonKey, () =>
    sdk.getCurrentSeason(organization.id),
  );
};
