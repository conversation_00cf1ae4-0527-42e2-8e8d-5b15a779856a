import { useEffect } from "react";
import { useQuery } from "@tanstack/react-query";

import { OrganizationId, APIError, GCSubscription } from "@mio/helpers";

import { useSdk } from "./useSdk";

const gcSubscriptionsKey = ["gc-subscriptions"];

export const useGetGCSubscriptions = (orgId: OrganizationId) => {
  const sdk = useSdk();

  useEffect(() => {
    return () => query.remove();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const query = useQuery<GCSubscription[], APIError>(
    gcSubscriptionsKey,
    () => sdk.findGCSubscriptions(orgId),
    {
      refetchOnMount: false,
      refetchOnReconnect: false,
      refetchOnWindowFocus: false,
    },
  );

  return {
    query,
  };
};
