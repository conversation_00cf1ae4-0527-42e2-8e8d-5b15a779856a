import { useState } from "react";
import { useQuery, useQueryClient, useMutation } from "@tanstack/react-query";
import { useParams } from "react-router-dom";

import { APIError, isError, TeamEvent, UrlParams, UUID } from "@mio/helpers";

import { useSdk } from "./useSdk";
import { useOrganization } from "./useOrganization";
import { useRequiredTeam } from "./useTeam";
import { useActivePlayer } from "./usePlayers";
import { PlayerEventResponseParams } from "../sdk";

const teamEventsKey = ["team-events"];

export const useGetTeamEvents = (initialQuery: TeamEvent.QueryDto) => {
  const organization = useOrganization();
  const team = useRequiredTeam();
  const [queryDto, setQuery] = useState(initialQuery);
  const sdk = useSdk();
  const client = useQueryClient();

  const composedKey = [
    ...teamEventsKey,
    team.id,
    queryDto.startDate?.toDateString(),
    queryDto.endDate?.toDateString(),
    queryDto.agenda,
  ];

  const addEvent = (event: TeamEvent.SingleTeamEvent) => {
    const existingData = client.getQueryData<TeamEvent.TeamEvent[]>(composedKey) || [];

    client.setQueryData<TeamEvent.TeamEvent[]>(composedKey, [...existingData, event]);
  };

  const query = useQuery<TeamEvent.ExtendedTeamEvent[], APIError>(composedKey, () =>
    sdk.getTeamEvents(organization.id, team.id, queryDto),
  );

  return {
    query,
    setQuery,
    addEvent,
  };
};

const teamTrainingEventsKey = ["team-training-events"];

export const useGetTrainingEvents = (params: TeamEvent.QueryDto) => {
  const organization = useOrganization();
  const team = useRequiredTeam();
  const [currentQuery, setQuery] = useState(params);
  const sdk = useSdk();

  const composedKey = [
    ...teamTrainingEventsKey,
    team.id,
    currentQuery?.startDate?.toDateString(),
    currentQuery?.endDate?.toDateString(),
  ];

  const query = useQuery<TeamEvent.ExtendedTeamEvent[], APIError>(composedKey, () =>
    sdk.getTeamEvents(organization.id, team.id, {
      /* we only want training sessions so some parameters are hardcoded */
      agenda: TeamEvent.Agenda.TrainingSession,

      startDate: currentQuery?.startDate,
      endDate: currentQuery?.endDate,
    }),
  );

  return {
    query,
    setQuery,
    searchQuery: currentQuery,
  };
};

export const useCreateTeamEvent = () => {
  const sdk = useSdk();

  return useMutation<TeamEvent.SingleTeamEvent, APIError, TeamEvent.CreateSingleDto>(
    sdk.createTeamEvent,
  );
};

export const useEventId = () => {
  const params = useParams();

  const validated = UUID.parse<TeamEvent.EventId>(params[UrlParams.TeamEventId]);

  return isError(validated) ? undefined : validated;
};

export const useGetPlayerTeamEvents = (initialQuery: TeamEvent.QueryDto) => {
  const [queryDto, setQuery] = useState(initialQuery);
  const sdk = useSdk();
  const player = useActivePlayer();
  const client = useQueryClient();

  const composedKey = [
    ...teamEventsKey,
    ...Object.values({
      ...queryDto,
      startDate: queryDto.startDate?.toDateString(),
      endDate: queryDto.endDate?.toDateString(),
    }),
  ];

  const query = useQuery<TeamEvent.PlayerTeamEvent[], APIError>(composedKey, () =>
    sdk.player.getTeamEvents(player.id, queryDto),
  );

  const respondToEvent = useMutation<
    TeamEvent.PlayerTeamEvent,
    APIError,
    PlayerEventResponseParams
  >(sdk.player.respondToTeamEvent, {
    onSuccess: (updatedEvent) => {
      const currentEvents = client.getQueryData<TeamEvent.PlayerTeamEvent[]>(composedKey);
      const updatedEvents = currentEvents?.map((event) => {
        if (event.id === updatedEvent.id) {
          return updatedEvent;
        }

        return event;
      });

      client.setQueryData<TeamEvent.PlayerTeamEvent[]>(composedKey, updatedEvents);
    },
  });

  return { query, setQuery, activeFilter: queryDto, respondToEvent };
};
