import { useCallback } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { isFunction } from "lodash/fp";

import { AccessTokenDto, APIError, LoginCodeDto, PlayerUser } from "@mio/helpers";
import { useSdk } from "./useSdk";
import { AuthParams, useAuthToken, useLogoutContext } from "./auth.shared";

export const useAuth = (params?: AuthParams) => {
  const sdk = useSdk();
  const client = useQueryClient();
  const { token, setToken, deleteToken } = useAuthToken();
  const { setLogoutMode } = useLogoutContext();

  const loginState = useMutation<AccessTokenDto, APIError, LoginCodeDto>(sdk.player.loginWithCode, {
    onSuccess: (tokenDto) => {
      client.removeQueries();
      setLogoutMode(false);
      setToken(tokenDto.token);
    },
  });

  const registerState = useMutation<void, APIError, PlayerUser.SignInDto>(
    sdk.player.requestLoginCode,
    {
      onSuccess: () => {
        if (isFunction(params?.onRegisterSuccess)) {
          params?.onRegisterSuccess();
        }
      },
    },
  );

  const onLogout = useCallback(() => {
    client.removeQueries();
    setLogoutMode(true);
    deleteToken();
  }, [deleteToken, client, setLogoutMode]);

  return {
    login: loginState,
    isLogged: !!token,
    requestCode: registerState,
    token,
    logout: onLogout,
  };
};
