import { useQuery, useQueryClient } from "@tanstack/react-query";

import { APIError, PlayerUser } from "@mio/helpers";

import { useSdk } from "./useSdk";
import { StateError, StateErrors } from "./errors";

const cacheKey = ["currentPlayerUser"];

export const useCurrentPlayerUser = () => {
  const sdk = useSdk();

  const query = useQuery<PlayerUser.PlayerUser, APIError>(cacheKey, sdk.player.getCurrentUser);

  return query;
};

export const useProvidedCurrentPlayerUser = () => {
  const client = useQueryClient();

  const playerUser = client.getQueryData<PlayerUser.PlayerUser>(cacheKey);

  if (!playerUser) {
    throw new StateError(StateErrors.UserNotFound);
  }

  return playerUser;
};
