import { useQuery } from "@tanstack/react-query";

import {
  OrganizationId,
  APIError,
  TeamId,
  FootballMatchStats,
  StatsCategory,
  FootballMatchAttemptFor,
  FootballMatchAttemptAgainst,
} from "@mio/helpers";

import { useSdk } from "../useSdk";

const cacheKeyPlayersGoals = "football-match-stats-players-goals";
const cacheKeyTeamAttemptsFor = "stats-team-goals-scored";
const cacheKeyTeamAttemptsAgainst = "stats-team-goals-conceded";

export const useGetMatchGoalsStats = (orgId: OrganizationId, teamId: TeamId) => {
  const sdk = useSdk();

  return useQuery<FootballMatchStats.TeamPlayersStats[], APIError>(
    [cacheKeyPlayersGoals, orgId, teamId],
    () => sdk.footballMatchStats.getMatchGoalStats(orgId, teamId, StatsCategory.Goals),
    {
      refetchOnWindowFocus: false,
    },
  );
};

export const useGetMatchAssistsStats = (orgId: OrganizationId, teamId: TeamId) => {
  const sdk = useSdk();

  return useQuery<FootballMatchStats.TeamPlayersStats[], APIError>(
    [cacheKeyPlayersGoals, orgId, teamId],
    () => sdk.footballMatchStats.getMatchGoalStats(orgId, teamId, StatsCategory.Assists),
    {
      refetchOnWindowFocus: false,
    },
  );
};

export const useGetMatchKeyPassesStats = (orgId: OrganizationId, teamId: TeamId) => {
  const sdk = useSdk();

  return useQuery<FootballMatchStats.TeamPlayersStats[], APIError>(
    [cacheKeyPlayersGoals, orgId, teamId],
    () => sdk.footballMatchStats.getMatchGoalStats(orgId, teamId, StatsCategory.KeyPasses),
    {
      refetchOnWindowFocus: false,
    },
  );
};

export const useGetMatchTeamAttemptsForStats = (orgId: OrganizationId, teamId: TeamId) => {
  const sdk = useSdk();

  return useQuery<FootballMatchAttemptFor.AggregatedTeamGoals, APIError>(
    [cacheKeyTeamAttemptsFor, orgId, teamId],
    () => sdk.footballMatchStats.getMatchTeamAttemptsForStats(orgId, teamId),
    {
      refetchOnWindowFocus: false,
    },
  );
};

export const useGetMatchTeamAttemptsAgainstStats = (orgId: OrganizationId, teamId: TeamId) => {
  const sdk = useSdk();

  return useQuery<FootballMatchAttemptAgainst.AggregatedTeamGoals, APIError>(
    [cacheKeyTeamAttemptsAgainst, orgId, teamId],
    () => sdk.footballMatchStats.getMatchTeamAttemptsAgainstStats(orgId, teamId),
    {
      refetchOnWindowFocus: false,
    },
  );
};
