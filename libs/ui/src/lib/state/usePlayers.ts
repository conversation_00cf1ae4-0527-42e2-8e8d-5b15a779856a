import {
  useMutation,
  useQuery,
  useQueryClient,
  useInfiniteQuery,
  UseMutationResult,
} from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";

import {
  AssignTeamDto,
  ChangeStatusDto,
  OrganizationId,
  PlayerQueryDto,
  PlayerSorting,
  PlayersSearchParams,
  PopulatedPlayer,
  TeamId,
  APIError,
  CreatePlayerDto,
  Player,
  PlayerId,
  UUID,
  UrlParams,
  isError,
  GetPlayerDto,
  ApplyToOrganizationDto,
  Assets,
  PlayerTeamStatus,
} from "@mio/helpers";

import { useSdk } from "./useSdk";
import {
  FindOrganizationPlayerDto,
  RemovePlayerFromOrganizationPayload,
  RemovePlayerFromTeamPayload,
  playerAssets,
} from "../sdk";
import { StateError, StateErrors } from "./errors";

export type PlayerQueryParams = { organizationId: OrganizationId; query: PlayerQueryDto };

const currentPlayersKey = ["currentPlayers"] as const;
const globalPlayersKey = ["searched-players"] as const;

export const createTeamPlayersKey = (teamId: TeamId) => ["teams", teamId, "players"];

export const useTeamPlayers = (teamId: TeamId, organizationId: OrganizationId) => {
  const sdk = useSdk();
  const client = useQueryClient();
  const [searchParams, setSearchParams] = useState<PlayersSearchParams | undefined>();

  const queryKey = createTeamPlayersKey(teamId);

  const query = useQuery<PopulatedPlayer[], APIError>(queryKey, () =>
    sdk.getTeamPlayers({
      organizationId,
      teamId,
      query: {
        ...searchParams?.query,
        pagination: {
          limit: 0,
          skip: 0,
        },
        sortBy: searchParams?.query.sortBy || PlayerSorting.CreationDate,
      },
    }),
  );

  const changeStatus = useMutation<PopulatedPlayer, APIError, ChangeStatusDto>(sdk.changeStatus, {
    onSuccess: (updatedPlayer) => {
      if (query.isSuccess) {
        if (
          updatedPlayer.profiles.find((profile) => "teamId" in profile && profile.teamId === teamId)
            ?.status === PlayerTeamStatus.RemovedTeam
        ) {
          client.setQueryData(
            queryKey,
            query.data.filter((player) => player.id !== updatedPlayer.id),
          );
        } else {
          client.setQueryData(
            queryKey,
            query.data.map((player) => (player.id === updatedPlayer.id ? updatedPlayer : player)),
          );
        }
      }
    },
  });

  const removeFromTeam = useMutation<PopulatedPlayer, APIError, RemovePlayerFromTeamPayload>(
    sdk.removeFromTeam,
    {
      onSuccess: (updatedPlayer) => {
        if (query.isSuccess) {
          client.setQueryData(
            queryKey,
            query.data.filter((player) => player.id !== updatedPlayer.id),
          );
        }
      },
    },
  );

  return {
    query,
    removeFromTeam,
    changeStatus,
    search: (params: PlayersSearchParams) => {
      query.remove();
      setSearchParams(params);
    },
  };
};

export const useCreateApplicant = () => {
  const sdk = useSdk();

  return useMutation<undefined, APIError, CreatePlayerDto>(sdk.createApplicant);
};

export const useApplyToOrganization = () => {
  const sdk = useSdk();

  return useMutation<undefined, APIError, ApplyToOrganizationDto>(sdk.applyToOrganization);
};

export const useUpdatePlayerProfile = () => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<Player, APIError, Player>(sdk.player.updatePlayerProfile, {
    onSuccess: () => {
      queryClient.refetchQueries(currentPlayersKey);
    },
  });
};

export const useFindPlayer = (organizationId: OrganizationId) => {
  const sdk = useSdk();

  return useMutation<boolean, APIError, GetPlayerDto>((payload) =>
    sdk.findPlayer(organizationId, payload),
  );
};

export const useFindOrganizationPlayer = (dto: FindOrganizationPlayerDto) => {
  const sdk = useSdk();

  return useQuery<boolean, APIError>(
    ["find-organization-player", dto.organizationId, dto.playerId],
    () => sdk.findOrganizationPlayer(dto),
  );
};

export const useSearchPlayers = (organizationId: OrganizationId, pageSize = 20) => {
  const sdk = useSdk();
  const client = useQueryClient();
  const [searchParams, setSearchParams] = useState<PlayersSearchParams | undefined>();
  const [page, setPage] = useState(0);

  const searchKey = [...globalPlayersKey, searchParams];

  useEffect(() => {
    return () => query.remove();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const query = useInfiniteQuery(
    searchKey,
    () => {
      return sdk.queryPlayers({
        organizationId,
        query: {
          ...searchParams?.query,
          pagination: {
            limit: pageSize,
            skip: page * pageSize,
          },
          sortBy: searchParams?.query.sortBy || PlayerSorting.CreationDate,
        },
      });
    },
    {
      enabled: !!searchParams,
      refetchOnMount: false,
      refetchOnReconnect: false,
      refetchOnWindowFocus: false,
      getNextPageParam: (lastPage) => {
        const hasNextPage = lastPage.total >= page + pageSize;
        return hasNextPage;
      },
      onSuccess: () => {
        setPage((currentPage) => currentPage + 1);
      },
    },
  );

  const assignToTeam = useMutation<PopulatedPlayer, APIError, AssignTeamDto>(sdk.assignTeam, {
    onSuccess: (updatedPlayer) => {
      if (query.isSuccess) {
        client.setQueryData(searchKey, {
          ...query.data,
          pages: query.data.pages.map((page) => {
            return {
              ...page,
              data: page.data.map((player) =>
                player.id === updatedPlayer.id ? updatedPlayer : player,
              ),
            };
          }),
        });
      }
    },
  });

  const removeFromTeam = useMutation<PopulatedPlayer, APIError, RemovePlayerFromTeamPayload>(
    sdk.removeFromTeam,
    {
      onSuccess: (updatedPlayer) => {
        if (query.isSuccess) {
          client.setQueryData(searchKey, {
            ...query.data,
            pages: query.data.pages.map((page) => {
              return {
                ...page,
                data: page.data.map((player) =>
                  player.id === updatedPlayer.id ? updatedPlayer : player,
                ),
              };
            }),
          });
        }
      },
    },
  );

  return {
    query,
    assignToTeam,
    search: (params: PlayersSearchParams) => {
      query.remove();
      setPage(0);
      setSearchParams(params);
    },
    removeFromTeam,
  };
};

export type RemoveFromTeamHandler = UseMutationResult<
  PopulatedPlayer,
  APIError,
  RemovePlayerFromTeamPayload
>;

export type AssignToTeamHandler = UseMutationResult<PopulatedPlayer, APIError, AssignTeamDto>;

export type ChangeStatusHandler = UseMutationResult<PopulatedPlayer, APIError, ChangeStatusDto>;

export const useCurrentPlayers = () => {
  const sdk = useSdk();

  return useQuery<Player[], APIError>(currentPlayersKey, sdk.player.getCurrentPlayers);
};

export const useOptionalPlayerId = () => {
  const params = useParams();

  const validated = UUID.parse<PlayerId>(params[UrlParams.PlayerId]);

  return isError(validated) ? undefined : validated;
};

const usePlayerId = () => {
  const params = useParams();

  const validated = UUID.parse<PlayerId>(params[UrlParams.PlayerId]);

  if (isError(validated)) {
    throw new StateError(StateErrors.InvalidPlayerId);
  }

  return validated;
};

export const useActivePlayer = () => {
  const players = useCurrentPlayers();

  const activePlayerId = usePlayerId();

  const activePlayer = players.data?.find((player) => player.id === activePlayerId);

  if (!activePlayer) {
    throw new StateError(StateErrors.PlayerNotFound);
  }

  return activePlayer;
};

export const useSubmitPlayerDocuments = () => {
  const sdk = useSdk();
  const client = useQueryClient();

  return useMutation<Player, APIError, PlayerId>(sdk.player.submitDocuments, {
    onSuccess: () => {
      client.refetchQueries(currentPlayersKey);
    },
  });
};

export const useAddPlayerPhoto = () => {
  const sdk = useSdk();
  const client = useQueryClient();

  const request = (params: playerAssets.UploadImageParams) =>
    sdk.player.uploadImage(params).then((res) =>
      sdk.player.setPhoto({
        imageId: res.id,
        playerId: params.playerId,
      }),
    );

  return useMutation<Player, APIError, playerAssets.UploadImageParams>(request, {
    onSuccess: () => {
      client.refetchQueries(currentPlayersKey);
    },
  });
};

export const useRemovePlayerPhoto = () => {
  const sdk = useSdk();
  const client = useQueryClient();

  const request = (params: playerAssets.DeleteImageParams) =>
    /* 
    use promise.catch as well so that we can remove the document reference even if 
       for some reason the image doesn't exist anymore 
    */
    sdk.player
      .deleteImage(params)
      .catch(() =>
        sdk.player.removePhoto({
          imageId: params.imageId,
          playerId: params.playerId,
        }),
      )
      .then(() =>
        sdk.player.removePhoto({
          imageId: params.imageId,
          playerId: params.playerId,
        }),
      );

  return useMutation<Player, APIError, playerAssets.DeleteImageParams>(request, {
    onSuccess: () => {
      client.refetchQueries(currentPlayersKey);
    },
  });
};

export const useAddImageDocument = () => {
  const sdk = useSdk();
  const client = useQueryClient();

  const request = (params: playerAssets.UploadImageParams) =>
    sdk.player.uploadImage(params).then((res) =>
      sdk.player.uploadDocument({
        imageId: res.id,
        playerId: params.playerId,
      }),
    );

  return useMutation<Player, APIError, playerAssets.UploadImageParams>(request, {
    onSuccess: (player) => {
      client.refetchQueries(["player-document-images", player.id]);
      client.refetchQueries(currentPlayersKey);
    },
  });
};

export const useRemoveImageDocument = () => {
  const sdk = useSdk();
  const client = useQueryClient();

  const request = (params: playerAssets.DeleteImageParams) =>
    sdk.player.deleteImage(params).then(() =>
      sdk.player.removeDocument({
        imageId: params.imageId,
        playerId: params.playerId,
      }),
    );

  return useMutation<Player, APIError, playerAssets.DeleteImageParams>(request, {
    onSuccess: (player) => {
      client.refetchQueries(["player-document-images", player.id]);
      client.refetchQueries(currentPlayersKey);
    },
  });
};

export const usePlayerDocumentImages = () => {
  const sdk = useSdk();
  const player = useActivePlayer();

  return useQuery<Assets.Image.ImageAsset[], APIError>(["player-document-images", player.id], () =>
    sdk.player.getPlayerDocuments(player.id),
  );
};

export const usePlayerImage = (imageId: Assets.Image.ImageId) => {
  const sdk = useSdk();
  const player = useActivePlayer();

  return useQuery<Assets.Image.ImageAsset, APIError>(["player-image", imageId], () =>
    sdk.player.getImage({
      playerId: player.id,
      imageId,
    }),
  );
};

export const usePlayerImageId = () => {
  const params = useParams();

  const id = params[UrlParams.PlayerImageId];

  const parsedId = Assets.Image.Entity.toImageId(id);

  if (isError(parsedId)) {
    throw new StateError(StateErrors.InvalidPlayerImageId);
  }

  return parsedId;
};

export const useRemovePlayerFromOrganization = () => {
  const sdk = useSdk();
  const client = useQueryClient();

  const changeStatus = useMutation<PlayerId, APIError, RemovePlayerFromOrganizationPayload>(
    sdk.player.removeFromOrganization,
    {
      onSuccess: () => {
        client.refetchQueries(currentPlayersKey);
        client.refetchQueries(globalPlayersKey);
      },
    },
  );

  return changeStatus;
};
