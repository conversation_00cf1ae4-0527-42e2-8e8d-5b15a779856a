import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import {
  OrganizationId,
  APIError,
  EmailTemplate,
  CreateEmailTemplateDto,
  EmailTemplateId,
} from "@mio/helpers";

import { useSdk } from "./useSdk";

const cacheKey = ["email-templates"];

export const useGetEmailTemplates = (orgId: OrganizationId) => {
  const sdk = useSdk();

  return useQuery<EmailTemplate[], APIError>(cacheKey, () => sdk.getEmailTemplates(orgId), {
    refetchOnWindowFocus: false,
  });
};

export const useCreateEmailTemplate = () => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<undefined, APIError, CreateEmailTemplateDto>(
    (dto) => sdk.createEmailTemplate(dto),
    {
      onSuccess: () => {
        queryClient.refetchQueries(cacheKey);
      },
    },
  );
};

export const useUpdateEmailTemplate = () => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<undefined, APIError, EmailTemplate>((dto) => sdk.updateEmailTemplate(dto), {
    onSuccess: () => {
      queryClient.refetchQueries(cacheKey);
    },
  });
};

export const useDeleteEmailTemplate = (organizationId: OrganizationId) => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<undefined, APIError, EmailTemplateId>(
    (templateId) => sdk.deleteEmailTemplate({ organizationId, templateId }),
    {
      onSuccess: () => {
        queryClient.refetchQueries(cacheKey);
      },
    },
  );
};
