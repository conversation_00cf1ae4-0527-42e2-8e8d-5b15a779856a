import { useQuery, useMutation } from "@tanstack/react-query";
import { useParams } from "react-router-dom";

import {
  APIError,
  isError,
  Organization,
  OrganizationId,
  OrganizationSlug,
  PublicOrganization,
  UrlParams,
  UUID,
  UpdateOrganizationDto,
  InviteId,
} from "@mio/helpers";

import { StateError, StateErrors } from "./errors";
import { useProvidedCurrentProfile } from "./useProfile";
import { useSdk } from "./useSdk";
import { usePersistedActiveOrganization } from "../misc";

export const useOrganization = (): PublicOrganization => {
  const profile = useProvidedCurrentProfile();
  const { activeOrganizationId } = usePersistedActiveOrganization();
  const activeOrg = profile.organizations.find((elem) => elem.id === activeOrganizationId);

  const activeOrganization = activeOrg || profile.organizations[0];

  if (!activeOrganization) {
    throw new StateError(StateErrors.OrganizationNotFound);
  }

  return activeOrganization;
};

export const useOrganizationId = () => {
  const params = useParams();

  const validated = UUID.parse<OrganizationId>(params[UrlParams.OrganizationId]);

  return isError(validated) ? undefined : validated;
};

const useOrganizationSlug = () => {
  const params = useParams();

  const validated = Organization.parseSlug(params[UrlParams.OrganizationSlug]);

  return isError(validated) ? undefined : validated;
};

const cacheKey = (slug?: OrganizationSlug) => ["publicOrganizations", slug] as const;

export const usePublicOrganization = () => {
  const sdk = useSdk();
  const slug = useOrganizationSlug();

  const updateOrganization = useMutation<
    undefined,
    APIError,
    UpdateOrganizationDto & { organizationId: OrganizationId }
  >(sdk.updateOrganization);

  const query = useQuery<PublicOrganization | null, APIError>(
    cacheKey(slug),
    () => {
      return slug ? sdk.getPublicOrganization(slug) : Promise.resolve(null);
    },
    {
      refetchOnWindowFocus: false,
      enabled: !!slug,
      retry: false,
    },
  );

  return { query, updateOrganization };
};

export const useJoinOrganization = () => {
  const sdk = useSdk();

  return useMutation<undefined, APIError, InviteId>(sdk.joinOrganization);
};
