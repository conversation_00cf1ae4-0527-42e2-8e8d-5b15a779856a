export enum StateErrors {
  UserNotFound = "UserNotFound",
  ProfileNotFound = "ProfileNotFound",
  OrganizationNotFound = "OrganizationNotFound",
  InvalidTeamId = "InvalidTeamId",
  InvalidTrainingSessionReviewId = "InvalidTrainingSessionReviewId",
  InvalidSeasonId = "InvalidSeasonId",
  TeamNotFound = "TeamNotFound",
  MissingPermissions = "MissingPermissions",
  PlayerNotFound = "PlayerNotFound",
  InvalidPlayerImageId = "InvalidPlayerImageId",
  InvalidPlayerId = "InvalidPlayerId",
}

export class StateError extends Error {
  readonly type = "StateError";

  constructor(public message: StateErrors) {
    super();
  }
}

export const isStateError = (error: unknown): error is StateError => error instanceof StateError;
