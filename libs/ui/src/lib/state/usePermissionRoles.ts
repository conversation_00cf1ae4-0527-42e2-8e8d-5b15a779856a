import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { DeleteRoleParams, UpdateRoleParams } from "../sdk";
import { APIError, OrganizationId, PermissionsModule } from "@mio/helpers";
import { useSdk } from "./useSdk";

type Role = PermissionsModule.Role.Role;

export const useRoles = (orgId: OrganizationId) => {
  const sdk = useSdk();
  const client = useQueryClient();
  const key = [orgId, "permission-roles"];

  const query = useQuery<Role[], APIError>(key, () => sdk.getRoles(orgId), {
    refetchOnWindowFocus: false,
  });

  const addRole = (role: Role) => {
    const existingRoles = client.getQueryData<Role[]>(key) || [];
    client.setQueryData(key, [...existingRoles, role]);
  };

  const updateRole = (role: Role) => {
    const existingRoles = client.getQueryData<Role[]>(key) || [];
    client.setQueryData(
      key,
      existingRoles.map((elem) => (elem.id === role.id ? role : elem)),
    );
  };

  const removeRole = (role: Role) => {
    const existingRoles = client.getQueryData<Role[]>(key) || [];
    client.setQueryData(
      key,
      existingRoles.filter((elem) => elem.id !== role.id),
    );
  };

  return { query, addRole, updateRole, removeRole };
};

export const useCreateRole = () => {
  const sdk = useSdk();

  return useMutation<Role, APIError, PermissionsModule.Role.SaveDto>(sdk.createRole);
};

export const useUpdateRole = () => {
  const sdk = useSdk();

  return useMutation<Role, APIError, UpdateRoleParams>(sdk.updateRole);
};

export const useDeleteRole = () => {
  const sdk = useSdk();

  return useMutation<void, APIError, DeleteRoleParams>(sdk.deleteRole);
};
