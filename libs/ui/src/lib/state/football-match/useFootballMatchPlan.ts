import { useParams } from "react-router-dom";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import {
  OrganizationId,
  APIError,
  FootballMatchPlan,
  TeamId,
  UUID,
  UrlParams,
  isError,
} from "@mio/helpers";

import { useSdk } from "../useSdk";

import { StateError, StateErrors } from "../errors";

const cacheKey = "football-match-plans";
const cacheKeySingle = "football-match-plan";

export const useGetFootballMatchPlans = (orgId: OrganizationId, teamId: TeamId) => {
  const sdk = useSdk();

  return useQuery<FootballMatchPlan.CompletePlan[], APIError>(
    [cacheKey, orgId, teamId],
    () => sdk.footballMatch.getPlans(orgId, teamId),
    {
      refetchOnWindowFocus: false,
    },
  );
};

export const useGetFootballMatchPlan = (
  orgId: OrganizationId,
  teamId: TeamId,
  footballMatchPlanId: FootballMatchPlan.Id,
) => {
  const sdk = useSdk();

  return useQuery<FootballMatchPlan.CompletePlan, APIError>(
    [cacheKeySingle, footballMatchPlanId],
    () => sdk.footballMatch.getPlan(orgId, teamId, footballMatchPlanId),
    {
      refetchOnWindowFocus: false,
    },
  );
};

export const useCreateFootballMatchPlan = (orgId: OrganizationId, teamId: TeamId) => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<FootballMatchPlan.CompletePlan, APIError, FootballMatchPlan.CreateDto>(
    (dto) => sdk.footballMatch.createPlan(dto),
    {
      onSuccess: (result) => {
        queryClient.refetchQueries([cacheKey, orgId, teamId]);
        queryClient.refetchQueries([cacheKeySingle, result?.id]);
      },
    },
  );
};

export const useUpdateFootballMatchPlan = (orgId: OrganizationId, teamId: TeamId) => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<FootballMatchPlan.CompletePlan, APIError, FootballMatchPlan.CompletePlan>(
    (dto) => sdk.footballMatch.updatePlan(dto),
    {
      onSuccess: (result) => {
        queryClient.refetchQueries([cacheKeySingle, result?.id]);
        queryClient.refetchQueries([cacheKey, orgId, teamId]);
      },
    },
  );
};

export const useRequiredFootballMatchPlanId = () => {
  const params = useParams();

  const validated = UUID.parse<FootballMatchPlan.Id>(params[UrlParams.FootballMatchPlanId]);

  if (isError(validated)) {
    throw new StateError(StateErrors.InvalidTeamId);
  }

  return validated;
};
