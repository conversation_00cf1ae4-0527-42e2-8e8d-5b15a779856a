import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import {
  OrganizationId,
  APIError,
  FootballMatchAttemptAgainst,
  TeamId,
  FootballMatchPlan,
} from "@mio/helpers";

import { useSdk } from "../useSdk";

const cacheKey = "football-match-goals-conceded";
const cacheKeySingle = "football-match-goal-conceded";

export const useGetFootballMatchAttemptsAgainst = (
  orgId: OrganizationId,
  teamId: TeamId,
  footballMatchPlanId: FootballMatchPlan.Id,
) => {
  const sdk = useSdk();

  return useQuery<FootballMatchAttemptAgainst.AttemptAgainst[], APIError>(
    [cacheKey, orgId, teamId, footballMatchPlanId],
    () => sdk.footballMatch.getAttemptsAgainst(orgId, teamId, footballMatchPlanId),
    {
      refetchOnWindowFocus: false,
    },
  );
};

export const useGetFootballMatchAttemptsAgainstExtra = (
  orgId: OrganizationId,
  teamId: TeamId,
  footballMatchPlanId: FootballMatchPlan.Id,
) => {
  const sdk = useSdk();

  return useQuery<FootballMatchAttemptAgainst.AttemptAgainst[], APIError>(
    [cacheKey, orgId, teamId, footballMatchPlanId, "extra-attempts-against"],
    () => sdk.footballMatch.getAttemptsAgainstExtra(orgId, teamId, footballMatchPlanId),
    {
      refetchOnWindowFocus: false,
    },
  );
};

export const useGetFootballMatchAttemptAgainst = (
  orgId: OrganizationId,
  teamId: TeamId,
  footballMatchAttemptAgainstId: FootballMatchAttemptAgainst.Id,
) => {
  const sdk = useSdk();

  return useQuery<FootballMatchAttemptAgainst.AttemptAgainst, APIError>(
    [cacheKeySingle, footballMatchAttemptAgainstId],
    () => sdk.footballMatch.getAttemptAgainst(orgId, teamId, footballMatchAttemptAgainstId),
    {
      refetchOnWindowFocus: false,
    },
  );
};

export const useCreateFootballMatchAttemptAgainst = (orgId: OrganizationId, teamId: TeamId) => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<
    FootballMatchAttemptAgainst.AttemptAgainst,
    APIError,
    FootballMatchAttemptAgainst.CreateDto
  >((dto) => sdk.footballMatch.createAttemptAgainst(dto), {
    onSuccess: (result) => {
      queryClient.refetchQueries([cacheKey, orgId, teamId, result?.footballMatchPlanId]);
      queryClient.refetchQueries([cacheKeySingle, result?.id]);
    },
  });
};

export const useUpdateFootballMatchAttemptAgainst = (orgId: OrganizationId, teamId: TeamId) => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<
    FootballMatchAttemptAgainst.AttemptAgainst,
    APIError,
    FootballMatchAttemptAgainst.AttemptAgainst
  >((dto) => sdk.footballMatch.updateAttemptAgainst(dto), {
    onSuccess: (result) => {
      queryClient.refetchQueries([cacheKeySingle, result?.id]);
      queryClient.refetchQueries([cacheKey, orgId, teamId, result?.footballMatchPlanId]);
    },
  });
};

export const useDeleteFootballMatchAttemptAgainst = (orgId: OrganizationId, teamId: TeamId) => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<
    FootballMatchAttemptAgainst.AttemptAgainst,
    APIError,
    FootballMatchAttemptAgainst.AttemptAgainst
  >((attemptFor) => sdk.footballMatch.deleteAttemptAgainst(attemptFor), {
    onSuccess: (result) => {
      console.log("result DELETE", result);
      queryClient.refetchQueries([
        cacheKey,
        orgId,
        teamId,
        result?.footballMatchPlanId,
        "extra-attempts-against",
      ]);

      queryClient.refetchQueries([cacheKeySingle, result?.id]);
    },
  });
};
