import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import {
  OrganizationId,
  APIError,
  FootballMatchAttemptFor,
  TeamId,
  FootballMatchPlan,
} from "@mio/helpers";

import { useSdk } from "../useSdk";

const cacheKey = "football-match-goals-scored";
const cacheKeySingle = "football-match-goal-scored";

export const useGetFootballMatchAttemptsFor = (
  orgId: OrganizationId,
  teamId: TeamId,
  footballMatchPlanId: FootballMatchPlan.Id,
) => {
  const sdk = useSdk();

  return useQuery<FootballMatchAttemptFor.AttemptFor[], APIError>(
    [cacheKey, orgId, teamId, footballMatchPlanId, "goals"],
    () => sdk.footballMatch.getAttemptsFor(orgId, teamId, footballMatchPlanId),
    {
      refetchOnWindowFocus: false,
    },
  );
};

export const useGetFootballMatchAttemptsForExtra = (
  orgId: OrganizationId,
  teamId: TeamId,
  footballMatchPlanId: FootballMatchPlan.Id,
) => {
  const sdk = useSdk();

  return useQuery<FootballMatchAttemptFor.AttemptFor[], APIError>(
    [cacheKey, orgId, teamId, footballMatchPlanId, "extra-attempts"],
    () => sdk.footballMatch.getAttemptsForExtra(orgId, teamId, footballMatchPlanId),
    {
      refetchOnWindowFocus: false,
    },
  );
};

export const useGetFootballMatchAttemptFor = (
  orgId: OrganizationId,
  teamId: TeamId,
  footballMatchAttemptForId: FootballMatchAttemptFor.Id,
) => {
  const sdk = useSdk();

  return useQuery<FootballMatchAttemptFor.AttemptFor, APIError>(
    [cacheKeySingle, footballMatchAttemptForId],
    () => sdk.footballMatch.getAttemptFor(orgId, teamId, footballMatchAttemptForId),
    {
      refetchOnWindowFocus: false,
    },
  );
};

export const useCreateFootballMatchAttemptFor = (orgId: OrganizationId, teamId: TeamId) => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<
    FootballMatchAttemptFor.AttemptFor,
    APIError,
    FootballMatchAttemptFor.CreateDto
  >((dto) => sdk.footballMatch.createAttemptFor(dto), {
    onSuccess: (result) => {
      queryClient.refetchQueries([cacheKey, orgId, teamId, result?.footballMatchPlanId]);
      queryClient.refetchQueries([cacheKeySingle, result?.id]);
    },
  });
};

export const useUpdateFootballMatchAttemptFor = (orgId: OrganizationId, teamId: TeamId) => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<
    FootballMatchAttemptFor.AttemptFor,
    APIError,
    FootballMatchAttemptFor.AttemptFor
  >((dto) => sdk.footballMatch.updateAttemptFor(dto), {
    onSuccess: (result) => {
      queryClient.refetchQueries([cacheKeySingle, result?.id]);
      queryClient.refetchQueries([cacheKey, orgId, teamId, result?.footballMatchPlanId]);
    },
  });
};

export const useDeleteFootballMatchAttemptFor = (orgId: OrganizationId, teamId: TeamId) => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<
    FootballMatchAttemptFor.AttemptFor,
    APIError,
    FootballMatchAttemptFor.AttemptFor
  >((attemptFor) => sdk.footballMatch.deleteAttemptFor(attemptFor), {
    onSuccess: (result) => {
      queryClient.refetchQueries([
        cacheKey,
        orgId,
        teamId,
        result?.footballMatchPlanId,
        "extra-attempts",
      ]);
      queryClient.refetchQueries([cacheKeySingle, result?.id]);
    },
  });
};
