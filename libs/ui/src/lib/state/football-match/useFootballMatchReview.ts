import { useParams } from "react-router-dom";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import {
  OrganizationId,
  APIError,
  FootballMatchReview,
  TeamId,
  UUID,
  UrlParams,
  isError,
  FootballMatchPlan,
} from "@mio/helpers";

import { useSdk } from "../useSdk";

import { StateError, StateErrors } from "../errors";

const cacheKey = "football-match-reviews";
const cacheKeySingle = "football-match-review";

export const useGetFootballMatchReviews = (orgId: OrganizationId, teamId: TeamId) => {
  const sdk = useSdk();

  return useQuery<FootballMatchReview.CompleteReview[], APIError>(
    [cacheKey, orgId, teamId],
    () => sdk.footballMatch.getReviews(orgId, teamId),
    {
      refetchOnWindowFocus: false,
    },
  );
};

export const useGetFootballMatchReview = (
  orgId: OrganizationId,
  teamId: TeamId,
  footballMatchPlanId: FootballMatchPlan.Id,
) => {
  const sdk = useSdk();

  return useQuery<FootballMatchReview.CompleteReview | null, APIError>(
    [cacheKeySingle, footballMatchPlanId],
    () => sdk.footballMatch.getReview(orgId, teamId, footballMatchPlanId),
    {
      refetchOnWindowFocus: false,
    },
  );
};

export const useCreateFootballMatchReview = () => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<FootballMatchReview.CompleteReview, APIError, FootballMatchReview.CreateDto>(
    (dto) => sdk.footballMatch.createReview(dto),
    {
      onSuccess: (data: FootballMatchReview.CompleteReview) => {
        queryClient.refetchQueries([cacheKey, data.organizationId, data.teamId]);
      },
    },
  );
};

export const useUpdateFootballMatchReview = () => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<
    FootballMatchReview.CompleteReview,
    APIError,
    FootballMatchReview.CompleteReview
  >((dto) => sdk.footballMatch.updateReview(dto), {
    onSuccess: (data: FootballMatchReview.CompleteReview) => {
      queryClient.refetchQueries([cacheKeySingle, data.footballMatchPlanId]);
      queryClient.refetchQueries([cacheKey, data.organizationId, data.teamId]);
    },
  });
};

export const useRequiredFootballMatchReviewId = () => {
  const params = useParams();

  const validated = UUID.parse<FootballMatchReview.Id>(params[UrlParams.FootballMatchReviewId]);

  if (isError(validated)) {
    throw new StateError(StateErrors.InvalidTeamId);
  }

  return validated;
};
