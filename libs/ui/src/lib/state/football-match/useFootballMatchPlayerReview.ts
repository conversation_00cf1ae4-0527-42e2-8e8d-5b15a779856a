import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import {
  OrganizationId,
  APIError,
  TeamId,
  FootballMatchPlan,
  FootballMatchPlayerReview,
} from "@mio/helpers";

import { useSdk } from "../useSdk";

const cacheKey = "football-match-player-reviews";
const cacheKeySingle = "football-match-player-review";

export const useGetFootballMatchPlayerReviews = (
  orgId: OrganizationId,
  teamId: TeamId,
  footballMatchPlanId: FootballMatchPlan.Id,
) => {
  const sdk = useSdk();

  return useQuery<FootballMatchPlayerReview.CompletePlayerReview[], APIError>(
    [cacheKey, orgId, teamId, footballMatchPlanId],
    () => sdk.footballMatch.getPlayerReviews(orgId, teamId, footballMatchPlanId),
    {
      refetchOnWindowFocus: false,
    },
  );
};

export const useGetAverageFootballMatchPlayerReviews = (orgId: OrganizationId, teamId: TeamId) => {
  const sdk = useSdk();

  return useQuery<FootballMatchPlayerReview.AverageRatingDto[], APIError>(
    [cacheKey, orgId, teamId],
    () => sdk.footballMatch.getAveragePlayerReviews(orgId, teamId),
    {
      refetchOnWindowFocus: false,
    },
  );
};

export const useUpsertFootballMatchReviews = (
  organizationId: OrganizationId,
  teamId: TeamId,
  footballMatchPlanId: FootballMatchPlan.Id,
) => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<
    FootballMatchPlayerReview.CompletePlayerReview[],
    APIError,
    FootballMatchPlayerReview.CreateDto[]
  >(
    (dto) =>
      sdk.footballMatch.upsertPlayerReviews(dto, organizationId, teamId, footballMatchPlanId),
    {
      onSuccess: () => {
        queryClient.refetchQueries([cacheKey, organizationId, teamId, footballMatchPlanId]);
      },
    },
  );
};

export const useUpdateFootballMatchReviews = (
  orgId: OrganizationId,
  teamId: TeamId,
  footballMatchPlanId: FootballMatchPlan.Id,
) => {
  const sdk = useSdk();
  const queryClient = useQueryClient();

  return useMutation<
    FootballMatchPlayerReview.CompletePlayerReview[],
    APIError,
    FootballMatchPlayerReview.CompletePlayerReview[]
  >((dto) => sdk.footballMatch.updatePlayerReviews(dto, orgId, teamId, footballMatchPlanId), {
    onSuccess: () => {
      queryClient.refetchQueries([cacheKeySingle, orgId, teamId, footballMatchPlanId]);
      queryClient.refetchQueries([cacheKey, orgId, teamId, footballMatchPlanId]);
    },
  });
};
