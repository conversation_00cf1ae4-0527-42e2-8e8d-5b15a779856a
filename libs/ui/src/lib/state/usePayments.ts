import { useMutation, useQuery } from "@tanstack/react-query";
import { APIError, PaymentRequest, PlayerId } from "@mio/helpers";
import { useSdk } from "./useSdk";

const playerPaymentsKey = ["player-payments"] as const;

export const usePlayerPayments = (playerId: PlayerId) => {
  const sdk = useSdk();

  return useQuery<PaymentRequest.Entity[], APIError>([...playerPaymentsKey, playerId], () =>
    sdk.payments.getPlayerPayments(playerId),
  );
};

export const useCreateCheckoutSession = (paymentRequestId: PaymentRequest.Id) => {
  const sdk = useSdk();

  return useMutation(() => sdk.payments.createCheckoutSession(paymentRequestId));
};
