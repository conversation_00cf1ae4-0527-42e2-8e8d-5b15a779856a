import { useForm } from "react-hook-form";
import { useEffect } from "react";

import { CredentialsDto, isError, Primitive, CoachUser, UserTypes } from "@mio/helpers";

export type DataShape = Primitive<Omit<CredentialsDto, "type">>;

export const useLoginForm = (type: UserTypes) => {
  const { handleSubmit, register, formState, setError, watch, getValues } = useForm<DataShape>();

  const hasSubmitted = formState.submitCount > 0;

  const email = watch("email");
  const password = watch("password");

  useEffect(() => {
    const validated = CoachUser.toLoginDto({ email, password }, type);

    if (isError(validated) && hasSubmitted) {
      setError("email", { message: validated.errors["email"] ? "Invalid email" : "" });
      setError("password", { message: validated.errors["password"] ? "Invalid password" : "" });
    } else {
      setError("email", { message: "" });
      setError("password", { message: "" });
    }
  }, [email, password, setError, type, hasSubmitted]);

  return {
    data: getValues(),
    handleSubmit,
    register,
    errors: formState.errors,
    dirtyFields: formState.dirtyFields,
    hasSubmitted: formState.submitCount > 0,
  };
};
