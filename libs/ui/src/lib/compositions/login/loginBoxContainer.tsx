import { FC } from "react";

import { isError, Coach<PERSON>ser, UserTypes, ErrorTypes } from "@mio/helpers";
import { useAuth } from "../../state";
import { LoginBox } from "./loginBox";
import { DataShape } from "./useLoginForm";

type Props = {
  type: UserTypes;
  delayLogin?: number;
  onSuccess?: () => void;
  onPasswordToggle?: (isVisible: boolean) => void;
};

export const LoginBoxContainer: FC<Props> = ({ type, delayLogin, onSuccess, onPasswordToggle }) => {
  const { login } = useAuth({ delayLogin });

  const handleLogin = (data: DataShape) => {
    const validated = CoachUser.toLoginDto(data, type);

    if (isError(validated)) {
      return;
    }

    login.mutate(validated, {
      onSuccess: () => {
        if (onSuccess) {
          onSuccess();
        }
      },
    });
  };

  const defaultErrorMessage = "Something went wrong.";
  const errorMessage =
    login.error?.type === ErrorTypes.UnexpectedError ? defaultErrorMessage : "Invalid credentials";

  return (
    <LoginBox
      type={type}
      onLogin={handleLogin}
      isLoading={login.isLoading}
      serverError={login.error ? errorMessage : undefined}
      onPasswordToggle={onPasswordToggle}
    />
  );
};
