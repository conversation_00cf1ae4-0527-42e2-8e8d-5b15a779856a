import { FC, ReactNode } from "react";

import { Alert, Box } from "../../primitives";
import { CenteredLayout } from "../layouts";

type Props = {
  message?: ReactNode;
};

export const PageError: FC<Props> = ({ message = "Something went wrong" }) => {
  return (
    <CenteredLayout>
      <Alert severity="error" sx={{ mt: 3 }}>
        {message}
      </Alert>
    </CenteredLayout>
  );
};

export const LocalError: FC<Props> = ({ message = "Something went wrong" }) => {
  return (
    <Box>
      <Alert severity="error">{message}</Alert>
    </Box>
  );
};
