import { useState, useRef, useEffect, ReactNode } from "react";

import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import ClickAwayListener from "@mui/material/ClickAwayListener";
import Grow from "@mui/material/Grow";
import Paper from "@mui/material/Paper";
import Popper from "@mui/material/Popper";
import MenuItem from "@mui/material/MenuItem";
import MenuList from "@mui/material/MenuList";
import ButtonGroup from "@mui/material/ButtonGroup";
import Button from "@mui/material/Button";

type Option<T> = {
  label: ReactNode;
  value: T;
};

type Props<T> = {
  options: Option<T>[];
  accessibleLabel: string;
  onChange: (newValue: T) => void;
  onActivate: () => void;
  value: T;
  displayValue?: ReactNode;
};

export function SplitButton<T>({
  options,
  accessibleLabel,
  onChange,
  value,
  displayValue,
  onActivate,
}: Props<T>) {
  const [open, setOpen] = useState(false);
  const anchorRef = useRef<HTMLDivElement>(null);
  const valueIndex = options.findIndex((elem) => elem.value === value) || 0;

  const [selectedIndex, setSelectedIndex] = useState(valueIndex);

  useEffect(() => {
    setSelectedIndex(valueIndex);
  }, [valueIndex]);

  const handleMenuItemClick = (value: T, index: number) => {
    onChange(value);
    setSelectedIndex(index);
    setOpen(false);
  };

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };

  const handleClose = (event: Event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target as HTMLElement)) {
      return;
    }

    setOpen(false);
  };

  return (
    <>
      <ButtonGroup ref={anchorRef} aria-label={accessibleLabel}>
        <Button onClick={onActivate}>{displayValue || options[selectedIndex].label}</Button>
        <Button
          size="small"
          aria-controls={open ? "split-button-menu" : undefined}
          aria-expanded={open ? "true" : undefined}
          aria-label={accessibleLabel}
          aria-haspopup="menu"
          onClick={handleToggle}
        >
          <ArrowDropDownIcon />
        </Button>
      </ButtonGroup>

      <Popper open={open} anchorEl={anchorRef.current} role={undefined} transition>
        {({ TransitionProps, placement }) => (
          <Grow
            {...TransitionProps}
            style={{
              transformOrigin: placement === "bottom" ? "center top" : "center bottom",
            }}
          >
            <Paper sx={{ zIndex: 1 }}>
              <ClickAwayListener onClickAway={handleClose}>
                <MenuList id="split-button-menu" autoFocusItem>
                  {options.map((option, index) => (
                    <MenuItem
                      key={index}
                      disabled={index === 2}
                      selected={index === selectedIndex}
                      onClick={() => handleMenuItemClick(option.value, index)}
                    >
                      {option.label}
                    </MenuItem>
                  ))}
                </MenuList>
              </ClickAwayListener>
            </Paper>
          </Grow>
        )}
      </Popper>
    </>
  );
}
