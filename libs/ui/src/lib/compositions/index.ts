import { <PERSON>ginB<PERSON>, LoginBoxContainer } from "./login";
import { RegisterBox } from "./register/registerBox";
import { RegisterBoxContainer } from "./register/registerBoxContainer";

export const Login = { Box: LoginBox, Container: LoginBoxContainer };

export const Register = { Box: RegisterBox, Container: RegisterBoxContainer };

export * from "./confirmation";
export * from "./loaders";
export * from "./errors";
export * from "./layouts";
export * from "./modal";
export * from "./snackbar";
export * from "./link";
export * from "./password-reset";
export * from "./split-button";
export * from "./file-upload";
export * from "./uploaded-file";
export * from "./field-zones/fieldZones";
