import { isFunction } from "lodash/fp";
import { FC, ReactNode, useEffect, useState } from "react";

import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  DialogContentText,
} from "../../primitives";

type Props = {
  open: boolean;
  onConfirm: () => void;
  title: ReactNode;

  content?: ReactNode;
  confirmButtonText?: ReactNode;
  cancelButtonText?: ReactNode;
  onClose?: () => void;
};

type HookProps = Omit<Props, "open">;

export const useConfirmation = (props: HookProps) => {
  const [isOpen, toggleOpen] = useState(false);

  const open = () => toggleOpen(true);

  return {
    ConfirmationUI: <Confirmation {...props} open={isOpen} onClose={() => toggleOpen(false)} />,
    open,
  };
};

export const Confirmation: FC<Props> = ({
  open,
  onConfirm,
  onClose,
  title,
  content,
  confirmButtonText = "Yes",
  cancelButtonText = "Cancel",
}) => {
  const [isOpen, toggleOpen] = useState(open);

  useEffect(() => {
    toggleOpen(open);
  }, [open]);

  return (
    <Dialog
      open={isOpen}
      fullWidth
      onClose={() => {
        toggleOpen(false);
        if (isFunction(onClose)) {
          onClose();
        }
      }}
      aria-labelledby="confirmation-dialog-title"
      aria-describedby="confirmation-dialog-description"
    >
      <DialogTitle id="confirmation-dialog-title">{title}</DialogTitle>
      <DialogContent>
        <DialogContentText id="confirmation-dialog-description">{content}</DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button
          variant="outlined"
          onClick={() => {
            toggleOpen(false);
            if (isFunction(onClose)) {
              onClose();
            }
          }}
        >
          {cancelButtonText}
        </Button>
        <Button
          variant="outlined"
          onClick={() => {
            toggleOpen(false);
            onConfirm();
          }}
          autoFocus
        >
          {confirmButtonText}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
