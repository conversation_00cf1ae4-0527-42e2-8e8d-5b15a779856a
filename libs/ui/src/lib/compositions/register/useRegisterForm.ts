import { useForm } from "react-hook-form";
import { useEffect, useState } from "react";

import {
  CredentialsDto,
  isError,
  PasswordErrors,
  Primitive,
  ProfileDto,
  Profile,
  CoachUser,
} from "@mio/helpers";

export type DataShape = Primitive<Omit<CredentialsDto, "type" | "email"> & ProfileDto>;

export const useRegistrationForm = () => {
  const { handleSubmit, register, formState, setError, watch, getValues, clearErrors } =
    useForm<DataShape>();
  const [passwordErrors, setPasswordError] = useState<PasswordErrors[]>([]);

  const password = watch("password");
  const firstName = watch("firstName") || undefined;
  const lastName = watch("lastName") || undefined;

  useEffect(() => {
    const validated = Profile.toCreateDto({ firstName, lastName });

    if (isError(validated)) {
      if (validated.errors?.firstName) {
        setError("firstName", { message: validated.errors?.firstName });
      } else {
        clearErrors("firstName");
      }

      if (validated.errors?.lastName) {
        setError("lastName", { message: validated.errors?.lastName });
      } else {
        clearErrors("lastName");
      }
    } else {
      clearErrors("firstName");
      clearErrors("lastName");
    }
  }, [firstName, lastName, setError, clearErrors]);

  useEffect(() => {
    const validated = CoachUser.validatePassword(password);

    if (validated.length) {
      setError("password", { message: "Invalid password" });
      setPasswordError(validated);
    } else {
      clearErrors("password");
      setPasswordError([]);
    }
  }, [password, setError, clearErrors]);

  return {
    data: getValues(),
    handleSubmit,
    register,
    password,
    errors: formState.errors,
    passwordErrors,
    dirtyFields: formState.dirtyFields,
  };
};
