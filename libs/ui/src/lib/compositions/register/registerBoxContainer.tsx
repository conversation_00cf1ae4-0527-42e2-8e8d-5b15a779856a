import { FC } from "react";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, isError } from "@mio/helpers";
import { useAuth } from "../../state";
import { RegisterBox } from "./registerBox";
import { DataShape } from "./useRegisterForm";

type Props = {
  email: Email;
  invite: InviteId;
  onPasswordToggle: (visiblePassword: boolean) => void;
  onSuccess?: () => void;
};

export const RegisterBoxContainer: FC<Props> = ({ email, invite, onSuccess, onPasswordToggle }) => {
  const { register } = useAuth();

  const handleRegister = (data: DataShape) => {
    const validated = CoachUser.toInviteRegistrationDto({
      ...data,
      invite,
    });

    if (isError(validated)) {
      return;
    }

    register.mutate(validated, { onSuccess });
  };

  return (
    <RegisterBox
      email={email}
      onRegister={handleRegister}
      isLoading={register.isLoading}
      serverError={register.error?.message}
      succeeded={register.status === "success"}
      onPasswordToggle={onPasswordToggle}
    />
  );
};
