import { PasswordErrors, PasswordRules } from "@mio/helpers";

export const usePasswordRequirements = (errors: PasswordErrors[]) => {
  const isValidString = !errors.includes(PasswordErrors.NotAString);
  const hasCorrectLength =
    !errors.includes(PasswordErrors.TooShort) && !errors.includes(PasswordErrors.TooLong);
  const hasCorrectDigits = !errors.includes(PasswordErrors.MinDigits);
  const hasCorrectLetters = !errors.includes(PasswordErrors.MinLetters);

  return [
    {
      displayLabel: `Between ${PasswordRules.min} and ${PasswordRules.max} characters`,
      readableLabel: `It must be between ${PasswordRules.min} and ${PasswordRules.max} characters`,
      valid: isValidString && hasCorrectLength,
    },
    {
      displayLabel: `At least ${PasswordRules.digits} number`,
      readableLabel: `It must contain at least ${PasswordRules.digits} number`,
      valid: isValidString && hasCorrectDigits,
    },
    {
      displayLabel: `At least ${PasswordRules.letters} letter`,
      readableLabel: `It must contain at least ${PasswordRules.letters} letter`,
      valid: isValidString && hasCorrectLetters,
    },
  ];
};
