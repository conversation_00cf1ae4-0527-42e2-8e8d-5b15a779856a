import { FC, PropsWithChildren } from "react";

import { Alert, Box, CircularProgress, Stack } from "../../primitives";
import { CenteredLayout } from "../layouts";

type Props = PropsWithChildren<{
  message?: string;
}>;

export const PageLoader: FC<Props> = ({ message = "Loading..." }) => {
  return (
    <CenteredLayout>
      <Box mt={3}>
        <Alert severity="info" sx={{ mb: 2 }}>
          {message}
        </Alert>
        <CircularProgress />
      </Box>
    </CenteredLayout>
  );
};

type LoaderProps = Props & { inline?: boolean };

export const LocalLoader: FC<LoaderProps> = ({ message = "Loading...", inline }) => {
  return (
    <Stack direction={inline ? "row" : "column"} gap={2}>
      <Alert severity="info">{message}</Alert>
      <CircularProgress />
    </Stack>
  );
};
