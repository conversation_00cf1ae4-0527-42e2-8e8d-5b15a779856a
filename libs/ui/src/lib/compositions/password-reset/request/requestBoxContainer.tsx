import { FC } from "react";

import { isError, <PERSON><PERSON>ser, UserTypes } from "@mio/helpers";
import { useInitiatePasswordReset } from "../../../state";
import { RequestBox } from "./requestBox";
import { DataShape } from "./useForm";

type Props = {
  type: UserTypes;
};

export const RequestBoxContainer: FC<Props> = ({ type }) => {
  const startPasswordReset = useInitiatePasswordReset();

  const handleSubmit = (data: DataShape) => {
    const validated = CoachUser.toPasswordResetInitiationDto(data);

    if (isError(validated)) {
      return;
    }

    startPasswordReset.mutate(validated);
  };

  return (
    <RequestBox
      type={type}
      onSubmit={handleSubmit}
      isLoading={startPasswordReset.isLoading}
      success={startPasswordReset.isSuccess}
      serverError={startPasswordReset.isError ? "Something went wrong" : undefined}
    />
  );
};
