import { useForm } from "react-hook-form";
import { useEffect } from "react";

import { isError, PasswordResetInitiationDto, Primitive, CoachUser, UserTypes } from "@mio/helpers";

export type DataShape = Primitive<PasswordResetInitiationDto>;

export const useRequestForm = (type: UserTypes) => {
  const { handleSubmit, register, formState, setError, watch, getValues } = useForm<DataShape>({
    defaultValues: {
      type,
    },
  });

  const hasSubmitted = formState.submitCount > 0;

  const email = watch("email");

  useEffect(() => {
    const validated = CoachUser.toPasswordResetInitiationDto({ email });

    if (isError(validated) && hasSubmitted) {
      setError("email", { message: validated.errors["email"] ? "Invalid email" : "" });
    } else {
      setError("email", { message: "" });
    }
  }, [email, setError, hasSubmitted]);

  return {
    data: getValues(),
    handleSubmit,
    register,
    errors: formState.submitCount > 0 ? formState.errors : {},
  };
};
