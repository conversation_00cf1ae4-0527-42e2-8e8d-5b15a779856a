import { FC } from "react";
import { useSearchParams } from "react-router-dom";

import { isError, CoachUser, UserTypes } from "@mio/helpers";
import { useResetPassword } from "../../../state";
import { UpdateBox } from "./box";
import { DataShape } from "./useForm";
import { Alert } from "../../../primitives";

type Props = {
  type: UserTypes;
  onPasswordToggle: (visiblePassword: boolean) => void;
};

export const BoxContainer: FC<Props> = ({ type, onPasswordToggle }) => {
  const [search] = useSearchParams();
  const resetPassword = useResetPassword();

  const params = CoachUser.toPasswordResetQueryDto({
    code: search.get("code"),
    email: search.get("email"),
    type: search.get("type"),
  });

  if (isError(params)) {
    return (
      <Alert severity="error">
        Invalid or missing url params: {Object.keys(params.errors).join(", ")}
      </Alert>
    );
  }

  const handleSubmit = (data: DataShape) => {
    const validated = CoachUser.toPasswordResetDto(data);

    if (isError(validated)) {
      return;
    }

    resetPassword.mutate(validated);
  };

  return (
    <UpdateBox
      type={type}
      onSubmit={handleSubmit}
      onPasswordToggle={onPasswordToggle}
      isLoading={resetPassword.isLoading}
      success={resetPassword.isSuccess}
      serverError={resetPassword.isError ? "Something went wrong" : undefined}
      code={params.code}
      email={params.email}
    />
  );
};
