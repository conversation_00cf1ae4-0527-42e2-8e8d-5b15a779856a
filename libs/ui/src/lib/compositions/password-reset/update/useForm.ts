import { useForm } from "react-hook-form";

import { Email, PasswordResetActionDto, Primitive, CoachUser, UserTypes } from "@mio/helpers";

export type DataShape = Primitive<PasswordResetActionDto>;

export const useUpdateForm = (type: UserTypes, code: string, email: Email) => {
  const { handleSubmit, register, watch, getValues, formState } = useForm<DataShape>({
    defaultValues: {
      type,
      code,
      email,
    },
  });

  const newPassword = watch("newPassword");

  const passwordErrors = CoachUser.validatePassword(newPassword);

  return {
    data: getValues(),
    handleSubmit,
    register,
    passwordErrors,
    dirtyFields: formState.dirtyFields,
    newPassword,
  };
};
