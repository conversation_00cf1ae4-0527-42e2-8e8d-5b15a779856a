/* eslint-disable */
export default {
  displayName: "ui",
  preset: "../../jest.preset.js",
  transform: {
    "^.+\\.[tj]sx?$": ["babel-jest", { presets: ["@nx/react/babel"] }],
  },
  moduleFileExtensions: ["ts", "tsx", "js", "jsx"],
  coverageDirectory: "../../coverage/libs/ui",
  moduleNameMapper: {
    "\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm)$":
      "<rootDir>/__mocks__/fileMock.js",
  },
};
