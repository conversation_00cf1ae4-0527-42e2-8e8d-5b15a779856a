{"extends": "../../tsconfig.base.json", "compilerOptions": {"jsx": "react-jsx", "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "jsxImportSource": "@emotion/react"}, "files": [], "exclude": ["/**/*.*"], "references": [{"path": "./tsconfig.lib.json"}, {"path": "./tsconfig.spec.json"}]}