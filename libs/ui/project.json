{"name": "ui", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/ui/src", "projectType": "library", "tags": [], "targets": {"type-check": {"executor": "nx:run-commands", "options": {"command": "npx tsc -b ./libs/ui"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/ui"], "options": {"jestConfig": "libs/ui/jest.config.ts"}}}}