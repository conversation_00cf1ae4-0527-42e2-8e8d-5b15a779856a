import { omit } from "lodash/fp";
import { z } from "zod";

import { zodToDomain } from "../../misc";
import { OrganizationId } from "../organization";
import { ProfileId } from "../profile";
import {
  Tagged,
  UUID,
  StringOfLength,
  GenericRecord,
  CustomDate,
  ParsingError,
  PositiveInteger,
} from "../shared";

export type SeasonId = Tagged<"SeasonId", UUID>;

const SeasonBaseParser = z.object({
  organizationId: UUID.parser<OrganizationId>(),
  name: StringOfLength.parser(2, 100),
  startDate: CustomDate.validParser({ stripTime: true }),
  endDate: CustomDate.validParser({ stripTime: true }),
  objective: StringOfLength.parser(2, 200),
});

const SeasonParser = SeasonBaseParser.merge(
  z.object({
    id: UUID.parser<SeasonId>(),
  }),
);

const SeasonDeletedParser = SeasonParser.merge(
  z.object({
    deletedBy: UUID.parser<ProfileId>(),
    deletedAt: CustomDate.validParser(),
  }),
);

const getSeasonByDatesDto = z.object({
  startDate: CustomDate.validParser({ stripTime: true }),
  endDate: CustomDate.validParser({ stripTime: true }),
  organizationId: UUID.parser<OrganizationId>(),
});

export type Season = z.infer<typeof SeasonParser>;

export type SeasonDeleted = z.infer<typeof SeasonDeletedParser>;

export type CreateSeasonDto = z.infer<typeof SeasonBaseParser>;

const updateDtoParser = SeasonBaseParser.merge(SeasonParser.pick({ id: true }));

export type UpdateSeasonDto = z.infer<typeof updateDtoParser>;

export type UpsertSeasonDto = CreateSeasonDto | UpdateSeasonDto;

export type GetSeasonByDatesDto = z.infer<typeof getSeasonByDatesDto>;

export const Season = {
  create: (dto: CreateSeasonDto, createId = () => UUID.generate<SeasonId>()): Season => {
    return {
      id: createId(),
      organizationId: dto.organizationId,
      name: dto.name,
      startDate: dto.startDate,
      endDate: dto.endDate,
      objective: dto.objective,
    };
  },

  createDeleted: (dto: Season, modifier: ProfileId, now = CustomDate.now()): SeasonDeleted => {
    return {
      ...dto,
      deletedBy: modifier,
      deletedAt: now,
    };
  },

  /* used in tests */
  parse: (dto: GenericRecord) => SeasonParser.parse(dto),

  toEntity: (source: GenericRecord) => zodToDomain(SeasonParser, source),

  toEntities: (source: GenericRecord[]) => zodToDomain(z.array(SeasonParser), source),

  toCreateDto: (source: unknown) => zodToDomain(SeasonBaseParser, source),

  toUpdateDto: (source: unknown) => zodToDomain(updateDtoParser, source),

  toUpsertDto: (source: unknown) =>
    zodToDomain(z.union([updateDtoParser, SeasonBaseParser]), source),

  update: (dto: UpdateSeasonDto, season: Season): Season => {
    return {
      ...season,
      ...omit("id", dto),
    };
  },

  updateDto: updateDtoParser,
  createDto: SeasonBaseParser,

  parseId: SeasonParser.shape.id,

  parseGetSeasonByDatesDto: (source: unknown) => {
    const parsed = getSeasonByDatesDto.safeParse(source);

    return parsed.success ? parsed.data : new ParsingError(parsed.error);
  },

  toSeasonId: (source: unknown) => {
    const parsed = UUID.parser<SeasonId>().safeParse(source);

    return parsed.success ? parsed.data : new ParsingError(parsed.error);
  },

  duplicateSeasonExists: (existingSeasons: Season[], newSeason: GetSeasonByDatesDto) =>
    existingSeasons?.some(
      (existingSeason) =>
        existingSeason.startDate >= newSeason.startDate ||
        existingSeason.startDate <= newSeason.endDate ||
        existingSeason.endDate >= newSeason.startDate ||
        existingSeason.endDate <= newSeason.endDate,
    ),

  toDemoInstance: (
    id: SeasonId = UUID.generate<SeasonId>(),
    organizationId: OrganizationId = UUID.generate<OrganizationId>(),
  ): Season => ({
    id,
    organizationId,
    name: "Season 24/25" as Season["name"],
    startDate: CustomDate.now(),
    endDate: CustomDate.addDays(365 as PositiveInteger),
    objective: "Enjoy our football" as Season["objective"],
  }),
};
