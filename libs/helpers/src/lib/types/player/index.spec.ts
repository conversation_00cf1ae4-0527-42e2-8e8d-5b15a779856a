import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, is<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Phone } from "../shared";
import { breakTest } from "../../test";
import { OrganizationId } from "../organization";

import { CreatePlayerDto, Player, PlayerId } from "./index";
import { PlayerGenders } from "../shared/Gender";
import { PlayingExperience } from "../shared/PlayingExperience";
import { EmbeddedGuardian } from "./Guardian";

describe("Profile entity", () => {
  describe(Player.createNewPlayer.name, () => {
    const id = UUID.generate<PlayerId>();
    const orgId = UUID.generate<OrganizationId>();

    const validParentGuardian = {
      firstName: "Guard" as const,
      lastName: "Parent" as const,
      dob: new Date("1960-01-13"),
      email: "<EMAIL>" as Email,
      phone: "07123123456" as Phone,
    } as EmbeddedGuardian;

    const validPayload = {
      organizationId: orgId,
      firstName: "Player" as const,
      lastName: "McPlayerFace" as const,
      dob: new Date("2008-06-06"),
      id,
      email: "<EMAIL>" as <PERSON><PERSON>,
      gender: { name: <PERSON><PERSON><PERSON><PERSON>.M },
      address: {
        postcode: "W10 4BQ",
        address: "Some street 69",
      },
      guardian: validParentGuardian,
      playingExperience: PlayingExperience.Club,
      acceptedTerms: false,
    } as unknown as CreatePlayerDto;

    it("fails when player and parent/guardian email is the same", () => {
      const result = Player.parseCreateDto({
        ...validPayload,
        email: "<EMAIL>" as Email,
        guardian: {
          ...validParentGuardian,
          email: "<EMAIL>" as Email,
        },
      });

      if (!isError(result)) {
        return breakTest(result);
      }

      expect(result).toBeInstanceOf(ParsingError);
      expect(result.errors["email"]).toBeDefined();
    });
  });
});
