import { z } from "zod";

import { UUID } from "../shared";
import { CoachUserId } from "../user";
import { OrganizationId } from "../organization";
import { PlayerId } from "../player";

export enum Type {
  Coach = "coach",
  Player = "player",
  Organization = "organization",
}

/* for the coach's personal storage quota */
const coachOwner = z.object({
  type: z.literal(Type.Coach),
  coachId: UUID.parser<CoachUserId>(),
});

/* for the player's personal storage quota */
const playerOwner = z.object({
  type: z.literal(Type.Player),
  playerId: UUID.parser<PlayerId>(),
});

/* for the organization's storage quota */
const organizationOwner = z.object({
  type: z.literal(Type.Organization),
  organizationId: UUID.parser<OrganizationId>(),
});

export const Ownership = {
  coachOwner,
  playerOwner,
  organizationOwner,
};
