import { z } from "zod";

import { zodToDomain } from "../../misc";
import { CustomDate, CustomEnum, CustomNumber, StringErrors, StringOfLength } from "../shared";

//payments copied from GC developer documentation
// https://developer.gocardless.com/api-reference#payments-list-payments
export enum GCPaymentStatusEnum {
  pending_customer_approval = "pending_customer_approval", //we’re waiting for the customer to approve this payment
  pending_submission = "pending_submission", //the payment has been created, but not yet submitted to the banks
  submitted = "submitted", //the payment has been submitted to the banks
  confirmed = "confirmed", //the payment has been confirmed as collected
  paid_out = "paid_out", //the payment has been included in a payout
  cancelled = "cancelled", //the payment has been cancelled
  customer_approval_denied = "customer_approval_denied", // the customer has denied approval for the payment. You should contact the customer directly
  failed = "failed", //the payment failed to be processed. Note that payments can fail after being confirmed if the failure message is sent late by the banks.
  charged_back = "charged_back", // the payment has been charged back
}

const currencyParser = z.string().min(3, StringErrors.TooShort).max(3, StringErrors.TooLong);

const baseParser = z.object({
  id: z.string(),
  description: StringOfLength.parser(1, 100),

  created_at: z
    .string()
    .nullable()
    .transform((dateInput) => {
      if (dateInput) {
        const parsedDate = CustomDate.toValidDate(dateInput);

        return parsedDate;
      }

      return dateInput;
    }),
  charge_date: z
    .string()
    .nullable()
    .transform((dateInput) => {
      if (dateInput) {
        const parsedDate = CustomDate.toValidDate(dateInput);

        return parsedDate;
      }

      return dateInput;
    }),

  amount: CustomNumber.parsePositiveInteger,
  currency: currencyParser,

  status: CustomEnum.parser(GCPaymentStatusEnum),
});

export type GCPayment = z.infer<typeof baseParser>;

export const GCPayment = {
  toEntity: (source: unknown) => zodToDomain(baseParser, source),

  toEntities: (source: unknown) => zodToDomain(z.array(baseParser), source),
};
