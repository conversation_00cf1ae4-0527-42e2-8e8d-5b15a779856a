import { z } from "zod";

import { zodToDomain } from "../../misc";
import {
  CustomBoolean,
  CustomDate,
  CustomEnum,
  CustomNumber,
  StringErrors,
  StringOfLength,
} from "../shared";

export enum IntervalUnitEnum {
  weekly = "weekly",
  monthly = "monthly",
  yearly = "yearly",
}

export enum GCSubscriptionStatusEnum {
  pending_customer_approval = "pending_customer_approval",
  customer_approval_denied = "customer_approval_denied",
  active = "active",
  finished = "finished",
  cancelled = "cancelled",
  paused = "paused",
}

const currencyParser = z.string().min(3, StringErrors.TooShort).max(3, StringErrors.TooLong);

const baseParser = z.object({
  id: z.string(),
  name: StringOfLength.parser(1, 50),

  day_of_Month: z.number().optional(),
  created_at: z
    .string()
    .nullable()
    .transform((dateInput) => {
      if (dateInput) {
        const parsedDate = CustomDate.toValidDate(dateInput);

        return parsedDate;
      }

      return dateInput;
    }),
  end_date: z
    .string()
    .nullable()
    .transform((dateInput) => {
      if (dateInput) {
        const parsedDate = CustomDate.toValidDate(dateInput);

        return parsedDate;
      }

      return dateInput;
    }),
  start_date: z
    .string()
    .nullable()
    .transform((dateInput) => {
      if (dateInput) {
        const parsedDate = CustomDate.toValidDate(dateInput);

        return parsedDate;
      }

      return dateInput;
    }),

  amount: CustomNumber.parsePositiveInteger,
  currency: currencyParser,
  count: CustomNumber.parsePositiveInteger.nullable(),

  interval: CustomNumber.parsePositiveInteger,
  interval_unit: CustomEnum.parser(IntervalUnitEnum).optional(),
  retry_if_Possible: CustomBoolean.parser.optional(),

  status: CustomEnum.parser(GCSubscriptionStatusEnum),
});

export type GCSubscription = z.infer<typeof baseParser>;

export const GCSubscription = {
  toEntity: (source: unknown) => zodToDomain(baseParser, source),

  toEntities: (source: unknown) => zodToDomain(z.array(baseParser), source),
};
