import { z } from "zod";

import { zodToDomain } from "../../misc";
import { PlayerTeamProfileId } from "../player-team-profile";
import { UUID, GenericRecord, StringOfLength } from "../shared";
import {
  AggregatedTeamGoals,
  AggregatedTeamGoalsParser,
} from "../football-match/footballMatchAttemptFor";

const BaseParser = z.object({
  playerTeamProfileId: UUID.parser<PlayerTeamProfileId>(),
  playerId: UUID.parser<PlayerTeamProfileId>(),
  firstName: StringOfLength.parser(1, 100),
  lastName: StringOfLength.parser(1, 100),
  stat: z.number().int().min(0).max(100),
  appearances: z.number().int().min(0).max(100),
  statPerMatch: z.number().min(0).max(200),
});

export type TeamPlayersStats = z.infer<typeof BaseParser>;

const JointTeamPlayerStatsParser = z.object({
  statsRaw: z.array(BaseParser),
  aggregatedTeamStats: AggregatedTeamGoalsParser,
});

export type JointTeamPlayerStats = z.infer<typeof JointTeamPlayerStatsParser>;

export const Entity = {
  createJointTeamPlayerStats: (
    statsRaw: TeamPlayersStats[],
    aggregatedTeamStats?: AggregatedTeamGoals | null | never[],
  ) => ({
    statsRaw,
    aggregatedTeamStats,
  }),

  parse: (dto: GenericRecord) => BaseParser.parse(dto),

  toPlayersGoalsStatsEntity: (source: GenericRecord) => zodToDomain(BaseParser, source),

  toPlayersGoalsStatsEntities: (source: unknown[]) => zodToDomain(z.array(BaseParser), source),

  toJointTeamPlayerStatsEntity: (source: GenericRecord) =>
    zodToDomain(JointTeamPlayerStatsParser, source),

  createDto: BaseParser,
};
