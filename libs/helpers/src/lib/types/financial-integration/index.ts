import { omit } from "lodash/fp";
import { z } from "zod";

import { zodToDomain } from "../../misc";
import { Organization } from "../organization";
import { CustomEnum, ParsingError, StringErrors, StringOfLength, Tagged, UUID } from "../shared";

export const FinancialIntegrationType = {
  GoCardless: "go-cardless",
  Stripe: "stripe",
} as const;

export enum FinancialIntegrationItemStatus {
  Active = "active",
  Inactive = "inactive",
}

export type FinancialIntegrationType =
  typeof FinancialIntegrationType[keyof typeof FinancialIntegrationType];

export type FinancialIntegrationId = Tagged<"IntegrationId", UUID>;

export type FinancialIntegrationKey = Tagged<"IntegrationKey", string>;

const baseParser = z.object({
  organizationId: Organization.parseId,
  name: StringOfLength.parser(1, 50),
  type: CustomEnum.parser(FinancialIntegrationType),

  key: z
    .string()
    .min(5, StringErrors.TooShort)
    .transform((k) => k as unknown as FinancialIntegrationKey),
});

const ivParser = z.string().min(5, StringErrors.TooShort);
const keyEncryptedParser = z.string().min(16, StringErrors.TooShort);

const fullParser = baseParser
  .merge(
    z.object({
      id: UUID.parser<FinancialIntegrationId>(),
      iv: ivParser,
      keyEncrypted: keyEncryptedParser,
    }),
  )
  .omit({
    key: true,
  });

const publicFullParser = fullParser.omit({ iv: true, keyEncrypted: true });

const updateParser = fullParser.pick({ name: true, id: true });

export type CreateFinancialIntegrationDto = z.infer<typeof baseParser>;

export type UpdateFinancialIntegrationDto = z.infer<typeof updateParser>;

export type FinancialIntegration = z.infer<typeof fullParser>;

export type PublicIntegration = z.infer<typeof publicFullParser>;

/* to grow for each provider we will support */
const externalNavigationSchema = z.object({
  type: z.literal(FinancialIntegrationType.GoCardless),
});

export type ExternalNavigationSchema = z.infer<typeof externalNavigationSchema>;

export const FinancialIntegration = {
  toEntity: (source: unknown) => zodToDomain(fullParser, source),

  toEntities: (source: unknown) => zodToDomain(z.array(fullParser), source),

  toPublicEntities: (source: unknown) => zodToDomain(z.array(publicFullParser), source),

  toPublic: (integration: FinancialIntegration): PublicIntegration => {
    return omit("keyEncrypted", integration);
  },

  create: (
    dto: CreateFinancialIntegrationDto,
    encryptedKey: z.infer<typeof keyEncryptedParser>,
    iv: z.infer<typeof ivParser>,
    createId = () => UUID.generate<FinancialIntegrationId>(),
  ): FinancialIntegration => {
    return {
      id: createId(),
      iv,
      keyEncrypted: encryptedKey,
      ...omit("key", dto),
    };
  },

  toCreateDto: (source: unknown): ParsingError | CreateFinancialIntegrationDto => {
    const result = baseParser.safeParse(source);

    if (result.success) {
      return result.data;
    }

    return new ParsingError(result.error);
  },

  toUpdateDto: (source: unknown): ParsingError | UpdateFinancialIntegrationDto => {
    const result = updateParser.safeParse(source);

    if (result.success) {
      return result.data;
    }

    return new ParsingError(result.error);
  },

  createDtoParser: baseParser,
  updateDtoParser: updateParser,

  update: (
    dto: UpdateFinancialIntegrationDto,
    integration: FinancialIntegration,
  ): FinancialIntegration => ({
    ...integration,
    ...dto,
  }),

  externalNavigationSchema,
  toExternalNavigationSchema: (source: unknown) => {
    const parsed = externalNavigationSchema.safeParse(source);

    return parsed.success ? parsed.data : new ParsingError(parsed.error);
  },
};
