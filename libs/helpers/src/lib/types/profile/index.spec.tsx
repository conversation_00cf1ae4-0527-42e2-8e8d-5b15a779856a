import { ParsingError, UUID, isError } from "../shared";
import { breakTest } from "../../test";
import { Profile, ProfileId } from "./index";
import { CoachUserId } from "../user";

describe("Profile entity", () => {
  describe(Profile.toEntity.name, () => {
    const id = UUID.generate<ProfileId>();
    const user = UUID.generate<CoachUserId>();

    const validPayload = {
      id,
      user,
      firstName: "John",
      lastName: "Doe",
    };

    it("returns a Profile", () => {
      const result = Profile.toEntity(validPayload);

      expect(result).toEqual(validPayload);
    });

    it("fails without firstName or lastName", () => {
      const result = Profile.toEntity({
        ...validPayload,
        firstName: undefined,
        lastName: undefined,
      });

      if (!isError(result)) {
        return breakTest(result);
      }

      expect(result).toBeInstanceOf(ParsingError);
      expect(result.errors["firstName"]).toBeDefined();
      expect(result.errors["lastName"]).toBeDefined();
    });

    it("returns a ParsingError when firstName is too short", () => {
      const result = Profile.toEntity({ ...validPayload, firstName: "" });

      if (!isError(result)) {
        return breakTest(result);
      }

      expect(result).toBeInstanceOf(ParsingError);
      expect(result.errors["firstName"]).toBeDefined();
    });

    it("returns a ParsingError when firstName is not a string", () => {
      const result = Profile.toEntity({ ...validPayload, firstName: 5 });

      if (!isError(result)) {
        return breakTest(result);
      }

      expect(result).toBeInstanceOf(ParsingError);
      expect(result.errors["firstName"]).toBeDefined();
    });

    it("returns a ParsingError when firstName is too long", () => {
      const result = Profile.toEntity({
        ...validPayload,
        firstName: new Array(101).fill("a").join(""),
      });

      if (!isError(result)) {
        return breakTest(result);
      }

      expect(result).toBeInstanceOf(ParsingError);
      expect(result.errors["firstName"]).toBeDefined();
    });

    it("returns a ParsingError when lastName is too short", () => {
      const result = Profile.toEntity({ ...validPayload, lastName: "" });

      if (!isError(result)) {
        return breakTest(result);
      }

      expect(result).toBeInstanceOf(ParsingError);
      expect(result.errors["lastName"]).toBeDefined();
    });

    it("returns a ParsingError when lastName is not a string", () => {
      const result = Profile.toEntity({ ...validPayload, lastName: 5 });

      if (!isError(result)) {
        return breakTest(result);
      }

      expect(result).toBeInstanceOf(ParsingError);
      expect(result.errors["lastName"]).toBeDefined();
    });

    it("returns a ParsingError when lastName is too long", () => {
      const result = Profile.toEntity({
        ...validPayload,
        lastName: new Array(101).fill("a").join(""),
      });

      if (!isError(result)) {
        return breakTest(result);
      }

      expect(result).toBeInstanceOf(ParsingError);
      expect(result.errors["lastName"]).toBeDefined();
    });

    it("returns a ParsingError when CoachUserId is missing", () => {
      const result = Profile.toEntity({
        ...validPayload,
        user: undefined,
      });

      if (!isError(result)) {
        return breakTest(result);
      }

      expect(result).toBeInstanceOf(ParsingError);
      expect(result.errors["user"]).toBeDefined();
    });

    it("returns a ParsingError when CoachUserId is invalid", () => {
      const result = Profile.toEntity({
        ...validPayload,
        user: "not a uuid",
      });

      if (!isError(result)) {
        return breakTest(result);
      }

      expect(result).toBeInstanceOf(ParsingError);
      expect(result.errors["user"]).toBeDefined();
    });
  });

  describe(Profile.create.name, () => {
    const id = UUID.generate<ProfileId>();
    const user = UUID.generate<CoachUserId>();

    const validPayload = Profile.createDto.parse({
      firstName: "John",
      lastName: "Doe",
    });

    it("returns a Profile", () => {
      const result = Profile.create(validPayload, user, () => id);

      expect(result).toEqual({
        ...validPayload,
        id,
        user,
      });
    });
  });
});
