import { z } from "zod";

import { zodToDomain } from "../../misc";
import { CoachUserId } from "../user";
import { CustomArray, isError, ParsingError, StringOfLength, Tagged, UUID } from "../shared";
import { Organization, PublicOrganization } from "../organization";
import { faker } from "@faker-js/faker";

export type ProfileId = Tagged<"ProfileId", UUID>;

export const profileDtoParser = z.object({
  firstName: StringOfLength.parser(1, 100),
  lastName: StringOfLength.parser(1, 100),
});

export type ProfileDto = z.infer<typeof profileDtoParser>;

const profileParser = profileDtoParser.merge(
  z.object({
    id: UUID.parser<ProfileId>(),
    user: UUID.parser<CoachUserId>(),
  }),
);

const publicProfileParser = profileParser.merge(
  z.object({
    organizations: CustomArray.arrayParser<PublicOrganization>(Organization.publicParser),
  }),
);

export type Profile = z.infer<typeof profileParser>;

export type PublicProfile = z.infer<typeof publicProfileParser>;

export const Profile = {
  createDto: profileDtoParser,
  parser: profileParser,
  idParser: profileParser.shape.id,

  toCreateDto: (data: unknown) => {
    const result = profileDtoParser.safeParse(data);

    if (result.success) {
      return result.data;
    }

    return new ParsingError<{ firstName: string; lastName: string }>(result.error);
  },
  toCreateDtoOrThrow: (data: unknown) => {
    const result = Profile.toCreateDto(data);

    if (isError(result)) {
      throw result;
    }

    return result;
  },

  create: (
    dto: ProfileDto,
    user: CoachUserId,
    createId = () => UUID.generate<ProfileId>(),
  ): Profile => {
    return {
      id: createId(),
      user,
      firstName: dto.firstName,
      lastName: dto.lastName,
    };
  },

  toPublic: (profile: Profile, organizations: PublicOrganization[]): PublicProfile => {
    return {
      ...profile,
      organizations,
    };
  },

  toEntity: (source: unknown) => zodToDomain(profileParser, source),

  toEntities: (source: unknown): Profile[] | ParsingError => {
    const parsed = z.array(profileParser).safeParse(source);

    return parsed.success ? parsed.data : new ParsingError(parsed.error);
  },

  toPublicEntity: (source: unknown) => zodToDomain(publicProfileParser, source),

  getFullName: (player: Profile | PublicProfile) => {
    return `${player.firstName} ${player.lastName}`;
  },

  toDemoInstance: (
    user: CoachUserId = UUID.generate<CoachUserId>(),
    id: ProfileId = UUID.generate<ProfileId>(),
    firstName = faker.name.firstName("male"),
    lastName = faker.name.lastName("male"),
  ) =>
    ({
      id,
      user,
      firstName: firstName as StringOfLength<1, 100>,
      lastName: lastName as StringOfLength<1, 100>,
    } as Profile),
};
