import { isError } from "lodash";

import { ParsingError, UUID, CustomDate, CustomNumber } from "../shared";
import { breakTest } from "../../test";
import { Invite, InviteId } from "./index";

describe("Invite entity", () => {
  describe(Invite.toEntity.name, () => {
    const fiveDays = CustomNumber.parsePositiveInteger.parse(5);
    const futureDate = CustomDate.addDays(fiveDays);
    const pastDate = CustomDate.subDays(fiveDays);

    const validPayload = {
      email: "<EMAIL>",
      organization: UUID.generate(),
      invitedBy: "system",
      id: UUID.generate(),
      created: pastDate,
      expires: futureDate,
    };

    it("returns an Invite", () => {
      const result = Invite.toEntity(validPayload);
      expect(result).toEqual(validPayload);
    });

    it("returns a ParsingError when the email is missing", () => {
      const result = Invite.toEntity({ ...validPayload, email: undefined });

      if (!isError(result)) {
        return breakTest(result);
      }

      expect(result).toBeInstanceOf(ParsingError);
      expect(result.errors["email"]).toBeDefined();
    });

    it("returns a ParsingError when the email is invalid", () => {
      const result = Invite.toEntity({ ...validPayload, email: "not an email" });

      if (!isError(result)) {
        return breakTest(result);
      }

      expect(result).toBeInstanceOf(ParsingError);
      expect(result.errors["email"]).toBeDefined();
    });

    it("returns a ParsingError when the id is missing", () => {
      const result = Invite.toEntity({ ...validPayload, id: undefined });

      if (!isError(result)) {
        return breakTest(result);
      }

      expect(result).toBeInstanceOf(ParsingError);
      expect(result.errors["id"]).toBeDefined();
    });

    it("returns a ParsingError when the id is invalid", () => {
      const result = Invite.toEntity({ ...validPayload, id: "not a UUID" });

      if (!isError(result)) {
        return breakTest();
      }

      expect(result).toBeInstanceOf(ParsingError);
      expect(result.errors["id"]).toBeDefined();
    });

    it("returns a ParsingError when the organization is missing", () => {
      const result = Invite.toEntity({ ...validPayload, organization: undefined });

      if (!isError(result)) {
        return breakTest(result);
      }

      expect(result).toBeInstanceOf(ParsingError);
      expect(result.errors["organization"]).toBeDefined();
    });

    it("returns a ParsingError when the organization is invalid", () => {
      const result = Invite.toEntity({ ...validPayload, organization: "not a uuid" });

      if (!isError(result)) {
        return breakTest(result);
      }

      expect(result).toBeInstanceOf(ParsingError);
      expect(result.errors["organization"]).toBeDefined();
    });

    it("returns a ParsingError when the created date is missing", () => {
      const result = Invite.toEntity({ ...validPayload, created: undefined });

      if (!isError(result)) {
        return breakTest(result);
      }

      expect(result).toBeInstanceOf(ParsingError);
      expect(result.errors["created"]).toBeDefined();
    });

    it("returns a ParsingError when the created date is invalid", () => {
      const result = Invite.toEntity({ ...validPayload, created: "not a date" });

      if (!isError(result)) {
        return breakTest(result);
      }

      expect(result).toBeInstanceOf(ParsingError);
      expect(result.errors["created"]).toBeDefined();
    });

    it("returns a ParsingError when the created date is in the future", () => {
      const result = Invite.toEntity({ ...validPayload, created: CustomDate.addDays(fiveDays) });

      if (!isError(result)) {
        return breakTest(result);
      }

      expect(result).toBeInstanceOf(ParsingError);
      expect(result.errors["created"]).toBeDefined();
    });

    it("returns a ParsingError when the expiration date is missing", () => {
      const result = Invite.toEntity({ ...validPayload, expires: undefined });

      if (!isError(result)) {
        return breakTest(result);
      }

      expect(result).toBeInstanceOf(ParsingError);
      expect(result.errors["expires"]).toBeDefined();
    });

    it("returns a ParsingError when the expiration date is invalid", () => {
      const result = Invite.toEntity({ ...validPayload, expires: "not a valid date" });

      if (!isError(result)) {
        return breakTest(result);
      }

      expect(result).toBeInstanceOf(ParsingError);
      expect(result.errors["expires"]).toBeDefined();
    });
  });

  describe(Invite.create.name, () => {
    const fiveDays = CustomNumber.parsePositiveInteger.parse(5);
    const futureDate = CustomDate.addDays(fiveDays);
    const now = CustomDate.now();
    const id = UUID.generate<InviteId>();

    const validPayload = Invite.toCreateDto.parse({
      email: "<EMAIL>",
      organization: UUID.generate(),
      invitedBy: "system",
    });

    it("returns an Invite", () => {
      const result = Invite.create(
        validPayload,
        futureDate,
        undefined,
        () => id,
        () => now,
      );

      expect(result).toEqual({
        ...validPayload,
        id,
        expires: futureDate,
        created: now,
      });
    });
  });
});
