import { z } from "zod";
import { pipe } from "lodash/fp";

import { Organization, OrganizationId } from "../organization";
import { PermissionEntity, Role } from "../permissions";
import {
  Email,
  Tagged,
  UUID,
  CustomDate,
  FutureDate,
  ParsingError,
  PositiveInteger,
} from "../shared";
import { CoachUserId } from "../user";
import { faker } from "@faker-js/faker";

export type InviteId = Tagged<"InviteId", UUID>;

const createInviteDtoParser = z.object({
  email: Email.parser,
  organization: Organization.parseId,
  invitedBy: z.union([z.literal("system"), Email.parser]),
  permissions: z.union([z.literal(PermissionEntity.Type.Owner), Role.Role.idParser]).optional(),
  locale: z.union([z.literal("en"), z.literal("es")]).optional(),
});

export type CreateInviteDto = z.infer<typeof createInviteDtoParser>;

const inviteBase = z.object({
  id: UUID.parser<InviteId>(),
  invitedBy: z.union([z.literal("system"), Email.parser]),
  permissions: z.union([z.literal(PermissionEntity.Type.Owner), Role.Role.idParser]).optional(),
  userId: UUID.parser<CoachUserId>().optional(),
});

const pendingInviteParser = createInviteDtoParser.merge(inviteBase).merge(
  z.object({
    expires: CustomDate.futureParser,
    created: CustomDate.pastOrPresentParser,
  }),
);

const redeemedInviteParser = createInviteDtoParser.merge(inviteBase).merge(
  z.object({
    redeemed: CustomDate.pastOrPresentParser,
    created: CustomDate.pastParser(),
  }),
);

const expiredInviteParser = createInviteDtoParser.merge(inviteBase).merge(
  z.object({
    expires: CustomDate.pastParser(),
    created: CustomDate.pastParser(),
  }),
);

/* the 'passthough()' is important for the parsing, otherwise the 'redeemed' 
  field will never be included in the final result */
const inviteParser = z.union([
  pendingInviteParser.passthrough(),
  redeemedInviteParser.passthrough(),
  expiredInviteParser.passthrough(),
]);

export type PendingInvite = z.infer<typeof pendingInviteParser>;
export type RedeemedInvite = z.infer<typeof redeemedInviteParser>;
export type ExpiredInvite = z.infer<typeof expiredInviteParser>;

export type Invite = z.infer<typeof inviteParser>;

export const Invite = {
  toCreateDto: createInviteDtoParser,
  redeemedParser: redeemedInviteParser,

  parseCreateDto: (source: unknown) => {
    const parsed = createInviteDtoParser.safeParse(source);

    return parsed.success ? parsed.data : new ParsingError(parsed.error);
  },

  create: (
    dto: CreateInviteDto,
    expires: FutureDate,
    userId?: CoachUserId | undefined,
    createId = () => UUID.generate<InviteId>(),
    now = () => CustomDate.now(),
  ): PendingInvite => {
    return {
      id: createId(),
      email: dto.email,
      organization: dto.organization,
      invitedBy: dto.invitedBy,
      permissions: dto.permissions,
      userId,
      expires,
      created: now(),
    };
  },

  redeem(invite: PendingInvite, now = CustomDate.now): RedeemedInvite {
    return {
      ...invite,
      redeemed: now(),
    } as RedeemedInvite;
  },

  extendExpiration(invite: PendingInvite | ExpiredInvite, expires: FutureDate) {
    return {
      ...invite,
      expires,
    };
  },

  toEntity: (source: unknown): Invite | ParsingError => {
    const parsed = inviteParser.safeParse(source);

    if (parsed.success) {
      return parsed.data;
    }
    return new ParsingError(parsed.error);
  },

  toEntities: (source: unknown): Invite[] | ParsingError => {
    const parsed = z.array(inviteParser).safeParse(source);

    if (parsed.success) {
      return parsed.data;
    }

    return new ParsingError(parsed.error);
  },

  isRedeemed: (source: Invite): source is RedeemedInvite => {
    return "redeemed" in source;
  },

  isPending: (source: Invite): source is PendingInvite => {
    return !Invite.isRedeemed(source) && CustomDate.isFutureDate(source.expires);
  },

  isExpired: (source: Invite): source is ExpiredInvite => {
    return !Invite.isRedeemed(source) && !Invite.isPending(source);
  },

  filterNonRedeemed: (source: Invite[]) => {
    return source.filter((invite) => !Invite.isRedeemed(invite)) as
      | PendingInvite[]
      | ExpiredInvite[];
  },

  toRedeemedDemoInstance: (dto?: CreateInviteDto): RedeemedInvite => {
    const params: CreateInviteDto = dto || {
      email: Email.parser.parse(`${faker.name.firstName()}@team-assist.co.uk`),
      organization: UUID.generate<OrganizationId>(),
      invitedBy: "system",
    };

    return pipe(Invite.create, Invite.redeem)(params, CustomDate.addDays(1 as PositiveInteger));
  },

  toPendingDemoInstance: (dto?: CreateInviteDto): PendingInvite => {
    const params: CreateInviteDto = dto || {
      email: Email.parser.parse(`${faker.name.firstName()}@team-assist.co.uk`),
      organization: UUID.generate<OrganizationId>(),
      invitedBy: "system",
    };

    return Invite.create(params, CustomDate.addDays(1 as PositiveInteger));
  },

  toExpiredDemoInstance: (dto?: CreateInviteDto): ExpiredInvite => {
    const params: CreateInviteDto = dto || {
      email: Email.parser.parse(`${faker.name.firstName()}@team-assist.co.uk`),
      organization: UUID.generate<OrganizationId>(),
      invitedBy: "system",
    };

    return {
      id: UUID.generate<InviteId>(),
      email: params.email,
      organization: params.organization,
      invitedBy: params.invitedBy,
      expires: CustomDate.subDays(1 as PositiveInteger),
      created: CustomDate.subDays(2 as PositiveInteger),
    };
  },
};
