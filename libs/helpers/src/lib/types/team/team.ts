import { omit } from "lodash/fp";
import { z } from "zod";

import { zodToDomain } from "../../misc";
import { OrganizationId } from "../organization";
import { Player, PlayerId } from "../player";
import { ProfileId } from "../profile";
import {
  Tagged,
  UUID,
  StringOfLength,
  GenericRecord,
  CustomDate,
  ParsingError,
  BELOW_RANGE,
  ABOVE_RANGE,
  PositiveInteger,
  CustomEnum,
  CustomNumber,
} from "../shared";
import { PriceErrors } from "../stripe/price";
import { FinancialIntegrationItemStatus } from "../financial-integration";
import { StripeEntities } from "..";
import { CurrencyLowerCase } from "../stripe";

export const WITHIN_RANGE_MSG = "The player is within the team age range";
export const YOUNGER_MSG = "The player is younger than the team age range";
export const OLDER_MSG = "The player is older than the team age range";

export type TeamId = Tagged<"TeamId", UUID>;

export const TeamGenders = {
  M: "male",
  F: "female",
  Mixed: "mixed",
} as const;

export enum FeeInterval {
  Monthly = "month",
  Yearly = "year",
  Daily = "day",
  Weekly = "week",
}

export type TeamGenders = typeof TeamGenders[keyof typeof TeamGenders];

const TeamBaseParser = z.object({
  organizationId: UUID.parser<OrganizationId>(),
  name: StringOfLength.parser(2, 100),
  ageDescription: StringOfLength.parser(2, 100),
  slogan: StringOfLength.parser(2, 200).optional().nullable(),
  playersBornAfter: CustomDate.pastParser(),
  playersBornBefore: CustomDate.validParser(),
  gender: z.nativeEnum(TeamGenders),
  fee: CustomNumber.positiveNumberParser.optional(),
  feeInterval: CustomEnum.parser(FeeInterval).optional(),
  currency: CustomEnum.parser(CurrencyLowerCase).optional(),
  financialIntegrationStatus: CustomEnum.parser(FinancialIntegrationItemStatus).optional(),
  stripeProductId: z.string().optional(),
  stripePriceId: z.string().optional(),
});

export const TeamParser = TeamBaseParser.merge(
  z.object({
    id: UUID.parser<TeamId>(),
  }),
);

const createParser = TeamBaseParser.refine(
  (data) => {
    if (
      data.financialIntegrationStatus === FinancialIntegrationItemStatus.Active &&
      (!data.fee || !data.feeInterval)
    ) {
      return false;
    }

    return true;
  },
  {
    path: ["fee", "feeInterval"],
    message: PriceErrors.Required,
  },
);

const TeamDeletedParser = TeamParser.merge(
  z.object({
    deletedBy: UUID.parser<ProfileId>(),
    deletedAt: CustomDate.validParser(),
    players: z.array(UUID.parser<PlayerId>()),
  }),
);

export type Team = z.infer<typeof TeamParser>;

export type TeamDeleted = z.infer<typeof TeamDeletedParser>;

export type CreateTeamDto = z.infer<typeof createParser>;

const updateDtoParser = TeamBaseParser.merge(TeamParser.pick({ id: true }));

export type UpdateTeamDto = z.infer<typeof updateDtoParser>;

export type UpsertTeamDto = CreateTeamDto | UpdateTeamDto;

export const Team = {
  create: (dto: CreateTeamDto, createId = () => UUID.generate<TeamId>()): Team => {
    return {
      id: createId(),
      organizationId: dto.organizationId,
      name: dto.name,
      ageDescription: dto.ageDescription,
      playersBornAfter: dto.playersBornAfter,
      playersBornBefore: dto.playersBornBefore,
      gender: dto.gender,
      slogan: dto.slogan,
      fee: dto.fee,
      feeInterval: dto.feeInterval,
      currency: dto.currency,
      financialIntegrationStatus: dto.financialIntegrationStatus,
    };
  },

  createDeleted: (
    dto: Team,
    players: PlayerId[],
    modifier: ProfileId,
    now = CustomDate.now(),
  ): TeamDeleted => {
    return {
      ...dto,
      deletedBy: modifier,
      deletedAt: now,
      players,
    };
  },

  /* used in tests */
  parse: (dto: GenericRecord) => TeamParser.parse(dto),

  toEntity: (source: GenericRecord) => zodToDomain(TeamParser, source),

  toEntities: (source: GenericRecord[]) => zodToDomain(z.array(TeamParser), source),

  toCreateDto: (source: unknown) => zodToDomain(TeamBaseParser, source),

  toUpdateDto: (source: unknown) => zodToDomain(updateDtoParser, source),

  toUpsertDto: (source: unknown) => zodToDomain(z.union([updateDtoParser, createParser]), source),

  update: (dto: UpdateTeamDto, team: Team): Team => {
    return {
      ...team,
      ...omit("id", dto),
    };
  },

  updateDto: updateDtoParser,
  createDto: createParser,

  parseId: TeamParser.shape.id,
  parser: TeamParser,

  toTeamId: (source: unknown) => {
    const parsed = UUID.parser<TeamId>().safeParse(source);

    return parsed.success ? parsed.data : new ParsingError(parsed.error);
  },

  getPlayerAgeCompatibilityLabel: (team: Team, player: Player) => {
    const playerWithinAgeRange = CustomDate.isWithinRange(
      team.playersBornAfter,
      team.playersBornBefore,
      player.dob,
    );

    if (playerWithinAgeRange === BELOW_RANGE) {
      return OLDER_MSG;
    }

    if (playerWithinAgeRange === ABOVE_RANGE) {
      return YOUNGER_MSG;
    }

    return WITHIN_RANGE_MSG;
  },

  isIntegratedWithFinancialSystem: (team: Team) =>
    team.financialIntegrationStatus === FinancialIntegrationItemStatus.Active &&
    team.fee &&
    team.fee > 0,

  toDemoInstance: (
    dto?: CreateTeamDto,
    id: TeamId = UUID.generate<TeamId>(),
    organizationId: OrganizationId = UUID.generate<OrganizationId>(),
  ) => {
    const age = Math.floor(Math.random() * (50 - 5 + 1)) + 5;

    return {
      id,
      organizationId: dto?.organizationId || organizationId,
      ageDescription: dto?.ageDescription || "Under 16s",
      gender: TeamGenders.M,
      name: dto?.name || `Under ${age}s`,
      playersBornAfter: CustomDate.subYears(age as PositiveInteger),
      playersBornBefore: CustomDate.subYears((age + 1) as PositiveInteger),
      fee: StripeEntities.DefaultMonthlyFee,
      feeInterval: FeeInterval.Monthly,
    } as Team;
  },
};
