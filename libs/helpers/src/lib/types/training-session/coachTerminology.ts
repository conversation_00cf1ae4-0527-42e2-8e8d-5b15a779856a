export enum PhaseOfPlay {
  InPossession = "in-possession",
  OutOfPossession = "out-of-possession",
  PositiveTransition = "positive-transition",
  NegativeTransition = "negative-transition",
  SetPieces = "set-pieces",
}

// This is a foundational concept adopted by some Football Associations and a paramout one for the English football
// https://learn.englandfootball.com/articles/resources/2022/the-fa-4-corner-model
export enum FourCornerModel {
  Social = "social",
  Physical = "physical",
  Psychological = "psychological",
  TechnicalOrTactical = "technical-or-tactical",
}

export enum TypeOfPractice {
  TechnicalPractice = "technical-practice",
  Function = "function",
  SmallSidedGame = "small-sided-game",
}

export enum PitchThirds {
  Attacking = "attacking",
  Midfield = "midfield",
  Defensive = "defensive",
}

// valid for both of Attacking and Defending Principles of play
// https://learn.englandfootball.com/articles/resources/2023/How-to-work-on-the-principles-of-football
export enum AttackingPrinciplesOfPlay {
  Penetration = "penetration",
  Creativity = "creativity",
  Movement = "movement",
  SupportPlay = "support-play",
  CreateSpace = "create-space",
}

export enum DefendingPrinciplesOfPlay {
  Delay = "delay",
  Cover = "cover",
  Compactness = "compactness",
  Balance = "balance",
  Patience = "patience",
  Press = "press",
}

export enum PracticeSpectrum {
  Unopposed = "unopposed",
  UnopposedWithInterference = "unopposed-with-interference",
  Overload = "overload",
  MatchUp = "match-up",
}

// this article explains the different type of attributes and their meaning
// there is a "related" section with links to each group
// https://www.footballmanagerblog.org/2018/04/technical-attributes-football-manager-guide.html
export enum PlayerAttributes {
  // Technical
  Dribbling = "dribbling",
  Technique = "technique",
  Shooting = "shooting",
  Crossing = "crossing",
  Finishing = "finishing",

  Heading = "heading",
  Passing = "passing",
  FirstTouch = "first-touch",

  Tackling = "tackling",
  Marking = "marking",
  Cover = "cover",
  DefensiveBalance = "defensive-balance",
  RecoveryRuns = "recovery-runs",
  Delaying = "delaying",

  ThrowIns = "throw-ins",
  PenaltyTaking = "penalty-taking",
  FreeKickTaking = "free-kick-taking",
  CornerTaking = "corner-taking",

  // Mental
  Aggression = "aggression",
  Anticipation = "anticipation",
  Bravery = "bravery",
  Composure = "composure",
  Concentration = "concentration",
  Decisions = "decisions",
  Determination = "determination",
  Flair = "flair",
  Leadership = "leadership",
  OffTheBall = "off-the-ball",
  Positioning = "positioning",
  TeamWork = "team-work",
  Vision = "vision",
  WorkRate = "work-rate",

  Scanning = "scanning",
  Movement = "movement",
  Deception = "deception",
  Timing = "timing",

  // Fitness
  Acceleration = "acceleration",
  Agility = "agility",
  Balance = "balance",
  JumpingReach = "jumping-reach",
  NaturalFitness = "natural-fitness",
  Pace = "pace",
  Stamina = "stamina",
  Strength = "strength",

  // Hidden
  Temperament = "temperament",
  Adaptability = "adaptability",
  Controversy = "controversy",
  Consistency = "consistency",
  ImportantMatches = "important-matches",
  Ambition = "ambition",
  InjuryProneness = "injury-proneness",
  Loyalty = "loyalty",
  Dirtiness = "dirtiness",
  Pressure = "pressure",
  Professionalism = "professionalism",
  Sportsmanship = "sportsmanship",
  Versatility = "versatility",

  // Goalkeeper
  AerialReach = "aerial-reach",
  CommandOfArea = "command-of-area",
  Communication = "communication",
  Eccentricity = "eccentricity",
  Handling = "handling",
  Kicking = "kicking",
  OneOnOnes = "one-on-ones",
  TendencyToPunch = "tendency-to-punch",
  Reflexes = "reflexes",
  TendencyToRushOut = "tendency-to-rush-out",
  Throwing = "throwing",
}

// TODO: Extract this, as it's not longer specific to training sessions
export enum Attendance {
  Attended = "attended",
  Late = "late",
  MissingWithReason = "missing-with-reason",
  MissingNoReason = "missing-no-reason",
}

export enum PlayerPerformanceBasicRatings {
  Abysmal = "abysmal",
  Bad = "bad",
  Neutral = "neutral",
  Good = "good",
  Outstanding = "outstanding",
}
