import { omit } from "lodash/fp";
import { z } from "zod";

import { zodToDomain } from "../../misc";
import { OrganizationId } from "../organization";
import { ProfileId } from "../profile";
import { Tagged, UUID, GenericRecord, CustomDate, CustomEnum } from "../shared";
import { TeamId } from "../team";
import { Attendance, PlayerAttributes, PlayerPerformanceBasicRatings } from "./coachTerminology";
import { ReviewId } from "./trainingSessionReview";
import { PlayerId, Player } from "../player";

export type PlayerReviewId = Tagged<"TrainingSessionPlayerReviewId", UUID>;

const baseParser = z.object({
  organizationId: UUID.parser<OrganizationId>(),
  teamId: UUID.parser<TeamId>(),
  playerId: UUID.parser<PlayerId>(),

  trainingSessionReviewId: UUID.parser<ReviewId>(),

  rating: CustomEnum.parser(PlayerPerformanceBasicRatings),
  attendance: CustomEnum.parser(Attendance),
  highlights: z.array(CustomEnum.parser(PlayerAttributes)),
});

const parser = baseParser.merge(
  z.object({
    id: UUID.parser<PlayerReviewId>(),
  }),
);

const deletedParser = parser.merge(
  z.object({
    deletedBy: UUID.parser<ProfileId>(),
    deletedAt: CustomDate.validParser(),
  }),
);

const extendedParser = parser.merge(
  z.object({
    player: Player.leanParser,
  }),
);

export type ExtendedReview = z.infer<typeof extendedParser>;

export type PlayerReview = z.infer<typeof parser>;

export type PlayerReviewDeleted = z.infer<typeof deletedParser>;

export type CreateDto = z.infer<typeof baseParser>;

const updateDtoParser = baseParser.merge(parser.pick({ id: true }));

export type UpdateDto = z.infer<typeof updateDtoParser>;

export type UpsertDto = CreateDto | UpdateDto;

const queryDtoParser = z.object({
  startDate: CustomDate.validParser({ stripTime: true }).optional(),
  endDate: CustomDate.validParser({ stripTime: true }).optional(),
});

export type QueryDto = z.infer<typeof queryDtoParser>;

export const Entity = {
  create: (dto: CreateDto, createId = () => UUID.generate<PlayerReviewId>()): PlayerReview => {
    return {
      id: createId(),
      organizationId: dto.organizationId,
      teamId: dto.teamId,
      trainingSessionReviewId: dto.trainingSessionReviewId,
      playerId: dto.playerId,

      rating: dto.rating,

      attendance: dto.attendance,
      highlights: dto.highlights,
    };
  },

  createDeleted: (
    dto: PlayerReview,
    modifier: ProfileId,
    now = CustomDate.now(),
  ): PlayerReviewDeleted => {
    return {
      ...dto,
      deletedBy: modifier,
      deletedAt: now,
    };
  },

  parse: (dto: GenericRecord) => parser.parse(dto),

  toEntity: (source: GenericRecord) => zodToDomain(parser, source),

  toEntities: (source: GenericRecord[]) => zodToDomain(z.array(parser), source),

  toCreateDto: (source: unknown) => zodToDomain(baseParser, source),

  toUpdateDto: (source: unknown) => zodToDomain(updateDtoParser, source),

  toUpsertDto: (source: unknown) => zodToDomain(z.union([updateDtoParser, baseParser]), source),

  update: (dto: UpdateDto, trainingSessionReview: PlayerReview): PlayerReview => {
    return {
      ...trainingSessionReview,
      ...omit("id", dto),
    };
  },

  updateDto: updateDtoParser,
  createDto: baseParser,

  parseId: parser.shape.id,
  parser,
  extendedParser,
  queryDtoParser,

  toQueryDto: (source: unknown) => zodToDomain(queryDtoParser, source),

  toExtendedEntities: (source: unknown) => zodToDomain(z.array(extendedParser), source),

  toPlayerReviewId: (source: unknown) => zodToDomain(UUID.parser<PlayerReviewId>(), source),

  toTestInstance: (): PlayerReview =>
    ({
      id: UUID.generate<PlayerReviewId>(),
      organizationId: UUID.generate<OrganizationId>(),
      teamId: UUID.generate<TeamId>(),
      trainingSessionReviewId: UUID.generate<ReviewId>(),
      playerId: UUID.generate<PlayerId>(),

      rating: PlayerPerformanceBasicRatings.Good,

      attendance: Attendance.Attended,
      highlights: [PlayerAttributes.Acceleration, PlayerAttributes.Adaptability],
    } as PlayerReview),
};
