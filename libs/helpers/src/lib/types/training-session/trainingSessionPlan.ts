import { omit } from "lodash/fp";
import { z } from "zod";

import { zodToDomain } from "../../misc";
import { OrganizationId } from "../organization";
import { ProfileId } from "../profile";
import {
  Tagged,
  UUID,
  StringOfLength,
  GenericRecord,
  CustomDate,
  CustomNumber,
  CustomEnum,
} from "../shared";
import { TeamId } from "../team";
import { FourCornerModel, PhaseOfPlay } from "./coachTerminology";
import { SeasonPlanBlockId, SeasonPlanThemeId } from "./seasonPlan";
import { Entity as TrainingSessionPractice } from "./trainingSessionPractice";

export type PlanId = Tagged<"TrainingSessionPlanId", UUID>;

const PlanBaseParser = z.object({
  organizationId: UUID.parser<OrganizationId>(),
  teamId: UUID.parser<TeamId>(),

  name: StringOfLength.parser(2, 100),
  //pre-populated in the UI from Event with overwrite option
  location: StringOfLength.parser(2, 500),
  sessionNumber: CustomNumber.parsePositiveInteger,
  //pre-populated in the UI from Event with overwrite option
  startDateTime: CustomDate.validParser(),
  //pre-populated in the UI from Event with overwrite option
  endDateTime: CustomDate.validParser(),
  //pre-populated in the UI from Event with overwrite option
  expectedAttendees: CustomNumber.parsePositiveInteger,

  seasonPlanBlockId: UUID.parser<SeasonPlanBlockId>().optional(),
  seasonPlanThemeId: UUID.parser<SeasonPlanThemeId>().optional(),

  topic: StringOfLength.parser(2, 200),
  keyReviewFactors: StringOfLength.parser(2, 500),
  phaseOfPlay: CustomEnum.parser(PhaseOfPlay),

  fourCornerPlayerConsiderations: z.array(
    z.object({
      corner: CustomEnum.parser(FourCornerModel),
      consideration: StringOfLength.parser(2, 500),
    }),
  ),

  learningOutcomes: StringOfLength.parser(2, 200),
  playersEngagement: StringOfLength.parser(2, 1000),
  coachBehaviours: StringOfLength.parser(2, 1000),
  safetyConsiderations: StringOfLength.parser(2, 1000),
  arrivalAndWarmUp: StringOfLength.parser(2, 1000),
});

const PlanParser = PlanBaseParser.merge(
  z.object({
    id: UUID.parser<PlanId>(),
  }),
);

const PlanDeletedParser = PlanParser.merge(
  z.object({
    deletedBy: UUID.parser<ProfileId>(),
    deletedAt: CustomDate.validParser(),
  }),
);

const fullSession = PlanParser.merge(
  z.object({
    practices: z.array(TrainingSessionPractice.fullParser),
  }),
);

export type CompletePlan = z.infer<typeof fullSession>;

export type Plan = z.infer<typeof PlanParser>;

export type PlanDeleted = z.infer<typeof PlanDeletedParser>;

export type CreateDto = z.infer<typeof PlanBaseParser>;

const updateDtoParser = PlanBaseParser.merge(PlanParser.pick({ id: true }));

export type UpdateDto = z.infer<typeof updateDtoParser>;

export type UpsertDto = CreateDto | UpdateDto;

export const Entity = {
  create: (dto: CreateDto, createId = () => UUID.generate<PlanId>()): Plan => {
    return {
      id: createId(),
      organizationId: dto.organizationId,
      teamId: dto.teamId,
      name: dto.name,
      startDateTime: dto.startDateTime,
      endDateTime: dto.endDateTime,

      arrivalAndWarmUp: dto.arrivalAndWarmUp,
      topic: dto.topic,
      coachBehaviours: dto.coachBehaviours,
      learningOutcomes: dto.learningOutcomes,
      playersEngagement: dto.playersEngagement,
      safetyConsiderations: dto.safetyConsiderations,

      expectedAttendees: dto.expectedAttendees,
      fourCornerPlayerConsiderations: dto.fourCornerPlayerConsiderations,
      keyReviewFactors: dto.keyReviewFactors,
      location: dto.location,
      phaseOfPlay: dto.phaseOfPlay,
      sessionNumber: dto.sessionNumber,
      seasonPlanBlockId: dto.seasonPlanBlockId,
      seasonPlanThemeId: dto.seasonPlanThemeId,
    };
  },

  createDeleted: (dto: Plan, modifier: ProfileId, now = CustomDate.now()): PlanDeleted => {
    return {
      ...dto,
      deletedBy: modifier,
      deletedAt: now,
    };
  },

  parse: (dto: GenericRecord) => PlanParser.parse(dto),

  toEntity: (source: GenericRecord) => zodToDomain(PlanParser, source),

  toEntities: (source: GenericRecord[]) => zodToDomain(z.array(PlanParser), source),

  toCreateDto: (source: unknown) => zodToDomain(PlanBaseParser, source),

  toUpdateDto: (source: unknown) => zodToDomain(updateDtoParser, source),

  toUpsertDto: (source: unknown) => zodToDomain(z.union([updateDtoParser, PlanBaseParser]), source),

  update: (dto: UpdateDto, trainingSessionPlan: Plan): Plan => {
    return {
      ...trainingSessionPlan,
      ...omit("id", dto),
    };
  },

  updateDto: updateDtoParser,
  createDto: PlanBaseParser,

  parseId: PlanParser.shape.id,

  toPlanId: (source: unknown) => zodToDomain(UUID.parser<PlanId>(), source),

  toTestInstance: (): Plan =>
    ({
      id: UUID.generate<PlanId>(),
      organizationId: UUID.generate<OrganizationId>(),
      teamId: UUID.generate<TeamId>(),

      name: "Wednesday training session" as StringOfLength<2, 100>,
      startDateTime: CustomDate.now(),
      endDateTime: CustomDate.now(),

      arrivalAndWarmUp: "Rondos and handball" as StringOfLength<2, 1000>,
      topic: "Penetration" as StringOfLength<2, 200>,
      coachBehaviours: "Calm and approachable" as StringOfLength<2, 1000>,
      learningOutcomes: "Players scoring more goals" as StringOfLength<2, 200>,
      playersEngagement: "Fun and running" as StringOfLength<2, 1000>,
      safetyConsiderations: "Make sure the playing area is clean" as StringOfLength<2, 1000>,
      expectedAttendees: CustomNumber.toPositiveInteger(15),
      fourCornerPlayerConsiderations: [
        {
          corner: FourCornerModel.Psychological,
          consideration: "Awareness of surroundings and other players." as StringOfLength<2, 500>,
        },
      ],
      keyReviewFactors:
        "In possession play is improving with each week, and the overall play is much more fluid and effective. Players are making better decisions, and working more cohesively as part of the build up. Aim to improve this ahead of a big game on Sunday" as StringOfLength<
          2,
          500
        >,
      location: "Cantelowes Gardens" as StringOfLength<2, 500>,
      phaseOfPlay: PhaseOfPlay.InPossession,
      sessionNumber: CustomNumber.toPositiveInteger(22),
      seasonPlanBlockId: UUID.generate<SeasonPlanBlockId>(),
      seasonPlanBloThemeId: UUID.generate<SeasonPlanThemeId>(),
    } as Plan),
};
