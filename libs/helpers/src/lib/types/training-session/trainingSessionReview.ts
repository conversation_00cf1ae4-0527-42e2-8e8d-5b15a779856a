import { omit } from "lodash/fp";
import { z } from "zod";

import { zodToDomain } from "../../misc";
import { OrganizationId } from "../organization";
import { ProfileId } from "../profile";
import {
  Tagged,
  UUID,
  StringOfLength,
  GenericRecord,
  CustomDate,
  CustomEnum,
  CustomNumber,
} from "../shared";
import { TeamId } from "../team";
import { PlayerPerformanceBasicRatings } from "./coachTerminology";
import { PlanId } from "./trainingSessionPlan";
import { TeamEvent } from "../team-event";
import { extendedParser as teamEventExtendedParser } from "../team-event/teamEvent";
import * as TrainingSessionPlayerReview from "./trainingSessionPlayerReview";
import { LeanPlayer, Player } from "../player";

export type ReviewId = Tagged<"TrainingSessionReviewId", UUID>;

const baseParser = z.object({
  organizationId: UUID.parser<OrganizationId>(),
  teamId: UUID.parser<TeamId>(),
  trainingSessionPlanId: UUID.parser<PlanId>(),
  teamEventId: UUID.parser<TeamEvent.EventId>(),

  learningOutcomes: StringOfLength.parser(2, 200),
  playersEngagement: StringOfLength.parser(2, 1000),
  coachBehaviours: StringOfLength.parser(2, 1000),
  safetyConsiderations: StringOfLength.parser(2, 1000),
  arrivalAndWarmUp: StringOfLength.parser(2, 1000),

  summary: StringOfLength.parser(2, 500).optional(),
  details: StringOfLength.parser(2, 1000).optional(),

  rating: CustomEnum.parser(PlayerPerformanceBasicRatings),
  expectationsAchievedRating: CustomNumber.integerInRangeParser(1, 10),
});

const parser = baseParser.merge(
  z.object({
    id: UUID.parser<ReviewId>(),
  }),
);

const parserWithPlayerReviews = parser.merge(
  z.object({
    playerReviews: z.array(TrainingSessionPlayerReview.Entity.parser),
  }),
);

export type EntityWithPlayerReviews = z.infer<typeof parserWithPlayerReviews>;

const extendedParser = parserWithPlayerReviews.merge(
  z.object({
    event: teamEventExtendedParser,
    players: z.array(Player.leanParser),
  }),
);

export type ExtendedReview = z.infer<typeof extendedParser>;

const deletedParser = parser.merge(
  z.object({
    deletedBy: UUID.parser<ProfileId>(),
    deletedAt: CustomDate.validParser(),
  }),
);

export type Review = z.infer<typeof parser>;

export type ReviewDeleted = z.infer<typeof deletedParser>;

export type CreateDto = z.infer<typeof baseParser>;

const updateDtoParser = baseParser.merge(parser.pick({ id: true }));

export type UpdateDto = z.infer<typeof updateDtoParser>;

export type UpsertDto = CreateDto | UpdateDto;

export const Entity = {
  create: (dto: CreateDto, createId = () => UUID.generate<ReviewId>()): Review => {
    return {
      id: createId(),
      organizationId: dto.organizationId,
      teamId: dto.teamId,
      trainingSessionPlanId: dto.trainingSessionPlanId,
      teamEventId: dto.teamEventId,

      arrivalAndWarmUp: dto.arrivalAndWarmUp,
      coachBehaviours: dto.coachBehaviours,
      learningOutcomes: dto.learningOutcomes,
      playersEngagement: dto.playersEngagement,
      safetyConsiderations: dto.safetyConsiderations,

      rating: dto.rating,
      expectationsAchievedRating: dto.expectationsAchievedRating,
    };
  },

  createDeleted: (dto: Review, modifier: ProfileId, now = CustomDate.now()): ReviewDeleted => {
    return {
      ...dto,
      deletedBy: modifier,
      deletedAt: now,
    };
  },

  parse: (dto: GenericRecord) => parser.parse(dto),

  toEntity: (source: GenericRecord) => zodToDomain(parser, source),

  toEntities: (source: GenericRecord[]) => zodToDomain(z.array(parser), source),

  toCreateDto: (source: unknown) => zodToDomain(baseParser, source),

  toUpdateDto: (source: unknown) => zodToDomain(updateDtoParser, source),

  toUpsertDto: (source: unknown) => zodToDomain(z.union([updateDtoParser, baseParser]), source),

  update: (dto: UpdateDto, review: Review): Review => {
    return {
      ...review,
      ...omit("id", dto),
    };
  },

  updateDto: updateDtoParser,
  createDto: baseParser,
  parser,
  extendedParser,
  toExtendedEntity: (source: unknown) => zodToDomain(extendedParser, source),

  parseId: parser.shape.id,

  toReviewId: (source: unknown) => zodToDomain(UUID.parser<ReviewId>(), source),

  toEntityWithPlayerReviews: (source: unknown) => zodToDomain(parserWithPlayerReviews, source),

  addEvent: (
    source: EntityWithPlayerReviews,
    event: TeamEvent.ExtendedTeamEvent,
    players: LeanPlayer[],
  ): ExtendedReview => {
    return {
      ...source,
      event,
      players,
    };
  },

  addPlayerReview: (
    review: ExtendedReview,
    playerReview: TrainingSessionPlayerReview.PlayerReview,
  ): ExtendedReview => ({ ...review, playerReviews: [...review.playerReviews, playerReview] }),

  editPlayerReview: (
    review: ExtendedReview,
    playerReview: TrainingSessionPlayerReview.PlayerReview,
  ): ExtendedReview => ({
    ...review,
    playerReviews: review.playerReviews.map((elem) =>
      elem.id === playerReview.id ? playerReview : elem,
    ),
  }),

  toTestInstance: (id = UUID.generate<ReviewId>()): Review => ({
    id: id,
    organizationId: UUID.generate<OrganizationId>(),
    teamId: UUID.generate<TeamId>(),
    trainingSessionPlanId: UUID.generate<PlanId>(),
    teamEventId: UUID.generate<TeamEvent.EventId>(),

    arrivalAndWarmUp: "Rondos and handball" as Review["arrivalAndWarmUp"],
    coachBehaviours:
      "Project positivity and promote safe environment where players can make mistakes" as Review["coachBehaviours"],
    learningOutcomes: "Be confident on the ball" as Review["learningOutcomes"],
    playersEngagement: "High ball rolling time. They must have fun." as Review["playersEngagement"],
    safetyConsiderations: "All equipment off the playing area" as Review["safetyConsiderations"],

    rating: "good" as Review["rating"],
    expectationsAchievedRating: 8 as Review["expectationsAchievedRating"],
  }),
};
