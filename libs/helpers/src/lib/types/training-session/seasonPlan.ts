import { omit } from "lodash/fp";
import { z } from "zod";

import { zodToDomain } from "../../misc";
import { OrganizationId } from "../organization";
import { ProfileId } from "../profile";
import { SeasonId } from "../season";
import {
  Tagged,
  UUID,
  StringOfLength,
  GenericRecord,
  CustomDate,
  CustomNumber,
  PositiveInteger,
} from "../shared";
import { TeamId } from "../team";
import { StripeEntities } from "..";

export type SeasonPlanId = Tagged<"SeasonPlanId", UUID>;
export type SeasonPlanBlockId = Tagged<"SeasonPlanBlockId", UUID>;
export type SeasonPlanThemeId = Tagged<"SeasonPlanThemeId", UUID>;

const SeasonPlanThemeParser = z.object({
  id: UUID.parser<SeasonPlanThemeId>(),
  name: StringOfLength.parser(2, 100),
  description: StringOfLength.parser(2, 200).optional(),
  weeks: z.array(CustomNumber.parsePositiveInteger),
});

const SeasonPlanBlockParser = z.object({
  id: UUID.parser<SeasonPlanBlockId>(),
  name: StringOfLength.parser(2, 100),
  description: StringOfLength.parser(2, 200).optional(),
  themes: z.array(SeasonPlanThemeParser),
  weeks: z.array(CustomNumber.parsePositiveInteger),
});

const SeasonPlanBaseParser = z.object({
  organizationId: UUID.parser<OrganizationId>(),
  teamId: UUID.parser<TeamId>(),
  seasonId: UUID.parser<SeasonId>(),

  name: StringOfLength.parser(2, 100),
  description: StringOfLength.parser(2, 200).optional(),

  startDate: CustomDate.validParser(),
  endDate: CustomDate.validParser(),
  totalWeeks: CustomNumber.parsePositiveInteger,

  blocks: z.array(SeasonPlanBlockParser),
});

const SeasonPlanParser = SeasonPlanBaseParser.merge(
  z.object({
    id: UUID.parser<SeasonPlanId>(),
  }),
);

const SeasonPlanDeletedParser = SeasonPlanParser.merge(
  z.object({
    deletedBy: UUID.parser<ProfileId>(),
    deletedAt: CustomDate.validParser(),
  }),
);

export type SeasonPlan = z.infer<typeof SeasonPlanParser>;

export type SeasonPlanDeleted = z.infer<typeof SeasonPlanDeletedParser>;

export type CreateDto = z.infer<typeof SeasonPlanBaseParser>;

const updateDtoParser = SeasonPlanBaseParser.merge(SeasonPlanParser.pick({ id: true }));

export type UpdateDto = z.infer<typeof updateDtoParser>;

export type UpsertSeasonPlanDto = CreateDto | UpdateDto;

export const Entity = {
  create: (dto: CreateDto, createId = () => UUID.generate<SeasonPlanId>()): SeasonPlan => {
    return {
      id: createId(),
      organizationId: dto.organizationId,
      teamId: dto.teamId,
      seasonId: dto.seasonId,

      name: dto.name,
      startDate: dto.startDate,
      endDate: dto.endDate,
      blocks: dto.blocks,
      totalWeeks: dto.totalWeeks,
      description: dto.description,
    };
  },

  createDeleted: (
    dto: SeasonPlan,
    modifier: ProfileId,
    now = CustomDate.now(),
  ): SeasonPlanDeleted => {
    return {
      ...dto,
      deletedBy: modifier,
      deletedAt: now,
    };
  },

  parse: (dto: GenericRecord) => SeasonPlanParser.parse(dto),

  toEntity: (source: GenericRecord) => zodToDomain(SeasonPlanParser, source),

  toEntities: (source: GenericRecord[]) => zodToDomain(z.array(SeasonPlanParser), source),

  toCreateDto: (source: unknown) => zodToDomain(SeasonPlanBaseParser, source),

  toUpdateDto: (source: unknown) => zodToDomain(updateDtoParser, source),

  toUpsertDto: (source: unknown) =>
    zodToDomain(z.union([updateDtoParser, SeasonPlanBaseParser]), source),

  update: (dto: UpdateDto, SeasonPlan: SeasonPlan): SeasonPlan => {
    return {
      ...SeasonPlan,
      ...omit("id", dto),
    };
  },

  updateDto: updateDtoParser,
  createDto: SeasonPlanBaseParser,

  parseId: SeasonPlanParser.shape.id,

  toSeasonPlanId: (source: unknown) => zodToDomain(UUID.parser<SeasonPlanId>(), source),

  duplicateSeasonPlanExists: (existingSeasonPlans: SeasonPlan[], newSeason: SeasonPlan) =>
    existingSeasonPlans?.some(
      (existingSeason) =>
        existingSeason.startDate >= newSeason.startDate ||
        existingSeason.startDate <= newSeason.endDate ||
        existingSeason.endDate >= newSeason.startDate ||
        existingSeason.endDate <= newSeason.endDate,
    ),

  toTestInstance: (): SeasonPlan =>
    ({
      id: UUID.generate<SeasonPlanId>(),
      organizationId: UUID.generate<OrganizationId>(),
      teamId: UUID.generate<TeamId>(),
      seasonId: UUID.generate<SeasonId>(),

      name: "Wednesday training session" as StringOfLength<2, 100>,
      startDate: CustomDate.now(),
      endDate: CustomDate.now(),

      blocks: [
        {
          id: UUID.generate<SeasonPlanBlockId>(),
          name: "Out of possession" as StringOfLength<2, 100>,
          weeks: [1, 2, 3],
          themes: [
            {
              id: UUID.generate<SeasonPlanThemeId>(),
              name: "Cover and balance" as StringOfLength<2, 100>,
              weeks: [1, 2],
            },
          ],
        },
      ],
      totalWeeks: StripeEntities.DefaultMonthlyFee as PositiveInteger,
    } as SeasonPlan),
};
