import { omit } from "lodash/fp";
import { z } from "zod";

import { zodToDomain } from "../../misc";
import { OrganizationId } from "../organization";
import { ProfileId } from "../profile";
import {
  Tagged,
  UUID,
  StringOfLength,
  GenericRecord,
  CustomDate,
  CustomNumber,
  CustomEnum,
} from "../shared";
import { TeamId } from "../team";
import {
  AttackingPrinciplesOfPlay,
  DefendingPrinciplesOfPlay,
  PhaseOfPlay,
  PracticeSpectrum,
  TypeOfPractice,
} from "./coachTerminology";
import { PlanId } from "./trainingSessionPlan";

export type PracticeId = Tagged<"TrainingSessionPracticeId", UUID>;

const PracticeBaseParser = z.object({
  organizationId: UUID.parser<OrganizationId>(),
  teamId: UUID.parser<TeamId>(),
  trainingSessionPlanId: UUID.parser<PlanId>(),

  name: StringOfLength.parser(2, 100),
  numberOfPlayers: CustomNumber.parsePositiveInteger,
  //TODO: revise as this could be an Enum of PitchThirds or custom string but for now left as string only
  areaOnPitch: StringOfLength.parser(2, 20),

  topic: StringOfLength.parser(2, 200),
  type: CustomEnum.parser(TypeOfPractice),
  practiceSpectrum: CustomEnum.parser(PracticeSpectrum),
  attackingPrinciplesOfPlay: z.array(CustomEnum.parser(AttackingPrinciplesOfPlay)).optional(),
  defendingPrinciplesOfPlay: z.array(CustomEnum.parser(DefendingPrinciplesOfPlay)).optional(),

  phaseOfPlay: CustomEnum.parser(PhaseOfPlay),
  duration: CustomNumber.integerInRangeParser(1, 300),

  // TODO: This should be an uploadable image saved as a URL
  diagram: StringOfLength.parser(1, 2).optional(),
  // this is the description of the diagram in words and could be quite lengthy so no point of being "StringOfLength"
  description: z.string(),
  progressions: z
    .array(
      z.object({
        progressionDescription: z.string(),
        // TODO: This should be an uploadable image saved as a URL
        progressionDiagram: StringOfLength.parser(1, 2).optional(),
      }),
    )
    .optional(),
  coachingPoints: z.array(
    z.object({
      point: StringOfLength.parser(2, 100),
      details: StringOfLength.parser(2, 200),
    }),
  ),

  specificPlayers: StringOfLength.parser(2, 200).optional(),
});

const PracticeParser = PracticeBaseParser.merge(
  z.object({
    id: UUID.parser<PracticeId>(),
  }),
);

const PracticeDeletedParser = PracticeParser.merge(
  z.object({
    deletedBy: UUID.parser<ProfileId>(),
    deletedAt: CustomDate.validParser(),
  }),
);

export type Practice = z.infer<typeof PracticeParser>;

export type PracticeDeleted = z.infer<typeof PracticeDeletedParser>;

export type CreateDto = z.infer<typeof PracticeBaseParser>;

const updateDtoParser = PracticeBaseParser.merge(PracticeParser.pick({ id: true }));

export type UpdateDto = z.infer<typeof updateDtoParser>;

export type UpsertDto = CreateDto | UpdateDto;

export const Entity = {
  fullParser: PracticeParser,

  create: (dto: CreateDto, createId = () => UUID.generate<PracticeId>()): Practice => {
    return {
      id: createId(),
      organizationId: dto.organizationId,
      teamId: dto.teamId,
      trainingSessionPlanId: dto.trainingSessionPlanId,
      name: dto.name,

      topic: dto.topic,
      areaOnPitch: dto.areaOnPitch,
      coachingPoints: dto.coachingPoints,
      description: dto.description,
      diagram: dto.diagram,
      duration: dto.duration,
      numberOfPlayers: dto.numberOfPlayers,
      practiceSpectrum: dto.practiceSpectrum,
      attackingPrinciplesOfPlay: dto.attackingPrinciplesOfPlay,
      defendingPrinciplesOfPlay: dto.defendingPrinciplesOfPlay,

      type: dto.type,
      progressions: dto.progressions,
      specificPlayers: dto.specificPlayers,

      phaseOfPlay: dto.phaseOfPlay,
    };
  },

  createDeleted: (dto: Practice, modifier: ProfileId, now = CustomDate.now()): PracticeDeleted => {
    return {
      ...dto,
      deletedBy: modifier,
      deletedAt: now,
    };
  },

  parse: (dto: GenericRecord) => PracticeParser.parse(dto),

  toEntity: (source: GenericRecord) => zodToDomain(PracticeParser, source),

  toEntities: (source: GenericRecord[]) => zodToDomain(z.array(PracticeParser), source),

  toCreateDto: (source: unknown) => zodToDomain(PracticeBaseParser, source),

  toUpdateDto: (source: unknown) => zodToDomain(updateDtoParser, source),

  toUpsertDto: (source: unknown) =>
    zodToDomain(z.union([updateDtoParser, PracticeBaseParser]), source),

  update: (dto: UpdateDto, trainingSessionPractice: Practice): Practice => {
    return {
      ...trainingSessionPractice,
      ...omit("id", dto),
    };
  },

  updateDto: updateDtoParser,
  createDto: PracticeBaseParser,

  parseId: PracticeParser.shape.id,

  toPracticeId: (source: unknown) => zodToDomain(UUID.parser<PracticeId>(), source),

  toTestInstance: (): Practice =>
    ({
      id: UUID.generate<PracticeId>(),
      organizationId: UUID.generate<OrganizationId>(),
      teamId: UUID.generate<TeamId>(),
      trainingSessionPlanId: UUID.generate<PlanId>(),

      name: "Wednesday training session" as StringOfLength<2, 100>,
      areaOnPitch: "Attacking third",
      coachingPoints: [
        {
          point: "Can players scan multiple times during play?",
          details:
            "Players need to scan both before and after receiving the ball in order to be able to find and execute the right pass.",
        },
      ],
      description:
        "Player A passes to Player B. Player B then turns and tries to play a through ball behind the opposition's defence...",
      diagram: "www.team-assiste.com/uploaded-images-of-diagrams",
      duration: 20,
      numberOfPlayers: 12,
      practiceSpectrum: PracticeSpectrum.Overload,
      type: TypeOfPractice.Function,
      progressions: [
        {
          progressionDescription:
            "Player A can NOW dribbles past the opposition player OR still pass to player B",
        },
      ],
      specificPlayers: "defenders + numbers 6 and 8",

      topic: "Penetration" as StringOfLength<2, 200>,
      phaseOfPlay: PhaseOfPlay.InPossession,
      attackingPrinciplesOfPlay: [
        AttackingPrinciplesOfPlay.CreateSpace,
        AttackingPrinciplesOfPlay.Movement,
      ],
    } as Practice),
};
