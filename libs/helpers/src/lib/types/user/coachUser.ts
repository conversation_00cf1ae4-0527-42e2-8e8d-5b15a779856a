import { z } from "zod";
import { omit } from "lodash/fp";
import { faker } from "@faker-js/faker";

import {
  Email,
  Password,
  UUID,
  Tagged,
  HashedPassword,
  ParsingError,
  GenericRecord,
  CustomDate,
  CustomEnum,
  CustomNumber,
  EmailErrors,
  PasswordErrors,
  CustomEnumErrors,
  DomainError,
  ErrorMessages,
} from "../shared";
import { zodToDomain } from "../../misc";
import { Profile, profileDtoParser } from "../profile";
import { Invite, InviteId, RedeemedInvite } from "../invite";
import { PermissionEntity } from "../permissions";
import {
  AccessTokenParser,
  AuthTypes,
  LoginCode,
  loginCodeDtoParser,
  loginCodeParser,
  UserTypes,
} from "./shared";
import { Permission } from "../permissions/permission";

/* if we simply use UUID, a function like doSomething(id: UserId) {} will also
accept any other UUID like a CompanyId and so on which is unsafe. Hence the
additional Tagged<> functionality to ensure uniqueness */
export type CoachUserId = Tagged<"CoachUserId", UUID>;

const passwordResetParser = z.object({
  code: z.string(),
  expiration: CustomDate.validParser(),
});

const oneTimePasswordAuthParser = z.object({
  code: loginCodeParser,
  expiresAt: CustomDate.validParser(),
});

const credentialsAuthParser = z.object({
  type: z.literal(AuthTypes.Credentials),
  email: Email.parser,
  password: HashedPassword.parser,
  passwordReset: passwordResetParser.optional(),

  oneTimeCode: oneTimePasswordAuthParser.optional(),
});

const publicCredentialsAuthParser = credentialsAuthParser.omit({
  password: true,
  passwordReset: true,
});

const withId = z.object({
  id: UUID.parser<CoachUserId>(),
});

const coachParser = withId.merge(
  z.object({
    type: z.literal(UserTypes.Coach),
    authentication: credentialsAuthParser,
  }),
);

const publicCoachParser = coachParser.omit({ authentication: true }).merge(
  z.object({
    authentication: publicCredentialsAuthParser,
  }),
);

const passwordResetInitiationParser = z.object({
  email: Email.parser,
  type: CustomEnum.parser(UserTypes),
});

export type PasswordResetInitiationDto = z.infer<typeof passwordResetInitiationParser>;

const passwordResetActionParser = z.object({
  code: z.string(),
  email: Email.parser,
  type: z.nativeEnum(UserTypes),
  newPassword: Password.parser,
});

const passwordResetQueryDto = passwordResetActionParser.pick({
  code: true,
  email: true,
  type: true,
});

type PasswordResetQueryDto = z.infer<typeof passwordResetQueryDto>;

export type PasswordResetActionDto = z.infer<typeof passwordResetActionParser>;

const credentialsParser = z.object({
  type: z.literal(UserTypes.Coach),
  email: Email.parser,
  password: Password.parser,
});

const credentialsParserWithBasicPassword = credentialsParser.omit({ password: true }).merge(
  z.object({
    password: z.string(),
  }),
);

export type CredentialsDto = z.infer<typeof credentialsParser>;

const registerWithInviteDto = credentialsParser
  .pick({ password: true })
  .merge(profileDtoParser)
  .merge(
    z.object({
      invite: UUID.parser<InviteId>(),
    }),
  );

export type InviteRegistrationDto = z.infer<typeof registerWithInviteDto>;

const userParser = coachParser;
const publicCoachUserParser = publicCoachParser.merge(
  Profile.parser.pick({ firstName: true, lastName: true }),
);

export type CoachUser = z.infer<typeof userParser>;

export type PublicCoachUser = z.infer<typeof publicCoachUserParser>;

const populatedUserParser = publicCoachParser.merge(
  z.object({
    profile: Profile.parser,
    invite: Invite.redeemedParser,
    permissions: z.array(PermissionEntity.Permission.parser),
  }),
);

export type Coach<T extends "private" | "public" | "populated" = "private"> = T extends "private"
  ? CoachUser
  : T extends "public"
  ? PublicCoachUser
  : T extends "populated"
  ? z.infer<typeof populatedUserParser>
  : never;

export const CoachUser = {
  registerWithInviteDto,

  toPasswordResetInitiationDto(
    source: unknown,
  ): PasswordResetInitiationDto | ParsingError<{ email: EmailErrors[] }> {
    const parsed = passwordResetInitiationParser.safeParse(source);

    return parsed.success ? parsed.data : new ParsingError(parsed.error);
  },

  toPasswordResetQueryDto(source: unknown):
    | PasswordResetQueryDto
    | ParsingError<{
        email: EmailErrors[];
        code: string[];
        type: CustomEnumErrors[];
      }> {
    const parsed = passwordResetQueryDto.safeParse(source);

    return parsed.success ? parsed.data : new ParsingError(parsed.error);
  },

  toPasswordResetDto(source: unknown):
    | PasswordResetActionDto
    | ParsingError<{
        email: EmailErrors[];
        code: string[];
        newPassword: PasswordErrors[];
        type: CustomEnumErrors[];
      }> {
    const parsed = passwordResetActionParser.safeParse(source);

    return parsed.success ? parsed.data : new ParsingError(parsed.error);
  },

  toLoginCodeDto: (source: unknown) => zodToDomain(loginCodeDtoParser, source),

  passwordResetInitiationParser,
  passwordResetActionParser,
  credentialsParser,
  coachParser,

  createUserWithCredentials: (
    dto: CredentialsDto,
    encryptedPassword: HashedPassword,
    createId = () => UUID.generate<CoachUserId>(),
  ): CoachUser => {
    return {
      id: createId(),
      type: dto.type,
      authentication: {
        email: dto.email,
        password: encryptedPassword,
        type: AuthTypes.Credentials,
      },
    };
  },

  toEntity(source: unknown): CoachUser | ParsingError {
    const parseResult = userParser.safeParse(source);

    if (parseResult.success) {
      return parseResult.data;
    }

    return new ParsingError(parseResult.error);
  },

  setPasswordReset(user: CoachUser, code: string): CoachUser {
    return {
      ...user,
      authentication: {
        ...user.authentication,
        passwordReset: {
          code,
          expiration: CustomDate.addMinutes(CustomNumber.castToPositiveInteger(15)),
        },
      },
    };
  },

  updatePassword(user: CoachUser, encryptedPassword: HashedPassword): CoachUser {
    return {
      ...user,
      authentication: {
        type: user.authentication.type,
        email: user.authentication.email,
        password: encryptedPassword,
      },
    };
  },

  toEntities(source: unknown): CoachUser[] | ParsingError {
    const parseResult = z.array(userParser).safeParse(source);

    if (parseResult.success) {
      return parseResult.data;
    }

    return new ParsingError(parseResult.error);
  },

  validatePassword: (source: unknown) => {
    return Password.validateAllCriteria(source);
  },

  toInviteRegistrationDto: (data: unknown) => {
    return zodToDomain(registerWithInviteDto, data);
  },

  publicFromUser: (user: CoachUser, profile: Profile): PublicCoachUser => {
    return {
      ...user,
      authentication: omit("password", user.authentication),
      firstName: profile.firstName,
      lastName: profile.lastName,
    };
  },

  publicFromUnknown: (source: unknown) => {
    const parseResult = publicCoachUserParser.safeParse(source);

    if (parseResult.success) {
      return parseResult.data;
    }

    return new ParsingError(parseResult.error);
  },

  populatedFromUser: (
    user: CoachUser,
    profile: Profile,
    invite: RedeemedInvite,
    permissions: PermissionEntity.Permission[],
  ): Coach<"populated"> => {
    return {
      ...user,
      type: UserTypes.Coach,
      authentication: omit("password", user.authentication),
      profile,
      invite,
      permissions,
    };
  },

  toPopulatedEntitiesFromUnknown: (source: unknown): Coach<"populated">[] | ParsingError => {
    const parseResult = z.array(populatedUserParser).safeParse(source);

    if (parseResult.success) {
      return parseResult.data;
    }

    return new ParsingError(parseResult.error);
  },

  toLoginDto: (
    source: GenericRecord,
    userType: UserTypes,
  ): CredentialsDto | ParsingError<{ password: string[]; email: string[] }> => {
    const parsed = credentialsParserWithBasicPassword.safeParse({
      ...source,
      type: userType,
    });

    if (parsed.success) {
      return parsed.data as CredentialsDto;
    }

    return new ParsingError(parsed.error);
  },

  toAccessTokenDto: (source: unknown) => zodToDomain(AccessTokenParser, source),

  toUserId: (source: unknown) => {
    const parsed = withId.shape.id.safeParse(source);

    return parsed.success ? parsed.data : new ParsingError(parsed.error);
  },

  addLoginCode: (
    user: CoachUser,
    code: LoginCode,
    expiresAt = CustomDate.addMinutes(CustomNumber.toPositiveInteger(15), CustomDate.now),
  ): CoachUser => {
    return {
      ...user,
      authentication: {
        ...user.authentication,
        oneTimeCode: {
          code,
          expiresAt,
        },
      },
    };
  },

  signIn: (user: CoachUser, code: LoginCode, now = CustomDate.now()): DomainError | CoachUser => {
    if (user.authentication.oneTimeCode?.code !== code) {
      return new DomainError(ErrorMessages.InvalidCredentials);
    }

    if (CustomDate.isBefore(user.authentication.oneTimeCode?.expiresAt, now)) {
      return new DomainError(ErrorMessages.ActionExpired);
    }

    return {
      ...user,
      authentication: {
        ...user.authentication,
        oneTimeCode: {
          ...user.authentication.oneTimeCode,
        },
      },
    };
  },

  toPopulatedUserDemoInstance: (
    user: CoachUser,
    profile: Profile,
    invite: RedeemedInvite,
    permissions: Permission[],
  ): Coach<"populated"> => {
    return {
      ...user,
      profile,
      invite,
      permissions,
    };
  },

  toDemoInstance: (
    password = HashedPassword.parser.parse("Password1"),
    id = UUID.generate<CoachUserId>(),
    email = faker.name.firstName(),
  ): CoachUser => {
    return {
      id,
      type: UserTypes.Coach,
      authentication: {
        email: Email.parser.parse(`${email}@team-assist.co.uk`),
        password,
        type: AuthTypes.Credentials,
      },
    };
  },
};
