import { z } from "zod";
import { sample } from "lodash";

import { AccessToken, Tagged } from "../shared";

export enum AuthTypes {
  Credentials = "credentials",
  OneTimePassword = "one-time-password",
}

export enum UserTypes {
  "Player" = "player",
  "Coach" = "coach",
}

export const AccessTokenParser = z.object({
  token: AccessToken.parser,
});

export type AccessTokenDto = z.infer<typeof AccessTokenParser>;

export type LoginCode = Tagged<"LoginCode", string>;

const loginCodeLength = 8;

/* use ambiguous error message for security reasons */
const loginCodeError = "Invalid code";

export const loginCodeParser = z
  .string({ invalid_type_error: loginCodeError, required_error: loginCodeError })
  .length(loginCodeLength, loginCodeError)
  .transform((value) => value as LoginCode);

export const loginCodeDtoParser = z.object({
  code: loginCodeParser,
});

export const optionalLoginCodeDtoParser = z.object({
  code: loginCodeParser.optional(),
});

export type LoginCodeDto = z.infer<typeof loginCodeDtoParser>;

export const generateLoginCode = (): LoginCode => {
  const symbols = "abcdefghijklmnopqrstwxyz123456789".split("");

  const code = new Array(loginCodeLength).fill(1).reduce((total) => {
    const randomChar = sample(symbols);

    total += randomChar;

    return total;
  }, "");

  return code as LoginCode;
};
