import { get } from "lodash/fp";
import { z } from "zod";

import { zodToDomain } from "../../misc";

import {
  CustomDate,
  CustomNumber,
  DomainError,
  Email,
  ErrorMessages,
  ParsingError,
  Tagged,
  UUID,
} from "../shared";
import {
  AccessTokenParser,
  AuthTypes,
  LoginCode,
  loginCodeParser,
  UserTypes,
  generateLoginCode,
  loginCodeDtoParser,
  optionalLoginCodeDtoParser,
} from "./shared";

export type PlayerUserId = Tagged<"PlayerUserId", UUID>;

const activeMagicLinkAuthParser = z.object({
  type: z.literal(AuthTypes.OneTimePassword),
  email: Email.parser,

  code: loginCodeParser,
  expiresAt: CustomDate.validParser(),

  lastLogin: CustomDate.validParser().optional(),
});

const usedMagicLinkAuthParser = z.object({
  type: z.literal(AuthTypes.OneTimePassword),
  email: Email.parser,

  code: z.literal(undefined),
  expiresAt: z.literal(undefined),

  lastLogin: CustomDate.validParser(),
});

const withId = z.object({
  id: UUID.parser<PlayerUserId>(),
});

const unverifiedPlayerUserParser = z
  .object({
    type: z.literal(UserTypes.Player),
    authentication: activeMagicLinkAuthParser,
  })
  .merge(withId);

const verifiedPlayerUserParser = z
  .object({
    type: z.literal(UserTypes.Player),
    authentication: usedMagicLinkAuthParser,
  })
  .merge(withId);

const playerUserParser = z.union([unverifiedPlayerUserParser, verifiedPlayerUserParser]);

const publicMagicLinkAuthParser = z.object({
  type: z.literal(AuthTypes.OneTimePassword),
  email: Email.parser,

  lastLogin: CustomDate.validParser().optional(),
});

const publicPlayerParser = verifiedPlayerUserParser.omit({ authentication: true }).merge(
  z.object({
    authentication: publicMagicLinkAuthParser,
  }),
);

export type UnverifiedPlayerUser = z.infer<typeof unverifiedPlayerUserParser>;
export type VerifiedPlayerUser = z.infer<typeof verifiedPlayerUserParser>;
export type PlayerUser = z.infer<typeof playerUserParser>;

export type PublicPlayerUser = z.infer<typeof publicPlayerParser>;

const signInDtoParser = z.object({
  email: Email.parser,
});

export type SignInDto = z.infer<typeof signInDtoParser>;

export const Entity = {
  createUser: (
    email: Email,
    code: LoginCode,
    createId = () => UUID.generate<PlayerUserId>(),
    expiresAt = CustomDate.addMinutes(CustomNumber.toPositiveInteger(15), CustomDate.now),
  ): UnverifiedPlayerUser => {
    return {
      id: createId(),
      type: UserTypes.Player,
      authentication: {
        type: AuthTypes.OneTimePassword,
        email: email,
        code,
        expiresAt,
      },
    };
  },

  addLoginCode: (
    user: PlayerUser,
    code: LoginCode,
    expiresAt = CustomDate.addMinutes(CustomNumber.toPositiveInteger(15), CustomDate.now),
  ): UnverifiedPlayerUser => {
    return {
      ...user,
      authentication: {
        type: AuthTypes.OneTimePassword,
        email: user.authentication.email,
        code,
        expiresAt,
        lastLogin: user.authentication.lastLogin,
      },
    };
  },

  signIn: (
    user: PlayerUser,
    code: LoginCode,
    now = CustomDate.now(),
  ): DomainError | VerifiedPlayerUser => {
    if (user.authentication.code !== code) {
      return new DomainError(ErrorMessages.InvalidCredentials);
    }

    if (CustomDate.isBefore(user.authentication.expiresAt, now)) {
      return new DomainError(ErrorMessages.ActionExpired);
    }

    return {
      ...user,
      authentication: {
        type: AuthTypes.OneTimePassword,
        email: user.authentication.email,
        lastLogin: now,
      },
    };
  },

  toEntity(source: unknown): PlayerUser | ParsingError {
    return zodToDomain(playerUserParser, source);
  },

  toEntities: (source: unknown): PlayerUser[] | ParsingError => {
    return zodToDomain(z.array(playerUserParser), source);
  },

  toPublic: (user: PlayerUser): PublicPlayerUser => {
    return {
      ...user,
      authentication: {
        type: AuthTypes.OneTimePassword,
        email: user.authentication.email,
        lastLogin: get(["authentication", "lastLogin"], user),
      },
    };
  },

  toAccessTokenDto: (source: unknown) => zodToDomain(AccessTokenParser, source),
  toLoginCodeDto: (source: unknown) => zodToDomain(loginCodeDtoParser, source),
  toOptionalLoginCodeDto: (source: unknown) => zodToDomain(optionalLoginCodeDtoParser, source),
  toSignInDto: (source: unknown) => zodToDomain(signInDtoParser, source),
  AccessTokenParser,
  loginCodeDtoParser,
  signInDtoParser,

  parseId(source: unknown) {
    return zodToDomain(withId.shape.id, source);
  },

  isVerified(user: PlayerUser): user is VerifiedPlayerUser {
    return !user.authentication.code;
  },

  getEmail(user: PublicPlayerUser) {
    return user.authentication.email;
  },

  generateLoginCode,
};
