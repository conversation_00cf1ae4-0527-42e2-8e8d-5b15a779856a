import { omit } from "lodash/fp";

import { HashedPassword, isError, ParsingError, PasswordErrors, UUID } from "../shared";
import { CoachUser, CoachUserId } from "./coachUser";
import { breakTest } from "../../test";
import { UserTypes, AuthTypes } from "./shared";
import { Profile } from "../profile";

describe("UserEntity", () => {
  describe(CoachUser.createUserWithCredentials.name, () => {
    it("creates a user", () => {
      const id = UUID.generate<CoachUserId>();

      const credentialsDto = CoachUser.credentialsParser.parse({
        email: "<EMAIL>",
        type: UserTypes.Coach,
        password: "password1",
      });

      const hashedPassword = "Totally hashed" as HashedPassword;

      const result = CoachUser.createUserWithCredentials(credentialsDto, hashedPassword, () => id);

      expect(result).toStrictEqual({
        id,
        type: credentialsDto.type,
        authentication: {
          type: AuthTypes.Credentials,
          email: credentialsDto.email,
          password: hashedPassword,
        },
      });
    });
  });

  describe(CoachUser.toEntity.name, () => {
    const validUser = {
      id: UUID.generate(),
      type: UserTypes.Coach,
      authentication: {
        email: "<EMAIL>",
        password: "password1",
        type: AuthTypes.Credentials,
      },
    };
    it("passes with correct data", () => {
      const user = CoachUser.toEntity(validUser);

      if (isError(user)) {
        return breakTest();
      }

      expect(user).toEqual(validUser);
    });

    it("fails with invalid id", () => {
      const invalidUser = {
        ...validUser,
        id: "not a uuid",
      };
      const user = CoachUser.toEntity(invalidUser);

      if (!isError(user)) {
        return breakTest();
      }

      expect(user).toBeInstanceOf(ParsingError);
      expect(Object.keys(user.errors)).toHaveLength(1);
      expect(user.errors).toHaveProperty("id");
    });

    it("fails with invalid type", () => {
      const invalidUser = {
        ...validUser,
        type: "not a valid user type",
      };
      const user = CoachUser.toEntity(invalidUser);

      if (!isError(user)) {
        return breakTest();
      }

      expect(user).toBeInstanceOf(ParsingError);
      expect(user.errors).toHaveProperty("type");
    });

    it("fails with invalid email", () => {
      const invalidUser = {
        ...validUser,
        authentication: {
          ...validUser.authentication,
          email: "not an email",
        },
      };
      const user = CoachUser.toEntity(invalidUser);

      if (!isError(user)) {
        return breakTest();
      }

      expect(user).toBeInstanceOf(ParsingError);
      expect(user.errors["authentication.email"]).toBeDefined();
    });

    it("fails with invalid password", () => {
      const invalidUser = {
        ...validUser,
        authentication: {
          ...validUser.authentication,
          password: 55,
        },
      };
      const user = CoachUser.toEntity(invalidUser);

      if (!isError(user)) {
        return breakTest();
      }

      expect(user).toBeInstanceOf(ParsingError);
      expect(user.errors["authentication.password"]).toBeDefined();
    });

    it("fails with invalid auth type", () => {
      const invalidUser = {
        ...validUser,
        authentication: {
          ...validUser.authentication,
          type: "not a real auth type",
        },
      };
      const user = CoachUser.toEntity(invalidUser);

      if (!isError(user)) {
        return breakTest();
      }

      expect(user).toBeInstanceOf(ParsingError);
      expect(user.errors["authentication.type"]).toBeDefined();
    });
  });

  const validUser = CoachUser.coachParser.parse({
    type: UserTypes.Coach,
    id: UUID.generate<CoachUserId>(),
    authentication: {
      email: "<EMAIL>",
      type: AuthTypes.Credentials,
      password: "password1",
    },
  });

  describe(CoachUser.publicFromUser.name, () => {
    it("returns a User given correct data", () => {
      const profile = Profile.create(
        Profile.createDto.parse({
          firstName: "John",
          lastName: "Doe",
        }),
        validUser.id,
      );
      const user = CoachUser.publicFromUser(validUser, profile);

      expect(user).toEqual(
        omit("authentication.password", {
          ...validUser,
          firstName: profile.firstName,
          lastName: profile.lastName,
        }),
      );
    });

    /* other cases are already covered by User.toEntity */
  });

  describe(CoachUser.validatePassword.name, () => {
    it("passes with correct password", () => {
      const errors = CoachUser.validatePassword("password1");

      expect(errors).toHaveLength(0);
    });

    it("must follow all rules and have minimum length", () => {
      const errors = CoachUser.validatePassword(123);

      expect(errors).toHaveLength(4);
      expect(errors).toContain(PasswordErrors.NotAString);
      expect(errors).toContain(PasswordErrors.TooShort);
      expect(errors).toContain(PasswordErrors.MinLetters);
      expect(errors).toContain(PasswordErrors.MinDigits);
    });

    it("must follow all rules and have maximum length", () => {
      const errors = CoachUser.validatePassword(1 + new Array(50).fill("a").join(""));

      expect(errors).toHaveLength(1);
      expect(errors).toContain(PasswordErrors.TooLong);
    });
  });
});
