import { IntegerInRange, ValidDate } from "../shared";
import * as RecurringTeamEvent from "./recurringTeamEvent";
import * as EventScheduling from "./scheduling";

describe("RecurringTeamEvent entity", () => {
  describe(RecurringTeamEvent.Entity.getWeeklyRecurringDates.name, () => {
    it("weekly interval: simple case", () => {
      const startDate = new Date("2023-02-27") as ValidDate; // monday
      const endDate = new Date("2023-04-11") as ValidDate; // tuesday

      const result = RecurringTeamEvent.Entity.getWeeklyRecurringDates(
        EventScheduling.Type.Weekly,
        EventScheduling.DayOfWeek.Wednesday,
        startDate,
        endDate,
      );

      /*
       [
        2023-03-01,
        2023-03-08,
        2023-03-15,
        2023-03-22,
        2023-03-28,
        2023-04-05
       ] 
        */
      expect(result).toHaveLength(6);
      expect(result[0].getMonth()).toBe(2);
      expect(result[0].getDate()).toBe(1);
      expect(result[5].getMonth()).toBe(3);
      expect(result[5].getDate()).toBe(5);
    });

    it("weekly interval: include the starting date", () => {
      const startDate = new Date("2023-02-22") as ValidDate; // monday
      const endDate = new Date("2023-04-11") as ValidDate; // tuesday

      const result = RecurringTeamEvent.Entity.getWeeklyRecurringDates(
        EventScheduling.Type.Weekly,
        EventScheduling.DayOfWeek.Wednesday,
        startDate,
        endDate,
      );

      /*
         [
          2023-02-22
          2023-03-01,
          2023-03-08,
          2023-03-15,
          2023-03-22,
          2023-03-28,
          2023-04-05
         ] 
          */
      expect(result).toHaveLength(7);
    });

    it("weekly interval: include the end date", () => {
      const startDate = new Date("2023-02-27") as ValidDate; // monday
      const endDate = new Date("2023-04-12") as ValidDate; // tuesday

      const result = RecurringTeamEvent.Entity.getWeeklyRecurringDates(
        EventScheduling.Type.Weekly,
        EventScheduling.DayOfWeek.Wednesday,
        startDate,
        endDate,
      );

      /*
           [
            2023-03-01,
            2023-03-08,
            2023-03-15,
            2023-03-22,
            2023-03-28,
            2023-04-05,
            2023-04-12
           ] 
            */
      expect(result).toHaveLength(7);
    });

    it("weekly interval: no events", () => {
      const startDate = new Date("2023-02-27") as ValidDate; // monday
      const endDate = new Date("2023-02-28") as ValidDate; // tuesday

      const result = RecurringTeamEvent.Entity.getWeeklyRecurringDates(
        EventScheduling.Type.Weekly,
        EventScheduling.DayOfWeek.Wednesday,
        startDate,
        endDate,
      );

      expect(result).toHaveLength(0);
    });

    it("weekly interval: span to another year", () => {
      const startDate = new Date("2023-12-27") as ValidDate; // monday
      const endDate = new Date("2024-01-10") as ValidDate; // tuesday

      const result = RecurringTeamEvent.Entity.getWeeklyRecurringDates(
        EventScheduling.Type.Weekly,
        EventScheduling.DayOfWeek.Wednesday,
        startDate,
        endDate,
      );

      /*
           [
            2023-12-27,
            2023-01-03,
            2023-01-10,
           ] 
            */
      expect(result).toHaveLength(3);
    });

    it("bi-weekly interval: simple case", () => {
      const startDate = new Date("2023-02-27") as ValidDate; // monday
      const endDate = new Date("2023-04-11") as ValidDate; // tuesday

      const result = RecurringTeamEvent.Entity.getWeeklyRecurringDates(
        EventScheduling.Type.BiWeekly,
        EventScheduling.DayOfWeek.Wednesday,
        startDate,
        endDate,
      );

      /*
         [
         2023-03-01, 2023-03-15, 2023-03-29
         ] 
      */
      expect(result).toHaveLength(3);
    });
  });

  describe(RecurringTeamEvent.Entity.getMonthlyRecurringDates.name, () => {
    it("monthly interval: simple case", () => {
      const startDate = new Date("2023-02-26") as ValidDate; // monday
      const endDate = new Date("2023-09-21") as ValidDate; // tuesday

      const result = RecurringTeamEvent.Entity.getMonthlyRecurringDates(
        20 as IntegerInRange<1, 31>,
        startDate,
        endDate,
      );

      // 2023-02-27,
      // 2023-03-27,
      // 2023-04-27,
      // 2023-05-27,
      // 2023-06-27,
      // 2023-07-27,
      // 2023-08-27

      expect(result).toHaveLength(7);
    });

    it("monthly interval: missing dates from months", () => {
      const startDate = new Date("2023-02-27") as ValidDate; // monday
      const endDate = new Date("2023-09-11") as ValidDate; // tuesday

      const result = RecurringTeamEvent.Entity.getMonthlyRecurringDates(
        31 as IntegerInRange<1, 31>,
        startDate,
        endDate,
      );

      // 2023-03-31,
      // 2023-05-31,
      // 2023-07-31,
      // 2023-08-31,

      expect(result).toHaveLength(4);
    });

    it("monthly interval: include end and start date", () => {
      const startDate = new Date("2023-05-27") as ValidDate; // monday
      const endDate = new Date("2023-06-27") as ValidDate; // tuesday

      const result = RecurringTeamEvent.Entity.getMonthlyRecurringDates(
        27 as IntegerInRange<1, 31>,
        startDate,
        endDate,
      );

      // 2023-05-27,
      // 2023-06-27,

      expect(result).toHaveLength(2);
    });

    it("monthly interval: no events", () => {
      const startDate = new Date("2023-05-15") as ValidDate; // monday
      const endDate = new Date("2023-05-26") as ValidDate; // tuesday

      const result = RecurringTeamEvent.Entity.getMonthlyRecurringDates(
        27 as IntegerInRange<1, 31>,
        startDate,
        endDate,
      );

      expect(result).toHaveLength(0);
    });

    it("monthly interval: no events, different months for start - end", () => {
      const startDate = new Date("2023-05-28") as ValidDate; // monday
      const endDate = new Date("2023-06-13") as ValidDate; // tuesday

      const result = RecurringTeamEvent.Entity.getMonthlyRecurringDates(
        14 as IntegerInRange<1, 31>,
        startDate,
        endDate,
      );

      expect(result).toHaveLength(0);
    });

    it("monthly interval: span to next year", () => {
      const startDate = new Date("2023-11-25") as ValidDate; // monday
      const endDate = new Date("2024-02-28") as ValidDate; // tuesday

      const result = RecurringTeamEvent.Entity.getMonthlyRecurringDates(
        26 as IntegerInRange<1, 31>,
        startDate,
        endDate,
      );

      // 2023-11-27,
      // 2023-12-27,
      // 2024-01-27,
      // 2024-02-27,

      expect(result).toHaveLength(4);
      expect(result[0].getFullYear()).toBe(2023);
      expect(result[3].getFullYear()).toBe(2024);
    });
  });
});
