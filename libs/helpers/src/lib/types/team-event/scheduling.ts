import { z } from "zod";

import { SeasonId } from "../season";
import {
  CustomDate,
  CustomEnum,
  CustomNumber,
  DayOfWeek as CustomDateOfWeek,
  UUID,
} from "../shared";

export enum Type {
  Weekly = "weekly",
  BiWeekly = "bi-weekly",
  Monthly = "monthly",
}

export const DayOfWeek = {
  Monday: CustomDateOfWeek[1],
  Tuesday: CustomDateOfWeek[2],
  Wednesday: CustomDateOfWeek[3],
  Thursday: CustomDateOfWeek[4],
  Friday: CustomDateOfWeek[5],
  Saturday: CustomDateOfWeek[6],
  Sunday: CustomDateOfWeek[0],
} as const;

export type DayOfWeek = typeof DayOfWeek[keyof typeof DayOfWeek];

export enum EndCriteria {
  CustomDate = "custom-date",
  EndOfSeason = "end-of-season",
}

const duration = z.union([
  z.object({
    type: z.literal(EndCriteria.CustomDate),
    date: CustomDate.validParser(),
    seasonId: z.literal(undefined),
  }),
  z.object({
    type: z.literal(EndCriteria.EndOfSeason),
    date: z.literal(undefined),
    seasonId: UUID.parser<SeasonId>(),
  }),
]);

const baseParser = z.object({
  duration,

  /* all events default start times/end */
  startTime: CustomDate.validTimeParser,
  endTime: CustomDate.validTimeParser,

  /* start creating events from this date onwards */
  startFrom: CustomDate.validParser({ stripTime: true }),
});

const weeklyParser = z
  .object({
    type: z.union([z.literal(Type.Weekly), z.literal(Type.BiWeekly)]),
    day: CustomEnum.parser(DayOfWeek),
    date: z.literal(undefined),
  })
  .merge(baseParser);

/* if they select 31 and we're in February - we assume there won't be an event*/
const monthlyParser = z
  .object({
    type: z.literal(Type.Monthly),
    date: CustomNumber.integerInRangeParser(1, 31, ""),
    day: z.literal(undefined),
  })
  .merge(baseParser);

export const schedulingParser = z.union([weeklyParser, monthlyParser]);

export type Schedule = z.infer<typeof schedulingParser>;

export const getToday = () => {
  return Object.values(DayOfWeek)[new Date().getDay()];
};
