import { z } from "zod";
import { faker } from "@faker-js/faker";

import { zodToDomain } from "../../misc";
import { ProfileId } from "../profile";
import {
  Tagged,
  UUID,
  CustomDate,
  isError,
  ParsingError,
  CustomEnum,
  ValidDate,
  PositiveInteger,
  IntegerInRange,
} from "../shared";
import { schedulingParser } from "./scheduling";
import { Agenda, sharedEventFields } from "./shared";
import * as EventScheduling from "./scheduling";
import { TeamId } from "../team";
import { OrganizationId } from "../organization";
import { PlayerId } from "../player";

export type EventId = Tagged<"RecurringTeamEvent", UUID>;

export enum Status {
  Active = "active",
  Inactive = "inactive",
}

const baseParser = z
  .object({
    organizationId: UUID.parser<OrganizationId>(),
    teamId: UUID.parser<TeamId>(),

    status: CustomEnum.parser(Status).default(Status.Active),
    schedule: schedulingParser,
  })
  .merge(sharedEventFields);

const withIdParser = z.object({
  id: UUID.parser<EventId>(),
});

const parser = baseParser.merge(withIdParser);

export type RecurringTeamEvent = z.infer<typeof parser>;

const updateDtoParser = parser;

export type UpdateDto = z.infer<typeof updateDtoParser>;

const deletionFieldsParser = z.object({
  deletedBy: UUID.parser<ProfileId>(),
  deletedAt: CustomDate.validParser(),
});

const deletedEventParser = parser.merge(deletionFieldsParser);

export type DeletedEvent = z.infer<typeof deletedEventParser>;

export type CreateDto = z.infer<typeof baseParser>;

export type UpsertDto = CreateDto | UpdateDto;

export const Entity = {
  getWeeklyRecurringDates: (
    type: EventScheduling.Type.Weekly | EventScheduling.Type.BiWeekly,
    dayOfWeek: EventScheduling.DayOfWeek,
    startFrom: ValidDate,
    endAt: ValidDate,
  ): ValidDate[] => {
    const results: ValidDate[] = [];
    let dateCursor = new Date(startFrom) as ValidDate;

    const interval =
      type === EventScheduling.Type.Weekly ? (7 as PositiveInteger) : (14 as PositiveInteger);

    if (CustomDate.getDayOfWeek(dateCursor) !== dayOfWeek) {
      dateCursor = CustomDate.getNextDateFor(dayOfWeek, dateCursor);
    }

    while (CustomDate.isBefore(dateCursor, endAt) || CustomDate.isEqual(dateCursor, endAt)) {
      results.push(new Date(dateCursor) as ValidDate);
      dateCursor = CustomDate.addDays(interval, () => dateCursor);
    }

    return results;
  },

  getMonthlyRecurringDates: (
    targetDate: IntegerInRange<1, 31, "">,
    startFrom: ValidDate,
    endAt: ValidDate,
  ): ValidDate[] => {
    const results: ValidDate[] = [];

    const dateCursor = new Date(startFrom);

    while (dateCursor.getTime() <= endAt.getTime()) {
      const monthHasEnoughDays = CustomDate.getDaysInMonth(dateCursor) >= targetDate;

      if (monthHasEnoughDays) {
        const expectedDate = new Date(dateCursor);
        expectedDate.setDate(targetDate);

        if (
          expectedDate.getTime() >= startFrom.getTime() &&
          expectedDate.getTime() <= endAt.getTime()
        ) {
          results.push(expectedDate as ValidDate);
        }
      }

      dateCursor.setMonth(dateCursor.getMonth() + 1, 1);
    }

    return results;
  },

  create: (dto: CreateDto, createId = () => UUID.generate<EventId>()): RecurringTeamEvent => {
    return {
      id: createId(),
      organizationId: dto.organizationId,
      teamId: dto.teamId,
      status: dto.status,
      agenda: dto.agenda,
      name: dto.name,
      description: dto.description,
      location: dto.location,
      hosts: dto.hosts,
      invitations: dto.invitations,
      schedule: dto.schedule,
    };
  },

  createDeleted: (
    dto: RecurringTeamEvent,
    modifier: ProfileId,
    now = CustomDate.now(),
  ): DeletedEvent => {
    return {
      ...dto,
      deletedBy: modifier,
      deletedAt: now,
    };
  },

  toEntity: (source: unknown) => zodToDomain(parser, source),

  toEntities: (source: unknown) => zodToDomain(z.array(parser), source),

  toCreateDto: (source: unknown) => {
    const result = zodToDomain(baseParser, source);

    if (isError(result)) {
      return result as ParsingError;
    }
    return result;
  },

  toUpdateDto: (source: unknown) => {
    const result = zodToDomain(updateDtoParser, source);

    if (isError(result)) {
      return result;
    }
    return result;
  },

  toUpsertDto: (source: unknown) => {
    const parser = z.union([updateDtoParser, baseParser]);
    const result = parser.safeParse(source);

    if (result.success) {
      return result.data;
    }

    const error = new ParsingError(result.error);

    return error;
  },

  update: (dto: UpdateDto, event: RecurringTeamEvent): RecurringTeamEvent => {
    return {
      ...event,
      status: dto.status,
      agenda: dto.agenda,
      name: dto.name,
      description: dto.description,
      location: dto.location,
      hosts: dto.hosts,
      invitations: dto.invitations,
      schedule: dto.schedule,
    };
  },

  updateDto: updateDtoParser,
  createDto: baseParser,

  parseId: withIdParser.shape.id,
  Agenda,
  parser,

  toDisplayEventType: (type: Agenda) => {
    switch (type) {
      case Agenda.Match:
        return "Match";
      case Agenda.TrainingSession:
        return "Training session";
      case Agenda.Meeting:
        return "Meeting";
      case Agenda.Other:
        return "Other";

      default:
        break;
    }

    return "";
  },

  toEventId: (source: unknown) => {
    const parsed = UUID.parser<EventId>().safeParse(source);

    return parsed.success ? parsed.data : new ParsingError(parsed.error);
  },

  toDemoInstance: (
    agenda = Agenda.Match,
    hosts: ProfileId[] = [],
    invitations: PlayerId[] = [],
    teamId = UUID.generate<TeamId>(),
  ): RecurringTeamEvent => ({
    id: UUID.generate<EventId>(),
    organizationId: UUID.generate<OrganizationId>(),
    teamId,
    status: Status.Active,
    agenda,
    name: (agenda === Agenda.Match ? "Match" : "Training") as RecurringTeamEvent["name"],
    description: faker.lorem.sentences(2) as RecurringTeamEvent["description"],
    location: {
      postcode: faker.address.zipCode(),
      address: faker.address.streetAddress(),
    } as RecurringTeamEvent["location"],
    hosts,
    invitations,
    schedule: {
      type: EventScheduling.Type.Weekly,
      day: EventScheduling.DayOfWeek.Monday,
      duration: {
        type: EventScheduling.EndCriteria.CustomDate,
        date: CustomDate.addDays(60 as PositiveInteger),
      },

      startTime: CustomDate.timeNow(),
      endTime: CustomDate.timeAddMinutes(30 as PositiveInteger),

      startFrom: CustomDate.now(),
    } as RecurringTeamEvent["schedule"],
  }),
};
