import { z } from "zod";

import { CustomEnum, StringOfLength } from "../shared";
import { Player } from "../player";
import { Profile } from "../profile";
import { addressParser } from "../shared/Address";

export enum Agenda {
  "TrainingSession" = "training-session",
  "Match" = "match",
  "Meeting" = "meeting",
  "Other" = "other",
}

export const sharedEventFields = z.object({
  name: StringOfLength.parser(2, 100),

  agenda: CustomEnum.parser(Agenda),

  description: StringOfLength.parser(2, 2000).optional(),
  location: addressParser,

  hosts: z.array(Profile.idParser),
  invitations: z.array(Player.parseId),
});

export type EventAddress = z.infer<typeof addressParser>;
