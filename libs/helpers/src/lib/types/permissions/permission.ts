import { omit } from "lodash/fp";
import { z } from "zod";

import { OrganizationId } from "../organization";
import { ProfileId } from "../profile";
import { CustomArray, CustomEnum, ParsingError, Tagged, UUID } from "../shared";
import { TeamId } from "../team";
import { Actions } from "./action";
import { RoleId } from "./role";

export type PermissionId = Tagged<"PermissionId", UUID>;

export enum Type {
  Owner = "owner",
  Custom = "custom",
}

const withId = z.object({
  id: UUID.parser<PermissionId>(),
});

const baseParser = z.object({
  profileId: UUID.parser<ProfileId>(),
  organizationId: UUID.parser<OrganizationId>(),
});

const ownerParser = withId.merge(baseParser).merge(
  z.object({
    type: z.literal(Type.Owner),
  }),
);

const customParser = withId.merge(baseParser).merge(
  z.object({
    type: z.literal(Type.Custom),

    roleId: UUID.parser<RoleId>(),

    teamIds: CustomArray.arrayParser(UUID.parser<TeamId>()).optional(),
  }),
);

const unionParser = z.union([ownerParser, customParser]);

const createOwnerDtoParser = customParser.pick({ organizationId: true, profileId: true });
export type CreateOwnerDto = z.infer<typeof createOwnerDtoParser>;

const createCustomDtoParser = customParser.omit({ id: true, type: true });
export type CreateCustomDto = z.infer<typeof createCustomDtoParser>;

const updateCustomDtoParser = z.object({
  teamIds: CustomArray.arrayParser(UUID.parser<TeamId>()),
});
export type UpdateCustomDto = z.infer<typeof updateCustomDtoParser>;

export type OwnerPermission = z.infer<typeof ownerParser>;
export type CustomPermission = z.infer<typeof customParser>;
export type Permission = z.infer<typeof unionParser>;

const publicCustomParser = customParser.omit({ roleId: true }).merge(
  z.object({
    actions: z.array(CustomEnum.parser(Actions)),
  }),
);

const publicParser = z.union([ownerParser, publicCustomParser]);

export type PublicPermission = z.infer<typeof publicParser>;

export const Permission = {
  parser: unionParser,

  createCustomDtoParser,
  createOwnerDtoParser,
  updateCustomDtoParser,

  createOwner(dto: CreateOwnerDto, id = UUID.generate<PermissionId>()): OwnerPermission {
    return {
      id,
      organizationId: dto.organizationId,
      profileId: dto.profileId,
      type: Type.Owner,
    };
  },

  createCustom(dto: CreateCustomDto, id = UUID.generate<PermissionId>()): CustomPermission {
    return {
      ...dto,
      id,
      type: Type.Custom,
    };
  },

  updateTeams: (permission: CustomPermission, dto: UpdateCustomDto): CustomPermission => {
    return {
      ...permission,
      teamIds: dto.teamIds,
    };
  },

  toEntity(source: unknown): Permission | ParsingError {
    const parsed = unionParser.safeParse(source);

    return parsed.success ? parsed.data : new ParsingError(parsed.error);
  },

  toEntities: (source: unknown): Permission[] | ParsingError => {
    const parsed = z.array(unionParser).safeParse(source);

    return parsed.success ? parsed.data : new ParsingError(parsed.error);
  },

  toPublicEntities: (source: unknown): PublicPermission[] | ParsingError => {
    const parsed = z.array(publicParser).safeParse(source);

    return parsed.success ? parsed.data : new ParsingError(parsed.error);
  },

  parseActions(source: unknown) {
    const parsed = z.array(CustomEnum.parser(Actions)).safeParse(source);

    return parsed.success ? parsed.data : new ParsingError(parsed.error);
  },

  toPublic(permission: Permission, actions: Actions[]): PublicPermission {
    if (permission.type === Type.Owner) {
      return permission;
    }

    return {
      ...omit("roleId", permission),
      actions,
    };
  },

  hasAccess(
    actions: Actions[],
    permissions: PublicPermission[],
    teams?: TeamId[],
    passWithAny = false,
  ): boolean {
    const isOwner = permissions.some((permission) => permission.type === Type.Owner);

    if (isOwner) {
      return true;
    }

    const permissionsWithActions = permissions.reduce((acc, item) => {
      if (item.type !== Type.Owner) {
        const roleActions = item.actions || [];
        const teams = item.teamIds || [];

        acc = [...acc, { actions: roleActions, teams }];
      }
      return acc;
    }, [] as Array<{ actions: Actions[]; teams: TeamId[] }>);

    if (passWithAny) {
      /* allow if any action is matched */
      return actions.some((action) => {
        return permissionsWithActions.some((permissionWithActions) => {
          return (
            permissionWithActions.actions.includes(action) &&
            (teams && action !== Actions.ManageTeams
              ? teams.every((teamId) => permissionWithActions.teams.includes(teamId))
              : true)
          );
        });
      });
    }

    return actions.every((action) => {
      /* allow if all actions are matched */
      return permissionsWithActions.some((permissionWithActions) => {
        return (
          permissionWithActions.actions.includes(action) &&
          (teams && action !== Actions.ManageTeams
            ? teams.every((teamId) => permissionWithActions.teams.includes(teamId))
            : true)
        );
      });
    });
  },

  isOwnerType: (permission: Permission): permission is OwnerPermission => {
    return permission.type === Type.Owner;
  },

  hasOwnerPermission: (permissions: Permission[]) => {
    return permissions.some(Permission.isOwnerType);
  },

  toDemoOwnerInstance: (
    organizationId = UUID.generate<OrganizationId>(),
    profileId = UUID.generate<ProfileId>(),
    id = UUID.generate<PermissionId>(),
  ): OwnerPermission => ({
    id,
    organizationId,
    profileId,
    type: Type.Owner,
  }),

  toDemoCustomInstance: (
    roleId = UUID.generate<RoleId>(),
    organizationId = UUID.generate<OrganizationId>(),
    profileId = UUID.generate<ProfileId>(),
    id = UUID.generate<PermissionId>(),
    teamIds: TeamId[] = [],
  ): CustomPermission => ({
    id,
    organizationId,
    profileId,
    type: Type.Custom,
    roleId,
    teamIds,
  }),
};
