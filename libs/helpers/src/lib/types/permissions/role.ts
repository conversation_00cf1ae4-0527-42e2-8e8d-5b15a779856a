import { z } from "zod";

import { zodToDomain, zodToDomainOrThrow } from "../../misc";
import { OrganizationId } from "../organization";
import { CustomArray, CustomEnum, ParsingError, StringOfLength, Tagged, UUID } from "../shared";
import { Actions } from "./action";

export type RoleId = Tagged<"Permissions/RoleId", UUID>;

const parser = z.object({
  id: UUID.parser<RoleId>(),
  organizationId: UUID.parser<OrganizationId>(),

  name: StringOfLength.parser(1, 100),
  description: StringOfLength.parser(0, 500),

  actions: CustomArray.uniqueNonEmpty<Actions>(CustomEnum.parser(Actions)),
});

export type Role = z.infer<typeof parser>;

const saveDtoParser = parser.omit({ id: true });

export type SaveDto = z.infer<typeof saveDtoParser>;

export const Role = {
  parser,
  idParser: parser.shape.id,

  saveDtoParser,
  toSaveDto: (data: unknown) => zodToDomain(saveDtoParser, data),

  create: (dto: SaveDto, createId = () => UUID.generate<RoleId>()): Role => {
    return {
      id: createId(),
      ...dto,
    };
  },

  update: (role: Role, dto: SaveDto): Role => {
    return {
      ...role,
      ...dto,
    };
  },

  toEntity: (source: unknown): Role | ParsingError => zodToDomain(parser, source),
  toEntityOrThrow: (source: unknown) => zodToDomainOrThrow(parser, source),

  toEntities: (source: unknown): Role[] | ParsingError => zodToDomain(z.array(parser), source),

  toCoachDemoInstance: (): Role =>
    parser.parse({
      id: UUID.generate<RoleId>(),
      organizationId: UUID.generate<OrganizationId>(),

      name: "Coach",
      description: "Club coaches",

      actions: [Actions.ManageTeams, Actions.ManageEvents, Actions.ManageReviews],
    }),

  toAdminDemoInstance: (): Role =>
    parser.parse({
      id: UUID.generate<RoleId>(),
      organizationId: UUID.generate<OrganizationId>(),

      name: "Club Admin",
      description: "Club administrators",

      actions: [Actions.ManageOrganization, Actions.ManageUsers, Actions.ManagePlayers],
    }),
};
