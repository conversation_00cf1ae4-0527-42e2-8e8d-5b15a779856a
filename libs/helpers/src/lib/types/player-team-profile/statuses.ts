import { omit } from "lodash/fp";

export const PlayerTeamStatus = {
  ApplicantOrganization: "applicant-organization",
  ApplicantTeam: "applicant-team",
  TrialistInvited: "trialist-invited",
  Trialist: "trialist",
  TrialistUnsuccessful: "trialist-unsuccessful",
  RegistrationPending: "registration-pending",
  RegisteredMatches: "registered-matches",
  RegisteredTraining: "registered-training",
  Relocated: "relocated",
  Left: "left",
  RemovedTeam: "removed-team",
  RemovedOrganization: "removed-organization",
} as const;

export const PlayerTeamStatusWithTeam = omit("ApplicantOrganization", PlayerTeamStatus);

type PlayerTeamStatusKeys = keyof typeof PlayerTeamStatus;

export type PlayerTeamStatus = typeof PlayerTeamStatus[PlayerTeamStatusKeys];

export type PlayerTeamStatusWithTeam = Exclude<PlayerTeamStatus, "applicant-organization">;

export const toDisplayStatus = {
  "applicant-organization": "Organization applicant",
  "applicant-team": "Team applicant",
  "trialist-invited": "Invited trialist",
  trialist: "Trialist",
  "trialist-unsuccessful": "Unsuccessful trialist",
  "registration-pending": "Pending registration",
  "registered-matches": "Registered for matches",
  "registered-training": "Training only",
  relocated: "Relocated to another team",
  left: "Left the team",
  "removed-team": "Removed from team",
  "removed-organization": "Removed from organization",
} as const;
