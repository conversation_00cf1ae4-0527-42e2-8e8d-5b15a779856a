import { omit } from "lodash/fp";
import { z } from "zod";

import { zodToDomain } from "../../misc";
import { OrganizationId } from "../organization";
import { ProfileId } from "../profile";
import { Tagged, UUID, GenericRecord, CustomDate, ParsingError, CustomEnum } from "../shared";
import { TeamId } from "../team";
import { PlayerTeamProfileId } from "../player-team-profile";
import { PlayerId } from "../player";
import { CurrencyLowerCase } from "../stripe";

export type Id = Tagged<"PaymentId", UUID>;

export enum PaymentReason {
  Subscription = "subscription",
  Penalty = "penalty",
  Materials = "materials",
  Event = "event",
  Repayment = "repayment",
}

export enum PaymentStatus {
  Pending = "pending",
  Initiated = "initiated",
  Completed = "completed",
  Failed = "failed",
}

const BaseParser = z.object({
  organizationId: UUID.parser<OrganizationId>(),
  teamId: UUID.parser<TeamId>(),
  teamName: z.string().optional(),
  playerId: UUID.parser<PlayerId>(),
  playerTeamProfileId: UUID.parser<PlayerTeamProfileId>(),
  reason: CustomEnum.parser(PaymentReason),
  amount: z.number().positive(),
  currency: CustomEnum.parser(CurrencyLowerCase),
  stripePriceId: z.string().optional(),
  stripeProductId: z.string().optional(),
  stripeCheckoutSessionId: z.string().optional(),
  stripeCustomerId: z.string().optional(),
  stripeSubscriptionId: z.string().optional(),
  status: CustomEnum.parser(PaymentStatus),
});

const Parser = BaseParser.merge(
  z.object({
    id: UUID.parser<Id>(),
  }),
);

const DeletedParser = Parser.merge(
  z.object({
    deletedBy: UUID.parser<ProfileId>(),
    deletedAt: CustomDate.validParser(),
  }),
);

export type Entity = z.infer<typeof Parser>;

export type PaymentRequestDeleted = z.infer<typeof DeletedParser>;

export type CreateDto = z.infer<typeof BaseParser>;

const updateDtoParser = BaseParser.merge(Parser.pick({ id: true }));

export type UpdateDto = z.infer<typeof updateDtoParser>;

export type UpsertDto = CreateDto | UpdateDto;

export const Entity = {
  create: (dto: CreateDto, createId = () => UUID.generate<Id>()): Entity => {
    return {
      id: createId(),
      organizationId: dto.organizationId,
      teamId: dto.teamId,
      teamName: dto.teamName,
      playerTeamProfileId: dto.playerTeamProfileId,
      reason: dto.reason,
      playerId: dto.playerId,
      amount: dto.amount,
      currency: dto.currency,
      stripePriceId: dto.stripePriceId,
      stripeProductId: dto.stripeProductId,
      stripeCheckoutSessionId: dto.stripeCheckoutSessionId,
      status: PaymentStatus.Pending,
      stripeCustomerId: dto.stripeCustomerId,
      stripeSubscriptionId: dto.stripeSubscriptionId,
    };
  },

  createDeleted: (
    dto: Entity,
    modifier: ProfileId,
    now = CustomDate.now(),
  ): PaymentRequestDeleted => {
    return {
      ...dto,
      deletedBy: modifier,
      deletedAt: now,
    };
  },

  /* used in tests */
  parse: (dto: GenericRecord) => Parser.parse(dto),

  toEntity: (source: GenericRecord) => zodToDomain(Parser, source),

  toEntities: (source: GenericRecord[]) => zodToDomain(z.array(Parser), source),

  toCreateDto: (source: unknown) => zodToDomain(BaseParser, source),

  toUpdateDto: (source: unknown) => zodToDomain(updateDtoParser, source),

  toUpsertDto: (source: unknown) => zodToDomain(z.union([updateDtoParser, BaseParser]), source),

  update: (dto: UpdateDto, payment: Entity): Entity => {
    return {
      ...payment,
      ...omit("id", dto),
    };
  },

  updateDto: updateDtoParser,
  createDto: BaseParser,

  parseId: Parser.shape.id,

  toPaymentId: (source: unknown) => {
    const parsed = UUID.parser<Id>().safeParse(source);

    return parsed.success ? parsed.data : new ParsingError(parsed.error);
  },

  duplicatePaymentExists: (existingPayments: Entity[], newPayment: Entity) =>
    existingPayments?.some(
      (existingPayment) =>
        existingPayment.reason === PaymentReason.Subscription &&
        existingPayment.playerTeamProfileId === newPayment.playerTeamProfileId &&
        existingPayment.teamId === newPayment.teamId,
    ),

  toDemoInstance: (
    id: Id = UUID.generate<Id>(),
    organizationId: OrganizationId = UUID.generate<OrganizationId>(),
  ): Entity => ({
    id,
    organizationId,
    teamId: UUID.generate<TeamId>(),
    playerTeamProfileId: UUID.generate<PlayerTeamProfileId>(),
    reason: PaymentReason.Subscription,
    playerId: UUID.generate<PlayerId>(),
    amount: 100,
    currency: CurrencyLowerCase.EUR,
    stripeProductId: "prod_P1234567890",
    stripePriceId: "price_P1234567890",
    stripeCheckoutSessionId: "cs_P1234567890",
    status: PaymentStatus.Pending,
    stripeCustomerId: "cus_P1234567890",
    stripeSubscriptionId: "sub_P1234567890",
  }),
};
