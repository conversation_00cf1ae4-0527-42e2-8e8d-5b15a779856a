import { z } from "zod";
import { faker } from "@faker-js/faker";

import { zodToDomain } from "../../misc";
import { OrganizationId } from "../organization";
import { CustomEnum, StringOfLength, Tagged, UUID } from "../shared";
import { CoachUserId } from "../user";

export type EmailTemplateId = Tagged<"EmailTemplateId", UUID>;

export enum EmailTemplateVisibility {
  Private = "private",
  Company = "company",
}

const idParser = z.object({
  id: UUID.parser<EmailTemplateId>(),
});

const baseParser = z.object({
  name: StringOfLength.parser(1, 100),
  body: StringOfLength.parser(1, 5000).optional(),
  subject: StringOfLength.parser(1, 100).optional(),
  replyTo: StringOfLength.parser(1, 100).optional(),
  visibility: CustomEnum.parser(EmailTemplateVisibility),
  ownerId: UUID.parser<CoachUserId>(),
  organizationId: UUID.parser<OrganizationId>(),
});

const parser = baseParser.merge(idParser);

export type EmailTemplate = z.infer<typeof parser>;
export type CreateEmailTemplateDto = z.infer<typeof baseParser>;

export const EmailTemplate = {
  parser,
  createParser: baseParser,

  create(
    dto: CreateEmailTemplateDto,

    id = UUID.generate<EmailTemplateId>(),
  ): EmailTemplate {
    return {
      ...dto,
      id,
    };
  },

  update(existing: EmailTemplate, dto: CreateEmailTemplateDto): EmailTemplate {
    return {
      ...existing,
      ...dto,
    };
  },

  toCreateDto: (source: unknown) => zodToDomain(baseParser, source),

  toUpsertDto: (source: unknown) => {
    const upsertParser = z.union([parser, baseParser]);
    return zodToDomain(upsertParser, source);
  },

  toEntity: (source: unknown) => zodToDomain(parser, source),

  toEntities: (source: unknown) => zodToDomain(z.array(parser), source),

  toDemoInstance: (
    visibility = EmailTemplateVisibility.Company,
    id: EmailTemplateId = UUID.generate<EmailTemplateId>(),
    organizationId: OrganizationId = UUID.generate<OrganizationId>(),
  ): EmailTemplate => ({
    id,
    organizationId,
    name: faker.commerce.department() as EmailTemplate["name"],
    body: faker.lorem.sentences(4) as EmailTemplate["body"],
    subject: faker.company.catchPhrase() as EmailTemplate["subject"],
    replyTo: "<EMAIL>" as EmailTemplate["replyTo"],
    visibility,
    ownerId: UUID.generate<CoachUserId>(),
  }),
};
