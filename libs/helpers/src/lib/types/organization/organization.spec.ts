import { isError } from "lodash";

import { ParsingError, UUID } from "../shared";
import { breakTest } from "../../test";
import { Organization } from "./organization";

describe("Organization entity", () => {
  describe(Organization.toEntity.name, () => {
    const validPayload = {
      name: "Kinja",
      displayName: "Kinja",
      slug: "kinja",
      id: UUID.generate(),
      members: [],
      contactEmail: "<EMAIL>",
      senderEmail: "<EMAIL>",
    };

    it("returns an Organization", () => {
      const result = Organization.toEntity(validPayload);
      expect(result).toEqual(validPayload);
    });

    it("returns a ParsingError when the name missing", () => {
      const result = Organization.toEntity({ ...validPayload, name: undefined });

      if (!isError(result)) {
        return breakTest();
      }
      expect(result).toBeInstanceOf(ParsingError);
      expect(result.errors["name"]).toBeDefined();
    });

    it("returns a ParsingError when the name is too short", () => {
      const result = Organization.toEntity({ ...validPayload, name: "a" });

      if (!isError(result)) {
        return breakTest();
      }
      expect(result).toBeInstanceOf(ParsingError);
      expect(result.errors["name"]).toBeDefined();
    });

    it("returns a ParsingError when the name is too long", () => {
      const result = Organization.toEntity({
        ...validPayload,
        name: new Array(101).fill("a").join(""),
      });

      if (!isError(result)) {
        return breakTest();
      }
      expect(result).toBeInstanceOf(ParsingError);
      expect(result.errors["name"]).toBeDefined();
    });

    it("returns a ParsingError when the id is missing", () => {
      const result = Organization.toEntity({
        ...validPayload,
        id: undefined,
      });

      if (!isError(result)) {
        return breakTest();
      }
      expect(result).toBeInstanceOf(ParsingError);
      expect(result.errors["id"]).toBeDefined();
    });

    it("returns a ParsingError when the id is invalid", () => {
      const result = Organization.toEntity({
        ...validPayload,
        id: "not a uuid",
      });

      if (!isError(result)) {
        return breakTest();
      }
      expect(result).toBeInstanceOf(ParsingError);
      expect(result.errors["id"]).toBeDefined();
    });
  });
});
