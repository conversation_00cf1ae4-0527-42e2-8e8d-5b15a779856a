import { z } from "zod";
import { kebabCase } from "lodash/fp";
import { faker } from "@faker-js/faker";

import { zodToDomain, zodToDomainOrThrow } from "../../misc";
import { ProfileId } from "../profile";
import {
  Tagged,
  UUID,
  StringOfLength,
  GenericRecord,
  CustomArray,
  ParsingError,
  UUIDErrors,
  Email,
} from "../shared";

export type OrganizationId = Tagged<"OrganizationId", UUID>;

export type OrganizationSlug = Tagged<"OrganizationSlug", string>;

const slugOptionalParser = StringOfLength.parser(2, 100)
  .transform((data) => data as unknown as OrganizationSlug)
  .optional();

const slugParser = StringOfLength.parser(2, 100).transform(
  (data) => data as unknown as OrganizationSlug,
);

const organizationBaseParser = z.object({
  name: StringOfLength.parser(2, 100),
  displayName: StringOfLength.parser(2, 100),
  slug: slugParser,
  privacyPolicy: StringOfLength.parser(5, 50000).optional(),
  contactEmail: Email.parser,
  senderEmail: Email.parser,
});

const applicationsParser = z.object({
  open: z.boolean().default(false),
  headline: StringOfLength.parser(0, 200),
  description: StringOfLength.parser(0, 1000),
  closedMessage: StringOfLength.parser(0, 500),
  successMessage: StringOfLength.parser(0, 500),
});

const organizationParser = organizationBaseParser.merge(
  z.object({
    id: UUID.parser<OrganizationId>(),
    members: CustomArray.arrayParser(UUID.parser<ProfileId>()),
    applications: applicationsParser.optional(),
  }),
);

const publicParser = organizationParser.omit({ members: true });

const createOrganizationParser = organizationBaseParser
  .omit({ slug: true, displayName: true })
  .merge(
    z.object({
      slug: slugOptionalParser.optional(),
    }),
  );

const updateOrganizationParser = organizationBaseParser.merge(
  z.object({
    applications: applicationsParser,
  }),
);

export type UpdateOrganizationDto = z.infer<typeof updateOrganizationParser>;

export type Organization = z.infer<typeof organizationParser>;

export type PublicOrganization = z.infer<typeof publicParser> & {
  readonly __PublicOrganization: true;
};

export type CreateOrganizationDto = z.infer<typeof createOrganizationParser>;

export const Organization = {
  create: (
    dto: CreateOrganizationDto,
    createId = () => UUID.generate<OrganizationId>(),
  ): Organization => {
    return {
      id: createId(),
      name: dto.name,
      slug: dto.slug || (kebabCase(dto.name) as OrganizationSlug),
      displayName: dto.name,
      contactEmail: dto.contactEmail,
      senderEmail: dto.senderEmail,
      members: [],
    };
  },

  update: (dto: UpdateOrganizationDto, organization: Organization): Organization => {
    return {
      ...organization,
      ...dto,
    };
  },

  addMember: (organization: Organization, memberId: ProfileId): Organization => {
    const newMembers = [...organization.members.filter((elem) => elem !== memberId), memberId];

    return {
      ...organization,
      members: newMembers,
    };
  },

  /* used in tests */
  parse: (dto: GenericRecord) => organizationParser.parse(dto),

  fullParser: organizationParser,

  toEntity: (source: GenericRecord) => zodToDomain(organizationParser, source),
  toEntityOrThrow: (source: unknown) => zodToDomainOrThrow(organizationParser, source),

  toEntities: (source: unknown) => {
    const result = z.array(organizationParser).safeParse(source);

    if (result.success) {
      return result.data;
    }

    return new ParsingError(result.error);
  },

  toPublic: (organization: Organization): PublicOrganization => {
    return {
      name: organization.name,
      displayName: organization.displayName,
      id: organization.id,
      slug: organization.slug,
      applications: organization.applications,
      privacyPolicy: organization.privacyPolicy,
      contactEmail: organization.contactEmail,
      senderEmail: organization.senderEmail,
    } as PublicOrganization;
  },

  toPublicFromUnknown: (source: unknown) => zodToDomain(publicParser, source),
  publicParser,

  createDto: createOrganizationParser,
  updateDto: updateOrganizationParser,

  parseSlug: (source: unknown) => {
    const result = organizationParser.shape.slug.safeParse(source);

    if (result.success) {
      return result.data;
    }

    return new ParsingError(result.error);
  },

  parseUpdateDto: (source: unknown) => {
    const result = updateOrganizationParser.safeParse(source);

    if (result.success) {
      return result.data;
    }

    return new ParsingError(result.error);
  },

  parseId: organizationParser.shape.id,

  toOrganizationId: (source: unknown) => {
    const parsed = organizationParser.shape.id.safeParse(source);
    return parsed.success
      ? parsed.data
      : new ParsingError({ organizationId: UUIDErrors.InvalidUUID });
  },

  toDemoInstance: (
    dto?: CreateOrganizationDto,
    id: OrganizationId = UUID.generate<OrganizationId>(),
  ) => {
    const clubName = faker.company.name() + " FC";

    return {
      id,
      name: dto?.name || (clubName as StringOfLength<2, 100>),
      slug: dto?.slug || (kebabCase(clubName) as OrganizationSlug),
      displayName: dto?.name || (clubName as StringOfLength<2, 100>),
      contactEmail: "<EMAIL>" as Email,
      senderEmail: "<EMAIL>" as Email,
      members: [],
      applications: {
        closedMessage: "Sorry, closed" as StringOfLength<0, 500>,
        description: "We will rock you!" as StringOfLength<0, 1000>,
        headline: `Welcome to ${clubName}` as StringOfLength<0, 200>,
        open: true,
        successMessage: "Success!" as StringOfLength<0, 500>,
      },
      privacyPolicy: "We will protect your data!" as StringOfLength<5, 50000>,
    } as Organization;
  },

  toPublicDemoInstance: (
    dto?: CreateOrganizationDto,
    id: OrganizationId = UUID.generate<OrganizationId>(),
  ) => {
    const clubName = faker.company.name() + " FC";

    return {
      id,
      name: dto?.name || (clubName as StringOfLength<2, 100>),
      slug: dto?.slug || (kebabCase(clubName) as OrganizationSlug),
      displayName: dto?.name || (clubName as StringOfLength<2, 100>),
      contactEmail: "<EMAIL>" as Email,
      senderEmail: "<EMAIL>" as Email,

      applications: {
        closedMessage: "Sorry, closed" as StringOfLength<0, 500>,
        description: "We will rock you!" as StringOfLength<0, 1000>,
        headline: `Welcome to ${clubName}` as StringOfLength<0, 200>,
        open: true,
        successMessage: "Success!" as StringOfLength<0, 500>,
      },
      privacyPolicy: "We will protect your data!" as StringOfLength<5, 50000>,
    } as PublicOrganization;
  },
};
