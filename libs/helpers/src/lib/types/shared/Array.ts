import { z, ZodEffects, Zod<PERSON><PERSON>, ZodRaw<PERSON>hape, ZodType<PERSON>ny } from "zod";

import { Tagged } from "./Tagged";

export type NonEmptyArray<T> = [T, ...T[]];

export type NonEmptyUniqueArray<T> = Tagged<"UniqueNonEmptyArray", NonEmptyArray<T>>;

export type CustomArray<T> = Array<T>;

enum Errors {
  Required = "Required",
  Invalid = "Invalid",
  Empty = "Empty",
}

const arrayParser = <T>(parseItem: ZodEffects<ZodTypeAny, T, unknown> | ZodObject<ZodRawShape>) =>
  z
    .array(parseItem, { required_error: Errors.Required, invalid_type_error: Errors.Invalid })
    .transform((res) => res as CustomArray<T>);

const nonEmpty = <T>(parseItem: ZodEffects<ZodTypeAny, T, unknown> | ZodObject<ZodRawShape>) =>
  z
    .array(parseItem, { required_error: Errors.Required, invalid_type_error: Errors.Invalid })
    .nonempty({ message: Errors.Empty })
    .transform((res) => res as NonEmptyArray<T>);

const uniqueNonEmpty = <T>(...args: Parameters<typeof z.array>) =>
  z
    .array(args[0], { required_error: Errors.Required, invalid_type_error: Errors.Invalid })
    .nonempty({ message: Errors.Empty })
    .transform((res) => Array.from(new Set(res)) as NonEmptyUniqueArray<T>);

export const CustomArray = { nonEmpty, uniqueNonEmpty, arrayParser, Errors };
