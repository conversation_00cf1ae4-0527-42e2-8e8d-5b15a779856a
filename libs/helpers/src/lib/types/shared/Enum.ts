import { z } from "zod";

export enum CustomEnumErrors {
  Required = "Required",
  InvalidValue = "InvalidValue",
}

type EnumLike = {
  [k: string]: string | number;
  [nu: number]: string;
};

const parser = <T extends EnumLike>(data: T) =>
  z.nativeEnum(data, {
    required_error: CustomEnumErrors.Required,
    invalid_type_error: CustomEnumErrors.InvalidValue,
  });

export const CustomEnum = { parser };
