import { add, addDays, sub } from "date-fns";

import { CustomDate } from "./Date";

describe("CustomDate", () => {
  describe(CustomDate.validParser.name, () => {
    it("works with a valid Date object", () => {
      const date = new Date();
      const result = CustomDate.validParser().safeParse(date);

      expect(result.success).toBe(true);

      if (result.success) {
        expect(result.data).toStrictEqual(date);
      }
    });

    it("parses date differences", () => {
      const date = new Date();
      const incorrectDate = addDays(date, 1);
      const result = CustomDate.validParser().safeParse(incorrectDate);

      expect(result.success).toBe(true);

      if (result.success) {
        expect(result.data).not.toStrictEqual(date);
      }
    });

    it("removes time portion", () => {
      const dateString = "Tue, 07 Mar 2023 09:05:11 GMT";
      const date = new Date(dateString);
      const hoursBeforeParsing = date.getUTCHours();

      expect(hoursBeforeParsing).toBe(9);

      const result = CustomDate.validParser({ stripTime: true }).safeParse(dateString);

      expect(result.success).toBe(true);

      if (result.success) {
        expect(result.data.getHours()).toBe(0);
        expect(result.data.getMinutes()).toBe(0);
      }
    });

    it("breaks with invalid dates", () => {
      const result = CustomDate.validParser().safeParse(new Date("This will be invalid"));

      expect(result.success).toBe(false);
    });

    it("breaks when value is missing", () => {
      const result = CustomDate.validParser().safeParse(undefined);

      expect(result.success).toBe(false);
    });

    it("can be optional", () => {
      const result = CustomDate.validParser().optional().safeParse(undefined);

      expect(result.success).toBe(true);
    });

    it("works with a valid Date string", () => {
      const date = new Date();
      const result = CustomDate.validParser().safeParse(date.toISOString());

      expect(result.success).toBe(true);

      if (result.success) {
        expect(result.data).toStrictEqual(date);
      }
    });

    it("breaks with invalid date strings", () => {
      const result = CustomDate.validParser().safeParse("not a date string");

      expect(result.success).toBe(false);
    });

    it("works with a valid Date number", () => {
      const date = new Date();
      const result = CustomDate.validParser().safeParse(date.getTime());

      expect(result.success).toBe(true);

      if (result.success) {
        expect(result.data).toStrictEqual(date);
      }
    });
  });

  describe(CustomDate.dobParser.name, () => {
    it("works with a valid past Date object", () => {
      const date = new Date();
      const pastDate = sub(date, { years: 15 });

      const result = CustomDate.dobParser(5, 20).safeParse(pastDate);

      expect(result.success).toBe(true);

      if (result.success) {
        pastDate.setHours(0, 0, 0, 0);
        expect(result.data).toStrictEqual(pastDate);
      }
    });

    it("fails with invalid Date object", () => {
      const result = CustomDate.dobParser(5, 20).safeParse(new Date("Invalid"));

      expect(result.success).toBe(false);
    });

    it("fails with invalid Date string", () => {
      const result = CustomDate.dobParser(5, 20).safeParse("Invalid");

      expect(result.success).toBe(false);
    });

    it("fails with future Date", () => {
      const futureDate = add(new Date(), { years: 1 });
      const result = CustomDate.dobParser(5, 20).safeParse(futureDate);

      expect(result.success).toBe(false);
    });

    it("fails with current Date", () => {
      const result = CustomDate.dobParser(5, 20).safeParse(new Date());

      expect(result.success).toBe(false);
    });

    it("fails with too recent past Date", () => {
      const futureDate = sub(new Date(), { years: 1 });
      const result = CustomDate.dobParser(5, 20).safeParse(futureDate);

      expect(result.success).toBe(false);
    });

    it("fails with too distant past Date", () => {
      const futureDate = sub(new Date(), { years: 21 });
      const result = CustomDate.dobParser(5, 20).safeParse(futureDate);

      expect(result.success).toBe(false);
    });
  });
});
