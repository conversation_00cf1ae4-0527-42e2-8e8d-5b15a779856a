import { z } from "zod";

import { UnknownZodType, zodToDomain } from "../../misc";
import { ParsingError } from "./Error";

export const pagingParamsParser = z.object({
  limit: z.preprocess((value) => Number(value), z.number()),
  skip: z.preprocess((value) => Number(value), z.number()),
});

export const paginatedResponseParser = <T>(schema: UnknownZodType<T>) =>
  pagingParamsParser.merge(
    z.object({
      total: z.number(),
      data: schema,
    }),
  );

export const toPagingParams = (source: unknown): ParsingError | PagingParams =>
  zodToDomain(pagingParamsParser, source);

export type PagingParams = z.infer<typeof pagingParamsParser>;

export type PaginatedData<T> = {
  limit: number;
  skip: number;
  total: number;
  data: T[];
};
