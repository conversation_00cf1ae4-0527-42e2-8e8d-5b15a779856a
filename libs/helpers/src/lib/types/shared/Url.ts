import { z } from "zod";

import { zodToDomain } from "../../misc";

export type Type = string & {
  readonly __URL: true;
};

export enum Errors {
  Required = "Required",
  NotAString = "NotAString",
  NotAUrl = "NotAUrl",
}

export const parser = z
  .string({
    required_error: Errors.Required,
    invalid_type_error: Errors.NotAString,
  })
  .url(Errors.NotAUrl)
  .transform((data) => data as Type);

export const toUrl = (source: unknown) => zodToDomain(parser, source);
