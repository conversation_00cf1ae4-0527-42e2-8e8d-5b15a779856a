import { isString } from "lodash/fp";
import { z, ZodIssueCode } from "zod";

export const PasswordRules = { min: 8, max: 50, letters: 1, digits: 1 } as const;

export type Password = string & { readonly __CustomPassword: true } & typeof PasswordRules;

export type HashedPassword = string & { readonly __HashedPassword: true } & typeof PasswordRules;

export enum PasswordErrors {
  NotAString = "NotAString",
  TooShort = "TooShort",
  TooLong = "TooLong",
  MinLetters = "MinLettersError",
  MinDigits = "MinDigitsError",
}

const minLettersRegex = new RegExp(`.*[a-zA-Z]{${PasswordRules.letters}}.*`);
const minDigitsRegex = new RegExp(`.*[0-9]{${PasswordRules.digits}}.*`);

const parser = z
  .string({ invalid_type_error: PasswordErrors.NotAString })
  .min(PasswordRules.min, { message: PasswordErrors.TooShort })
  .max(PasswordRules.max, { message: PasswordErrors.TooLong })
  .superRefine((value, ctx) => {
    const trimmed = value.trim();

    const correctLetters = minLettersRegex.test(trimmed);

    if (!correctLetters) {
      ctx.addIssue({
        message: PasswordErrors.MinLetters,
        code: ZodIssueCode.custom,
      });
    }
  })
  .superRefine((value, ctx) => {
    const trimmed = value.trim();
    const correctDigits = !!trimmed && minDigitsRegex.test(trimmed);

    if (!correctDigits) {
      ctx.addIssue({
        message: PasswordErrors.MinDigits,
        code: ZodIssueCode.custom,
      });
    }
  })
  .transform((x) => x as Password);

/* the zod Password parser isn't able to report all failures at once */
const validateAllCriteria = (source: unknown): PasswordErrors[] => {
  const isValidString = isString(source);
  const trimmed = isString(source) ? source.trim() : "";

  const issues = [];

  const isTooShort = trimmed.length <= PasswordRules.min;
  const isTooLong = trimmed.length >= PasswordRules.max;

  const minLettersError = !minLettersRegex.test(trimmed);
  const minDigitsError = !minDigitsRegex.test(trimmed);

  if (!isValidString) {
    issues.push(PasswordErrors.NotAString);
  }

  if (isTooShort) {
    issues.push(PasswordErrors.TooShort);
  }

  if (isTooLong) {
    issues.push(PasswordErrors.TooLong);
  }

  if (minLettersError) {
    issues.push(PasswordErrors.MinLetters);
  }

  if (minDigitsError) {
    issues.push(PasswordErrors.MinDigits);
  }

  return issues;
};

const hashedParser = z.string().transform((x) => x as HashedPassword);

export const Password = { parser, validateAllCriteria };

export const HashedPassword = { parser: hashedParser };
