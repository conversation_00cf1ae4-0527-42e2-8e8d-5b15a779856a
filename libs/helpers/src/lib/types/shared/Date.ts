import { z } from "zod";
import {
  isPast,
  isFuture,
  isValid,
  addDays,
  isEqual,
  subDays,
  subYears,
  isAfter,
  isDate,
  differenceInYears,
  intervalToDuration,
  format,
  addMinutes,
  addWeeks,
  startOfDay,
  isBefore,
  nextFriday,
  nextThursday,
  nextWednesday,
  nextTuesday,
  nextMonday,
  nextSaturday,
  nextSunday,
  addMonths,
  subMinutes,
  getDaysInMonth,
} from "date-fns";
import { isNumber, isString } from "lodash/fp";

import { Tagged } from "./Tagged";
import { CustomNumber } from "./Number";
import { isError, ParsingError } from "./Error";

export const WITHIN_RANGE = "within";
export const ABOVE_RANGE = "above";
export const BELOW_RANGE = "below";

export type PastDate = Tagged<"PastDate", Date>;

export type FutureDate = Tagged<"FutureDate", Date>;

export type PresentDate = Tagged<"PresentDate", Date>;

export type ValidDate = PresentDate | PastDate | FutureDate;

export type ValidTime = Tagged<"ValidTime", Date>;

/* starts with Sunday because that's how JS Dates work; do not change the order */
export const DayOfWeek = [
  "sunday",
  "monday",
  "tuesday",
  "wednesday",
  "thursday",
  "friday",
  "saturday",
] as const;

const isNotEmpty = (input: unknown) => !!input;

const isValidDate = (input: unknown): input is Date => {
  if (isDate(input)) {
    return isValid(input);
  }
  if (isString(input)) {
    return isValid(new Date(input));
  }
  if (isNumber(input)) {
    return isValid(new Date(input));
  }
  return false;
};

const isPresentDate = (input: unknown): input is PresentDate =>
  isValidDate(input) && isEqual(new Date(input), new Date());

const isPastDate = (input: unknown): input is PastDate =>
  isValidDate(input) && isPast(new Date(input));

const isFutureDate = (input: unknown): input is FutureDate =>
  isValidDate(input) && isFuture(new Date(input));

const isPastOrPresentDate = (input: unknown): input is PresentDate | PastDate =>
  isValidDate(input) && (isPast(new Date(input)) || isPresentDate(new Date(input)));

export enum DateErrors {
  Required = "Required",
  InvalidDate = "InvalidDate",
  InvalidTime = "InvalidTime",
  NotPast = "NotPast",
  NotFuture = "NotFuture",
  NotPresent = "NotPresent",
  NotPastOrPresent = "NotPastOrPresent",
  TooSmall = "TooSmall",
  TooBig = "TooBig",
}

type ParseParams = {
  stripTime?: boolean;
};

const validDateParser = (params?: ParseParams) =>
  z
    .unknown({ required_error: DateErrors.Required })
    .refine(isValidDate, { message: DateErrors.InvalidDate })
    .transform(
      (date) => new Date(params?.stripTime ? new Date(date).toDateString() : date) as ValidDate,
    );

/* 
We store times as Dates in order to preserve timezones.
Then in the code we only use the time portion of the Date
*/
const validTimeParser = z
  .unknown({ required_error: DateErrors.Required })
  .refine(isValidDate, { message: DateErrors.InvalidTime })
  .transform((date) => new Date(date) as ValidTime);

const pastDateParser = (params?: ParseParams) =>
  z
    .unknown({ required_error: DateErrors.Required })
    .refine(isValidDate, {
      message: DateErrors.InvalidDate,
    })
    .refine(isPastDate, {
      message: DateErrors.NotPast,
    })
    .transform(
      (date) => new Date(params?.stripTime ? new Date(date).toDateString() : date) as PastDate,
    );

const futureDateParser = z
  .custom(isNotEmpty, {
    message: DateErrors.Required,
  })
  .refine(isValidDate, {
    message: DateErrors.InvalidDate,
  })
  .refine(isFutureDate, {
    message: DateErrors.NotFuture,
  })
  .transform((date) => new Date(date) as FutureDate);

const presentDateParser = z
  .custom(isPresentDate, {
    message: DateErrors.InvalidDate,
  })
  .refine(isValidDate, {
    message: DateErrors.InvalidDate,
  })
  .refine(isNotEmpty, {
    message: DateErrors.Required,
  })
  .transform((date) => new Date(date) as PresentDate);

const pastOrPresentParser = z
  .custom(isValidDate, {
    message: DateErrors.InvalidDate,
  })
  .refine(isPastOrPresentDate, {
    message: "Invalid Past or Present Date",
  })
  .refine(isNotEmpty, {
    message: DateErrors.Required,
  })
  .transform((date) => new Date(date) as PastDate | PresentDate);

type Refinement<Min extends number, Max extends number> = Partial<{
  maxYears: Max;
  minYears: Min;
}>;

const dobParser = <Min extends number, Max extends number>(min: Min, max: Max) =>
  pastDateParser({ stripTime: true })
    .refine(
      (date) => {
        const yearsAgo = differenceInYears(new Date(), new Date(date));
        return yearsAgo >= min;
      },
      { message: DateErrors.TooSmall },
    )
    .refine(
      (date) => {
        const yearsAgo = differenceInYears(new Date(), new Date(date));
        return yearsAgo <= max;
      },
      { message: DateErrors.TooBig },
    )
    .transform((date) => date as PastDate & Refinement<Min, Max>);

export const CustomDate = {
  validParser: validDateParser,
  validTimeParser,
  pastParser: pastDateParser,
  dobParser,
  futureParser: futureDateParser,
  presentParser: presentDateParser,
  pastOrPresentParser,
  isValidDate: (date: unknown) => isValidDate(date),
  isPastDate: (date: ValidDate) => isPastDate(date),
  isFutureDate: (date: ValidDate) => isFutureDate(date),
  isPresentDate: (date: ValidDate) => isPresentDate(date),
  isPastOrPresentDate,
  isBefore,
  isEqual,
  startOfDay,
  getDaysInMonth,

  mergeDateTime: (date: Date, dateTime: Date): ValidDate => {
    const newDate = new Date(date);
    newDate.setHours(
      dateTime.getHours(),
      date.getMinutes(),
      date.getSeconds(),
      date.getMilliseconds(),
    );
    return newDate as ValidDate;
  },

  getNextDateFor: (day: typeof DayOfWeek[number], from: ValidDate): ValidDate => {
    switch (day) {
      case DayOfWeek[0]:
        return nextSunday(from) as ValidDate;
      case DayOfWeek[1]:
        return nextMonday(from) as ValidDate;
      case DayOfWeek[2]:
        return nextTuesday(from) as ValidDate;
      case DayOfWeek[3]:
        return nextWednesday(from) as ValidDate;
      case DayOfWeek[4]:
        return nextThursday(from) as ValidDate;
      case DayOfWeek[5]:
        return nextFriday(from) as ValidDate;
      case DayOfWeek[6]:
        return nextSaturday(from) as ValidDate;
    }
  },

  getDayOfWeek: (date: ValidDate) => {
    return DayOfWeek[date.getDay()];
  },

  getAge: (birthday: string | Date | null | undefined) => {
    const asValidDate = CustomDate.validOrEmpty(birthday);

    if (asValidDate) {
      return intervalToDuration({ start: new Date(asValidDate), end: new Date() });
    }
    return -1;
  },

  getYearsAgo: (date: string | Date | null | undefined) => {
    const asValidDate = CustomDate.validOrEmpty(date);

    if (asValidDate) {
      return differenceInYears(new Date(), new Date(asValidDate));
    }
    return -1;
  },

  addDays: (days: number, now = () => CustomDate.now() as ValidDate): FutureDate => {
    const nowAsDate = new Date(now());
    return addDays(nowAsDate, CustomNumber.toPositiveInteger(days)) as FutureDate;
  },

  addWeeks: (weeks: number, now = () => CustomDate.now()): FutureDate => {
    const nowAsDate = new Date(now());
    return addWeeks(nowAsDate, CustomNumber.toPositiveInteger(weeks)) as FutureDate;
  },

  addMonths: (months: number, now = () => CustomDate.now()): ValidDate => {
    const nowAsDate = new Date(now());
    return addMonths(nowAsDate, CustomNumber.toPositiveInteger(months)) as ValidDate;
  },

  addMinutes: (minutes: number, now = () => CustomDate.now() as ValidDate): ValidDate => {
    const nowAsDate = new Date(now());
    return addMinutes(nowAsDate, CustomNumber.toPositiveInteger(minutes)) as ValidDate;
  },

  subDays: (days: number, now = () => CustomDate.now() as ValidDate): PastDate => {
    const nowAsDate = new Date(now());
    return subDays(nowAsDate, CustomNumber.toPositiveInteger(days)) as PastDate;
  },

  subYears: (years: number, now = () => CustomDate.now()): Date => {
    const nowAsDate = new Date(now());
    return subYears(nowAsDate, CustomNumber.toPositiveInteger(years));
  },

  toPastYear: (years: number, now = () => CustomDate.now()): PastDate => {
    const nowAsDate = new Date(now());
    return subYears(nowAsDate, CustomNumber.toPositiveInteger(years)) as PastDate;
  },

  now: (): PresentDate => {
    return new Date() as PresentDate;
  },

  timeNow: (): ValidTime => {
    return new Date() as ValidTime;
  },

  timeAddMinutes: (minutes: number, now = () => CustomDate.timeNow()): ValidTime => {
    const nowAsDate = new Date(now());
    return addMinutes(nowAsDate, CustomNumber.toPositiveInteger(minutes)) as ValidTime;
  },

  timeSubMinutes: (
    minutes: number,
    now = (): ValidDate | ValidTime => CustomDate.timeNow(),
  ): ValidTime => {
    const nowAsDate = new Date(now());
    return subMinutes(nowAsDate, CustomNumber.toPositiveInteger(minutes)) as ValidTime;
  },

  nowStartOfDay: (now = () => CustomDate.now()): ValidDate => {
    const nowAsDate = new Date(now());
    return startOfDay(nowAsDate) as ValidDate;
  },

  validOrEmpty: (date: unknown) => {
    const parsed = validDateParser().safeParse(date);

    return parsed.success ? parsed.data : undefined;
  },

  validOrEmptyTime: (date: unknown) => {
    const parsed = validTimeParser.safeParse(date);

    return parsed.success ? parsed.data : undefined;
  },

  pastOrEmpty: (date: unknown) => {
    const parsed = pastDateParser().safeParse(date);

    return parsed.success ? parsed.data : undefined;
  },

  toValidDate: (date: string | Date | null | undefined | unknown) => {
    const parsed = validDateParser().safeParse(date);

    if (parsed.success) {
      return parsed.data;
    }

    return new ParsingError(parsed.error);
  },

  toDisplayDate: (date: unknown) => {
    const asDate = CustomDate.toValidDate(date);

    if (isError(asDate)) {
      return "";
    }

    return format(asDate, "dd/MM/y");
  },

  toGoCardlessRequestDate: (date: unknown) => {
    const asDate = CustomDate.toValidDate(date);

    if (isError(asDate)) {
      return "";
    }

    return format(asDate, "yyyy-MM-dd");
  },

  toDisplayTime: (date: unknown) => {
    const asDate = CustomDate.toValidDate(date);

    if (isError(asDate)) {
      return "";
    }

    return format(asDate, "HH:mm");
  },

  toDisplayAge: (birthday: string | Date | null | undefined) => {
    const asAge = CustomDate.getAge(birthday);

    if (asAge === -1) {
      return "";
    }

    return asAge.years;
  },

  isAfter: (targetDate: ValidDate, deadline: ValidDate) => {
    return isAfter(targetDate, deadline);
  },

  isWithinRange: (lowerRange: ValidDate, upperRange: ValidDate, date: ValidDate) => {
    const isWithinLower = !isBefore(date, lowerRange);
    const isWithinUpper = !isAfter(date, upperRange);

    if (!isWithinLower) return BELOW_RANGE;
    if (!isWithinUpper) return ABOVE_RANGE;

    return WITHIN_RANGE;
  },
};
