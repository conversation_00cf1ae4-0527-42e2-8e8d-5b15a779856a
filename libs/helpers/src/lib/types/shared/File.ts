import { z } from "zod";

import { NonEmptyArray } from "./Array";
import { Tagged } from "./Tagged";

type FileTypes = Readonly<NonEmptyArray<string>>;

export enum Errors {
  Required = "Required",
  Invalid = "Invalid",
  InvalidSize = "InvalidSize",
  InvalidType = "InvalidType",
}

const rawFileParser = z.object({
  name: z.string(),
  size: z.number(),
  type: z.string(),
  stream: z.any().nullable(),
  arrayBuffer: z.any().nullable(),
});

export type RawFile = z.infer<typeof rawFileParser>;

export type FileSize<T extends number = number> = Readonly<{
  max_bytes: T;
}>;

export type CustomFile<T extends FileTypes, M extends FileSize> = Tagged<
  "File",
  RawFile & Blob & { types: T; max_bytes: M["max_bytes"] }
>;

export const parser = <Types extends FileTypes, <PERSON>ze extends FileSize>(
  size: FileSize,
  fileTypes: Types,
) =>
  z
    .custom<RawFile>(
      (file) => {
        const parsed = rawFileParser.safeParse(file);

        if (!parsed.success) {
          return false;
        }

        /* don't return the parsed result because it's a Blob class with a lot
        of complex fields we don't want to manually replicate. We just assure
        the ones our code cares about are there  */
        return file as RawFile;
      },
      { message: Errors.Required, path: ["file"] },
    )
    .refine(
      (file) => {
        return file.type && fileTypes.includes(file.type);
      },
      {
        message: Errors.InvalidType,
        path: ["type"],
      },
    )
    .refine(
      (file) => {
        return file.size && file.size <= size.max_bytes;
      },
      {
        message: Errors.InvalidSize,
        path: ["size"],
      },
    )
    .transform(
      (data) =>
        data as Omit<CustomFile<Types, Size>, "type" | "size"> & {
          type: Types[number];
          size: Size["max_bytes"];
        } & Blob,
    );
