import { z } from "zod";
import { StringOfLength } from "./String";

export const basePlayerGenders = {
  M: "male",
  F: "female",
  Undisclosed: "undisclosed",
} as const;

export const PlayerGenders = {
  ...basePlayerGenders,
  Other: "other",
} as const;

export type PlayerGenders = typeof PlayerGenders[keyof typeof PlayerGenders];

export const genderParser = z.union([
  z.object({
    name: z.union([
      z.literal(basePlayerGenders.F),
      z.literal(basePlayerGenders.M),
      z.literal(basePlayerGenders.Undisclosed),
    ]),
    description: z.literal(undefined).optional(),
  }),
  z.object({
    name: z.literal(PlayerGenders.Other),
    description: StringOfLength.parser(1, 100),
  }),
]);
