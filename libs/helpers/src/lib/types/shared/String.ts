import { z } from "zod";

import { Tagged } from "./Tagged";

export enum StringErrors {
  Required = "Required",
  NotAString = "NotAString",
  TooShort = "TooShort",
  TooLong = "TooLong",
}

export type StringOfLength<Min extends number, Max extends number, Refinement = ""> = Tagged<
  "StringOfLength",
  {
    min: Min;
    max: Max;
    __kind: Refinement;
  } & string
>;

const parser = <Min extends number, Max extends number, StringId extends string = string>(
  min: Min,
  max: Max,
  id = "" as StringId,
) =>
  z
    .string({
      required_error: StringErrors.Required,
      invalid_type_error: StringErrors.NotAString,
    })
    .min(min, { message: StringErrors.TooShort })
    .max(max, { message: StringErrors.TooLong })
    .transform((result) => result as StringOfLength<Min, Max, typeof id>);

export const StringOfLength = { parser };
