import { z } from "zod";

export type Phone = string & { readonly __CustomPhone: true };

const min = 4;
const max = 50;

export enum PhoneErrors {
  InvalidPhone = "InvalidPhone",
  Required = "Required",
  TooShort = "TooShort",
  TooLong = "TooLong",
}

/* phone validation is hard, let's keep it simple for now */
const parser = z
  .string({
    required_error: PhoneErrors.Required,
    invalid_type_error: PhoneErrors.InvalidPhone,
  })
  .min(min, { message: PhoneErrors.TooShort })
  .max(max, { message: PhoneErrors.TooLong })
  .transform((x) => x as Phone);

export const Phone = { parser };
