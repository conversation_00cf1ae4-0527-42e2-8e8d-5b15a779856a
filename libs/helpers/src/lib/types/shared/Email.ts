import { z } from "zod";

import { ParsingError } from "./Error";
import { StringOfLength } from "./String";
import { zodToDomain } from "../../misc";
import { CustomEnum } from "./Enum";

export type Email = string & { readonly __CustomEmail: true };

export enum EmailErrors {
  Required = "Required",
  InvalidEmail = "InvalidEmail",
  SameGuardianPlayerEmail = "SameGuardianPlayerEmail",
}

const parser = z
  .string({ invalid_type_error: EmailErrors.InvalidEmail, required_error: EmailErrors.Required })
  .email({ message: EmailErrors.InvalidEmail })
  .refine((data) => !!data, { message: EmailErrors.Required })
  .transform((x) => x as Email);

const optionalParser = z
  .string({ invalid_type_error: EmailErrors.InvalidEmail, required_error: EmailErrors.Required })
  .email({ message: EmailErrors.InvalidEmail })
  .transform((x) => x as Email)
  .optional();

const parse = <T extends Email>(source: unknown) => {
  const validated = parser.safeParse(source);

  return validated.success ? (validated.data as T) : new ParsingError(validated.error);
};

const emailDtoParser = z.object({
  to: z.array(parser),
  body: StringOfLength.parser(1, 50000),
  subject: StringOfLength.parser(1, 5000),
  locale: z.union([z.literal("en"), z.literal("es")]).optional(),
});

export type EmailDto = z.infer<typeof emailDtoParser>;

export enum MarketingEmailSubjects {
  Demo = "demo",
  Contact = "contact",
}

const marketingEmailDtoParser = z.object({
  email: parser,
  message: StringOfLength.parser(1, 1000),
  subject: CustomEnum.parser(MarketingEmailSubjects),
  fullName: StringOfLength.parser(1, 100),
});

export type MarketingEmailDto = z.infer<typeof marketingEmailDtoParser>;

export const Email = {
  parser,
  optionalParser,
  parse,
  marketingEmailDtoParser,
  emailDtoParser,
  toEmailDto: (source: unknown) => {
    const parsed = emailDtoParser.safeParse(source);

    return parsed.success ? parsed.data : new ParsingError(parsed.error);
  },

  toMarketingEmailDto: (source: unknown) => zodToDomain(marketingEmailDtoParser, source),
};
