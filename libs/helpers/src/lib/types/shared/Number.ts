import { z } from "zod";

import { Tagged } from "./Tagged";

export type PositiveInteger = Tagged<"PositiveInteger", number>;
export type PositiveNumber = Tagged<"PositiveNumber", number>;

export type IntegerInRange<Min extends number, Max extends number, Id extends string = ""> = Tagged<
  "IntegerInRange",
  {
    min: Min;
    max: Max;
    id: Id;
  } & number
>;

export type Decimal<P extends number> = Tagged<"Decimal", number>;
export type PositiveDecimal<P extends number> = Tagged<"PositiveDecimal", number>;

enum CustomNumberErrors {
  Required = "Required",
  NotANumber = "NotANumber",
  BelowRange = "BelowRange",
  AboveRange = "AboveRange",
}

const integerInRangeParser = <Min extends number, Max extends number, Id extends string = string>(
  min: Min,
  max: Max,
  _id = "" as Id,
) =>
  z.preprocess(
    (value) => Number(value),
    z
      .number({
        required_error: CustomNumberErrors.Required,
        invalid_type_error: CustomNumberErrors.NotANumber,
      })
      .min(min, { message: CustomNumberErrors.BelowRange })
      .max(max, { message: CustomNumberErrors.AboveRange })
      .transform((result) => result as IntegerInRange<Min, Max, typeof _id>),
  );

const positiveIntegerParser = z.preprocess(
  (value) => Number(value),
  z
    .number()
    .refine(Number.isInteger)
    .refine((value) => value >= 0)
    .transform((value) => value as PositiveInteger),
);

const positiveNumberParser = z.preprocess(
  (value) => Number(value),
  z
    .number()
    .refine((value) => value >= 0)
    .transform((value) => value as PositiveNumber),
);

const roundToPrecision = (value: number, precision: number): number => {
  const multiplier = Math.pow(10, precision);
  return Math.round(value * multiplier) / multiplier;
};

const decimalParser = <P extends number>(precision: P) =>
  z.preprocess(
    (value) => Number(value),
    z
      .number({
        required_error: CustomNumberErrors.Required,
        invalid_type_error: CustomNumberErrors.NotANumber,
      })
      .transform((value) => roundToPrecision(value, precision) as Decimal<P>),
  );

const positiveDecimalParser = <P extends number>(precision: P) =>
  z.preprocess(
    (value) => Number(value),
    z
      .number({
        required_error: CustomNumberErrors.Required,
        invalid_type_error: CustomNumberErrors.NotANumber,
      })
      .refine((value) => value >= 0)
      .transform((value) => roundToPrecision(value, precision) as PositiveDecimal<P>),
  );

const decimalInRangeParser = <P extends number>(min: number, max: number, precision: P) =>
  z.preprocess(
    (value) => Number(value),
    z
      .number({
        required_error: CustomNumberErrors.Required,
        invalid_type_error: CustomNumberErrors.NotANumber,
      })
      .refine((value) => value >= min && value <= max, `Value must be between ${min} and ${max}`)
      .transform((value) => roundToPrecision(value, precision) as Decimal<P>),
  );

export const CustomNumber = {
  parsePositiveInteger: positiveIntegerParser,
  castToPositiveInteger: (value: number) =>
    z.number().int().positive().parse(value) as PositiveInteger,
  toPositiveInteger: (value: number) => positiveIntegerParser.parse(value),
  integerInRangeParser,
  positiveNumberParser,

  toPositiveNumber: (value: number) => positiveNumberParser.parse(value),

  toIntegerInRange: (min: number, max: number, value: number) =>
    integerInRangeParser(min, max).parse(value),

  decimalParser,
  positiveDecimalParser,

  toDecimal: <P extends number>(value: number, precision: P) =>
    decimalParser(precision).parse(value),

  toPositiveDecimal: <P extends number>(value: number, precision: P) =>
    positiveDecimalParser(precision).parse(value),

  decimalInRangeParser,

  toDecimalInRange: <P extends number>(min: number, max: number, precision: P, value: number) =>
    decimalInRangeParser(min, max, precision).parse(value),
};
