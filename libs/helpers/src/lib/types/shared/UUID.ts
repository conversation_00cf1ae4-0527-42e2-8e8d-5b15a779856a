import { z } from "zod";
import { v4, V4Options } from "uuid";
import { ParsingError } from ".";

export type UUID = string & { readonly __UUIDv4: true };

export enum UUIDErrors {
  NotAString = "NotAString",
  InvalidUUID = "InvalidUUID",
}

const parser = <T extends UUID>() =>
  z
    .string({ required_error: UUIDErrors.NotAString, invalid_type_error: UUIDErrors.NotAString })
    .uuid({ message: UUIDErrors.InvalidUUID })
    .transform((x) => x as T);

const generate = <T extends UUID>(options?: V4Options) => v4(options) as T;

const parse = <T extends UUID>(source: unknown) => {
  const validated = parser().safeParse(source);

  return validated.success ? (validated.data as T) : new ParsingError({});
};

export const UUID = { parser, generate, parse };
