import { z } from "zod";

import { zodToDomain } from "../../misc";
import { CustomEnum } from "../shared";
import { CurrencyLowerCase } from "./currencies";

enum CollectionMethod {
  charge_automatically = "charge_automatically",
  send_invoice = "send_invoice",
}

enum SubscriptionStatus {
  incomplete = "incomplete",
  incomplete_expired = "incomplete_expired",
  trialing = "trialing",
  active = "active",
  past_due = "past_due",
  canceled = "canceled",
  unpaid = "unpaid",
}

const baseParser = z.object({
  id: z.string(),
  collection_method: CustomEnum.parser(CollectionMethod),
  currency: CustomEnum.parser(CurrencyLowerCase),

  latest_invoice: z.string().nullable(),
  livemode: z.boolean(),
  status: CustomEnum.parser(SubscriptionStatus),
});

export type Subscription = z.infer<typeof baseParser>;

export const Entity = {
  parser: baseParser,

  toEntity: (source: unknown) => zodToDomain(baseParser, source),

  toEntities: (source: unknown) => zodToDomain(z.array(baseParser), source),
};
