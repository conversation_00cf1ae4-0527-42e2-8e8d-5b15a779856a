import { z } from "zod";

import { zodToDomain } from "../../misc";
import { Price } from ".";
import { Team } from "../team";
import { CurrencyLowerCase } from "./currencies";

const baseParser = z.object({
  id: z.string().optional(),
  name: z.string(),

  active: z.boolean().optional().default(true),
  description: z.string(),

  default_price_data: Price.Entity.toCreateDto,
});

export type Product = z.infer<typeof baseParser>;

export const Entity = {
  parser: baseParser,

  createDto: (dto: Team, currency: CurrencyLowerCase, price: string): Product => {
    return {
      name: dto.name,
      description: dto.ageDescription,
      active: true,
      default_price_data: {
        currency,
        unit_amount_decimal: price,
        ...(dto.feeInterval && {
          recurring: {
            interval: dto.feeInterval,
            interval_count: dto.feeInterval && 1,
          },
        }),
      },
    };
  },

  toEntity: (source: unknown) => zodToDomain(baseParser, source),

  toEntities: (source: unknown) => zodToDomain(z.array(baseParser), source),
};
