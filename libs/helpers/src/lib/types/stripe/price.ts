import { z } from "zod";

import { zodToDomain } from "../../misc";
import { CustomEnum, CustomNumber } from "../shared";
import { CurrencyLowerCase } from "./currencies";

export enum PriceErrors {
  Required = "Required",
}

const baseParser = z.object({
  id: z.string().optional(),
  name: z.string().optional(),

  description: z.string().optional(),

  currency: CustomEnum.parser(CurrencyLowerCase),
  unitAmount: CustomNumber.parsePositiveInteger.default(1).optional(),
  unit_amount_decimal: z.string(),
  recurring: z
    .object({
      interval: z.enum(["day", "week", "month", "year"]),
      interval_count: z.number().int().positive(),
    })
    .optional(),
});

export type Price = z.infer<typeof baseParser>;

export const Entity = {
  toCreateDto: baseParser,

  toEntity: (source: unknown) => zodToDomain(baseParser, source),

  toEntities: (source: unknown) => zodToDomain(z.array(baseParser), source),
};
