import { omit } from "lodash/fp";
import { z } from "zod";

import { zodToDomain } from "../../misc";
import { OrganizationId } from "../organization";
import { ProfileId } from "../profile";
import { SeasonId } from "../season";
import {
  Tagged,
  UUID,
  StringOfLength,
  GenericRecord,
  CustomDate,
  CustomEnum,
  CustomNumber,
} from "../shared";
import { TeamId } from "../team";

export type Id = Tagged<"FootballMatchPlanId", UUID>;

export enum FootballMatchCompetition {
  league = "league",
  cup = "cup",
  friendly = "friendly",
}

export enum FootballMatchHost {
  Home = "home",
  Away = "away",
  Neutral = "neutral",
}

const BaseParser = z.object({
  organizationId: UUID.parser<OrganizationId>(),
  teamId: UUID.parser<TeamId>(),
  seasonId: UUID.parser<SeasonId>().optional(),

  name: StringOfLength.parser(2, 100),
  oppositionName: StringOfLength.parser(2, 100),
  gameWeek: CustomNumber.integerInRangeParser(1, 100),
  leaguePosition: CustomNumber.integerInRangeParser(1, 100).optional(),
  leaguePositionOpponent: CustomNumber.integerInRangeParser(1, 100).optional(),
  startDate: CustomDate.validParser(),
  endDate: CustomDate.validParser().optional(),
  host: CustomEnum.parser(FootballMatchHost).optional(),

  description: StringOfLength.parser(2, 100).optional(),
  competition: CustomEnum.parser(FootballMatchCompetition),

  learningOutcomes: StringOfLength.parser(2, 200).optional(),
  playersEngagement: StringOfLength.parser(2, 1000).optional(),
  coachBehaviours: StringOfLength.parser(2, 1000).optional(),
  safetyConsiderations: StringOfLength.parser(2, 1000).optional(),
  arrivalAndWarmUp: StringOfLength.parser(2, 1000).optional(),
});

const FullParser = BaseParser.merge(
  z.object({
    id: UUID.parser<Id>(),
  }),
);

const createParser = BaseParser.refine(
  (data) => {
    if (data.leaguePosition && data.leaguePositionOpponent) {
      return data.leaguePosition !== data.leaguePositionOpponent;
    }

    return true;
  },
  {
    message: "League position and opponent league position cannot be the same",
    path: ["leaguePosition", "leaguePositionOpponent"],
  },
);

const DeletedParser = FullParser.merge(
  z.object({
    deletedBy: UUID.parser<ProfileId>(),
    deletedAt: CustomDate.validParser(),
  }),
);

export type CompletePlan = z.infer<typeof FullParser>;
export type CreateDto = z.infer<typeof BaseParser>;

export type DeleteDto = z.infer<typeof DeletedParser>;

const updateDtoParser = BaseParser.merge(FullParser.pick({ id: true })).refine(
  (data) => {
    if (data.leaguePosition && data.leaguePositionOpponent) {
      return data.leaguePosition !== data.leaguePositionOpponent;
    }

    return true;
  },
  {
    message: "League position and opponent league position cannot be the same",
    path: ["leaguePosition"],
  },
);

export type UpdateDto = z.infer<typeof updateDtoParser>;

export type UpsertDto = CreateDto | UpdateDto;

export const Entity = {
  create: (dto: CreateDto, createId = () => UUID.generate<Id>()): CompletePlan => {
    return {
      id: createId(),
      organizationId: dto.organizationId,
      teamId: dto.teamId,
      seasonId: dto.seasonId,

      name: dto.name,
      oppositionName: dto.oppositionName,
      gameWeek: dto.gameWeek,
      startDate: dto.startDate,
      endDate: dto.endDate,
      host: dto.host,

      description: dto.description,
      competition: dto.competition,

      arrivalAndWarmUp: dto.arrivalAndWarmUp,
      coachBehaviours: dto.coachBehaviours,
      learningOutcomes: dto.learningOutcomes,
      playersEngagement: dto.playersEngagement,
      safetyConsiderations: dto.safetyConsiderations,

      leaguePosition: dto.leaguePosition,
      leaguePositionOpponent: dto.leaguePositionOpponent,
    };
  },

  createDeleted: (dto: UpdateDto, modifier: ProfileId, now = CustomDate.now()): DeleteDto => {
    return {
      ...dto,
      deletedBy: modifier,
      deletedAt: now,
    };
  },

  parse: (dto: GenericRecord) => FullParser.parse(dto),

  toEntity: (source: GenericRecord) => zodToDomain(FullParser, source),

  toEntities: (source: GenericRecord[]) => zodToDomain(z.array(FullParser), source),

  toCreateDto: (source: unknown) => zodToDomain(createParser, source),

  toUpdateDto: (source: unknown) => zodToDomain(updateDtoParser, source),

  toUpsertDto: (source: unknown) => zodToDomain(z.union([updateDtoParser, createParser]), source),

  update: (dto: UpdateDto, trainingSessionPlan: CompletePlan): CompletePlan => {
    return {
      ...trainingSessionPlan,
      ...omit("id", dto),
    };
  },

  updateDto: updateDtoParser,
  createDto: BaseParser,

  parseId: FullParser.shape.id,

  toId: (source: unknown) => zodToDomain(UUID.parser<Id>(), source),

  toTestInstance: (): CompletePlan =>
    ({
      id: UUID.generate<Id>(),
      organizationId: UUID.generate<OrganizationId>(),
      teamId: UUID.generate<TeamId>(),
      seasonId: UUID.generate<SeasonId>(),

      name: "Home football match vs Arsenal FC",
      oppositionName: "Arsenal FC",
      gameWeek: 1,
      startDate: CustomDate.now(),
      endDate: CustomDate.now(),

      description: "We are facing a team in the CUP. If we win, we move on to the 1/4 finals",
      competition: FootballMatchCompetition.cup,

      arrivalAndWarmUp: "Rondos and dynamics",
      coachBehaviours: "Energetic, make sure they approach it with less pressure",
      learningOutcomes: "Staying together as a team and supporting each other",
      playersEngagement: "Ask players for their opinion on tactics and starting line up",
      safetyConsiderations: "Make sure players have water and warm layers",

      leaguePosition: 1,
      leaguePositionOpponent: 2,
    } as CompletePlan),
};
