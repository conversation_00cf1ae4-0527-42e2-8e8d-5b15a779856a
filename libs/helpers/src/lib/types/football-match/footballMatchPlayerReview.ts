import { omit } from "lodash/fp";
import { z } from "zod";

import { zodToDomain } from "../../misc";
import { OrganizationId } from "../organization";
import { PlayerTeamProfileId } from "../player-team-profile";
import { ProfileId } from "../profile";
import {
  Tagged,
  UUID,
  GenericRecord,
  CustomDate,
  CustomEnum,
  CustomNumber,
  StringOfLength,
  Primitive,
} from "../shared";
import { TeamId } from "../team";
import { Attendance, PlayerAttributes } from "../training-session";
import { FootballMatchPlan } from ".";
import { FootballMatchReview } from ".";

export type Id = Tagged<"FootballMatchPlayerReviewId", UUID>;

export enum TacticalPlayerPositionsEnum {
  GK = "goalkeeper",
  RB = "right-back",
  RCB = "right-centre-back",
  CB = "centre-back",
  LB = "left-back",
  LCB = "left-centre-back",
  RWB = "right-wing-back",
  LWB = "left-wing-back",
  CDM = "central-defensive-midfielder",
  DM = "defensive-midfielder",
  CM = "central-midfielder",
  CAM = "central-attacking-midfielder",
  LM = "left-midfielder",
  RM = "right-midfielder",
  RW = "right-winger",
  LW = "left-winger",
  ST = "striker",
}

const positionPlayed = z.object({
  position: CustomEnum.parser(TacticalPlayerPositionsEnum),

  //TODO: make it in range 1 - 150
  minutesPlayed: CustomNumber.integerInRangeParser(1, 150).optional(),

  // TODO: make it in range 1 - 10
  rating: CustomNumber.integerInRangeParser(1, 10),
  comments: StringOfLength.parser(1, 500).optional(),
  highlights: z.array(CustomEnum.parser(PlayerAttributes)).optional(),
  improvementHighlights: z.array(CustomEnum.parser(PlayerAttributes)).optional(),
});

const BaseParser = z.object({
  organizationId: UUID.parser<OrganizationId>(),
  teamId: UUID.parser<TeamId>(),
  playerTeamProfileId: UUID.parser<PlayerTeamProfileId>(),
  footballMatchPlanId: UUID.parser<FootballMatchPlan.Id>(),
  footballMatchReviewId: UUID.parser<FootballMatchReview.Id>().optional(),

  // TODO: make it in range 1 - 10
  // Suggestion: Could we just reuse `positionPlayed` here? e.g. overallReview: positionPlayed
  // I didn't do this because overallRating looked like a bit of work to unpick, and I don't know how it's currently
  // being used
  overallRating: CustomNumber.integerInRangeParser(1, 10).optional(),
  overallPosition: CustomEnum.parser(TacticalPlayerPositionsEnum).optional(),
  overallHighlights: z.array(CustomEnum.parser(PlayerAttributes)).optional(),
  overallMinutesPlayed: CustomNumber.integerInRangeParser(0, 150).optional(),
  improvementHighlights: z.array(CustomEnum.parser(PlayerAttributes)).optional(),

  attendance: CustomEnum.parser(Attendance).optional(),
  positionsPlayed: z.array(positionPlayed).optional(),
});

const AverageRatingParser = z.object({
  averageOverallRating: z.number().min(1).max(10),
  playerTeamProfileId: UUID.parser<PlayerTeamProfileId>(),
  firstName: z.string(),
  lastName: z.string(),
  teamId: UUID.parser<TeamId>(),
  appearances: z.number().min(1),
});

const AverageRatingParserMany = z.array(AverageRatingParser);

const BaseParserMany = z.array(BaseParser);

const FullParser = BaseParser.merge(
  z.object({
    id: UUID.parser<Id>(),
    coachId: UUID.parser<ProfileId>(),
  }),
);

const DeletedParser = FullParser.merge(
  z.object({
    deletedBy: UUID.parser<ProfileId>(),
    deletedAt: CustomDate.validParser(),
  }),
);

export type CompletePlayerReview = z.infer<typeof FullParser>;

export type Deleted = z.infer<typeof DeletedParser>;

export type CreateDto = z.infer<typeof BaseParser>;

export type CreateManyDto = z.infer<typeof BaseParserMany>;

export type AverageRatingDto = z.infer<typeof AverageRatingParser>;

const updateDtoParser = BaseParser.merge(FullParser.pick({ id: true, coachId: true }));

export type UpdateDto = z.infer<typeof updateDtoParser>;

export type UpsertDto = CreateDto | UpdateDto;

export const Entity = {
  create: (
    dto: CreateDto,
    coachId: ProfileId,
    createId = () => UUID.generate<Id>(),
  ): CompletePlayerReview => {
    return {
      id: createId(),
      organizationId: dto.organizationId,
      teamId: dto.teamId,
      playerTeamProfileId: dto.playerTeamProfileId,
      footballMatchPlanId: dto.footballMatchPlanId,
      footballMatchReviewId: dto.footballMatchReviewId,

      overallRating: dto.overallRating,
      positionsPlayed: dto.positionsPlayed,

      attendance: dto.attendance,
      overallHighlights: dto.overallHighlights,
      overallPosition: dto.overallPosition,
      overallMinutesPlayed: dto.overallMinutesPlayed,

      coachId: coachId,
    };
  },

  createMany: (
    dtos: CreateDto[],
    coachId: ProfileId,
    createId = () => UUID.generate<Id>(),
  ): CompletePlayerReview[] => {
    return dtos.map((dto) => Entity.create(dto, coachId, createId));
  },

  reviewExists: (review: Partial<Primitive<CreateDto>>): boolean => {
    return (
      !!review.overallRating ||
      !!review.overallPosition ||
      !!review.overallMinutesPlayed ||
      !!review.attendance
    );
  },

  createDeleted: (dto: UpdateDto, modifier: ProfileId, now = CustomDate.now()): Deleted => {
    return {
      ...dto,
      deletedBy: modifier,
      deletedAt: now,
    };
  },

  parse: (dto: GenericRecord) => FullParser.parse(dto),

  toEntity: (source: GenericRecord) => zodToDomain(FullParser, source),

  toEntities: (source: GenericRecord[]) => zodToDomain(z.array(FullParser), source),

  toAverageRatingEntity: (source: unknown) => zodToDomain(AverageRatingParser, source),

  toAverageRatingEntities: (source: unknown) => zodToDomain(AverageRatingParserMany, source),

  toCreateDto: (source: unknown) => zodToDomain(BaseParser, source),

  toCreateManyDto: (source: unknown) => zodToDomain(z.array(BaseParser), source),

  toCreateManyCustomDto: (source: unknown) =>
    zodToDomain(z.object({ reviews: z.array(BaseParser) }), source),

  toUpdateDto: (source: unknown) => zodToDomain(updateDtoParser, source),

  toUpsertDto: (source: unknown) => zodToDomain(z.union([updateDtoParser, BaseParser]), source),

  toUpsertManyDto: (source: unknown) =>
    zodToDomain(z.array(z.union([updateDtoParser, BaseParser])), source),

  toUpsertManyCustomDto: (source: unknown) =>
    zodToDomain(z.object({ reviews: z.array(z.union([updateDtoParser, BaseParser])) }), source),

  update: (
    dto: UpdateDto,
    footballMatchPlayerReview: CompletePlayerReview,
  ): CompletePlayerReview => {
    return {
      ...footballMatchPlayerReview,
      ...omit("id", dto),
    };
  },

  updateDto: updateDtoParser,
  createDto: BaseParser,
  createManyDto: BaseParserMany,

  parseId: FullParser.shape.id,

  toId: (source: unknown) => zodToDomain(UUID.parser<Id>(), source),

  toTestInstance: (): CompletePlayerReview =>
    ({
      id: UUID.generate<Id>(),
      organizationId: UUID.generate<OrganizationId>(),
      teamId: UUID.generate<TeamId>(),
      playerTeamProfileId: UUID.generate<PlayerTeamProfileId>(),
      footballMatchPlanId: UUID.generate<FootballMatchPlan.Id>(),

      overallRating: 1,
      overallHighlights: [PlayerAttributes.Delaying],
      overallPosition: TacticalPlayerPositionsEnum.CDM,
      overallMinutesPlayed: 90,

      positionsPlayed: [
        {
          position: TacticalPlayerPositionsEnum.CB,
          comments: "Did well really",
          highlights: [PlayerAttributes.Delaying, PlayerAttributes.Marking],
          improvementHighlights: [PlayerAttributes.Aggression],
          minutesPlayed: 77,
          rating: 8,
        },
        {
          position: TacticalPlayerPositionsEnum.ST,
          comments: "Was shit upfront",
          highlights: [],
          improvementHighlights: [PlayerAttributes.Finishing],
          minutesPlayed: 10,
          rating: 2,
        },
      ],

      attendance: Attendance.Attended,
    } as CompletePlayerReview),
};
