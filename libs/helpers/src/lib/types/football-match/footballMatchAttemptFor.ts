import { omit } from "lodash/fp";
import { z } from "zod";

import { zodToDomain } from "../../misc";
import { OrganizationId } from "../organization";
import { PlayerTeamProfileId } from "../player-team-profile";
import { ProfileId } from "../profile";
import {
  Tagged,
  UUID,
  GenericRecord,
  CustomDate,
  CustomEnum,
  CustomNumber,
  StringOfLength,
} from "../shared";
import { TeamId } from "../team";
import { PlayerAttributes } from "../training-session";
import { FootballMatchPlan } from ".";
import { FootballMatchReview } from ".";

export type Id = Tagged<"FootballMatchAttemptForId", UUID>;

export enum GoalSituationType {
  openPlay = "open-play",
  setPiece = "set-piece",
  errorLeadingToAGoal = "error-leading-to-a-goal",
  pressing = "pressing",
}

export enum GoalSituationOpenPlaySubType {
  cross = "cross",
  longShot = "long-shot",
  combinationPlay = "combination-play",
  throughBall = "through-ball",
  dribble = "dribble",
  oneToOne = "one-to-one",
  counterAttack = "counter-attack",
  rebound = "rebound",
}

export enum GoalSituationSetPieceSubType {
  corner = "corner",
  directFreeKick = "direct-free-kick",
  indirectFreeKick = "indirect-free-kick",
  penalty = "penalty",
  throwIn = "throw-in",
  goalKick = "goal-kick",
}

export enum FootballMatchHalves {
  first = "first",
  second = "second",
  extraTimeFirst = "extra-time-first",
  extraTimeSecond = "extra-time-second",
  afterMatchPenalties = "after-match-penalties",
}

export enum AttemptForBodyPart {
  rightFoot = "right-foot",
  leftFoot = "left-foot",
  head = "head",
  chest = "chest",
  knee = "knee",
  shoulder = "shoulder",
  hip = "hip",
  back = "back",
  hand = "hand",
  other = "other",
}

export enum GoalType {
  goal = "goal",
  ownGoal = "own-goal",
}

export enum AttemptForType {
  goal = "goal",
  shotOnGoal = "shot-on-goal",
  shotOffGoal = "shot-off-goal",
  shotBlocked = "shot-blocked",
}

const BaseParser = z.object({
  organizationId: UUID.parser<OrganizationId>(),
  teamId: UUID.parser<TeamId>(),
  footballMatchPlanId: UUID.parser<FootballMatchPlan.Id>(),
  footballMatchReviewId: UUID.parser<FootballMatchReview.Id>().optional(),

  minute: CustomNumber.integerInRangeParser(1, 120).optional(),

  addedTime: CustomNumber.integerInRangeParser(0, 45).optional(),
  type: CustomEnum.parser(AttemptForType),
  goalType: CustomEnum.parser(GoalType).optional(),

  half: CustomEnum.parser(FootballMatchHalves).optional(),

  shooter: UUID.parser<PlayerTeamProfileId>().optional(),
  ownGoalScorer: StringOfLength.parser(1, 100).optional(),
  bodyPart: CustomEnum.parser(AttemptForBodyPart).optional(),
  zone: CustomNumber.integerInRangeParser(1, 18).optional(),

  xG: CustomNumber.decimalInRangeParser(0, 1, 2).optional(),

  assist: UUID.parser<PlayerTeamProfileId>().optional(),
  assistBodyPart: CustomEnum.parser(AttemptForBodyPart).optional(),
  assistZone: CustomNumber.integerInRangeParser(1, 18).optional(),

  keyPass: UUID.parser<PlayerTeamProfileId>().optional(),
  keyPassZone: CustomNumber.integerInRangeParser(1, 18).optional(),

  keyContributions: z.array(UUID.parser<PlayerTeamProfileId>()).optional(),
  highlights: z.array(CustomEnum.parser(PlayerAttributes)).optional(),

  situationType: CustomEnum.parser(GoalSituationType).optional(),
  situationOpenPlaySubType: CustomEnum.parser(GoalSituationOpenPlaySubType).optional(),
  situationSetPieceSubType: CustomEnum.parser(GoalSituationSetPieceSubType).optional(),
});

// Schema for the aggregation output
export const AggregatedTeamGoalsParser = z.object({
  bodyPart: z.record(CustomEnum.parser(AttemptForBodyPart), z.number().int().min(0)),
  situationType: z.record(CustomEnum.parser(GoalSituationType), z.number().int().min(0)),
  situationSetPieceSubType: z.record(
    CustomEnum.parser(GoalSituationSetPieceSubType),
    z.number().int().min(0),
  ),
  situationOpenPlaySubType: z.record(
    CustomEnum.parser(GoalSituationOpenPlaySubType),
    z.number().int().min(0),
  ),
  half: z.record(CustomEnum.parser(FootballMatchHalves), z.number().int().min(0)),
  zone: z.record(z.string(), z.number().int().min(0)),
  goalTimes: z
    .object({
      "early-goals": z.number().int().min(0),
      "late-goals": z.number().int().min(0),
      "mid-match-goals": z.number().int().min(0),
    })
    .optional(),
  matchGoals: z.array(z.object({ gameWeek: z.number(), goalsScored: z.number().int().min(0) })),
});

// Example type inferred from the schema
export type AggregatedTeamGoals = z.infer<typeof AggregatedTeamGoalsParser>;

const FullParser = BaseParser.merge(
  z.object({
    id: UUID.parser<Id>(),
  }),
);

const DeletedParser = FullParser.merge(
  z.object({
    deletedBy: UUID.parser<ProfileId>(),
    deletedAt: CustomDate.validParser(),
  }),
);

export type AttemptFor = z.infer<typeof FullParser>;

const ScoredGoalWithGameWeekParser = FullParser.merge(z.object({ gameWeek: z.number() }));

type ScoredGoalWithGameWeek = z.infer<typeof ScoredGoalWithGameWeekParser>;

export type Deleted = z.infer<typeof DeletedParser>;

export type CreateDto = z.infer<typeof BaseParser>;

const updateDtoParser = BaseParser.merge(FullParser.pick({ id: true }));

export type UpdateDto = z.infer<typeof updateDtoParser>;

export const Entity = {
  create: (dto: CreateDto, createId = () => UUID.generate<Id>()): AttemptFor => {
    return {
      id: createId(),
      organizationId: dto.organizationId,
      teamId: dto.teamId,
      footballMatchPlanId: dto.footballMatchPlanId,
      footballMatchReviewId: dto.footballMatchReviewId,

      minute: dto.minute,
      addedTime: dto.addedTime,
      type: dto.type,

      half: dto.half,

      shooter: dto.shooter,
      ownGoalScorer: dto.ownGoalScorer,
      bodyPart: dto.bodyPart,
      zone: dto.zone,

      assist: dto.assist,
      assistBodyPart: dto.assistBodyPart,
      assistZone: dto.assistZone,

      keyPass: dto.keyPass,
      keyPassZone: dto.keyPassZone,

      keyContributions: dto.keyContributions,
      highlights: dto.highlights,

      situationType: dto.situationType,
      situationOpenPlaySubType: dto.situationOpenPlaySubType,
      situationSetPieceSubType: dto.situationSetPieceSubType,
    };
  },

  parse: (dto: GenericRecord) => FullParser.parse(dto),

  toEntity: (source: GenericRecord) => zodToDomain(FullParser, source),

  toEntities: (source: unknown[]) => zodToDomain(z.array(FullParser), source),

  toCreateDto: (source: unknown) => zodToDomain(BaseParser, source),

  toUpdateDto: (source: unknown) => zodToDomain(updateDtoParser, source),

  toUpsertDto: (source: unknown) => zodToDomain(z.union([updateDtoParser, BaseParser]), source),

  toAggregatedTeamGoals: (source: unknown) => zodToDomain(AggregatedTeamGoalsParser, source),

  toManyAggregatedTeamGoals: (source: unknown) =>
    zodToDomain(z.array(AggregatedTeamGoalsParser), source),

  update: (dto: UpdateDto, trainingSessionReview: AttemptFor): AttemptFor => {
    return {
      ...trainingSessionReview,
      ...omit("id", dto),
    };
  },

  updateDto: updateDtoParser,
  createDto: BaseParser,

  parseId: FullParser.shape.id,

  toId: (source: unknown) => zodToDomain(UUID.parser<Id>(), source),

  toScoredGoalsWithGameWeek: (source: unknown) =>
    zodToDomain(z.array(ScoredGoalWithGameWeekParser), source),

  aggregateTeamGoals: (stats: ScoredGoalWithGameWeek[]) => {
    const result = stats.reduce(
      (acc, stat) => {
        if (stat.bodyPart) {
          acc.bodyPart[stat.bodyPart] = (acc.bodyPart[stat.bodyPart] || 0) + 1;
        }

        if (stat.situationType) {
          acc.situationType[stat.situationType] = (acc.situationType[stat.situationType] || 0) + 1;
        }

        if (stat.situationSetPieceSubType) {
          acc.situationSetPieceSubType[stat.situationSetPieceSubType] =
            (acc.situationSetPieceSubType[stat.situationSetPieceSubType] || 0) + 1;
        }

        if (stat.situationOpenPlaySubType) {
          acc.situationOpenPlaySubType[stat.situationOpenPlaySubType] =
            (acc.situationOpenPlaySubType[stat.situationOpenPlaySubType] || 0) + 1;
        }

        if (stat.half) {
          acc.half[stat.half] = (acc.half[stat.half] || 0) + 1;
        }

        if (stat.zone) {
          acc.zone[stat.zone] = (acc.zone[stat.zone] || 0) + 1;
        }

        // Goal times
        const minute = (stat.minute || 0) + (stat.addedTime || 0);
        if (minute < 25 && stat.half === FootballMatchHalves.first) {
          acc.goalTimes["early-goals"] += 1;
        } else if (minute >= 30 && stat.half === FootballMatchHalves.second) {
          acc.goalTimes["late-goals"] += 1;
        } else {
          acc.goalTimes["mid-match-goals"] += 1;
        }

        if (acc.matchGoals[stat.gameWeek]) {
          acc.matchGoals[stat.gameWeek].goalsScored += 1;
        } else {
          acc.matchGoals[stat.gameWeek] = {
            gameWeek: stat.gameWeek,
            goalsScored: 1,
          };
        }

        return acc;
      },
      {
        bodyPart: {} as Record<string, number>,
        situationType: {} as Record<string, number>,
        situationSetPieceSubType: {} as Record<string, number>,
        situationOpenPlaySubType: {} as Record<string, number>,
        half: {} as Record<string, number>,
        zone: {} as Record<string, number>,
        goalTimes: {
          "early-goals": 0,
          "mid-match-goals": 0,
          "late-goals": 0,
        },
        matchGoals: {} as Record<string, { gameWeek: number; goalsScored: number }>,
      },
    );

    // Transform the matchGoals object into an array format
    return {
      ...result,
      matchGoals: Object.values(result.matchGoals).map((goal) => ({
        gameWeek: goal.gameWeek,
        goalsScored: goal.goalsScored,
      })),
    };
  },

  toDemoInstance: (): AttemptFor =>
    ({
      id: UUID.generate<Id>(),
      organizationId: UUID.generate<OrganizationId>(),
      teamId: UUID.generate<TeamId>(),
      footballMatchPlanId: UUID.generate<FootballMatchPlan.Id>(),
      footballMatchReviewId: UUID.generate<FootballMatchReview.Id>(),

      //TODO: make it in range 1-120
      minute: CustomNumber.toIntegerInRange(1, 120, 12),

      //TODO: make it in range 0-45
      addedTime: CustomNumber.toIntegerInRange(0, 45, 0),

      type: AttemptForType.goal,

      half: FootballMatchHalves.second,

      shooter: UUID.generate<PlayerTeamProfileId>(),
      assist: UUID.generate<PlayerTeamProfileId>(),
      keyPass: UUID.generate<PlayerTeamProfileId>(),

      highlights: [PlayerAttributes.Adaptability, PlayerAttributes.DefensiveBalance],
    } as AttemptFor),
};
