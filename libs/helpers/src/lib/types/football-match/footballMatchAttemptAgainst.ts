import { omit } from "lodash/fp";
import { z } from "zod";

import { zodToDomain } from "../../misc";
import { OrganizationId } from "../organization";
import { PlayerTeamProfileId } from "../player-team-profile";
import { ProfileId } from "../profile";
import {
  Tagged,
  UUID,
  GenericRecord,
  CustomDate,
  CustomEnum,
  CustomNumber,
  StringOfLength,
} from "../shared";
import { TeamId } from "../team";
import { PlayerAttributes } from "../training-session";
import { FootballMatchPlan } from ".";
import { FootballMatchReview } from ".";
import {
  FootballMatchHalves,
  AttemptForBodyPart,
  GoalSituationOpenPlaySubType,
  GoalSituationSetPieceSubType,
  GoalSituationType,
} from "./footballMatchAttemptFor";
import { TacticalPlayerPositionsEnum } from "./footballMatchPlayerReview";

export type Id = Tagged<"FootballMatchAttemptAgainstId", UUID>;

export enum AttemptAgainstType {
  ownGoal = "own-goal",
  goal = "goal",
  shotOnGoal = "shot-on-goal",
  shotOffGoal = "shot-off-goal",
  shotBlocked = "shot-blocked",
}

const BaseParser = z.object({
  organizationId: UUID.parser<OrganizationId>(),
  teamId: UUID.parser<TeamId>(),
  footballMatchPlanId: UUID.parser<FootballMatchPlan.Id>(),
  footballMatchReviewId: UUID.parser<FootballMatchReview.Id>().optional(),

  minute: CustomNumber.integerInRangeParser(1, 120).optional(),

  addedTime: CustomNumber.integerInRangeParser(0, 45).optional(),
  type: CustomEnum.parser(AttemptAgainstType).optional(),

  half: CustomEnum.parser(FootballMatchHalves).optional(),

  shooter: StringOfLength.parser(2, 100).optional(),
  ownGoalScorer: UUID.parser<PlayerTeamProfileId>().optional(),
  errorLeadingDirectlyToTheGoal: UUID.parser<PlayerTeamProfileId>().optional(),
  scorerPosition: CustomEnum.parser(TacticalPlayerPositionsEnum).optional(),
  bodyPart: CustomEnum.parser(AttemptForBodyPart).optional(),
  zone: CustomNumber.integerInRangeParser(1, 18).optional(),

  xG: CustomNumber.decimalInRangeParser(0, 1, 2).optional(),

  assist: StringOfLength.parser(2, 100).optional(),
  assisterPosition: CustomEnum.parser(TacticalPlayerPositionsEnum).optional(),
  assistBodyPart: CustomEnum.parser(AttemptForBodyPart).optional(),
  assistZone: CustomNumber.integerInRangeParser(1, 18).optional(),

  keyPass: StringOfLength.parser(2, 100).optional(),
  keyPassPosition: CustomEnum.parser(TacticalPlayerPositionsEnum).optional(),
  keyPassZone: CustomNumber.integerInRangeParser(1, 18).optional(),

  keyContributions: StringOfLength.parser(2, 100).optional(),
  highlights: z.array(CustomEnum.parser(PlayerAttributes)).optional(),
  improvementHighlights: z.array(CustomEnum.parser(PlayerAttributes)).optional(),
  mistakes: StringOfLength.parser(2, 200).optional(),

  situationType: CustomEnum.parser(GoalSituationType).optional(),
  situationOpenPlaySubType: CustomEnum.parser(GoalSituationOpenPlaySubType).optional(),
  situationSetPieceSubType: CustomEnum.parser(GoalSituationSetPieceSubType).optional(),
});

export const AggregatedTeamGoalsParser = z.object({
  bodyPart: z.record(CustomEnum.parser(AttemptForBodyPart), z.number().int().min(0)),
  situationType: z.record(CustomEnum.parser(GoalSituationType), z.number().int().min(0)),
  situationSetPieceSubType: z.record(
    CustomEnum.parser(GoalSituationSetPieceSubType),
    z.number().int().min(0),
  ),
  situationOpenPlaySubType: z.record(
    CustomEnum.parser(GoalSituationOpenPlaySubType),
    z.number().int().min(0),
  ),
  half: z.record(CustomEnum.parser(FootballMatchHalves), z.number().int().min(0)),
  zone: z.record(z.string(), z.number().int().min(0)),
  goalTimes: z
    .object({
      "early-goals": z.number().int().min(0),
      "late-goals": z.number().int().min(0),
      "mid-match-goals": z.number().int().min(0),
    })
    .optional(),
  matchGoals: z.array(z.object({ gameWeek: z.number(), goalsConceded: z.number().int().min(0) })),
});

export type AggregatedTeamGoals = z.infer<typeof AggregatedTeamGoalsParser>;

const FullParser = BaseParser.merge(
  z.object({
    id: UUID.parser<Id>(),
  }),
);

const DeletedParser = FullParser.merge(
  z.object({
    deletedBy: UUID.parser<ProfileId>(),
    deletedAt: CustomDate.validParser(),
  }),
);

export type AttemptAgainst = z.infer<typeof FullParser>;

const ScoredGoalWithGameWeekParser = FullParser.merge(z.object({ gameWeek: z.number() }));

type ScoredGoalWithGameWeek = z.infer<typeof ScoredGoalWithGameWeekParser>;

export type Deleted = z.infer<typeof DeletedParser>;

export type CreateDto = z.infer<typeof BaseParser>;

const updateDtoParser = BaseParser.merge(FullParser.pick({ id: true }));

export type UpdateDto = z.infer<typeof updateDtoParser>;

export const Entity = {
  create: (dto: CreateDto, createId = () => UUID.generate<Id>()): AttemptAgainst => {
    return {
      id: createId(),
      organizationId: dto.organizationId,
      teamId: dto.teamId,
      footballMatchPlanId: dto.footballMatchPlanId,
      footballMatchReviewId: dto.footballMatchReviewId,

      minute: dto.minute,
      addedTime: dto.addedTime,
      type: dto.type,
      half: dto.half,

      shooter: dto.shooter,
      ownGoalScorer: dto.ownGoalScorer,
      errorLeadingDirectlyToTheGoal: dto.errorLeadingDirectlyToTheGoal,
      scorerPosition: dto.scorerPosition,
      bodyPart: dto.bodyPart,
      zone: dto.zone,

      assist: dto.assist,
      assisterPosition: dto.assisterPosition,
      assistBodyPart: dto.assistBodyPart,
      assistZone: dto.assistZone,

      keyPass: dto.keyPass,
      keyPassPosition: dto.keyPassPosition,
      keyPassZone: dto.keyPassZone,

      keyContributions: dto.keyContributions,
      highlights: dto.highlights,
      improvementHighlights: dto.improvementHighlights,
      mistakes: dto.mistakes,

      situationType: dto.situationType,
      situationOpenPlaySubType: dto.situationOpenPlaySubType,
      situationSetPieceSubType: dto.situationSetPieceSubType,
    };
  },

  parse: (dto: GenericRecord) => FullParser.parse(dto),

  toEntity: (source: GenericRecord) => zodToDomain(FullParser, source),

  toEntities: (source: GenericRecord[]) => zodToDomain(z.array(FullParser), source),

  toCreateDto: (source: unknown) => zodToDomain(BaseParser, source),

  toUpdateDto: (source: unknown) => zodToDomain(updateDtoParser, source),

  toUpsertDto: (source: unknown) => zodToDomain(z.union([updateDtoParser, BaseParser]), source),

  toAggregatedTeamGoals: (source: unknown) => zodToDomain(AggregatedTeamGoalsParser, source),

  toManyAggregatedTeamGoals: (source: unknown) =>
    zodToDomain(z.array(AggregatedTeamGoalsParser), source),

  update: (dto: UpdateDto, trainingSessionReview: AttemptAgainst): AttemptAgainst => {
    return {
      ...trainingSessionReview,
      ...omit("id", dto),
    };
  },

  updateDto: updateDtoParser,
  createDto: BaseParser,

  parseId: FullParser.shape.id,

  toId: (source: unknown) => zodToDomain(UUID.parser<Id>(), source),

  toConcededGoalsWithGameWeek: (source: unknown) =>
    zodToDomain(z.array(ScoredGoalWithGameWeekParser), source),

  aggregateTeamGoals: (stats: ScoredGoalWithGameWeek[]) => {
    const result = stats.reduce(
      (acc, stat) => {
        if (stat.bodyPart) {
          acc.bodyPart[stat.bodyPart] = (acc.bodyPart[stat.bodyPart] || 0) + 1;
        }

        if (stat.situationType) {
          acc.situationType[stat.situationType] = (acc.situationType[stat.situationType] || 0) + 1;
        }

        if (stat.situationSetPieceSubType) {
          acc.situationSetPieceSubType[stat.situationSetPieceSubType] =
            (acc.situationSetPieceSubType[stat.situationSetPieceSubType] || 0) + 1;
        }

        if (stat.situationOpenPlaySubType) {
          acc.situationOpenPlaySubType[stat.situationOpenPlaySubType] =
            (acc.situationOpenPlaySubType[stat.situationOpenPlaySubType] || 0) + 1;
        }

        if (stat.half) {
          acc.half[stat.half] = (acc.half[stat.half] || 0) + 1;
        }

        if (stat.zone) {
          acc.zone[stat.zone] = (acc.zone[stat.zone] || 0) + 1;
        }

        // Goal times
        const minute = (stat.minute || 0) + (stat.addedTime || 0);
        if (minute < 25 && stat.half === FootballMatchHalves.first) {
          acc.goalTimes["early-goals"] += 1;
        } else if (minute >= 30 && stat.half === FootballMatchHalves.second) {
          acc.goalTimes["late-goals"] += 1;
        } else {
          acc.goalTimes["mid-match-goals"] += 1;
        }

        if (acc.matchGoals[stat.gameWeek]) {
          acc.matchGoals[stat.gameWeek].goalsConceded += 1;
        } else {
          acc.matchGoals[stat.gameWeek] = {
            gameWeek: stat.gameWeek,
            goalsConceded: 1,
          };
        }

        return acc;
      },
      {
        bodyPart: {} as Record<string, number>,
        situationType: {} as Record<string, number>,
        situationSetPieceSubType: {} as Record<string, number>,
        situationOpenPlaySubType: {} as Record<string, number>,
        half: {} as Record<string, number>,
        zone: {} as Record<string, number>,
        goalTimes: {
          "early-goals": 0,
          "mid-match-goals": 0,
          "late-goals": 0,
        },
        matchGoals: {} as Record<string, { gameWeek: number; goalsConceded: number }>,
      },
    );

    // Transform the matchGoals object into an array format
    return {
      ...result,
      matchGoals: Object.values(result.matchGoals).map((goal) => ({
        gameWeek: goal.gameWeek,
        goalsConceded: goal.goalsConceded,
      })),
    };
  },

  toTestInstance: (): AttemptAgainst => {
    return {
      id: UUID.generate<Id>(),
      organizationId: UUID.generate<OrganizationId>(),
      teamId: UUID.generate<TeamId>(),
      footballMatchPlanId: UUID.generate<FootballMatchPlan.Id>(),
      footballMatchReviewId: UUID.generate<FootballMatchReview.Id>(),
      minute: CustomNumber.toIntegerInRange(1, 45, 12),
      addedTime: CustomNumber.toIntegerInRange(1, 45, 12),
      type: AttemptAgainstType.goal,
      half: FootballMatchHalves.first,
      shooter: "scorer",
      ownGoalScorer: UUID.generate<PlayerTeamProfileId>(),
      errorLeadingDirectlyToTheGoal: UUID.generate<PlayerTeamProfileId>(),
      scorerPosition: TacticalPlayerPositionsEnum.CAM,
      bodyPart: AttemptForBodyPart.leftFoot,
      zone: CustomNumber.toIntegerInRange(1, 18, 5),
      assist: "assist",
      assisterPosition: TacticalPlayerPositionsEnum.ST,
      assistBodyPart: AttemptForBodyPart.rightFoot,
      assistZone: CustomNumber.toIntegerInRange(1, 18, 6),
      keyPass: "keyPass",
    } as AttemptAgainst;
  },
};
