import { omit } from "lodash/fp";
import { z } from "zod";

import { zodToDomain } from "../../misc";
import { OrganizationId } from "../organization";
import { ProfileId } from "../profile";
import { SeasonId } from "../season";
import {
  Tagged,
  UUID,
  StringOfLength,
  GenericRecord,
  CustomDate,
  CustomNumber,
  CustomEnum,
} from "../shared";
import { TeamId } from "../team";
import { FootballMatchPlan } from ".";
import { FootballMatchHost } from "./footballMatchPlan";

export type Id = Tagged<"FootballMatchReviewId", UUID>;

export enum FootballMatchOutcomeEnum {
  Win = "win",
  Loss = "loss",
  Draw = "draw",
}

export enum WeatherConditionsEnum {
  Sunny = "sunny",
  Rain = "rain",
  Snow = "snow",
  Wet = "wet",
  Fog = "fog",
  Overcast = "overcast",
}

const BaseParser = z.object({
  organizationId: UUID.parser<OrganizationId>(),
  teamId: UUID.parser<TeamId>(),
  footballMatchPlanId: UUID.parser<FootballMatchPlan.Id>(),
  seasonId: UUID.parser<SeasonId>().optional(),

  name: StringOfLength.parser(2, 100).optional(),
  startDate: CustomDate.validParser().optional(),
  endDate: CustomDate.validParser().optional(),

  learningOutcomes: StringOfLength.parser(2, 200).optional(),
  playersEngagement: StringOfLength.parser(2, 1000).optional(),
  coachBehaviours: StringOfLength.parser(2, 1000).optional(),
  safetyConsiderations: StringOfLength.parser(2, 1000).optional(),
  arrivalAndWarmUp: StringOfLength.parser(2, 1000).optional(),

  rating: CustomNumber.integerInRangeParser(1, 10),

  oppositionName: StringOfLength.parser(2, 100).optional(),

  //TODO: make it in range 1 - 100
  goalsScored: CustomNumber.integerInRangeParser(0, 100),
  //TODO: make it in range 1 - 100
  goalsConceded: CustomNumber.integerInRangeParser(0, 100),

  outcome: CustomEnum.parser(FootballMatchOutcomeEnum),

  host: CustomEnum.parser(FootballMatchHost),
  weatherConditions: CustomEnum.parser(WeatherConditionsEnum),

  //TODO: make it in range 1 - 10
  pitchRating: CustomNumber.integerInRangeParser(1, 10).optional(),
  //TODO: make it in range 1 - 100
  refereeRating: CustomNumber.integerInRangeParser(1, 10).optional(),

  minutesDelayed: CustomNumber.parsePositiveInteger.optional(),
});

const FullParser = BaseParser.merge(
  z.object({
    id: UUID.parser<Id>(),
  }),
);

const DeletedParser = FullParser.merge(
  z.object({
    deletedBy: UUID.parser<ProfileId>(),
    deletedAt: CustomDate.validParser(),
  }),
);

export type CompleteReview = z.infer<typeof FullParser>;

export type Deleted = z.infer<typeof DeletedParser>;

export type CreateDto = z.infer<typeof BaseParser>;

const updateDtoParser = BaseParser.merge(FullParser.pick({ id: true }));

export type UpdateDto = z.infer<typeof updateDtoParser>;

export type UpsertDto = CreateDto | UpdateDto;

export const Entity = {
  create: (dto: CreateDto, createId = () => UUID.generate<Id>()): CompleteReview => {
    return {
      id: createId(),
      organizationId: dto.organizationId,
      teamId: dto.teamId,
      footballMatchPlanId: dto.footballMatchPlanId,
      seasonId: dto.seasonId,

      name: dto.name,

      oppositionName: dto.oppositionName,

      startDate: dto.startDate,
      endDate: dto.endDate,

      arrivalAndWarmUp: dto.arrivalAndWarmUp,
      coachBehaviours: dto.coachBehaviours,
      learningOutcomes: dto.learningOutcomes,
      playersEngagement: dto.playersEngagement,
      safetyConsiderations: dto.safetyConsiderations,

      rating: dto.rating,

      goalsConceded: dto.goalsConceded,
      goalsScored: dto.goalsScored,
      minutesDelayed: dto.minutesDelayed,
      host: dto.host,
      outcome: dto.outcome,
      pitchRating: dto.pitchRating,
      refereeRating: dto.refereeRating,
      weatherConditions: dto.weatherConditions,
    };
  },

  createDeleted: (dto: UpdateDto, modifier: ProfileId, now = CustomDate.now()): Deleted => {
    return {
      ...dto,
      deletedBy: modifier,
      deletedAt: now,
    };
  },

  parse: (dto: GenericRecord) => FullParser.parse(dto),

  toEntity: (source: GenericRecord) => zodToDomain(FullParser, source),

  toEntities: (source: GenericRecord[]) => zodToDomain(z.array(FullParser), source),

  toCreateDto: (source: unknown) => zodToDomain(BaseParser, source),

  toCreateManyDto: (source: unknown) => zodToDomain(z.array(BaseParser), source),

  toUpdateDto: (source: unknown) => zodToDomain(updateDtoParser, source),

  toUpsertDto: (source: unknown) => zodToDomain(z.union([updateDtoParser, BaseParser]), source),

  update: (dto: UpdateDto, footballMatchReview: CompleteReview): CompleteReview => {
    return {
      ...footballMatchReview,
      ...omit("id", dto),
    };
  },

  updateDto: updateDtoParser,
  createDto: BaseParser,

  parseId: FullParser.shape.id,

  toId: (source: unknown) => zodToDomain(UUID.parser<Id>(), source),

  toTestInstance: (): CompleteReview =>
    ({
      id: UUID.generate<Id>(),
      organizationId: UUID.generate<OrganizationId>(),
      teamId: UUID.generate<TeamId>(),
      footballMatchPlanId: UUID.generate<FootballMatchPlan.Id>(),

      name: "Review of a great match",
      startDate: CustomDate.now(),
      endDate: CustomDate.now(),

      oppositionName: "Barcelona FC",
      host: FootballMatchHost.Home,

      arrivalAndWarmUp: "Went well with the rondos, but dynamics were lazy",
      coachBehaviours: "I did alright, encouraged them and spoke levelly and calmly",
      learningOutcomes:
        "Players failed to stick together and were shouting at each other and bickering",
      playersEngagement: "Players did well to propose tactical changes and come up with ideas",
      safetyConsiderations: "Players had water and were attentive of the med bag",

      rating: 8,

      goalsConceded: 2,
      goalsScored: 2,
      minutesDelayed: 5,
      outcome: FootballMatchOutcomeEnum.Draw,
      pitchRating: 4,
      refereeRating: 3,
      weatherConditions: WeatherConditionsEnum.Fog,
    } as CompleteReview),
};
