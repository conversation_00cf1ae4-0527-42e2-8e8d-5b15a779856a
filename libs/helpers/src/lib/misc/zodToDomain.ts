import { z } from "zod";

import { ParsingError } from "../types/shared";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type UnknownZodType<T> = z.ZodType<T, any, unknown>;

export const zodToDomain = <T, D>(
  schema: UnknownZodType<T>,
  data: D,
): ParsingError | z.infer<typeof schema> => {
  const result = schema.safeParse(data);

  if (result.success) {
    return result.data;
  }

  return new ParsingError(result.error);
};

export function zodToDomainOrThrow<T, D>(
  schema: UnknownZodType<T>,
): (data: D) => z.infer<typeof schema>;
export function zodToDomainOrThrow<T, D>(
  schema: UnknownZodType<T>,
  data: D,
): z.infer<typeof schema>;
export function zodToDomainOrThrow<T, D>(
  schema: UnknownZodType<T>,
  data?: D,
): ((data: D) => z.infer<typeof schema>) | z.infer<typeof schema> {
  // If data is undefined, return a function that expects the data
  if (data === undefined) {
    return (innerData: D): z.infer<typeof schema> => {
      const result = schema.safeParse(innerData);

      if (result.success) {
        return result.data;
      }

      throw new ParsingError(result.error);
    };
  }

  // Otherwise, process the data immediately
  const result = schema.safeParse(data);

  if (result.success) {
    return result.data;
  }

  throw new ParsingError(result.error);
}
