{"name": "helpers", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/helpers/src", "projectType": "library", "tags": [], "targets": {"type-check": {"executor": "nx:run-commands", "options": {"command": "npx tsc -b ./libs/helpers"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/helpers"], "options": {"jestConfig": "libs/helpers/jest.config.ts"}}}}