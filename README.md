# Setting up for local development

1. A tool like `nvm` for managing the `node.js` version. We have an `.nvmrc` file for that which allows running `nvm install` and `nvm use` without explicitly specifying the correct version

2. We're using `docker-compose` to spin up a local `mongodb` instance, the vscode 'docker`extension makes this process easy and visual. This needs to be running before starting the`api` project. If you prefer so you can also connect to your own cloud based database, it doesn't really matter.

3. This is an NX monorepo so installing the `nx console` vscode extension is highly advised

4. We have a few key projects, which we'll get into more details into their own README files. On a high level we have:

- `api` - a nest.js backend for all our front end apps
- `client` - this is the coach-facing application where team and player management happens. Pure SPA CRA React app
- `player-portal` - the player facing app. Pure SPA CRA React app
- `marketing` - a next.js landing page for the product

5. We have a centralized `package.json` and `node_modules` shared across apps - make sure to run `npm install` at the project's root

6. The `api` project will need to have a local .env file to be able to run. Ask for it, we don't store it anywhere yet besides local computers of devs

# Essential data seeding to get you started

For now it's possible through a dedicated, invite-only Postman collection, but any http client would work if you have the api secret key and know the endpoints. This is the minimum starting setup which is not available as a self-service feature yet

1. Create an `Organization` with a name and data of your own choosing (or use the default ones) via `Coach portal -> organizations -> Create organization`
2. Take note of the newly created organization's id (not mongo's `_id`) or check the database
3. Go to `Coach portal -> auth -> Create invite`, edit the email (with your own) and if needed - the organization_id (from the previous step) fields and send. Take note of the invite id you get in return or check it in the DB
4. Go to `Coach portal -> auth -> register`, edit the names, password and invite_id (from the previous step) fields. After this step there will be a coach user with that email and password. You can login with it and start creating teams etc
