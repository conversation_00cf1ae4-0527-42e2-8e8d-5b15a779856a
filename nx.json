{"$schema": "./node_modules/nx/schemas/nx-schema.json", "generators": {"@nx/react": {"application": {"babel": true, "style": "@emotion/styled", "linter": "eslint", "bundler": "webpack"}, "component": {"style": "@emotion/styled"}, "library": {"style": "@emotion/styled", "linter": "eslint"}}, "@nx/next": {"application": {"style": "css", "linter": "eslint"}}}, "defaultProject": "client", "targetDefaults": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "cache": true}, "e2e": {"inputs": ["default", "^production"], "cache": true}, "@nx/jest:jest": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "cache": true, "options": {"passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}, "@nx/eslint:lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "cache": true}, "@nx/vite:test": {"cache": true, "inputs": ["default", "^production"]}, "@nx/vite:build": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}}, "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "sharedGlobals": ["{workspaceRoot}/babel.config.json"], "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/src/test-setup.[jt]s"]}, "parallel": 1, "useInferencePlugins": false, "defaultBase": "main"}