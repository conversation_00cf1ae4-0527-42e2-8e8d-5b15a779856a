# MongoDB Replica Set Setup for Transactions

This document explains how to set up MongoDB with replica set support for local development, which is required for transactions to work.

## Why Replica Sets Are Required for Transactions

MongoDB requires a replica set configuration to support transactions. Even for local development, you need to configure MongoDB as a replica set (even if it's just a single-node replica set).

## Setup Instructions

Follow these steps to configure MongoDB for transaction support:

1. **Update your docker-compose.yml file** to include replica set configuration:

   ```yaml
   version: "3"
   services:
     mongodb:
       image: mongo:5.0
       container_name: mongodb
       hostname: mongodb
       ports:
         - "27017:27017"
       volumes:
         - /data/db:/data/db/coach
       command: ["--replSet", "rs0", "--bind_ip_all"]
   ```

2. **Start MongoDB**:

   ```bash
   docker-compose up -d
   ```

3. **Initialize the replica set**:

   ```bash
   docker exec mongodb mongosh --eval '
     rs.initiate({
       _id: "rs0",
       members: [
         { _id: 0, host: "mongodb:27017" }
       ]
     })
   '
   ```

4. **Update your MongoDB connection string** in your `.env` file:
   ```
   DB_HOST=mongodb://localhost:27017/coach?replicaSet=rs0&directConnection=true
   ```

## Troubleshooting

If you encounter connection issues, you might need to add the following entry to your `/etc/hosts` file:

```
127.0.0.1 mongodb
```

However, most systems will work without this step, as Docker's internal DNS resolution handles the container hostname correctly.

## When to Repeat Setup

You only need to perform these steps:

- The first time you set up the project on a new computer
- If you completely remove the MongoDB container and its volumes
- If you encounter issues with the replica set configuration

The replica set configuration is persistent and stored in the MongoDB data volume, so you don't need to repeat this process every time you start development.
