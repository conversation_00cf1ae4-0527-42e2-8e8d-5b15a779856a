# See http://help.github.com/ignore-files/ for more about ignoring files.

# compiled output
/dist
/tmp
/out-tsc

# dependencies
/node_modules

# IDEs and editors
/.idea
.project
.classpath
.idx
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# misc
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings

# System Files
.DS_Store
Thumbs.db
# ignore all env files
/**/*.env
/**/*.env.*

# Next.js
.next
apps/api/uploads/

.nx/

vite.config.*.timestamp*
vitest.config.*.timestamp*


.cursor/rules/nx-rules.mdc
.github/instructions/nx.instructions.md
apps/api/.local.secrets
apps/api/.prod.secrets
.cursor/mcp.json
