export interface TranslationSchema {
  common: {
    "back-to-home": string;
    errors: {
      "organization-not-found": string;
    };
    loading: string;
  };
  application: {
    "confirm-title": string;
    "confirm-content": string;
    "apply-button": {
      applying: string;
      apply: string;
    };
    "already-applied": string;
    labels: {
      me: string;
      guardian: string;
    };
    form: {
      player: {
        title: string;
        description: string;
        fields: {
          "first-name": string;
          "last-name": string;
          dob: string;
          email: string;
          phone: string;
          gender: string;
          "gender-other": string;
          "medical-conditions": string;
          "playing-experience": string;
          "experience-description": string;
          "preferred-position": string;
          postcode: string;
          "playing-experience-options": {
            club: string;
            school: string;
            recreational: string;
            none: string;
          };
          "playing-experience-description": string;
          "preferred-playing-position": string;
          address: {
            address: string;
            postcode: string;
          };
          "gender-options": {
            male: string;
            female: string;
            undisclosed: string;
            other: string;
          };
          "age-confirmation": string;
        };
      };
      guardian: {
        title: string;
        description: string;
        fields: {
          "first-name": string;
          "last-name": string;
          dob: string;
          email: string;
          phone: string;
        };
      };
      "age-confirmation": string;
      terms: {
        agree: string;
        "accept-first": string;
      };
      submit: string;
      errors: {
        generic: string;
        "already-registered": string;
      };
    };
    header: {
      "not-open": string;
    };
    errors: {
      generic: string;
    };
  };
  login: {
    title: string;
    fields: {
      email: string;
      "login-code": string;
    };
    buttons: {
      "send-code": string;
      login: string;
    };
    errors: {
      generic: string;
    };
  };
  validation: {
    dob: {
      "too-big": string;
      "too-small": string;
    };
    email: {
      "same-guardian-player-email": string;
    };
  };
  eligibility: {
    checking: string;
    "email-exists": string;
    "guardian-exists": string;
    "login-button": string;
  };
  auth: {
    form: {
      legend: string;
      code: {
        label: string;
      };
      submit: string;
      error: string;
    };
    page: {
      title: string;
      "invalid-link": string;
      "login-success": string;
      "send-new-code": string;
    };
    register: {
      legend: string;
      email: {
        label: string;
      };
      submit: string;
      error: string;
    };
    "register-page": {
      title: string;
      "enter-code-manually": string;
    };
  };
  documents: {
    title: string;
    photo: {
      title: string;
    };
    passport: {
      title: string;
      loading: string;
      error: string;
      "view-image": string;
    };
    submit: {
      button: string;
      "confirm-title": string;
      "confirm-content": string;
      "submitted-on": string;
      success: string;
      error: string;
    };
  };
  events: {
    item: {
      "starts-at": string;
      "ends-at": string;
      "meet-at": string;
      "description-title": string;
      "attendance-label": string;
      responses: {
        yes: string;
        no: string;
        maybe: string;
      };
      location: {
        "get-directions": string;
      };
      responding: string;
    };
    list: {
      loading: string;
      error: string;
      "no-events": string;
      "response-success": string;
      "response-error": string;
    };
    filters: {
      "event-type": {
        label: string;
        all: string;
        training: string;
        match: string;
        meeting: string;
        other: string;
      };
      "date-periods": {
        "past-30": string;
        "next-7": string;
        "next-30": string;
      };
      dates: {
        "start-date": string;
        "end-date": string;
      };
      submit: string;
    };
    page: {
      title: string;
    };
  };
  "player-selector": {
    loading: string;
    error: string;
    label: string;
    roles: {
      me: string;
      guardian: string;
    };
  };
  sidebar: {
    title: string;
    navigation: {
      events: string;
      dashboard: string;
      profile: string;
      documents: string;
    };
    actions: {
      logout: string;
    };
  };
  payments: {
    item: {
      complete: string;
      completed: string;
      console: {
        redirecting: string;
      };
    };
    page: {
      title: string;
      errors: {
        "no-player": string;
        generic: string;
      };
      loading: string;
    };
  };
  "player-selector-page": {
    title: string;
    loading: string;
    error: string;
    roles: {
      me: string;
      guardian: string;
    };
    select: string;
  };
  profile: {
    page: {
      title: string;
      alerts: {
        success: string;
      };
    };
    form: {
      title: string;
      sections: {
        player: {
          title: string;
          "first-name": string;
          "last-name": string;
          dob: string;
          email: string;
          phone: string;
          gender: {
            label: string;
            options: {
              male: string;
              female: string;
              undisclosed: string;
              other: string;
              specify: string;
            };
          };
          "medical-conditions": string;
          experience: {
            label: string;
            options: {
              club: string;
              school: string;
              recreational: string;
              none: string;
            };
            description: string;
          };
          position: string;
          address: {
            "first-line": string;
            postcode: string;
          };
        };
        guardian: {
          title: string;
          subtitle: string;
          "first-name": string;
          "last-name": string;
          dob: string;
          email: string;
          phone: string;
        };
      };
      "age-alert": string;
      submit: string;
      error: string;
    };
  };
  providers: {
    loading: {
      user: string;
      players: string;
    };
    errors: {
      user: string;
      players: string;
      "no-players": string;
    };
    actions: {
      login: string;
      home: string;
    };
  };
  language: {
    switch: {
      en: string;
      es: string;
    };
  };
}
