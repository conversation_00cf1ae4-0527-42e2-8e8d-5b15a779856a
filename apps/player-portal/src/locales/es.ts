import { TranslationSchema } from "./types";

export const translations: TranslationSchema = {
  common: {
    "back-to-home": "Volver al inicio",
    errors: {
      "organization-not-found": "Organización no encontrada.",
    },
    loading: "Cargando...",
  },
  application: {
    "confirm-title": "Por favor confirma",
    "confirm-content": "¿Estás seguro que quieres aplicar?",
    "apply-button": {
      applying: "Aplicando...",
      apply: "Aplicar",
    },
    "already-applied": "Ese jugador ya ha aplicado al club.",
    labels: {
      me: "(Yo)",
      guardian: "(<PERSON><PERSON>)",
    },
    form: {
      player: {
        title: "Preguntas sobre el jugador",
        description:
          "Las siguientes preguntas están específicamente relacionadas con el jugador que está registrando. Asegúrese de que todos los detalles sean correctos antes de enviar el formulario, de lo contrario, la solicitud podría no ser aceptada.",
        fields: {
          "first-name": "<PERSON><PERSON>",
          "last-name": "<PERSON><PERSON><PERSON><PERSON>",
          dob: "<PERSON>cha de nacimiento",
          email: "Correo electrónico",
          phone: "Teléfono",
          gender: "Género",
          "gender-other": "Por favor especifica",
          "medical-conditions": "Condiciones médicas",
          "playing-experience": "Experiencia previa como jugador",
          "experience-description": "Cuéntanos más sobre tu experiencia como jugador",
          "preferred-position": "Posición preferida",
          postcode: "Código postal",
          "playing-experience-options": {
            club: "Club",
            school: "Escuela",
            recreational: "Recreativo",
            none: "Ninguna",
          },
          "playing-experience-description": "Cuéntanos más sobre tu experiencia como jugador",
          "preferred-playing-position": "Posición preferida",
          address: {
            address: "Dirección del jugador ( Primera línea )",
            postcode: "Código postal",
          },
          "gender-options": {
            male: "Masculino",
            female: "Femenino",
            undisclosed: "Prefiero no decirlo",
            other: "Otro",
          },
          "age-confirmation": "Parece que tienes {{age}} años. ¿Es correcto?",
        },
      },
      guardian: {
        title: "Preguntas sobre padres/tutores",
        description:
          "Las siguientes preguntas están específicamente relacionadas con el padre/tutor del jugador que está registrando. Asegúrese de que todos los detalles sean correctos antes de enviar el formulario, de lo contrario, la solicitud podría no ser aceptada.",
        fields: {
          "first-name": "Nombre",
          "last-name": "Apellido",
          dob: "Fecha de nacimiento",
          email: "Correo electrónico",
          phone: "Teléfono",
        },
      },
      "age-confirmation": "Parece que tienes {{age}} años. ¿Es correcto?",
      terms: {
        agree: "Acepto los términos",
        "accept-first": "Por favor acepta los términos primero",
      },
      submit: "Enviar",
      errors: {
        generic: "Algo salió mal",
        "already-registered": "Jugador ya registrado",
      },
    },
    header: {
      "not-open": "Organización no encontrada",
    },
    errors: {
      generic: "Algo salió mal",
    },
  },
  login: {
    title: "Iniciar sesión",
    fields: {
      email: "Correo electrónico",
      "login-code": "Código de acceso",
    },
    buttons: {
      "send-code": "Enviar código de acceso",
      login: "Iniciar sesión",
    },
    errors: {
      generic: "Algo salió mal",
    },
  },
  validation: {
    dob: {
      "too-big": "La fecha de nacimiento parece inválida, por favor ingrese un año más reciente.",
      "too-small": "Un jugador debe tener al menos 2 años",
    },
    email: {
      "same-guardian-player-email":
        "El tutor y el jugador no pueden compartir la misma dirección de correo electrónico",
    },
  },
  eligibility: {
    checking: "Verificando elegibilidad del jugador...",
    "email-exists": "Ya existe un jugador con ese correo electrónico.",
    "guardian-exists": "Ya existe un jugador con esos nombres y detalles de tutor.",
    "login-button": "Iniciar sesión",
  },
  auth: {
    form: {
      legend: "Formulario de inicio de sesión del portal del jugador",
      code: {
        label: "Código",
      },
      submit: "Iniciar sesión",
      error: "Algo salió mal.",
    },
    page: {
      title: "Iniciar sesión con código",
      "invalid-link": "Enlace de inicio de sesión inválido.",
      "login-success": "Inicio de sesión exitoso. Serás redirigido en un segundo.",
      "send-new-code": "Enviar nuevo código",
    },
    register: {
      legend: "Formulario de registro de candidatos",
      email: {
        label: "Correo electrónico",
      },
      submit: "Enviar",
      error: "Algo salió mal.",
    },
    "register-page": {
      title: "Enviar código de acceso",
      "enter-code-manually": "Ingresar el código manualmente",
    },
  },
  documents: {
    title: "Documentos del jugador",
    photo: {
      title: "Foto",
    },
    passport: {
      title: "DNI / Pasaporte",
      loading: "Cargando...",
      error: "Ocurrió un error",
      "view-image": "Ver imagen",
    },
    submit: {
      button: "Enviar documentos",
      "confirm-title": "Por favor confirma",
      "confirm-content":
        "¿Estás seguro de que quieres enviar tus documentos? No podrás editarlos después.",
      "submitted-on": "Documentos enviados el {{date}}",
      success: "¡Documentos enviados con éxito!",
      error: "Error al enviar los documentos.",
    },
  },
  events: {
    item: {
      "starts-at": "Comienza a las",
      "ends-at": "Termina a las",
      "meet-at": "Reunirse a las",
      "description-title": "Descripción y notas",
      "attendance-label": "Estado de asistencia al evento",
      responses: {
        yes: "Sí",
        no: "No",
        maybe: "Quizás",
      },
      location: {
        "get-directions": "Obtener direcciones",
      },
      responding: "Respondiendo al evento...",
    },
    list: {
      loading: "Cargando eventos...",
      error: "Error al cargar los eventos",
      "no-events": "No hay eventos",
      "response-success": "Respuesta guardada",
      "response-error": "Error al guardar la respuesta",
    },
    filters: {
      "event-type": {
        label: "Tipo de evento",
        all: "Todos",
        training: "Entrenamiento",
        match: "Partido",
        meeting: "Reunión",
        other: "Otro",
      },
      "date-periods": {
        "past-30": "Últimos 30 días",
        "next-7": "Próximos 7 días",
        "next-30": "Próximos 30 días",
      },
      dates: {
        "start-date": "Fecha de inicio",
        "end-date": "Fecha de fin",
      },
      submit: "Mostrar",
    },
    page: {
      title: "Eventos del equipo",
    },
  },
  "player-selector": {
    loading: "Cargando jugadores...",
    error: "Error al cargar jugadores.",
    label: "Jugador activo",
    roles: {
      me: "Yo",
      guardian: "Tutor",
    },
  },
  sidebar: {
    title: "Team Assist",
    navigation: {
      events: "Eventos",
      dashboard: "Panel",
      profile: "Perfil",
      documents: "Documentos",
    },
    actions: {
      logout: "Cerrar sesión",
    },
  },
  payments: {
    item: {
      complete: "Completar",
      completed: "¡Completado!",
      console: {
        redirecting: "Redirigiendo a la página de pago...",
      },
    },
    page: {
      title: "Pagos",
      errors: {
        "no-player": "Ningún jugador seleccionado",
        generic: "Algo salió mal",
      },
      loading: "Cargando pagos...",
    },
  },
  "player-selector-page": {
    title: "Jugadores asociados",
    loading: "Cargando jugadores...",
    error: "Error al cargar jugadores.",
    roles: {
      me: "(Yo)",
      guardian: "(Tutor)",
    },
    select: "Seleccionar",
  },
  profile: {
    page: {
      title: "Detalles del perfil",
      alerts: {
        success: "Perfil actualizado.",
      },
    },
    form: {
      title: "Detalles del perfil",
      sections: {
        player: {
          title: "Preguntas sobre el jugador",
          "first-name": "Nombre",
          "last-name": "Apellido",
          dob: "Fecha de nacimiento",
          email: "Correo electrónico",
          phone: "Teléfono",
          gender: {
            label: "Género",
            options: {
              male: "Masculino",
              female: "Femenino",
              undisclosed: "Prefiero no decirlo",
              other: "Otro",
              specify: "Por favor, especifica",
            },
          },
          "medical-conditions": "Condiciones médicas",
          experience: {
            label: "Experiencia previa como jugador",
            options: {
              club: "Club",
              school: "Escuela",
              recreational: "Recreativo",
              none: "Ninguna",
            },
            description: "Cuéntanos más sobre tu experiencia como jugador",
          },
          position: "Posición preferida",
          address: {
            "first-line": "Dirección del jugador (Primera línea)",
            postcode: "Código postal",
          },
        },
        guardian: {
          title: "Preguntas sobre padres/tutores",
          subtitle:
            "Las siguientes preguntas están específicamente relacionadas con el padre/tutor del jugador que estás registrando. Por favor, asegúrate de que todos los detalles sean correctos antes de enviar el formulario, de lo contrario la solicitud podría no ser aceptada.",
          "first-name": "Nombre",
          "last-name": "Apellido",
          dob: "Fecha de nacimiento",
          email: "Correo electrónico",
          phone: "Teléfono",
        },
      },
      "age-alert": "Parece que tienes {{age}} años. ¿Es correcto?",
      submit: "Guardar",
      error: "Algo salió mal",
    },
  },
  providers: {
    loading: {
      user: "Cargando usuario...",
      players: "Cargando jugadores...",
    },
    errors: {
      user: "Algo salió mal.",
      players: "Error al cargar jugadores.",
      "no-players": "No encontramos jugadores asociados.",
    },
    actions: {
      login: "Iniciar sesión de nuevo",
      home: "Volver al inicio",
    },
  },
  language: {
    switch: {
      en: "Inglés",
      es: "Español",
    },
  },
};
