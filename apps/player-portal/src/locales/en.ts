import { TranslationSchema } from "./types";

export const translations: TranslationSchema = {
  common: {
    "back-to-home": "Back to home",
    errors: {
      "organization-not-found": "Organization not found.",
    },
    loading: "Loading...",
  },
  application: {
    "confirm-title": "Please confirm",
    "confirm-content": "Are you sure you want to apply?",
    "apply-button": {
      applying: "Applying...",
      apply: "Apply",
    },
    "already-applied": "That player has already applied to the club.",
    labels: {
      me: "(Me)",
      guardian: "(Guardian)",
    },
    form: {
      player: {
        title: "Questions about the player",
        description:
          "The following questions are specifically related to the player you are registering for. Please ensure all details are correct before submitting the form, otherwise the application may not be accepted.",
        fields: {
          "first-name": "First name",
          "last-name": "Surname",
          dob: "Date of birth",
          email: "Email",
          phone: "Phone",
          gender: "Gender",
          "gender-other": "Please specify",
          "medical-conditions": "Medical conditions",
          "playing-experience": "Previous player experience",
          "experience-description": "Tell us more about your playing experience",
          "preferred-position": "Preferred playing position",
          postcode: "Postcode",
          "playing-experience-options": {
            club: "Club",
            school: "School",
            recreational: "Recreational",
            none: "None",
          },
          "playing-experience-description": "Tell us more about your playing experience",
          "preferred-playing-position": "Preferred playing position",
          address: {
            address: "Player home address ( First line )",
            postcode: "Postcode",
          },
          "gender-options": {
            male: "Male",
            female: "Female",
            undisclosed: "Do not wish to disclose",
            other: "Other",
          },
          "age-confirmation": "You appear to be {{age}} years old. Is that correct?",
        },
      },
      guardian: {
        title: "Questions About Parents/Guardians",
        description:
          "The following questions are specifically related to the parent/guardian of the player that you are registering for. Please ensure all details are correct before submitting the form, otherwise the application may not be accepted.",
        fields: {
          "first-name": "First name",
          "last-name": "Surname",
          dob: "Date of birth",
          email: "Email",
          phone: "Phone",
        },
      },
      "age-confirmation": "You appear to be {{age}} years old. Is that correct?",
      terms: {
        agree: "I agree to the terms",
        "accept-first": "Please accept the terms first",
      },
      submit: "Submit",
      errors: {
        generic: "Something went wrong",
        "already-registered": "Player already registered",
      },
    },
    header: {
      "not-open": "Organization not found",
    },
    errors: {
      generic: "Something went wrong",
    },
  },
  login: {
    title: "Login",
    fields: {
      email: "Email",
      "login-code": "Login code",
    },
    buttons: {
      "send-code": "Send login code",
      login: "Log in",
    },
    errors: {
      generic: "Something went wrong",
    },
  },
  validation: {
    dob: {
      "too-big": "Date of birth seems invalid, please enter a more recent year.",
      "too-small": "A player must be at least 2 years old",
    },
    email: {
      "same-guardian-player-email": "Guardian and player cannot share the same email address",
    },
  },
  eligibility: {
    checking: "Checking player eligibility...",
    "email-exists": "Player with that email already exists.",
    "guardian-exists": "Player with that names and guardian details already exists.",
    "login-button": "Login",
  },
  auth: {
    form: {
      legend: "Player portal login form",
      code: {
        label: "Code",
      },
      submit: "Login",
      error: "Something went wrong.",
    },
    page: {
      title: "Login with code",
      "invalid-link": "Invalid login link.",
      "login-success": "Login successful. You should get redirected in a second.",
      "send-new-code": "Send new code",
    },
    register: {
      legend: "Candidate app register form",
      email: {
        label: "Email",
      },
      submit: "Send",
      error: "Something went wrong.",
    },
    "register-page": {
      title: "Send login code",
      "enter-code-manually": "Enter the code manually",
    },
  },
  documents: {
    title: "Player documents",
    photo: {
      title: "Photo",
    },
    passport: {
      title: "ID card / Passport",
      loading: "Loading...",
      error: "An error occurred",
      "view-image": "View image",
    },
    submit: {
      button: "Submit documents",
      "confirm-title": "Please confirm",
      "confirm-content":
        "Are you sure you want to submit your documents? You won't be able to edit them afterwards.",
      "submitted-on": "Documents submitted on {{date}}",
      success: "Documents submitted successfully!",
      error: "Failed to submit documents.",
    },
  },
  events: {
    item: {
      "starts-at": "Starts at",
      "ends-at": "Ends at",
      "meet-at": "Meet at",
      "description-title": "Description & Notes",
      "attendance-label": "Event attendance status",
      responses: {
        yes: "Yes",
        no: "No",
        maybe: "Maybe",
      },
      location: {
        "get-directions": "Get directions",
      },
      responding: "Responding to event...",
    },
    list: {
      loading: "Loading events...",
      error: "Error loading events",
      "no-events": "No events",
      "response-success": "Event response saved",
      "response-error": "Error saving event response",
    },
    filters: {
      "event-type": {
        label: "Event type",
        all: "All",
        training: "Training",
        match: "Match",
        meeting: "Meeting",
        other: "Other",
      },
      "date-periods": {
        "past-30": "Past 30 days",
        "next-7": "Next 7 days",
        "next-30": "Next 30 days",
      },
      dates: {
        "start-date": "Start date",
        "end-date": "End date",
      },
      submit: "Show",
    },
    page: {
      title: "Team events",
    },
  },
  "player-selector": {
    loading: "Loading players...",
    error: "Failed to load players.",
    label: "Active player",
    roles: {
      me: "Me",
      guardian: "Guardian",
    },
  },
  sidebar: {
    title: "Team Assist",
    navigation: {
      events: "Events",
      dashboard: "Dashboard",
      profile: "Profile",
      documents: "Documents",
    },
    actions: {
      logout: "Logout",
    },
  },
  payments: {
    item: {
      complete: "Complete",
      completed: "Completed!",
      console: {
        redirecting: "Redirecting to payment page...",
      },
    },
    page: {
      title: "Payments",
      errors: {
        "no-player": "No player selected",
        generic: "Something went wrong",
      },
      loading: "Loading payments...",
    },
  },
  "player-selector-page": {
    title: "Associated players",
    loading: "Loading players...",
    error: "Failed to load players.",
    roles: {
      me: "(Me)",
      guardian: "(Guardian)",
    },
    select: "Select",
  },
  profile: {
    page: {
      title: "Profile details",
      alerts: {
        success: "Profile updated.",
      },
    },
    form: {
      title: "Profile details",
      sections: {
        player: {
          title: "Questions about the player",
          "first-name": "First name",
          "last-name": "Surname",
          dob: "Date of birth",
          email: "Email",
          phone: "Phone",
          gender: {
            label: "Gender",
            options: {
              male: "Male",
              female: "Female",
              undisclosed: "Do not wish to disclose",
              other: "Other",
              specify: "Please specify",
            },
          },
          "medical-conditions": "Medical conditions",
          experience: {
            label: "Previous player experience",
            options: {
              club: "Club",
              school: "School",
              recreational: "Recreational",
              none: "None",
            },
            description: "Tell us more about your playing experience",
          },
          position: "Preferred playing position",
          address: {
            "first-line": "Player home address ( First line )",
            postcode: "Postcode",
          },
        },
        guardian: {
          title: "Questions About Parents/Guardians",
          subtitle:
            "The following questions are specifically related to the parent/guardian of the player that you are registering for. Please ensure all details are correct before submitting the form, otherwise the application may not be accepted.",
          "first-name": "First name",
          "last-name": "Surname",
          dob: "Date of birth",
          email: "Email",
          phone: "Phone",
        },
      },
      "age-alert": "You appear to be {{age}} years old. Is that correct?",
      submit: "Save",
      error: "Something went wrong",
    },
  },
  providers: {
    loading: {
      user: "Loading user...",
      players: "Loading players...",
    },
    errors: {
      user: "Something went wrong.",
      players: "Failed to load players.",
      "no-players": "We didn't find any associated players.",
    },
    actions: {
      login: "Log in again",
      home: "Go back to home",
    },
  },
  language: {
    switch: {
      en: "English",
      es: "Spanish",
    },
  },
};
