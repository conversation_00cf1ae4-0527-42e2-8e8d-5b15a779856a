import { FC, PropsWithChildren, useCallback } from "react";

import { useAuth, PublicRoute, PrivateRoute } from "@mio/ui";
import { Routing } from "./shared";

export const AppPublicRoute: FC<PropsWithChildren> = ({ children }) => {
  const { login, isLogged } = useAuth();

  const authFunction = useCallback(() => {
    return { isLoading: login.isLoading, isLogged };
  }, [login, isLogged]);

  return (
    <PublicRoute redirectTo={Routing.Urls.home} authFunction={authFunction}>
      {children}
    </PublicRoute>
  );
};

export const AppPrivateRoute: FC<PropsWithChildren> = ({ children }) => {
  const { login, isLogged } = useAuth();

  const authFunction = useCallback(() => {
    return { isLoading: login.isLoading, isLogged };
  }, [login, isLogged]);

  return (
    <PrivateRoute redirectTo={Routing.Urls.register} authFunction={authFunction}>
      {children}
    </PrivateRoute>
  );
};
