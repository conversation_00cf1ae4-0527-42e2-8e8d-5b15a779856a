import { FC } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import { omitBy, isObject } from "lodash/fp";
import { useTranslation } from "react-i18next";

import {
  Box,
  Stack,
  DatePicker,
  TextField,
  LoadingButton,
  SearchIcon,
  useFormResolver,
  FormControl,
  Button,
  Select,
  InputLabel,
  MenuItem,
} from "@mio/ui";
import { CustomDate, isError, Primitive, TeamEvent, CustomNumber } from "@mio/helpers";

type DataShape = Primitive<TeamEvent.QueryDto>;

type Props = {
  isLoading?: boolean;
  activeFilter: TeamEvent.QueryDto;
  onQueryChange: (dto: TeamEvent.QueryDto) => void;
};

export const EventFilters: FC<Props> = ({ onQueryChange, isLoading, activeFilter }) => {
  const { t } = useTranslation();
  const initialDatePeriod = TeamEvent.Entity.constructFutureRange(
    CustomNumber.castToPositiveInteger(7),
  );

  const formResolver = useFormResolver<DataShape>(TeamEvent.Entity.toQueryDto);

  const { handleSubmit, watch, formState, control, setValue } = useForm<DataShape>({
    defaultValues: {
      startDate: initialDatePeriod.startDate,
      endDate: initialDatePeriod.endDate,
    },
    mode: "onChange",
    resolver: formResolver,
  });

  const query = watch();

  const { errors: formErrors, submitCount } = formState;
  const hasSubmitted = submitCount > 0;
  const errors = hasSubmitted ? {} : formErrors;

  const applyDateRange = (data: unknown) => {
    const withoutEmptyFields = omitBy((value) => value === "", isObject(data) ? data : {});
    const parsed = TeamEvent.Entity.toQueryDto(withoutEmptyFields);

    if (!isError(parsed)) {
      onQueryChange({ ...activeFilter, ...parsed });
    }
  };

  return (
    <Stack gap={2}>
      <Stack direction="row" alignItems="center" flexWrap="wrap" gap={1}>
        <FormControl sx={{ minWidth: "200px" }}>
          <InputLabel>{t("events.filters.event-type.label")}</InputLabel>
          <Select
            label={t("events.filters.event-type.label")}
            value={activeFilter.agenda || "all"}
            onChange={(event) => {
              const value = event.target.value;
              onQueryChange({
                ...activeFilter,
                agenda: value === "all" ? undefined : (value as TeamEvent.Agenda),
              });
            }}
          >
            <MenuItem value="all">{t("events.filters.event-type.all")}</MenuItem>
            <MenuItem value={TeamEvent.Agenda.TrainingSession}>
              {t("events.filters.event-type.training")}
            </MenuItem>
            <MenuItem value={TeamEvent.Agenda.Match}>
              {t("events.filters.event-type.match")}
            </MenuItem>
            <MenuItem value={TeamEvent.Agenda.Meeting}>
              {t("events.filters.event-type.meeting")}
            </MenuItem>
            <MenuItem value={TeamEvent.Agenda.Other}>
              {t("events.filters.event-type.other")}
            </MenuItem>
          </Select>
        </FormControl>

        <Button
          variant="text"
          onClick={() => {
            const dates = TeamEvent.Entity.constructPastRange(
              CustomNumber.castToPositiveInteger(30),
            );
            setValue("startDate", dates.startDate);
            setValue("endDate", dates.endDate);
          }}
        >
          {t("events.filters.date-periods.past30")}
        </Button>
        <Button
          variant="text"
          onClick={() => {
            const dates = TeamEvent.Entity.constructFutureRange(
              CustomNumber.castToPositiveInteger(7),
            );
            setValue("startDate", dates.startDate);
            setValue("endDate", dates.endDate);
          }}
        >
          {t("events.filters.date-periods.next7")}
        </Button>
        <Button
          variant="text"
          onClick={() => {
            const dates = TeamEvent.Entity.constructFutureRange(
              CustomNumber.castToPositiveInteger(30),
            );
            setValue("startDate", dates.startDate);
            setValue("endDate", dates.endDate);
          }}
        >
          {t("events.filters.date-periods.next30")}
        </Button>
      </Stack>

      <Box mt={1} component="form" onSubmit={handleSubmit(applyDateRange)}>
        <Stack direction="row" alignItems="center" flexWrap="wrap" gap={2}>
          <FormControl sx={{ width: { xs: 1, md: 1 / 5 } }}>
            <Controller
              name="startDate"
              control={control}
              render={({ field: { onChange, ...restField } }) => (
                <DatePicker
                  label={t("events.filters.dates.start-date")}
                  inputFormat="dd/MM/y"
                  openTo="day"
                  onChange={(newValue) => {
                    onChange(CustomDate.validOrEmpty(newValue));
                  }}
                  renderInput={(params) => (
                    <TextField
                      fullWidth
                      helperText={errors.startDate?.message || ""}
                      error={!!errors.startDate?.message}
                      aria-invalid={!!errors.startDate?.message}
                      {...params}
                    />
                  )}
                  {...restField}
                />
              )}
            />
          </FormControl>

          <FormControl sx={{ width: { xs: 1, md: 1 / 5 } }}>
            <Controller
              name="endDate"
              control={control}
              render={({ field: { onChange, ...restField } }) => (
                <DatePicker
                  label={t("events.filters.dates.end-date")}
                  inputFormat="dd/MM/y"
                  openTo="day"
                  minDate={query.startDate}
                  onChange={(newValue) => {
                    onChange(CustomDate.validOrEmpty(newValue));
                  }}
                  renderInput={(params) => (
                    <TextField
                      fullWidth
                      helperText={errors.endDate?.message || ""}
                      error={!!errors.endDate?.message}
                      aria-invalid={!!errors.endDate?.message}
                      {...params}
                    />
                  )}
                  {...restField}
                />
              )}
            />
          </FormControl>

          <Stack justifyContent="center" alignItems="center" ml={3}>
            <LoadingButton
              type="submit"
              startIcon={<SearchIcon />}
              loading={isLoading}
              variant="contained"
              color="secondary"
              size="large"
            >
              {t("events.filters.submit")}
            </LoadingButton>
          </Stack>
        </Stack>
      </Box>
    </Stack>
  );
};
