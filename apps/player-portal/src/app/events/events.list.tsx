import { FC } from "react";
import { useTranslation } from "react-i18next";

import { CustomDate, CustomNumber, TeamEvent } from "@mio/helpers";
import {
  Alert,
  Card,
  Divider,
  LocalLoader,
  PageError,
  Stack,
  Typography,
  playersState,
  useGetPlayerTeamEvents,
  ToggleSnackbar,
} from "@mio/ui";

import EventItem from "./event.item";
import { EventFilters } from "./filters";
import { useEventsByDate } from "./useEventsByDate";

const EventsList: FC = () => {
  const { t } = useTranslation();
  const initialDatePeriod = TeamEvent.Entity.constructFutureRange(
    CustomNumber.castToPositiveInteger(7),
  );
  const player = playersState.useActivePlayer();

  const { query, setQuery, activeFilter, respondToEvent } =
    useGetPlayerTeamEvents(initialDatePeriod);
  const eventsByDate = useEventsByDate(query?.data || []);

  if (query.isError) return <PageError message={t("events.list.error")} />;

  return (
    <Stack gap={4}>
      <EventFilters
        onQueryChange={setQuery}
        isLoading={query.isLoading}
        activeFilter={activeFilter}
      />

      {query?.data?.length === 0 && <Alert severity="info">{t("events.list.no-events")}</Alert>}

      {query.isLoading && <LocalLoader message={t("events.list.loading")} />}

      <Stack gap={4}>
        {eventsByDate.map((item) => (
          <Card
            key={item.date}
            sx={{
              p: 2,
              pt: 2,
              border: 1,
              borderColor: "grey.200",
              "&:hover": { borderColor: "grey.400" },
            }}
            elevation={0}
          >
            <Stack direction="row" gap={4}>
              <Stack direction="row" gap={2}>
                <Typography component="h2" variant="h6" fontWeight="bold">
                  {CustomDate.toDisplayDate(item.date)}
                </Typography>

                <Divider orientation="vertical" flexItem />
              </Stack>

              <Stack gap={2} flexGrow={1}>
                {item.events.map((event, index) => (
                  <EventItem
                    event={event}
                    key={event.id}
                    addDivider={index !== item.events.length - 1}
                    onRespondToEvent={(status) => {
                      respondToEvent.mutate({
                        teamEventId: event.id,
                        status,
                        playerId: player.id,
                      });
                    }}
                    isRespondingToEvent={respondToEvent.isLoading}
                  />
                ))}
              </Stack>
            </Stack>
          </Card>
        ))}
      </Stack>

      <ToggleSnackbar open={respondToEvent.isSuccess}>
        <Alert severity="success">{t("events.list.response-success")}</Alert>
      </ToggleSnackbar>

      <ToggleSnackbar open={respondToEvent.isError}>
        <Alert severity="error">{t("events.list.response-error")}</Alert>
      </ToggleSnackbar>
    </Stack>
  );
};

export default EventsList;
