import { FC, useId } from "react";
import { useTranslation } from "react-i18next";

import { TeamEvent, CustomDate } from "@mio/helpers";
import {
  Stack,
  Typography,
  Box,
  Button,
  ExpandMoreIcon,
  CheckIcon,
  playersState,
  LocalLoader,
  NavigationIcon,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from "@mio/ui";
import { EventLabel } from "./event-label";

type Props = {
  event: TeamEvent.PlayerTeamEvent;
  addDivider: boolean;
  onRespondToEvent: (status: TeamEvent.AttendanceStatus) => void;
  isRespondingToEvent: boolean;
};

const EventItem: FC<Props> = ({ event, addDivider, onRespondToEvent, isRespondingToEvent }) => {
  const { t } = useTranslation();
  const player = playersState.useActivePlayer();

  const attendance = TeamEvent.Entity.getAttendance(event, player.id);
  const location = TeamEvent.Entity.getLocation(event);
  const description = TeamEvent.Entity.getDescription(event);
  const notesId = useId();

  return (
    <Stack
      sx={{
        borderBottom: addDivider ? 1 : "none",
        borderColor: "divider",
        display: "grid",
        gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
        gap: 2,
        pb: 1,
      }}
    >
      <EventLabel event={event} />

      <Stack gap={3}>
        <Typography component="h3" variant="body1" fontWeight="bold" lineHeight="initial">
          {event.team.name}: {TeamEvent.Entity.getName(event)}
        </Typography>

        <Stack flexWrap="wrap">
          <Typography color="text.secondary">
            <Box component="span" display="inline-block" minWidth="80px">
              {t("events.item.starts-at")}
            </Box>{" "}
            {CustomDate.toDisplayTime(event.startDateTime)}{" "}
          </Typography>
          <Typography color="text.secondary">
            <Box component="span" display="inline-block" minWidth="80px">
              {t("events.item.ends-at")}
            </Box>{" "}
            {CustomDate.toDisplayTime(event.endDateTime)}{" "}
          </Typography>
          {event.meetupMinutesBefore && (
            <Typography color="text.secondary">
              {t("events.item.meet-at")}{" "}
              {CustomDate.toDisplayTime(
                CustomDate.timeSubMinutes(event.meetupMinutesBefore, () => event.startDateTime),
              )}
            </Typography>
          )}

          {(description || event.notes) && (
            <Box mt={3}>
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />} aria-controls={notesId}>
                  {t("events.item.description-title")}
                </AccordionSummary>
                <AccordionDetails id={notesId}>
                  <Stack gap={1}>
                    <Typography>{description}</Typography>
                    <Typography>{event.notes}</Typography>
                  </Stack>
                </AccordionDetails>
              </Accordion>
            </Box>
          )}
        </Stack>
      </Stack>

      <Stack gap={3}>
        <Stack
          direction="row"
          gap={1}
          color="text.secondary"
          role="radiogroup"
          aria-label={t("events.item.attendance-label")}
        >
          <Button
            role="radio"
            aria-disabled={isRespondingToEvent}
            aria-checked={attendance?.status === TeamEvent.AttendanceStatus.Positive}
            variant="outlined"
            color="inherit"
            startIcon={attendance?.status === TeamEvent.AttendanceStatus.Positive && <CheckIcon />}
            onClick={() => {
              if (!isRespondingToEvent) {
                onRespondToEvent(TeamEvent.AttendanceStatus.Positive);
              }
            }}
          >
            {t("events.item.responses.yes")}
          </Button>
          <Button
            variant="outlined"
            color="inherit"
            startIcon={attendance?.status === TeamEvent.AttendanceStatus.Negative && <CheckIcon />}
            role="radio"
            aria-disabled={isRespondingToEvent}
            aria-checked={attendance?.status === TeamEvent.AttendanceStatus.Negative}
            onClick={() => {
              if (!isRespondingToEvent) {
                onRespondToEvent(TeamEvent.AttendanceStatus.Negative);
              }
            }}
          >
            {t("events.item.responses.no")}
          </Button>
          <Button
            variant="outlined"
            color="inherit"
            startIcon={attendance?.status === TeamEvent.AttendanceStatus.Maybe && <CheckIcon />}
            role="radio"
            aria-disabled={isRespondingToEvent}
            aria-checked={attendance?.status === TeamEvent.AttendanceStatus.Maybe}
            onClick={() => {
              if (!isRespondingToEvent) {
                onRespondToEvent(TeamEvent.AttendanceStatus.Maybe);
              }
            }}
          >
            {t("events.item.responses.maybe")}
          </Button>
        </Stack>

        {isRespondingToEvent && (
          <Box>
            <LocalLoader message={t("events.item.responding")} />
          </Box>
        )}

        <Stack gap={1}>
          <Typography color="text.secondary" fontSize="0.8em">
            {`${location.address}, ${location.postcode}`}
          </Typography>
          <Box>
            <Button
              variant="text"
              component="a"
              href={TeamEvent.Entity.getLocationLink(event)}
              target="_blank"
              startIcon={<NavigationIcon />}
            >
              {t("events.item.location.get-directions")}
            </Button>
          </Box>
        </Stack>
      </Stack>
    </Stack>
  );
};

export default EventItem;
