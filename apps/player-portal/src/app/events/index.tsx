import { FC } from "react";
import { useTranslation } from "react-i18next";

import { Stack, Typography } from "@mio/ui";

import { SidebarLayout } from "../layouts";
import EventsList from "./events.list";

const PlayerEvents: FC = () => {
  const { t } = useTranslation();

  return (
    <SidebarLayout>
      <Stack gap={5}>
        <Typography variant="h4" component="h1">
          {t("events.page.title")}
        </Typography>

        <EventsList />
      </Stack>
    </SidebarLayout>
  );
};

export default PlayerEvents;
