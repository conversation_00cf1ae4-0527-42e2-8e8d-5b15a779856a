import { FC } from "react";
import { startCase } from "lodash/fp";
import { exhaustive } from "exhaustive";

import { TeamEvent } from "@mio/helpers";
import {
  Box,
  Stack,
  MatchEventGraphic,
  useTheme,
  TrainingEventGraphic,
  MeetingEventGraphic,
  OtherEventGraphic,
} from "@mio/ui";

type Props = {
  event: TeamEvent.PlayerTeamEvent;
};

export const EventLabel: FC<Props> = ({ event }) => {
  const theme = useTheme();
  const agenda = TeamEvent.Entity.getAgenda(event);

  const bgColor = exhaustive(agenda, {
    [TeamEvent.Agenda.Match]: () => theme.palette.primary.main,
    [TeamEvent.Agenda.TrainingSession]: () => theme.palette.secondary.main,
    [TeamEvent.Agenda.Meeting]: () => theme.palette.other.purple,
    [TeamEvent.Agenda.Other]: () => theme.palette.other.blue,
    _: () => theme.palette.primary.main,
  });

  const EventGraphic = exhaustive(agenda, {
    [TeamEvent.Agenda.Match]: () => <MatchEventGraphic />,
    [TeamEvent.Agenda.TrainingSession]: () => <TrainingEventGraphic />,
    [TeamEvent.Agenda.Meeting]: () => <MeetingEventGraphic />,
    [TeamEvent.Agenda.Other]: () => <OtherEventGraphic />,
  });

  return (
    <Stack gap={2}>
      <Box>
        <Box
          sx={{
            display: "inline-block",
            backgroundColor: bgColor,
            color: "primary.contrastText",
            fontWeight: "bold",
            letterSpacing: 1,
            p: "4px",
            fontSize: ".7em",
            borderRadius: "4px",
            textAlign: "center",
            minWidth: "100px",
          }}
        >
          {startCase(TeamEvent.Entity.getAgenda(event))}
        </Box>
      </Box>

      <Box maxWidth="100px">{EventGraphic}</Box>
    </Stack>
  );
};
