import { useEffect } from "react";
import { useForm } from "react-hook-form";

import { CustomDate, Player } from "@mio/helpers";
import { useFormResolver } from "@mio/ui";

import { DataShape } from "./types";

/* encapsulates form logic */
export const usePlayerDetailsForm = (playerProfile: Player) => {
  const formResolver = useFormResolver<DataShape>(Player.toUpdateDto);

  const formMethods = useForm<DataShape>({
    defaultValues: {
      address: playerProfile.address,
      dob: playerProfile.dob,
      email: playerProfile.email,
      firstName: playerProfile.firstName,
      gender: playerProfile.gender,
      guardian: playerProfile.guardian,
      lastName: playerProfile.lastName,
      medicalConditions: playerProfile.medicalConditions,
      preferredPlayingPosition: playerProfile.preferredPlayingPosition,
      phone: playerProfile.phone,
      playingExperienceDescription: playerProfile.playingExperienceDescription,
      playingExperience: playerProfile.playingExperience,
    },
    resolver: formResolver,
    mode: "onChange",
  });

  const hasSubmitted = formMethods.formState.submitCount > 0;

  const { unregister } = formMethods;

  const data = formMethods.watch();
  const dob = formMethods.watch("dob");
  const guardianDob = formMethods.watch("guardian.dob");
  const playingExperience = formMethods.watch("playingExperience");
  const { errors } = formMethods.formState;

  const age = CustomDate.getYearsAgo(dob);
  const requiresAGuardian = age > 0 && dob && Player.requiresAGuardian(dob);
  const requiresAnEmail = age > 0 && dob && Player.requiresAnEmail(dob);

  /* deals with toggable fields */
  useEffect(() => {
    if (!requiresAGuardian) {
      unregister("guardian");
    }
  }, [requiresAGuardian, unregister]);

  /* deals with toggable fields */
  useEffect(() => {
    if (!requiresAnEmail) {
      unregister("email");
    }
  }, [requiresAnEmail, unregister]);

  return {
    formMethods,
    hasSubmitted,
    guardianDob,
    data,
    dob,
    playingExperience,
    errors,
    requiresAnEmail,
    requiresAGuardian,
    age,
  };
};
