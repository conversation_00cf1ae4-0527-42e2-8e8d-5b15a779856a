import { PlayerAddress, Primitive, UpdatePlayerDto } from "@mio/helpers";
import { EmbeddedGuardian } from "@mio/helpers/lib/types/player/Guardian";

/* Primitive doesn't work automatically with nested objects thus the below workaround*/
export type DataShape = Partial<
  Omit<Primitive<UpdatePlayerDto>, "guardian" | "address"> & {
    guardian?: Partial<Primitive<EmbeddedGuardian>>;
    address: Primitive<PlayerAddress>;
  }
>;
