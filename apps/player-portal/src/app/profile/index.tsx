import { FC, PropsWithChildren } from "react";
import { useTranslation } from "react-i18next";

import { Player, isError } from "@mio/helpers";
import { Alert, ToggleSnackbar, playersState } from "@mio/ui";

import { DataShape } from "./types";
import { ProfileDetailsForm } from "./profileDetailsForm";
import { SidebarLayout } from "../layouts";

export const ProfileDetailsPage: FC<PropsWithChildren> = () => {
  const { t } = useTranslation();
  const updatePlayerProfile = playersState.useUpdatePlayerProfile();
  const activePlayer = playersState.useActivePlayer();
  const selectedPlayerId = playersState.useOptionalPlayerId();

  const handleSubmit = (data: DataShape) => {
    const parsedDto = Player.toEntity({
      id: selectedPlayerId,
      ...data,
    });

    if (!isError(parsedDto)) {
      updatePlayerProfile.mutate(parsedDto);
    }
  };

  return (
    <SidebarLayout>
      <ProfileDetailsForm
        onSubmit={handleSubmit}
        loading={updatePlayerProfile.isLoading}
        serverError={updatePlayerProfile.error}
        playerProfile={activePlayer}
      />

      <ToggleSnackbar open={updatePlayerProfile.isSuccess}>
        <Alert severity="success">{t("profile.page.alerts.success")}</Alert>
      </ToggleSnackbar>
    </SidebarLayout>
  );
};
