import { FC, ReactElement } from "react";
import { <PERSON> } from "react-router-dom";
import { useTranslation } from "react-i18next";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  PageError,
  PageLoader,
  playersAuthState,
  playersState,
  playerUserState,
  Stack,
  Typography,
} from "@mio/ui";

type Props = {
  children: ReactElement;
};

export const PrivateProviders: FC<Props> = ({ children }) => {
  const { t } = useTranslation();
  const { logout } = playersAuthState.useAuth();
  const userQuery = playerUserState.useCurrentPlayerUser();

  if (userQuery.isLoading) {
    return <PageLoader message={t("providers.loading.user")} />;
  }

  if (userQuery.isError) {
    return (
      <PageError
        message={
          <Stack>
            <Typography>{t("providers.errors.user")}</Typography>
            <Button variant="outlined" onClick={logout}>
              {t("providers.actions.login")}
            </Button>
          </Stack>
        }
      />
    );
  }

  return children;
};

export const PlayersProvider: FC<Props> = ({ children }) => {
  const { t } = useTranslation();
  const playersQuery = playersState.useCurrentPlayers();

  if (playersQuery.isError) {
    return <PageError message={t("providers.errors.players")} />;
  }

  if (playersQuery.isLoading) {
    return <PageLoader message={t("providers.loading.players")} />;
  }

  if (playersQuery.data.length === 0) {
    return (
      <Alert severity="info">
        <Stack gap={2}>
          <Typography>{t("providers.errors.noPlayers")}</Typography>
          <Button component={Link} to="/">
            {t("providers.actions.home")}
          </Button>
        </Stack>
      </Alert>
    );
  }

  return children;
};
