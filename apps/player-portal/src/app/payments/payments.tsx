import { FC } from "react";
import { useTranslation } from "react-i18next";
import { Box, LocalError, LocalLoader, playersState, Typography, usePlayerPayments } from "@mio/ui";
import PaymentItem from "./paymentItem";

export const Payments: FC = () => {
  const { t } = useTranslation();
  const player = playersState.useActivePlayer();

  // Only call the hook if we have a valid player ID
  const { data, isLoading, isError } = usePlayerPayments(player?.id);

  if (!player) {
    return <LocalError message={t("payments.page.errors.no-player")} />;
  }

  if (isLoading) {
    return <LocalLoader message={t("payments.page.loading")} />;
  }

  if (isError) {
    return <LocalError message={t("payments.page.errors.generic")} />;
  }

  return (
    <Box>
      <Typography variant="h4" component="h1" fontWeight="bold">
        {t("payments.page.title")}
      </Typography>

      {data?.map((payment) => (
        <PaymentItem key={payment.id} payment={payment} />
      ))}
    </Box>
  );
};
