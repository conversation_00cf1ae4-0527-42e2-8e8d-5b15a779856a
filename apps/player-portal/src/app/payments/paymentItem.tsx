import { FC } from "react";
import { useTranslation } from "react-i18next";
import { Card, Box, Typography, Button } from "@mui/material";
import { PaymentRequest } from "@mio/helpers";
import { StripeEntities } from "@mio/helpers";
import { useCreateCheckoutSession } from "@mio/ui";

type Props = {
  payment: PaymentRequest.Entity;
};

const PaymentItem: FC<Props> = ({ payment }) => {
  const { t } = useTranslation();
  const { mutate: createCheckoutSession } = useCreateCheckoutSession(payment.id);

  const handleComplete = () => {
    createCheckoutSession(undefined, {
      onSuccess: (data) => {
        console.log(t("payments.item.console.redirecting"));
        if (data.data.url) {
          window.location.href = data.data.url;
        }
      },
    });
  };

  return (
    <Card key={payment.id} sx={{ mb: 2, p: 2 }}>
      <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
        <Box sx={{ flex: 1 }}>
          <Typography variant="h6">{payment.teamName}</Typography>
          <Typography color="text.secondary">{payment.reason}</Typography>
          <Typography variant="h5" sx={{ mt: 1 }}>
            {payment.currency === StripeEntities.CurrencyLowerCase.EUR
              ? StripeEntities.CurrencySymbol.EUR
              : StripeEntities.CurrencySymbol.GBP}{" "}
            {payment.amount.toFixed(2)}
          </Typography>
        </Box>
        {payment.status === PaymentRequest.PaymentStatus.Pending && (
          <Button variant="contained" color="primary" sx={{ ml: 2 }} onClick={handleComplete}>
            {t("payments.item.complete")}
          </Button>
        )}
        {payment.status === PaymentRequest.PaymentStatus.Completed && (
          <Typography variant="h6" color="success">
            {t("payments.item.completed")}
          </Typography>
        )}
      </Box>
    </Card>
  );
};

export default PaymentItem;
