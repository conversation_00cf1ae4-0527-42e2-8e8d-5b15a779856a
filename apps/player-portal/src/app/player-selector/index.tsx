import { FC } from "react";
import { <PERSON>, Navigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

import {
  LocalError,
  LocalLoader,
  Stack,
  playersState,
  Typography,
  Avatar,
  Button,
  Card,
  Box,
  playerUserState,
} from "@mio/ui";
import { Player, UrlParams, buildUrlWithParams } from "@mio/helpers";

import { SidebarLayout } from "../layouts";
import { Routing } from "../shared";

const PlayerSelectorPage: FC = () => {
  const { t } = useTranslation();
  const playersQuery = playersState.useCurrentPlayers();
  const myUser = playerUserState.useProvidedCurrentPlayerUser();

  if (playersQuery.isLoading) {
    return <LocalLoader message={t("player-selector.loading")} />;
  }

  if (playersQuery.isError) {
    return <LocalError message={t("player-selector.error")} />;
  }

  if (playersQuery.data.length === 1) {
    return (
      <Navigate
        to={`/${buildUrlWithParams(Routing.Urls.playerFull, {
          [UrlParams.PlayerId]: playersQuery.data[0].id,
        })}`}
      />
    );
  }

  return (
    <SidebarLayout>
      <Stack gap={5}>
        <Stack>
          <Typography variant="h3" component="h1">
            {t("player-selector.title")}
          </Typography>
        </Stack>

        <Stack gap={4}>
          {playersQuery.data.map((player) => {
            const avatar = Player.getInitials(player);

            return (
              <Card sx={{ p: 2 }} key={player.id}>
                <Stack direction="row" alignItems="center" gap={3}>
                  <Avatar sx={{ backgroundColor: "primary.main" }}>{avatar}</Avatar>
                  <Typography component="h2" variant="h6">
                    {Player.getFullName(player)}
                  </Typography>
                  <Typography>
                    {player.email === myUser.authentication.email
                      ? t("player-selector.roles.me")
                      : t("player-selector.roles.guardian")}
                  </Typography>
                  <Box sx={{ flexGrow: 1 }} />
                  <Button
                    variant="contained"
                    color="secondary"
                    component={Link}
                    to={
                      "/" +
                      buildUrlWithParams(Routing.Urls.playerFull, {
                        [UrlParams.PlayerId]: player.id,
                      })
                    }
                  >
                    {t("player-selector.select")}
                  </Button>
                </Stack>
              </Card>
            );
          })}
        </Stack>
      </Stack>
    </SidebarLayout>
  );
};

export default PlayerSelectorPage;
