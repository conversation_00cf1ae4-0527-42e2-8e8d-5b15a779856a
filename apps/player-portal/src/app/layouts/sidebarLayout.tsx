import { FC, PropsWithChildren } from "react";
import { NavLink } from "react-router-dom";
import { useTranslation } from "react-i18next";

import {
  Box,
  SidebarLayout as SharedSidebarLayout,
  Typography,
  visuallyHidden,
  Stack,
  Button,
  playersAuthState,
  Divider,
  playerUserState,
  playersState,
  Tooltip,
  SxProps,
  PersonIcon,
  FolderIcon,
  EventAvailableIcon,
  DashboardIcon,
  MenuItem,
  Select,
  FormControl,
} from "@mio/ui";
import { UrlParams, buildUrlWithParams } from "@mio/helpers";
import { setLanguagePreference } from "../../i18n";

import { PlayerSelector } from "./playerSelector";
import { Routing } from "../shared";
import { LogoWithText } from "../shared/components";

const navButtonStyles: SxProps = {
  color: "inherit",
  position: "relative",
  display: "inline-block",
  "&::after": {
    content: "''",
    position: "absolute",
    width: "0%",
    height: "1px",
    backgroundColor: "primary.contrastText",
    bottom: "-5px",
    left: 0,
    willChange: "width",
    transition: "width 0.1s ease-in-out",
  },
  "&.active::after, &:hover::after": {
    width: "100%",
  },
};

export const SidebarLayout: FC<PropsWithChildren> = ({ children }) => {
  const { t, i18n } = useTranslation();
  const { logout } = playersAuthState.useAuth();
  const myUser = playerUserState.useProvidedCurrentPlayerUser();
  const playerId = playersState.useOptionalPlayerId();

  const handleLanguageChange = (event: any) => {
    setLanguagePreference(event.target.value);
  };

  return (
    <SharedSidebarLayout
      title={
        <Box>
          <Typography component="h1" sx={{ ...visuallyHidden }}>
            {t("sidebar.title")}
          </Typography>

          <Box width={{ xs: "75px", md: "100px" }}>
            <LogoWithText variant="light" />
          </Box>
        </Box>
      }
      content={
        <Stack gap={4} mt={2} p={2} flexGrow={1} sx={{ color: "primary.contrastText" }}>
          <PlayerSelector />

          <Divider flexItem sx={{ borderColor: "inherit" }} />

          {playerId && (
            <Stack component="nav" gap={3}>
              <Stack gap={2}>
                <Button
                  component={NavLink}
                  variant="text"
                  to={
                    "/" +
                    buildUrlWithParams(Routing.Urls.eventsFull, {
                      [UrlParams.PlayerId]: playerId,
                    })
                  }
                  sx={navButtonStyles}
                >
                  <Stack direction="row" gap={2} alignItems="center">
                    <EventAvailableIcon />
                    <span>{t("sidebar.navigation.events")}</span>
                  </Stack>
                </Button>
              </Stack>

              <Stack gap={2}>
                <Button
                  component={NavLink}
                  variant="text"
                  to={buildUrlWithParams("/" + Routing.Urls.dashboardFull, {
                    [UrlParams.PlayerId]: playerId,
                  })}
                  sx={navButtonStyles}
                >
                  <Stack direction="row" gap={2} alignItems="center">
                    <DashboardIcon />
                    <span>{t("sidebar.navigation.dashboard")}</span>
                  </Stack>
                </Button>

                <Button
                  component={NavLink}
                  variant="text"
                  to={buildUrlWithParams("/" + Routing.Urls.playerProfileFull, {
                    [UrlParams.PlayerId]: playerId,
                  })}
                  sx={navButtonStyles}
                >
                  <Stack direction="row" gap={2} alignItems="center">
                    <PersonIcon />
                    <span>{t("sidebar.navigation.profile")}</span>
                  </Stack>
                </Button>

                <Button
                  component={NavLink}
                  variant="text"
                  to={buildUrlWithParams("/" + Routing.Urls.playerDocumentsFull, {
                    [UrlParams.PlayerId]: playerId,
                  })}
                  sx={navButtonStyles}
                >
                  <Stack direction="row" gap={2} alignItems="center">
                    <FolderIcon />
                    <span>{t("sidebar.navigation.documents")}</span>
                  </Stack>
                </Button>
              </Stack>
            </Stack>
          )}

          <Box sx={{ flexGrow: 1 }} />

          <Stack gap={1}>
            <Tooltip title={myUser.authentication.email}>
              <Typography
                fontWeight="bold"
                sx={{ overflow: "hidden", wordWrap: "nowrap", textOverflow: "ellipsis" }}
              >
                {myUser.authentication.email}
              </Typography>
            </Tooltip>

            <Stack direction="row" gap={1}>
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <Select
                  value={i18n.language}
                  onChange={handleLanguageChange}
                  variant="outlined"
                  sx={{ backgroundColor: "background.paper" }}
                >
                  <MenuItem value="en">{t("language.switch.en")}</MenuItem>
                  <MenuItem value="es">{t("language.switch.es")}</MenuItem>
                </Select>
              </FormControl>
              <Button variant="text" color="secondary" onClick={logout}>
                {t("sidebar.actions.logout")}
              </Button>
            </Stack>
          </Stack>
        </Stack>
      }
    >
      {children}
    </SharedSidebarLayout>
  );
};
