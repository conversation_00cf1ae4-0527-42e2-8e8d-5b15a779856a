import { FC } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

import {
  Select,
  MenuItem,
  LocalError,
  LocalLoader,
  playersState,
  Stack,
  FormLabel,
  Typography,
  DirectionsRunIcon,
  playerUserState,
} from "@mio/ui";
import { UrlParams, buildUrlWithParams, Player } from "@mio/helpers";

import { Routing } from "../shared";

export const PlayerSelector: FC = () => {
  const { t } = useTranslation();
  const playersQuery = playersState.useCurrentPlayers();
  const navigate = useNavigate();
  const selectedPlayerId = playersState.useOptionalPlayerId();
  const myUser = playerUserState.useProvidedCurrentPlayerUser();

  if (playersQuery.isLoading) {
    return <LocalLoader message={t("player-selector.loading")} />;
  }

  if (playersQuery.isError) {
    return <LocalError message={t("player-selector.error")} />;
  }

  const activePlayer = playersQuery.data.find((player) => player.id === selectedPlayerId);

  if (playersQuery.data.length === 1 && activePlayer) {
    return (
      <Stack>
        <Typography fontWeight="bold" color="primary.contrastText">
          {Player.getFullName(activePlayer)}
        </Typography>

        <Typography fontWeight="bold" variant="subtitle2">
          (
          {activePlayer.email === myUser.authentication.email
            ? t("player-selector.roles.me")
            : t("player-selector.roles.guardian")}
          )
        </Typography>
      </Stack>
    );
  }

  return (
    <Stack gap={2}>
      <FormLabel htmlFor="active-player-selector" sx={{ color: "primary.contrastText" }}>
        <DirectionsRunIcon />
        {t("player-selector.label")}
      </FormLabel>

      <Select
        fullWidth
        id="active-player-selector"
        value={selectedPlayerId || ""}
        sx={{ backgroundColor: "primary.contrastText" }}
        onChange={(event) => {
          navigate(
            "/" +
              buildUrlWithParams(Routing.Urls.playerFull, {
                [UrlParams.PlayerId]: event.target.value,
              }),
            {},
          );
        }}
      >
        {playersQuery.data.map((player) => (
          <MenuItem key={player.id} value={player.id}>
            {Player.getFullName(player)}
          </MenuItem>
        ))}
      </Select>

      {activePlayer && (
        <Typography fontWeight="bold" variant="subtitle2">
          (
          {activePlayer.email === myUser.authentication.email
            ? t("player-selector.roles.me")
            : t("player-selector.roles.guardian")}
          )
        </Typography>
      )}
    </Stack>
  );
};
