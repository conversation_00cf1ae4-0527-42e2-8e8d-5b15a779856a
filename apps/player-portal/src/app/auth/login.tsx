import { FC, useEffect } from "react";
import { useTranslation } from "react-i18next";

import {
  Typo<PERSON>,
  Stack,
  AppLink,
  useUrlSearchParamValues,
  Alert,
  playersAuthState,
  Box,
} from "@mio/ui";
import { PlayerUser, isError } from "@mio/helpers";

import LoginForm from "./login.form";
import { Routing } from "../shared";
import { BackgroundLayout } from "../layouts";
import { LogoWithText } from "../shared/components";

export const LoginPage: FC = () => {
  const { t } = useTranslation();
  const searchParams = useUrlSearchParamValues();
  const { login } = playersAuthState.useAuth();

  const parsedParams = PlayerUser.Entity.toOptionalLoginCodeDto(searchParams);

  useEffect(() => {
    const parsed = PlayerUser.Entity.toLoginCodeDto(parsedParams);
    const hasFired = login.isSuccess || login.isError;

    if (!isError(parsed) && !hasFired) {
      login.mutate(parsed);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (isError(parsedParams)) {
    return <Alert severity="error">{t("auth.page.invalid-link")}</Alert>;
  }

  const handleSubmit = (data: unknown) => {
    const parsed = PlayerUser.Entity.toLoginCodeDto(data);

    if (!isError(parsed)) {
      login.mutate(parsed);
    }
  };

  return (
    <BackgroundLayout>
      <Stack gap={2} sx={{ alignItems: "center" }}>
        <Box width="300px" mb={3}>
          <LogoWithText variant="dark" />
        </Box>

        <Typography variant="h4" component="h1" mb={3}>
          {t("auth.page.title")}
        </Typography>

        <Box width="100%" mb={3}>
          <LoginForm
            onSubmit={handleSubmit}
            isLoading={login.isLoading}
            error={login.error}
            code={parsedParams.code}
          />
        </Box>

        {login.isSuccess && <Alert severity="success">{t("auth.page.login-success")}</Alert>}

        <Box>
          <AppLink to={Routing.Urls.register}>{t("auth.page.send-new-code")}</AppLink>
        </Box>
      </Stack>
    </BackgroundLayout>
  );
};
