import { FC } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";

import {
  Box,
  Stack,
  TextField,
  useFormResolver,
  visuallyHidden,
  Alert,
  LoadingButton,
} from "@mio/ui";
import { APIError, LoginCode, LoginCodeDto, PlayerUser } from "@mio/helpers";

type Props = {
  onSubmit: (data: unknown) => void;
  isLoading: boolean;
  error: APIError | null;
  code?: LoginCode;
};

const LoginForm: FC<Props> = ({ onSubmit, isLoading, error, code }) => {
  const { t } = useTranslation();
  const { formMethods, errors } = useLoginForm();

  return (
    <Stack component="form" gap={3} onSubmit={formMethods.handleSubmit(onSubmit)}>
      <Box component="legend" sx={{ ...visuallyHidden }}>
        {t("auth.form.legend")}
      </Box>

      <TextField
        fullWidth
        label={t("auth.form.code.label")}
        name="code"
        defaultValue={code || ""}
        inputProps={{ ...formMethods.register("code") }}
        helperText={errors.code?.message || ""}
        error={!!errors.code?.message}
        aria-invalid={!!errors.code?.message}
      />

      <Box textAlign="center">
        <LoadingButton variant="contained" type="submit" loading={isLoading}>
          {t("auth.form.submit")}
        </LoadingButton>
      </Box>

      {error && <Alert severity="error">{t("auth.form.error")}</Alert>}
    </Stack>
  );
};

const useLoginForm = () => {
  const resolver = useFormResolver<LoginCodeDto>(PlayerUser.Entity.toLoginCodeDto);

  const formMethods = useForm<LoginCodeDto>({
    resolver,
  });

  const { formState } = formMethods;
  const { errors } = formState;
  const hasSubmitted = formState.submitCount > 0;

  return { formMethods, errors: hasSubmitted ? errors : {} };
};

export default LoginForm;
