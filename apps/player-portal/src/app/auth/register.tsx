import { FC } from "react";
import { Navigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { Typography, AppLink, Stack, playersAuthState, Box } from "@mio/ui";
import { PlayerUser, isError } from "@mio/helpers";

import RegisterForm from "./register.form";
import { Routing } from "../shared";
import { BackgroundLayout } from "../layouts";
import { LogoWithText } from "../shared/components";

export const RegisterPage: FC = () => {
  const { t } = useTranslation();
  const { requestCode } = playersAuthState.useAuth();

  const handleSubmit = (data: unknown) => {
    const parsed = PlayerUser.Entity.toSignInDto(data);

    if (!isError(parsed)) {
      requestCode.mutate(parsed);
    }
  };

  return (
    <BackgroundLayout>
      <Stack gap={2} sx={{ alignItems: "center" }}>
        <Box width="300px" mb={3}>
          <LogoWithText variant="dark" />
        </Box>

        <Typography variant="h4" component="h1" mb={3}>
          {t("auth.register-page.title")}
        </Typography>

        <Box width="100%">
          <RegisterForm
            onSubmit={handleSubmit}
            isLoading={requestCode.isLoading}
            error={requestCode.error}
          />
        </Box>

        {requestCode.isSuccess && <Navigate to={Routing.Urls.login} />}

        <Box mt={3}>
          <AppLink to={Routing.Urls.login}>{t("auth.register-page.enter-code-manually")}</AppLink>
        </Box>
      </Stack>
    </BackgroundLayout>
  );
};
