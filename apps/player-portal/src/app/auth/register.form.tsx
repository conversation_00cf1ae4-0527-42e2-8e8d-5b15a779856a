import { FC } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";

import {
  Alert,
  Box,
  LoadingButton,
  Stack,
  TextField,
  useFormResolver,
  visuallyHidden,
} from "@mio/ui";
import { APIError, PlayerUser } from "@mio/helpers";

type Props = {
  onSubmit: (data: unknown) => void;
  isLoading: boolean;
  error: APIError | null;
};

const RegisterForm: FC<Props> = ({ onSubmit, isLoading, error }) => {
  const { t } = useTranslation();
  const { formMethods, errors } = useRegisterForm();

  return (
    <Stack component="form" gap={3} onSubmit={formMethods.handleSubmit(onSubmit)}>
      <Box component="legend" sx={{ ...visuallyHidden }}>
        {t("auth.register.legend")}
      </Box>

      <TextField
        fullWidth
        label={t("auth.register.email.label")}
        name="email"
        type="email"
        inputProps={{ ...formMethods.register("email") }}
        helperText={errors.email?.message || ""}
        error={!!errors.email?.message}
        aria-invalid={!!errors.email?.message}
      />

      <Box textAlign="center">
        <LoadingButton variant="contained" type="submit" loading={isLoading}>
          {t("auth.register.submit")}
        </LoadingButton>
      </Box>

      {error && <Alert severity="error">{t("auth.register.error")}</Alert>}
    </Stack>
  );
};

const useRegisterForm = () => {
  const resolver = useFormResolver<PlayerUser.SignInDto>(PlayerUser.Entity.toSignInDto);

  const formMethods = useForm<PlayerUser.SignInDto>({
    resolver,
  });

  const { formState } = formMethods;
  const { errors } = formState;
  const hasSubmitted = formState.submitCount > 0;

  return { formMethods, errors: hasSubmitted ? errors : {} };
};

export default RegisterForm;
