import { FC } from "react";
import { Navigate, Route, Routes } from "react-router-dom";

import { AuthPages } from "./auth";
import { Routing } from "./shared";
import { AppPrivateRoute, AppPublicRoute } from "./auth.guards";
import { ApplicationFormPage } from "./application-form";
import { PlayersProvider, PrivateProviders } from "./private.providers";
import PlayerSelectorPage from "./player-selector";
import PlayerEvents from "./events";
import { ProfileDetailsPage } from "./profile";
import ExistingPlayerApplication from "./application-form/existing-player";
import { PlayerDocumentsPage } from "./documents";
import { Dashboard } from "./dashboard/dashboard";

export const AppRouting: FC = () => {
  return (
    <Routes>
      <Route
        path={Routing.Urls.login}
        element={
          <AppPublicRoute>
            <AuthPages.Login />
          </AppPublicRoute>
        }
      />
      <Route
        path={Routing.Urls.register}
        element={
          <AppPublicRoute>
            <AuthPages.Register />
          </AppPublicRoute>
        }
      />

      <Route path={Routing.Urls.apply} element={<ApplicationFormPage />} />

      <Route
        path="/*"
        element={
          <AppPrivateRoute>
            <PrivateProviders>
              <Routes>
                <Route
                  path={Routing.Urls.applyToAdditionalOrganization}
                  element={<ExistingPlayerApplication />}
                />

                <Route
                  path={`${Routing.Urls.players}/*`}
                  element={
                    <Routes>
                      <Route path="" element={<PlayerSelectorPage />} />

                      <Route
                        /* each page in here is in the context of a concrete :playerId url param
                       thus we have the PlayersProvider guard - it will load all associated
                       players for the current User. Then we can use the "useActivePlayer"
                       hook where needed without needing to make a new http request - 
                       it will try to find it in the alreay fetched players where 
                       player.id === :playerId from the url */
                        path={`${Routing.Urls.playerRoot}/*`}
                        element={
                          <PlayersProvider>
                            <Routes>
                              <Route path={Routing.Urls.events} element={<PlayerEvents />} />

                              <Route
                                path={Routing.Urls.playerProfile}
                                element={<ProfileDetailsPage />}
                              />

                              <Route
                                path={Routing.Urls.playerDocuments}
                                element={<PlayerDocumentsPage />}
                              />

                              <Route path={Routing.Urls.dashboard} element={<Dashboard />} />

                              <Route
                                path={Routing.Urls.wildcard}
                                element={<Navigate to={Routing.Urls.playerProfile} />}
                              />
                            </Routes>
                          </PlayersProvider>
                        }
                      />

                      <Route path={Routing.Urls.wildcard} element={<Navigate to="" />} />
                    </Routes>
                  }
                />

                <Route
                  path={Routing.Urls.wildcard}
                  element={<Navigate to={`/${Routing.Urls.players}`} />}
                />
              </Routes>
            </PrivateProviders>
          </AppPrivateRoute>
        }
      />

      <Route path={Routing.Urls.wildcard} element={<Navigate to={Routing.Urls.register} />} />
    </Routes>
  );
};
