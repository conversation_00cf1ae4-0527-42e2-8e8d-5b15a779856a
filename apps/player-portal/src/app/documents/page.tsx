import { FC } from "react";
import { useTranslation } from "react-i18next";

import {
  AccountCircleIcon,
  Box,
  Card,
  FolderSharedIcon,
  Stack,
  Typography,
  playersState,
} from "@mio/ui";
import { Player } from "@mio/helpers";

import { SidebarLayout } from "../layouts";
import { PlayerPhotoUpload, UploadedPlayerPhoto } from "./photo";
import { PlayerPassportUpload, UploadedPassportImages } from "./passport";
import { PlayerDocumentsSubmit } from "./submit";

export const PlayerDocumentsPage: FC = () => {
  const { t } = useTranslation();
  const player = playersState.useActivePlayer();

  return (
    <SidebarLayout>
      <Stack
        component="header"
        direction="row"
        gap={3}
        flexWrap="wrap"
        justifyContent="space-between"
        alignItems="center"
        mb={6}
      >
        <Typography variant="h4" component="h1" fontWeight="bold">
          {t("documents.title")}
        </Typography>

        <Box>
          <PlayerDocumentsSubmit player={player} />
        </Box>
      </Stack>

      <Stack gap={5} flexWrap="wrap" direction="row">
        <Box>
          <Card
            sx={{
              p: 2,
              display: "flex",
              flexDirection: "column",
              gap: 3,
              maxWidth: { xs: "100%", md: "250px" },
            }}
          >
            <Stack direction="row" gap={2} alignItems="center">
              <AccountCircleIcon fontSize="large" />
              <Typography variant="h6" component="h2">
                {t("documents.photo.title")}
              </Typography>
            </Stack>

            {player.documents?.photo_id ? (
              <UploadedPlayerPhoto id={player.documents?.photo_id} />
            ) : (
              <PlayerPhotoUpload />
            )}
          </Card>
        </Box>

        <UploadedPassportImages />

        {Player.canAddImageDocument(player) && (
          <Box>
            <Card
              sx={{
                p: 2,
                display: "flex",
                flexDirection: "column",
                gap: 3,
                maxWidth: { xs: "100%", md: "250px" },
              }}
            >
              <Stack direction="row" gap={2} alignItems="center">
                <FolderSharedIcon fontSize="large" />
                <Typography variant="h6" component="h2">
                  {t("documents.passport.title")}
                </Typography>
              </Stack>

              <PlayerPassportUpload />
            </Card>
          </Box>
        )}
      </Stack>
    </SidebarLayout>
  );
};
