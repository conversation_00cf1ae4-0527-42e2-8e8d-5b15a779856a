import { FC } from "react";

import { playersState, ImageUpload, LocalLoader, UploadedImage, LocalError } from "@mio/ui";
import { Assets } from "@mio/helpers";

export const PlayerPhotoUpload: FC = () => {
  const uploadPhoto = playersState.useAddPlayerPhoto();
  const player = playersState.useActivePlayer();

  const handleUpload = (file: Assets.Image.ParsedFile) => {
    uploadPhoto.mutate({ playerId: player.id, dto: { file } });
  };

  return (
    <ImageUpload
      isLoading={uploadPhoto.isLoading}
      onUpload={handleUpload}
      error={uploadPhoto.error ?? undefined}
      success={uploadPhoto.isSuccess}
    />
  );
};

type Props = {
  id: Assets.Image.ImageId;
};

export const UploadedPlayerPhoto: FC<Props> = ({ id }) => {
  const player = playersState.useActivePlayer();
  const imageQuery = playersState.usePlayerImage(id);
  const removePhoto = playersState.useRemovePlayerPhoto();

  const handleDelete = () => {
    removePhoto.mutate({ playerId: player.id, imageId: id });
  };

  if (imageQuery.isLoading) {
    return <LocalLoader />;
  }

  if (imageQuery.isError) {
    return <LocalError />;
  }

  return (
    <UploadedImage
      image={imageQuery.data}
      altText="Player photo"
      onRemove={handleDelete}
      isRemoving={removePhoto.isLoading}
      apiError={removePhoto.error ?? undefined}
      canRemove={player.documents?.submitted !== true}
      imageAction={
        <a href={imageQuery.data.url} target="_blank" rel="noreferrer">
          {imageQuery.data.name}
        </a>
      }
    />
  );
};
