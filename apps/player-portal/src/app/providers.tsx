import { FC, PropsWithChildren } from "react";
import { useNavigate } from "react-router-dom";

import {
  ThemeProvider,
  EnvConfigGuard,
  <PERSON><PERSON>rovider,
  DateProvider,
  createCustomTheme,
  useSDKConfig,
  useAuthToken,
  TokenContextProvider,
  config<PERSON><PERSON><PERSON>,
  SDKProvider,
} from "@mio/ui";
import { AccessToken } from "@mio/helpers";

import { environment } from "../environments/environment";
import { Routing } from "./shared";

const Providers: FC<PropsWithChildren> = ({ children }) => {
  const baseURL = environment.BASE_URL;
  const { deleteToken } = useAuthToken();
  const navigate = useNavigate();
  const theme = createCustomTheme();

  const sdk = useSDKConfig({
    baseURL,
    onUnauthorized: () => {
      deleteToken();
      navigate(Routing.Urls.register);
    },
    getAccessToken: () => localStorage.getItem("token") as AccessToken,
  });

  return (
    <StateProvider>
      <SDKProvider sdk={Promise.resolve(sdk)}>
        <ThemeProvider theme={theme}>
          <DateProvider>{children}</DateProvider>
        </ThemeProvider>
      </SDKProvider>
    </StateProvider>
  );
};

export const AppProviders: FC<PropsWithChildren> = ({ children }) => {
  return (
    <EnvConfigGuard env={environment} parser={configParser}>
      <TokenContextProvider>
        <Providers>{children}</Providers>
      </TokenContextProvider>
    </EnvConfigGuard>
  );
};
