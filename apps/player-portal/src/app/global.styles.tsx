import { FC, PropsWithChildren } from "react";

import { Global, css } from "@emotion/react";
import { CssBaseline } from "@mui/material";

export const GlobalStyles: FC<PropsWithChildren> = ({ children }) => {
  return (
    <>
      <Global
        styles={css`
          html,
          body,
          #root {
            height: 100%;
          }
        `}
      />
      <CssBaseline />
      {children}
    </>
  );
};
