import { UrlParams, sharedUrls } from "@mio/helpers";

export const Urls = {
  register: "/register",
  login: sharedUrls.playerLoginWithCode,

  home: "/",

  playerRoot: `:${UrlParams.PlayerId}`,
  playerFull: `players/:${UrlParams.PlayerId}`,

  players: "players",

  events: "events",

  eventsFull: `players/:${UrlParams.PlayerId}/events`,

  playerProfile: `profile`,
  playerProfileFull: `players/:${UrlParams.PlayerId}/profile`,

  playerDocuments: "documents",
  playerDocumentsFull: `players/:${UrlParams.PlayerId}/documents`,

  dashboard: "dashboard",
  dashboardFull: `players/:${UrlParams.PlayerId}/dashboard`,

  apply: `/:${UrlParams.OrganizationSlug}/apply`,
  applyToAdditionalOrganization: `/:${UrlParams.OrganizationSlug}/apply-additional`,

  wildcard: "/*",
} as const;
