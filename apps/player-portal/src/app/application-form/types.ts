import { PlayerAddress, Primitive, CreatePlayerDto } from "@mio/helpers";
import { EmbeddedGuardian } from "@mio/helpers/lib/types/player/Guardian";

/* Primitive doesn't work automatically with nested objects thus the below workaround*/
export type DataShape = Partial<
  Omit<Primitive<CreatePlayerDto>, "guardian" | "address"> & {
    guardian?: Partial<Primitive<EmbeddedGuardian>>;
    address: Primitive<PlayerAddress>;
  }
>;
