import { FC } from "react";
import { useTranslation } from "react-i18next";

import {
  <PERSON>Error,
  PageLoader,
  Stack,
  playersState,
  usePublicOrganization,
  CenteredLayout,
  Box,
  AppLink,
} from "@mio/ui";

import { Header } from "../header";
import ExistingPlayer from "./player";

const ExistingPlayerApplication: FC = () => {
  const { t } = useTranslation();
  const { query: orgQuery } = usePublicOrganization();
  const playersQuery = playersState.useCurrentPlayers();

  const isLoading = orgQuery.isLoading || playersQuery.isLoading;
  const isError = orgQuery.isError || playersQuery.isError;

  if (isLoading) {
    return <PageLoader />;
  }

  if (isError) {
    return <PageError />;
  }

  const organization = orgQuery.data;

  if (!organization) {
    return <PageError message={t("common.errors.organization-not-found")} />;
  }

  return (
    <CenteredLayout>
      <Stack gap={2} p={2}>
        <Box>
          <AppLink to={"/"}>{t("common.back-to-home")}</AppLink>
        </Box>
        <Header organization={organization} />

        <Stack component="ul" gap={5}>
          {playersQuery.data.map((player) => (
            <Stack key={player.id} component="li">
              <ExistingPlayer player={player} organization={organization.id} />
            </Stack>
          ))}
        </Stack>
      </Stack>
    </CenteredLayout>
  );
};

export default ExistingPlayerApplication;
