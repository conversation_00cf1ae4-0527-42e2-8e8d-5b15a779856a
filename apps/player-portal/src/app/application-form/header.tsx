import { FC } from "react";
import { useTranslation } from "react-i18next";

import { Typography, Alert } from "@mio/ui";
import { PublicOrganization } from "@mio/helpers";

type Props = {
  organization: PublicOrganization;
};

export const Header: FC<Props> = ({ organization }) => {
  const { t } = useTranslation();

  return (
    <header>
      <Typography variant="h3" component="h1" mb={2}>
        {organization.applications?.headline}
      </Typography>
      <Typography>
        {organization.applications?.open && (
          <span dangerouslySetInnerHTML={{ __html: organization.applications.description }} />
        )}
      </Typography>
      {!organization.applications?.open && (
        <Alert sx={{ mt: 2 }} severity="warning">
          {organization.applications?.closedMessage || t("application.header.not-open")}
        </Alert>
      )}
    </header>
  );
};
