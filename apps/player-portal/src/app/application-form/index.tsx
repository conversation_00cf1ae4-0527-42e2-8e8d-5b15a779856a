import { FC, PropsWithChildren } from "react";
import { Navigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { Player, isError, ErrorMessages, buildUrlWithParams, UrlParams } from "@mio/helpers";
import {
  Alert,
  CircularProgress,
  Box,
  CenteredLayout,
  playersState,
  usePublicOrganization,
  useAuth,
} from "@mio/ui";

import { DataShape } from "./types";
import { ApplicationForm } from "./form";

import { Routing } from "../shared";

export const ApplicationFormPage: FC<PropsWithChildren> = () => {
  const { t } = useTranslation();
  const createApplicant = playersState.useCreateApplicant();
  const { query } = usePublicOrganization();
  const { isLogged } = useAuth();

  const handleSubmit = (data: DataShape) => {
    const parsedDto = Player.parseCreateDto(data);

    if (!isError(parsedDto)) {
      createApplicant.mutate(parsedDto);
    }
  };

  if (isLogged && query.data) {
    return (
      <Navigate
        to={buildUrlWithParams(Routing.Urls.applyToAdditionalOrganization, {
          [UrlParams.OrganizationSlug]: query.data.slug,
        })}
      />
    );
  }

  if (query.isLoading) {
    return (
      <CenteredLayout>
        <Box mt={3}>
          <Alert severity="info" sx={{ mb: 2 }}>
            {t("common.loading")}
          </Alert>
          <CircularProgress />
        </Box>
      </CenteredLayout>
    );
  }

  if (query.isError && query.error.message === ErrorMessages.EntityNotFound) {
    return (
      <CenteredLayout>
        <Alert severity="error" sx={{ mt: 3 }}>
          {t("common.errors.organization-not-found")}
        </Alert>
      </CenteredLayout>
    );
  }

  if (query.isError) {
    return (
      <CenteredLayout>
        <Alert severity="error" sx={{ mt: 3 }}>
          {t("application.errors.generic")}
        </Alert>
      </CenteredLayout>
    );
  }

  if (!query.data) {
    return (
      <CenteredLayout>
        <Alert severity="error" sx={{ mt: 3 }}>
          {t("application.errors.generic")}
        </Alert>
      </CenteredLayout>
    );
  }

  return (
    <ApplicationForm
      organization={query.data}
      onSubmit={handleSubmit}
      loading={createApplicant.isLoading}
      serverError={createApplicant.error}
      success={createApplicant.isSuccess}
    />
  );
};
