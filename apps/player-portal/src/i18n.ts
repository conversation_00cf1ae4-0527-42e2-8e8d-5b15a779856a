import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";

import { translations as enTranslations } from "./locales/en";
import { translations as esTranslations } from "./locales/es";

const LANGUAGE_KEY = "preferred_language";

// Get browser language
const browserLang = navigator.language.split("-")[0];
// Get stored language preference
const storedLang = localStorage.getItem(LANGUAGE_KEY);

// Set initial language - prefer stored preference, then browser language (if Spanish), otherwise default to English
const initialLanguage = storedLang || (browserLang === "es" ? "es" : "en");

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources: {
      en: {
        translation: enTranslations,
      },
      es: {
        translation: esTranslations,
      },
    },
    lng: initialLanguage, // Set initial language
    detection: {
      order: ["localStorage", "navigator"],
      lookupLocalStorage: LANGUAGE_KEY,
      caches: ["localStorage"],
    },
    fallbackLng: "en",
    interpolation: {
      escapeValue: false,
    },
  });

export const setLanguagePreference = (lang: string) => {
  localStorage.setItem(LANGUAGE_KEY, lang);
  i18n.changeLanguage(lang);
};

export default i18n;
