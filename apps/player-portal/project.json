{"name": "player-portal", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/player-portal/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"outputPath": "dist/apps/player-portal", "index": "apps/player-portal/src/index.html", "baseHref": "/", "main": "apps/player-portal/src/main.tsx", "polyfills": "apps/player-portal/src/polyfills.ts", "tsConfig": "apps/player-portal/tsconfig.app.json", "assets": ["apps/player-portal/src/favicon.ico", "apps/player-portal/src/assets", "apps/player-portal/src/routes.json"], "styles": [], "scripts": [], "webpackConfig": "@nx/react/plugins/webpack"}, "configurations": {"development": {"extractLicenses": false, "optimization": false, "sourceMap": true, "vendorChunk": true}, "production": {"fileReplacements": [{"replace": "apps/player-portal/src/environments/environment.ts", "with": "apps/player-portal/src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false}, "staging": {"fileReplacements": [{"replace": "apps/player-portal/src/environments/environment.ts", "with": "apps/player-portal/src/environments/environment.staging.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "vendorChunk": false, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "2mb"}]}}}, "serve": {"executor": "@nx/webpack:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "player-portal:build", "hmr": true, "port": 4002}, "configurations": {"development": {"buildTarget": "player-portal:build:development"}, "production": {"buildTarget": "player-portal:build:production", "hmr": false}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "type-check": {"executor": "nx:run-commands", "options": {"command": "npx tsc -b ./apps/player-portal"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/player-portal/jest.config.ts"}}}}