{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "../../dist/out-tsc", "types": ["node", "@nx/react/typings/cssmodule.d.ts", "@nx/react/typings/image.d.ts"]}, "files": ["../../node_modules/@nx/react/typings/cssmodule.d.ts", "../../node_modules/@nx/react/typings/image.d.ts"], "exclude": ["**/*.spec.ts", "**/*.test.ts", "**/*.spec.tsx", "**/*.test.tsx", "jest.config.ts"], "include": ["**/*.js", "**/*.jsx", "**/*.ts", "**/*.tsx"]}