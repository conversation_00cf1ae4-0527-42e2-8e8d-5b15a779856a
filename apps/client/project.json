{"name": "client", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/client/src", "projectType": "application", "tags": [], "targets": {"type-check": {"executor": "nx:run-commands", "options": {"command": "npx tsc -b ./apps/client"}}, "build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/client", "index": "apps/client/src/index.html", "main": "apps/client/src/main.tsx", "polyfills": "apps/client/src/polyfills.ts", "tsConfig": "apps/client/tsconfig.app.json", "assets": ["apps/client/src/favicon.ico", "apps/client/src/assets", "apps/client/src/routes.json"], "styles": ["node_modules/normalize.css/normalize.css"], "scripts": [], "webpackConfig": "@nx/react/plugins/webpack"}, "configurations": {"production": {"fileReplacements": [{"replace": "apps/client/src/environments/environment.ts", "with": "apps/client/src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "vendorChunk": false, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "2mb"}]}, "staging": {"fileReplacements": [{"replace": "apps/client/src/environments/environment.ts", "with": "apps/client/src/environments/environment.staging.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "vendorChunk": false, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "2mb"}]}, "demo": {"fileReplacements": [{"replace": "apps/client/src/environments/environment.ts", "with": "apps/client/src/environments/environment.demo.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "vendorChunk": false, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "2mb"}]}}}, "serve": {"executor": "@nx/webpack:dev-server", "options": {"buildTarget": "client:build", "hmr": true, "port": 4001}, "configurations": {"production": {"buildTarget": "client:build:production", "hmr": false}, "development": {"buildTarget": "client:build:development"}, "demo": {"buildTarget": "client:build:demo"}}, "defaultConfiguration": "development"}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/apps/client"], "options": {"jestConfig": "apps/client/jest.config.ts"}}}}