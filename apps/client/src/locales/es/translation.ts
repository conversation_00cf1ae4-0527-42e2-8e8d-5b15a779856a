import { TranslationSchema } from "../types";

export const translation: TranslationSchema = {
  auth: {
    login: "Iniciar sesión",
    register: "Registrarse",
    "forgot-password": "¿Olvidaste tu contraseña?",
    "reset-password": "Restablecer contraseña",
    "join-organization": "Unirse a la organización",
    "email-placeholder": "Correo electrónico",
    "password-placeholder": "Contraseña",
    "confirm-password-placeholder": "Confirmar contraseña",
    "no-account": "¿No tienes una cuenta?",
    "have-account": "¿Ya tienes una cuenta?",
    "sign-up": "Registrarse",
    "sign-in": "Iniciar sesión",
    "reset-instructions":
      "Ingresa tu correo electrónico y te enviaremos instrucciones para restablecer tu contraseña.",
    "invalid-credentials": "Correo electrónico o contraseña inválidos",
    "registration-error": "Error en el registro. Por favor, inténtalo de nuevo.",
    "permission-denied": "No tienes permiso para acceder a este recurso",
    "load-error": "Error al cargar los datos",
    "team-assist": "Asistente de Equipo",
    "join-organization-title": "Unirse a la organización {{orgName}}",
    "invite-fetch-error": "Error al obtener la invitación",
    "invalid-invite": "Invitación inválida",
    "expired-invite": "La invitación ha expirado o ya fue utilizada",
    "generic-error": "Algo salió mal",
    "accept-invite": "Aceptar invitación",
    "welcome-to-team-assist": "Bienvenido a Team Assist",
    welcome: "Bienvenido",
    "registration-page": "Página de registro de Team Assist",
    "enter-email": "Ingresa tu correo electrónico",
    "back-to-login": "Volver al inicio de sesión",
    "enter-new-password": "Ingresa tu nueva contraseña",
  },
  common: {
    submit: "Enviar",
    cancel: "Cancelar",
    save: "Guardar",
    delete: "Eliminar",
    edit: "Editar",
    create: "Crear",
    back: "Volver",
    open: "Abrir",
    close: "Cerrar",
    loading: "Cargando...",
    error: "Algo salió mal",
    success: "¡Éxito!",
    confirm: "Confirmar",
    actions: "Acciones",
    continue: "Continuar",
    "permission-denied": "Permiso denegado",
    confirmation: {
      cancel: "Cancelar",
      confirm: "Confirmar",
      error: "Error",
      "error-message": "Algo salió mal. Por favor, inténtalo de nuevo o contacta a soporte",
      success: "Éxito",
      "success-message": "Cambios guardados exitosamente",
    },
    updated: "actualizada",
    created: "creada",
    name: "Nombre",
    "something-went-wrong": "Algo salió mal",
    email: "Correo electrónico",
    "please-confirm": "Por favor confirma",
    "not-available": "N/D",
  },
  teams: {
    title: "Equipos",
    "add-team": "Agregar equipo",
    "edit-team": "Editar equipo",
    "delete-team": "Eliminar equipo",
    "team-name": "Nombre del equipo",
    "age-group": "Grupo de edad",
    season: "Temporada",
    players: "Jugadores",
    coaches: "Entrenadores",
    "load-error": "Error al cargar los equipos",
    loading: "Cargando equipos...",
    "select-team": "Equipo",
    card: {
      gender: "Género",
      "age-description": "Descripción de edad",
      "born-before": "Jugadores nacidos antes de",
      "born-after": "Jugadores nacidos después de",
    },
  },
  validation: {
    required: "Este campo es requerido",
    "invalid-email": "Correo electrónico inválido",
    "password-mismatch": "Las contraseñas no coinciden",
    "min-length": "Debe tener al menos {{length}} caracteres",
    "max-length": "Debe tener menos de {{length}} caracteres",
  },
  "email-templates": {
    title: "Plantillas de correo",
    "add-template": "Agregar plantilla",
    "delete-confirm-title": "¿Eliminar plantilla?",
    "delete-warning": "Esta acción no se puede deshacer.",
    loading: "Cargando plantillas...",
    "load-error": "Error al cargar las plantillas.",
    "no-templates": "Aún no hay plantillas de correo.",
    subject: "Asunto",
    "edit-template": "Editar plantilla",
    "delete-template": "Eliminar plantilla",
    "template-name": "Nombre de la plantilla",
    visibility: "Visibilidad",
    "visibility-private": "Privado",
    "visibility-company": "Club",
    "visibility-description":
      "'Privado' asegurará que solo tú puedas ver y editar esta plantilla. 'Club' permitirá que todos los miembros del club puedan ver y editar esta plantilla.",
    body: "Contenido",
    "create-template": "Crear plantilla de correo",
    "no-subject": "Sin asunto",
  },
  events: {
    title: "Gestión de Eventos",
    "create-single": "Crear Único",
    "create-recurring": "Crear Recurrente",
    type: {
      single: "Único",
      recurring: "Recurrente",
    },
    filters: {
      "start-date": "Fecha inicial",
      "end-date": "Fecha final",
      show: "Mostrar",
    },
    hosts: {
      title: "Anfitriones",
      "add-placeholder": "Agregar anfitriones...",
      you: "Tú",
    },
    invitations: {
      title: "Jugadores invitados",
      "load-error": "Error al cargar los jugadores del equipo.",
      loading: "Cargando jugadores del equipo...",
      retry: "Reintentar",
    },
    form: {
      general: "General",
      name: "Nombre",
      agenda: "Agenda",
      description: "Descripción",
      "description-placeholder": "Una sesión de entrenamiento...",
      "location-address": "Dirección",
      "location-postcode": "Código postal",
      save: "Guardar",
      error: "Algo salió mal",
      "agenda-options": {
        training: "Sesión de entrenamiento",
        match: "Partido",
        meeting: "Reunión de equipo",
        other: "Otro",
      },
      "start-date-time": "Fecha y hora de inicio",
      "end-date-time": "Fecha y hora de fin",
    },
    scheduling: {
      title: "Programación",
      "start-time": "Hora de inicio",
      "end-time": "Hora de fin",
      "start-from": "Comenzar desde",
      type: "Tipo",
      "day-of-week": "Día de la semana",
      ends: "Termina",
      "end-of-season": "Fin de temporada",
      "custom-date": "Fecha personalizada",
      "end-date": "Fecha de finalización",
      types: {
        weekly: "Semanal",
        "bi-weekly": "Quincenal",
        monthly: "Mensual",
      },
    },
    recurring: {
      "create-title": "Crear nuevo evento recurrente",
      "loading-players": "Cargando jugadores del equipo...",
      "loading-season": "Cargando temporada actual...",
      "load-players-error": "Error al cargar los jugadores del equipo.",
      "load-season-error": "Error al cargar la temporada actual.",
      "edit-title": "Editar evento",
    },
    single: {
      "create-title": "Crear nuevo evento único",
      "loading-players": "Cargando jugadores del equipo...",
      "loading-season": "Cargando temporada actual...",
      "load-players-error": "Error al cargar los jugadores del equipo.",
      "load-season-error": "Error al cargar la temporada actual.",
    },
  },
  financial: {
    integration: {
      "create-title": "Formulario de creación de integración",
      name: "Nombre",
      type: "Tipo de integración",
      "api-key": "Clave API",
      submit: "Enviar",
      error: "Algo salió mal",
      "already-exists": "Ya existe una integración de ese tipo",
      types: {
        gocardless: "GoCardless",
        stripe: "Stripe",
      },
      item: {
        "menu-label": "Abrir menú de integración",
        edit: "Editar",
        delete: "Eliminar",
      },
      view: {
        "add-button": "+ Agregar integración",
        loading: "Cargando integraciones...",
        "load-error": "Error al cargar las integraciones",
      },
      update: {
        title: "Formulario de actualización de integración",
        "hidden-key": "********",
      },
      delete: {
        title: "¿Eliminar integración?",
        error: "No se pudo eliminar la integración",
      },
      form: {
        "create-title": "Crear nueva integración",
        "edit-title": "Editar integración {{name}}",
      },
    },
  },
  sidebar: {
    menu: {
      "players-list": "Lista de jugadores",
      "training-center": "Centro de entrenamiento",
      "match-center": "Centro de partidos",
      stats: "Estadísticas",
      "event-management": "Gestión de eventos",
      "club-admin": "Administración del club",
      "teams-admin": "Administración de equipos",
      "players-admin": "Administración de jugadores",
      "users-admin": "Administración de usuarios",
      finances: "Finanzas",
      settings: "Configuración",
      general: "General",
      "financial-integration": "Integración financiera",
      season: "Temporada",
      "email-templates": "Plantillas de correo",
      "player-stats": "Estadísticas de jugadores",
      "team-stats": "Estadísticas de equipos",
    },
    title: "Team Assist",
    "team-selector": {
      loading: "Cargando equipos...",
      "load-error": "Error al cargar los equipos",
      "active-team": "Equipo activo",
      "change-team": "Cambiar el equipo activo a {{name}}",
    },
    "user-menu": {
      "open-menu": "Abrir menú de perfil",
      logout: "Cerrar sesión",
      language: {
        "switch-to-english": "Switch to English",
        "switch-to-spanish": "Cambiar a Español",
      },
    },
  },
  organization: {
    settings: {
      title: "Configuración de la organización",
      general: {
        title: "General",
        name: "Nombre",
        "display-name": "Nombre para mostrar",
        slug: "Slug",
        "contact-email": "Correo de contacto",
        "privacy-policy": "Política de privacidad",
      },
      applications: {
        title: "Solicitudes",
        "preview-form": "Vista previa del formulario",
        "open-applications": "Abrir solicitudes",
        headline: "Encabezado",
        description: "Descripción",
        "closed-message": "Mensaje de solicitudes cerradas",
        "success-message": "Mensaje de solicitud exitosa",
      },
      save: "Guardar",
      "not-found": "Organización no encontrada",
      update: {
        error: "Algo salió mal",
        success: "Organización actualizada",
      },
    },
  },
  permissions: {
    denied: "No tienes permisos para ver este recurso.",
    "add-permission": "Añadir permiso",
    role: "Rol",
    "select-role": "Por favor seleccione un Rol de Permiso",
    "permission-exists": "El permiso ya existe.",
    "delete-permission-confirmation": "¿Eliminar permiso?",
    deleting: "Eliminando...",
    "permission-deleted": "Permiso eliminado.",
    "edit-teams": "Editar equipos",
    "permissions-updated": "Permisos actualizados.",
    "delete-permission": "Eliminar permiso",
  },
  players: {
    status: {
      change: "Cambiar estado",
      "change-title": "Cambiar estado de {{name}}",
      "status-label": "Estado:",
      registration: {
        "start-process": "¿Iniciar proceso de registro?",
        "start-process-description": "(solicitar documentos, fotos y pago del jugador)",
      },
      error: "Algo salió mal",
      cancel: "Cancelar",
      save: "Guardar",
      "applicant-organization": "Solicitante de Organización",
      "applicant-team": "Solicitante de Equipo",
      "trialist-invited": "Prueba Invitado",
      trialist: "A Prueba",
      "trialist-unsuccessful": "Prueba Sin Éxito",
      "registration-pending": "Registro pendiente",
      "registered-matches": "Registrado para partidos",
      "registered-training": "Entrenamiento solo",
      relocated: "Relocado a otro equipo",
      left: "Se fue del equipo",
      removed: "Removido del equipo",
      active: "Activo",
      inactive: "Inactivo",
      "removed-team": "Eliminado del Equipo",
      "removed-organization": "Eliminado de la Organización",
    },
    filters: {
      status: {
        label: "Estado del jugador",
        none: "Ninguno",
      },
      "sort-by": {
        label: "Ordenar por",
        options: {
          "creation-date": "Fecha de solicitud",
          names: "Nombres de jugadores",
          age: "Edad del jugador",
        },
      },
      search: "Buscar",
    },
    list: {
      loading: "Cargando jugadores...",
      "load-error": "Error al cargar los jugadores.",
      "no-players":
        "No hay jugadores en este equipo ({{name}}). Los filtros pueden afectar este resultado.",
      "showing-count": "Mostrando {{count}} jugadores.",
      "filters-note": "Los filtros pueden afectar los resultados.",
    },
    card: {
      born: "Nacido el {{date}}",
      "years-old": "({{age}} años)",
      details: "Detalles",
      documents: "Documentos",
      "documents-count": "Documentos ({{count}})",
    },
    admin: {
      filters: {
        "born-after": "Jugadores nacidos después de",
        "born-before": "Jugadores nacidos antes de",
        "clear-date-after": "Limpiar filtro de 'Jugadores nacidos después de'",
        "clear-date-before": "Limpiar filtro de 'Jugadores nacidos antes de'",
        status: {
          label: "Estado del jugador",
          none: "Ninguno",
        },
        team: {
          label: "Equipo",
          none: "Ninguno",
        },
        name: "Nombre del jugador",
        "clear-name": "Limpiar filtro de nombre",
        "sort-by": {
          label: "Ordenar por",
          options: {
            "creation-date": "Fecha de solicitud",
            names: "Nombres de jugadores",
            age: "Edad del jugador",
          },
        },
        search: "Buscar",
      },
      card: {
        gender: "Género",
        born: "Nacido el {{date}}",
        "years-old": "({{age}} años)",
        "teams-count": "Parte de {{count}} equipos",
        details: "Detalles",
        documents: "Documentos",
        "documents-count": "Documentos ({{count}})",
      },
      list: {
        loading: "Cargando jugadores...",
        "load-error": "Error al cargar los jugadores.",
        "no-players": "No se encontraron jugadores.",
        "showing-count": "Mostrando {{count}} de {{total}} jugadores",
        "loading-more": "Cargando más jugadores...",
        "load-more": "Cargar más",
      },
      email: {
        "send-to": "Enviar correo a",
        targets: {
          players: "Jugadores",
          guardians: "Tutores",
        },
      },
    },
    "assign-to-team": "Asignar a equipo",
    "assign-player-to-team": "Asignar {{name}} a un equipo",
    assign: "Asignar",
    details: {
      title: "Detalles de {{name}}",
      contact: "Contacto",
      guardian: "Tutor",
      teams: "Equipos del jugador",
      "no-teams": "Sin equipos aún",
      "additional-info": "Información adicional",
      address: "Dirección",
      postcode: "Código postal",
      phone: "Teléfono",
      email: "Correo electrónico",
      "copy-email": "Copiar correo",
      "send-email": "Enviar correo",
      name: "Nombre",
      born: "Nacido",
      "remove-from-team": "Eliminar del equipo",
      "remove-confirmation":
        "¿Estás seguro de que deseas eliminar al jugador del equipo? Esta acción es irreversible",
      "playing-experience": "Experiencia de juego",
      "medical-conditions": "Condiciones médicas",
      none: "Ninguna",
      "preferred-position": "Posición preferida",
      "not-available": "N/D",
      close: "Cerrar",
      "remove-from-organization": "Eliminar de la organización",
      "remove-from-organization-confirmation":
        "¿Estás seguro de que deseas eliminar al jugador de la organización? Esta acción es irreversible",
      "remove-from-organization-dialog-title": "Eliminar jugador de la organización?",
      "remove-from-team-dialog-title": "Eliminar jugador del equipo?",
    },
    documents: {
      photo: "Foto",
      "photo-alt": "Foto del jugador",
      "id-card": "DNI / Pasaporte",
      "view-document": "Ver documento",
    },
    profile: {
      title: "Detalles de {{name}}",
      tabs: {
        profile: "Perfil",
        stats: "Estadísticas",
      },
      strengths: "Fortalezas:",
      weaknesses: "Debilidades:",
      improvements: "Ha mostrado mejoras en:",
      "recent-sessions": "Sesiones recientes:",
      stats: {
        goals: "Goles",
        assists: "Asistencias",
        position: "Posición",
        rating: "Calificación promedio",
      },
      close: "Cerrar",
    },
    attendance: {
      attended: "Asistió",
      late: "Tarde",
      "missing-with-reason": "No asistió (con justificación)",
      "missing-no-reason": "No asistió (sin justificación)",
    },
    performance: {
      abysmal: "Pésimo",
      bad: "Malo",
      neutral: "Neutral",
      good: "Bueno",
      outstanding: "Sobresaliente",
    },
  },
  seasons: {
    loading: "Cargando temporada",
    "load-error": "Error al cargar la temporada",
    objective: "Objetivo",
    "start-date": "Fecha de inicio",
    "end-date": "Fecha de fin",
    "edit-season": "Editar temporada",
    "loading-seasons": "Cargando temporadas...",
    count: "{{count}} Temporadas",
    "add-season": "Agregar temporada",
    "list-navigation": "Navegación de lista de temporadas",
    "edit-season-title": 'Editar temporada "{{name}}"',
    "add-new-season": "Agregar nueva temporada",
    "back-to-seasons": "Volver a temporadas",
    name: "Nombre",
    "objective-placeholder": "Ganar todas las ligas...",
    "start-date-label": "La temporada comienza el:",
    "end-date-label": "La temporada termina el:",
  },
  email: {
    send: {
      title: "Enviar correo",
      subject: "Asunto",
      template: {
        label: "Plantilla de correo",
        none: "Ninguna",
        loading: "Cargando plantillas de correo...",
        "load-error": "Error al cargar las plantillas de correo.",
      },
      success: "Correo enviado",
      error: "Error al enviar el correo",
      close: "Cerrar",
      send: "Enviar",
    },
  },
  matches: {
    edit: {
      loading: "Cargando partido",
      "load-error": "Error al cargar el partido",
    },
    card: {
      description: "Descripción",
      competition: "Competición",
      date: "Fecha",
      league: "Liga",
      cup: "Copa",
      friendly: "Amistoso",
    },
    actions: {
      "add-review": "Agregar reseña",
      "edit-match": "Editar partido",
    },
    list: {
      loading: "Cargando partidos...",
      "load-error": "Error al cargar los partidos",
      "permission-denied": "Permiso denegado",
      title: "{{count}} partidos de fútbol",
      "add-new": "Agregar nuevo partido",
    },
    form: {
      "edit-title": "Editar plan de {{name}}",
      "create-title": "Agregar nuevo partido de fútbol",
      "back-to-match-center": "Volver al centro de partidos",
      fields: {
        name: "Título",
        "opposition-name": "Nombre del oponente",
        host: {
          label: "Anfitrión",
          home: "Local",
          away: "Visitante",
          neutral: "Neutral",
        },
        description: "Descripción",
        "start-date": "Fecha y hora de inicio",
        competition: {
          label: "Competición",
          league: "Liga",
          cup: "Copa",
          friendly: "Amistoso",
        },
        "learning-outcomes": "Objetivos de aprendizaje",
        "players-engagement": "Participación de jugadores",
        "coach-behaviours": "Comportamiento del entrenador",
        "safety-considerations": "Consideraciones de seguridad",
        "arrival-and-warm-up": "Llegada y calentamiento",
        "game-week": "Jornada",
        "league-position": "Posición en la liga",
        "league-position-opponent": "Posición en la liga del",
        opponent: " oponente",
      },
      save: "Guardar",
      error: "Algo salió mal",
      "match-already-exists-in-game-week-error": "El partido ya existe en la jornada",
    },
    review: {
      loading: "Cargando revisión y jugadores...",
      "load-error": "Error al cargar la revisión",
      "permission-denied": "Permiso denegado",
      tabs: {
        overview: "Resumen",
        goals: "Goles",
        players: "Jugadores",
        attempts: "Remates",
      },
      goals: {
        scored: {
          title: "Goles marcados",
          type: {
            normal: "Normal",
            "own-goal": "Gol en propia puerta",
          },
          fields: {
            minute: "Minuto",
            "added-time": "Tiempo añadido",
            half: {
              label: "Mitad",
              first: "Primera",
              second: "Segunda",
              "extra-time-first": "Primera prórroga",
              "extra-time-second": "Segunda prórroga",
              penalties: "Después de penaltis",
            },
            situation: {
              label: "Situación",
              "open-play": "Jugada",
              "set-piece": "Jugada a balón parado",
              detailed: "Situación detallada",
              types: {
                corner: "Córner",
                "direct-free-kick": "Tiro libre directo",
                "goal-kick": "Saque de puerta",
                "indirect-free-kick": "Tiro libre indirecto",
                penalty: "Penalti",
                "throw-in": "Saque de banda",
              },
            },
            scorer: "Goleador",
            zone: "Zona",
            "body-part": {
              label: "Parte del cuerpo",
              "right-foot": "Pie derecho",
              "left-foot": "Pie izquierdo",
              head: "Cabeza",
              chest: "Pecho",
              knee: "Rodilla",
              shoulder: "Hombro",
              hip: "Cadera",
              back: "Espalda",
              hand: "Mano",
              other: "Otro",
            },
            assist: "Asistencia",
            "assist-zone": "Zona de asistencia",
            "key-pass": "Pase clave",
            "key-pass-zone": "Zona de pase clave",
          },
          save: "Guardar",
          error: "Algo salió mal",
          success: "Revisión actualizada con éxito",
          updated: "Revisión actualizada",
          created: "Revisión creada",
        },
        conceded: {
          title: "Goles recibidos",
          "error-leading-to-goal": "Error que llevó al gol",
        },
      },
      match: {
        "back-to-match-center": "Volver al centro de partidos",
        title: "Revisión",
        fields: {
          "opposition-name": "Nombre del oponente",
          host: {
            label: "Anfitrión",
            home: "Local",
            away: "Visitante",
            neutral: "Neutral",
          },
          "goals-scored": "Goles marcados",
          "goals-conceded": "Goles recibidos",
          outcome: {
            label: "Resultado",
            win: "Victoria",
            loss: "Derrota",
            draw: "Empate",
          },
          rating: "Valoración del partido",
          weather: {
            label: "Condiciones meteorológicas",
            sunny: "Soleado",
            rain: "Lluvia",
            wet: "Húmedo",
            overcast: "Nublado",
            snow: "Nieve",
            fog: "Niebla",
          },
        },
        save: "Guardar",
        error: "Algo salió mal",
        success: "Revisión actualizada con éxito",
        updated: "Revisión actualizada",
        created: "Revisión creada",
      },
      players: {
        review: {
          title: "Revisión",
          addReview: "Agregar evaluación del jugador",
          editReview: "Editar evaluación del jugador",
          rating: {
            decrease: "Disminuir calificación del jugador",
            increase: "Aumentar calificación del jugador",
          },
          minutesPlayed: {
            decrease: "Disminuir minutos jugados",
            increase: "Aumentar minutos jugados",
          },
        },
        "back-to-match-center": "Volver al centro de partidos",
        title: "Revisiones de jugadores",
        save: "Guardar",
        error: "Algo salió mal",
        success: "¡Revisión creada!",
        attendance: "Asistencia",
        "attendance-types": {
          attended: "Asistió",
          late: "Tarde",
          "missing-with-reason": "No asistió (con justificación)",
          "missing-no-reason": "No asistió (sin justificación)",
        },
        "player-attributes": {
          // Technical
          dribbling: "Regate",
          technique: "Técnica",
          shooting: "Tiro",
          crossing: "Centro",
          finishing: "Definición",
          heading: "Juego de Cabeza",
          passing: "Pase",
          "first-touch": "Primer Toque",
          tackling: "Entrada",
          marking: "Marcaje",
          cover: "Cobertura",
          "defensive-balance": "Balance Defensivo",
          "recovery-runs": "Carreras de Recuperación",
          delaying: "Contención",
          "throw-ins": "Saques de Banda",
          "penalty-taking": "Lanzamiento de Penaltis",
          "free-kick-taking": "Lanzamiento de Faltas",
          "corner-taking": "Lanzamiento de Córners",
          // Mental
          aggression: "Agresividad",
          anticipation: "Anticipación",
          bravery: "Valentía",
          composure: "Serenidad",
          concentration: "Concentración",
          decisions: "Decisiones",
          determination: "Determinación",
          flair: "Creatividad",
          leadership: "Liderazgo",
          "off-the-ball": "Desmarque",
          positioning: "Posicionamiento",
          "team-work": "Trabajo en Equipo",
          vision: "Visión",
          "work-rate": "Ritmo de Trabajo",
          scanning: "Lectura",
          movement: "Movimiento",
          deception: "Engaño",
          timing: "Temporización",
          // Physical
          acceleration: "Aceleración",
          agility: "Agilidad",
          balance: "Equilibrio",
          "jumping-reach": "Alcance de Salto",
          "natural-fitness": "Forma Física Natural",
          pace: "Velocidad",
          stamina: "Resistencia",
          strength: "Fuerza",
          // Hidden
          temperament: "Temperamento",
          adaptability: "Adaptabilidad",
          controversy: "Controversia",
          consistency: "Consistencia",
          "important-matches": "Partidos Importantes",
          ambition: "Ambición",
          "injury-proneness": "Propensión a Lesiones",
          loyalty: "Lealtad",
          dirtiness: "Juego Sucio",
          pressure: "Presión",
          professionalism: "Profesionalismo",
          sportsmanship: "Deportividad",
          versatility: "Versatilidad",
          // Goalkeeper
          "aerial-reach": "Alcance Aéreo",
          "command-of-area": "Dominio del Área",
          communication: "Comunicación",
          eccentricity: "Excentricidad",
          handling: "Manejo",
          kicking: "Pateo",
          "one-on-ones": "Uno contra Uno",
          "tendency-to-punch": "Tendencia a Despejar",
          reflexes: "Reflejos",
          "tendency-to-rush-out": "Tendencia a Salir",
          throwing: "Lanzamiento",
        },
        "tactical-positions": {
          goalkeeper: "Portero",
          "right-back": "Lateral Derecho",
          "right-centre-back": "Central Derecho",
          "centre-back": "Central",
          "left-back": "Lateral Izquierdo",
          "left-centre-back": "Central Izquierdo",
          "right-wing-back": "Carrilero Derecho",
          "left-wing-back": "Carrilero Izquierdo",
          "central-defensive-midfielder": "Mediocentro Defensivo Central",
          "defensive-midfielder": "Mediocentro Defensivo",
          "central-midfielder": "Mediocentro",
          "central-attacking-midfielder": "Mediapunta Central",
          "left-midfielder": "Interior Izquierdo",
          "right-midfielder": "Interior Derecho",
          "right-winger": "Extremo Derecho",
          "left-winger": "Extremo Izquierdo",
          striker: "Delantero",
        },
        mainPosition: "Posición principal",
        minutesPlayed: "Minutos jugados",
        highlights: "Destacados",
      },
    },
  },
  match: {
    minute: "Minuto",
    "added-time": "Tiempo añadido",
    half: "Mitad",
    "first-half": "Primera",
    "second-half": "Segunda",
    "extra-time-first": "Primera prórroga",
    "extra-time-second": "Segunda prórroga",
    penalties: "Penaltis post partido",
    situation: "Situación",
    "open-play": "Juego abierto",
    "set-piece": "Jugada a balón parado",
    "error-leading-to-goal": "Error que lleva al gol",
    pressing: "Presión",
    "review-updated": "Revisión actualizada con éxito",
    "goal-type": "Tipo",
    "goal-type-normal": "Normal",
    "goal-type-own": "Gol en propia",
    "goal-scorer": "Goleador",
    zone: "Zona",
    "body-part": "Parte del cuerpo",
    "review-status": "Revisión {{action}}",
    "body-parts": {
      "right-foot": "Pie derecho",
      "left-foot": "Pie izquierdo",
      head: "Cabeza",
      chest: "Pecho",
      knee: "Rodilla",
      shoulder: "Hombro",
      hip: "Cadera",
      back: "Espalda",
      hand: "Mano",
      other: "Otro",
    },
    halves: {
      first: "Primera",
      second: "Segunda",
      "extra-time-first": "Primera prórroga",
      "extra-time-second": "Segunda prórroga",
      penalties: "Penaltis post partido",
    },
    "situation-detailed-options": {
      "open-play": "Juego abierto",
      "set-piece": "Jugada a balón parado",
      "error-leading-to-a-goal": "Error que lleva al gol",
      pressing: "Presión",
    },
    "open-play-sub-type": {
      "counter-attack": "Contraataque",
      "combination-play": "Jugada combinada",
      cross: "Cruz",
      dribble: "Dribble",
      "long-shot": "Tiro largo",
      "one-to-one": "Uno a uno",
      rebound: "Rebote",
      "through-ball": "Pase a través",
    },
    "set-piece-sub-type": {
      corner: "Córner",
      "direct-free-kick": "Tiro libre directo",
      "goal-kick": "Saque de puerta",
      "indirect-free-kick": "Tiro libre indirecto",
      penalty: "Penalti",
      "throw-in": "Saque de banda",
    },
    save: "Guardar",
    error: "Algo salió mal",
    success: "¡Revisión creada!",
    updated: "Revisión actualizada",
    created: "Revisión creada",
    review: "Revisión",
    "key-pass": "Pase clave",
    "key-pass-zone": "Zona de pase clave",
    "assist-zone": "Zona de asistencia",
    "assist-body-part": "Parte del cuerpo de la asistencia",
    "through-ball": "Pase a través",
    "early-goals": "Goles tempranos",
    "late-goals": "Goles tardíos",
    "mid-match-goals": "Goles en mitad de partido",
    assist: "Asistencia",
    "situation-detailed": "Situación detallada",
    "time-details": "Detalles de tiempo",
    "goal-details": "Detalles de gol",
    "key-pass-details": "Detalles de pase clave",
    "situation-details": "Detalles de situación",
    "assist-details": "Detalles de asistencia",
    type: "Tipo",
    "type-normal": "Normal",
    "type-own": "Gol en propia",
    "goal-scored": "Gol marcado",
    "goal-conceded": "Gol recibido",
    "attempt-details": "Detalles del Intento",
    shooter: "Tirador",
    "attempt-type-options": {
      "shot-on-goal": "Remate a gol",
      "shot-off-goal": "Remate fuera de gol",
      "shot-blocked": "Remate bloqueado",
      goal: "Gol",
    },
    "add-attempt": "Añadir remate",
    "add-attempt-against": "Añadir remate en contra",
    attempt: "Remate",
    "attempt-type": "Tipo de remate",
    attempts: "Remates",
    "attempts-against": "Remates en contra",
    "delete-attempt-confirmation": "¿Estás seguro de que quieres eliminar este remate?",
    "delete-attempt-confirmation-title": "Eliminar remate",
  },
  stats: {
    "first-name": "Nombre",
    "last-name": "Apellido",
    appearances: "Apariciones",
    "assists-per-match": "Asistencias por partido",
    assists: "Asistencias",
    loading: "Cargando estadísticas...",
    "load-error": "Error al cargar estadísticas",
    "goals-per-match": "Goles por partido",
    "goals-received-per-match": "Goles recibidos por partido",
    goals: "Goles",
    "goals-per-half": "Goles por mitad",
    "goal-times": "Tiempos de gol",
    "goals-came-from": "Los goles vinieron de",
    "open-play-goals": "Goles en juego abierto",
    "set-piece-goals": "Goles a balón parado",
    "goals-scored-with": "Goles marcados con",
    "goals-by-zone": "Goles por zona",
    "goal-types": {
      first: "Primera parte",
      second: "Segunda parte",
      "extra-time-first": "Primera prórroga",
      "extra-time-second": "Segunda prórroga",
      "after-match-penalties": "Penaltis",
      "0-15": "0-15 min",
      "16-30": "16-30 min",
      "31-45": "31-45 min",
      "46-60": "46-60 min",
      "61-75": "61-75 min",
      "76-90": "76-90 min",
      "91-105": "91-105 min",
      "106-120": "106-120 min",
      "open-play": "Juego abierto",
      "set-piece": "Balón parado",
      "error-leading-to-a-goal": "Error que lleva al gol",
      pressing: "Presión",
      "counter-attack": "Contraataque",
      "combined-play": "Jugada combinada",
      "individual-play": "Jugada individual",
      "corner-kick": "Córner",
      "free-kick": "Tiro libre",
      penalty: "Penalti",
      "throw-in": "Saque de banda",
      "right-foot": "Pie derecho",
      "left-foot": "Pie izquierdo",
      head: "Cabeza",
      chest: "Pecho",
      knee: "Rodilla",
      shoulder: "Hombro",
      hip: "Cadera",
      back: "Espalda",
      hand: "Mano",
      other: "Otro",
      "direct-free-kick": "Tiro libre directo",
      "goal-kick": "Saque de puerta",
      "indirect-free-kick": "Tiro libre indirecto",
      "through-ball": "Pase a través",
      "combination-play": "Jugada combinada",
      "early-goals": "Goles tempranos",
      "late-goals": "Goles tardíos",
      "mid-match-goals": "Goles en mitad de partido",
    },
    "goals-received-per-half": "Goles recibidos por mitad",
    "goals-received-came-from": "Goles recibidos vinieron de",
    "goals-received-scored-with": "Goles recibidos marcados con",
    "goals-received-by-zone": "Goles recibidos por zona",
    "goals-received-times": "Tiempos de goles recibidos",
    "goals-set-piece-received": "Goles a balón parado recibidos",
    "key-passes-per-match": "Pases clave por partido",
    "key-passes": "Pases clave",
    "average-rating": "Valoración media",
    "tabs-label": "Pestañas de estadísticas",
    "player-performance": "Rendimiento del jugador",
    match: "Partido",
    "goals-scored": "Goles marcados",
    "goals-conceded": "Goles recibidos",
    "game-week": "Jornada",
    "game-week-abbreviation": "J",
  },
  training: {
    attendance: "Asistencia",
    rating: "Valoración",
    highlights: "Destacados",
    "attendance-types": {
      attended: "Asistió",
      late: "Tarde",
      "missing-with-reason": "Ausente con justificación",
      "missing-no-reason": "Ausente sin justificación",
    },
    ratings: {
      abysmal: "Pésimo",
      bad: "Malo",
      neutral: "Neutral",
      good: "Bueno",
      outstanding: "Sobresaliente",
    },
    attributes: {
      acceleration: "Aceleración",
      adaptability: "Adaptabilidad",
      aggression: "Agresividad",
      agility: "Agilidad",
      ambition: "Ambición",
      anticipation: "Anticipación",
    },
    "learning-outcomes": "Objetivos de aprendizaje",
    "players-engagement": "Participación de jugadores",
    "coach-behaviours": "Comportamiento del entrenador",
    "safety-considerations": "Consideraciones de seguridad",
    "arrival-and-warm-up": "Llegada y calentamiento",
    summary: "Resumen",
    details: "Detalles",
    "expectations-achieved-rating": "Valoración de expectativas alcanzadas",
    "review-player": "Evaluar a {{name}}",
    "add-new-review": "Añadir nueva evaluación",
    "review-created": "¡Evaluación creada!",
    "review-count": "{{count}} evaluaciones",
    performance: "Rendimiento",
    "no-reviews-for-period": "No hay evaluaciones para el período seleccionado.",
    reviews: "Evaluaciones",
    players: "Jugadores",
    "average-rating": "Valoración media",
    "average-attendance": "Asistencia media",
    "chart-view": "Vista de gráfico",
    "table-view": "Vista de tabla",
    insights: {
      "goals-first-half": "Has marcado el {{percent}}% de tus goles en la primera parte.",
      "defensive-improvement":
        'Has trabajado en "Principios defensivos del juego" en las últimas {{weeks}} semanas y has encajado un {{percent}}% menos de goles en comparación con el período anterior trabajando en otros temas.',
      "player-combination":
        "El {{percent}}% de tus goles han venido de combinaciones entre {{player1}} y {{player2}}.",
    },
    filters: {
      "next-30-days": "Próximos 30 días",
      "all-next": "Todos los próximos",
      "past-30-days": "Últimos 30 días",
      "all-past": "Todo el pasado",
    },
    "show-insights": "Mostrar estadísticas",
    "show-reviews": "Mostrar evaluaciones",
    "loading-events": "Cargando eventos...",
    "failed-to-load-events": "Error al cargar eventos.",
    "no-trainings-yet": "No hay entrenamientos aún.",
    "player-not-found": "Jugador no encontrado.",
    "edit-review": "Editar evaluación",
    "add-review": "Añadir evaluación",
    plan: "Planificar",
    "review-details": "Detalles de evaluación",
    "back-to-training-center": "Volver al Centro de Entrenamiento",
    "session-review": "Evaluación de sesión",
    "review-tabs-label": "Pestañas de evaluación del Centro de Entrenamiento",
    "player-reviews": "Evaluaciones de jugadores",
    "training-center": "Centro de Entrenamiento",
    "create-new": "Crear nuevo",
    "upload-new": "Subir nuevo",
    "select-from-library": "Seleccionar de la lista",
    "add-practice": "Añadir tarea",
  },
  team: {
    open: "Abrir",
    "loading-teams": "Cargando equipos...",
    "failed-to-load-teams": "Error al cargar equipos.",
    "teams-list": "Lista de equipos",
    "teams-list-navigation": "Navegación de lista de equipos",
    "loading-team": "Cargando equipo",
    "failed-to-load-team": "Error al cargar equipo",
    "delete-team-title": 'Eliminar equipo "{{name}}"',
    "edit-team": "Editar equipo",
    "delete-team": "Eliminar equipo",
    "delete-team-confirmation":
      "Se perderán todos los datos de los jugadores de este equipo y la acción es irreversible. ¿Estás seguro de que quieres eliminar este equipo?",
    "teams-count": "{{count}} Equipos",
    "add-team": "Añadir equipo",
    "edit-team-title": "Editar equipo {{name}}",
    "add-new-team": "Añadir nuevo equipo",
    "back-to-teams": "Volver a equipos",
    name: "Nombre",
    "age-description": "Descripción de edad",
    "age-description-placeholder": "Menores de 15 años",
    gender: "Género",
    genders: {
      male: "Masculino",
      female: "Femenino",
      mixed: "Mixto",
    },
    "players-born-after": "Jugadores nacidos después de",
    "players-born-before": "Jugadores nacidos antes de",
    slogan: "Lema del equipo",
    "sync-with-financial-system": "Sincronizar con sistema financiero",
    fee: "Cuota",
    currency: "Moneda",
    "fee-interval": "Intervalo de cuota",
    "fee-intervals": {
      monthly: "Mensual",
      yearly: "Anual",
    },
  },
  users: {
    "email-already-registered": "El usuario con ese correo electrónico ya está registrado",
    permissions: "Permisos",
    roles: {
      owner: "Propietario",
    },
    send: "Enviar",
    invite: "Invitar",
    "invite-new-member": "Invitar nuevo miembro",
    "delete-invite-confirmation": "¿Eliminar la invitación para {{email}}?",
    "invite-deleted": "Invitación eliminada.",
    "failed-to-delete-invite": "No se pudo eliminar la invitación.",
    extend: "Extender",
    "extend-invite-confirmation": "¿Extender invitación para {{email}}?",
    "invite-extended": "Invitación extendida.",
    "failed-to-extend-invite": "No se pudo extender la invitación.",
    invitee: "Invitado",
    "invited-by": "Invitado por",
    "invited-on": "Invitado el",
    "expires-on": "Expira el",
    expired: "Expirado",
    "fetching-invites": "Cargando invitaciones...",
    "failed-to-load-invites": "Error al cargar invitaciones.",
    "no-unredeemed-invites": "No hay invitaciones sin reclamar.",
    status: "Estado",
    all: "Todos",
    pending: "Pendiente",
    me: "(yo)",
    "joined-on": "Se unió el",
    "no-permissions": "Sin permisos.",
    "remove-member": "Eliminar miembro",
    owner: "Propietario",
    "teams-count": "{{count}} equipos",
    "loading-users": "Cargando usuarios...",
    "failed-to-load-users": "Error al cargar usuarios.",
    "users-admin": "Administración de usuarios",
    "user-admin-tabs": "Pestañas de administración de usuarios",
    users: "Usuarios",
    invites: "Invitaciones",
    "permission-roles": "Roles de permisos",
  },
  roles: {
    "add-role": "Añadir rol",
    "create-new-role": "Crear nuevo rol",
    "create-role": "Crear rol",
    "role-created": "Rol creado.",
    "delete-role-confirmation": "¿Eliminar rol {{name}}?",
    "role-deleted": "Rol eliminado.",
    "failed-to-delete-role": "Error al eliminar rol.",
    "edit-role": "Editar rol {{name}}",
    "role-updated": "Rol actualizado.",
    "loading-roles": "Cargando roles...",
    "failed-to-load-roles": "Error al cargar roles.",
    "delete-role": "Eliminar rol {{name}}",
    name: "Nombre",
    description: "Descripción",
    "role-already-exists": "Ya existe un rol con el mismo nombre.",
  },
};

export default translation;
