export interface TranslationSchema {
  auth: {
    login: string;
    register: string;
    "forgot-password": string;
    "reset-password": string;
    "join-organization": string;
    "email-placeholder": string;
    "password-placeholder": string;
    "confirm-password-placeholder": string;
    "no-account": string;
    "have-account": string;
    "sign-up": string;
    "sign-in": string;
    "reset-instructions": string;
    "invalid-credentials": string;
    "registration-error": string;
    "permission-denied": string;
    "load-error": string;
    "welcome-to-team-assist": string;
    welcome: string;
    "registration-page": string;
    "enter-email": string;
    "back-to-login": string;
    "enter-new-password": string;
    "team-assist": string;
    "join-organization-title": string;
    "invite-fetch-error": string;
    "invalid-invite": string;
    "expired-invite": string;
    "generic-error": string;
    "accept-invite": string;
  };
  common: {
    submit: string;
    cancel: string;
    save: string;
    delete: string;
    edit: string;
    create: string;
    back: string;
    open: string;
    close: string;
    loading: string;
    error: string;
    success: string;
    confirm: string;
    actions: string;
    continue: string;
    "permission-denied": string;
    confirmation: {
      cancel: string;
      confirm: string;
      error: string;
      "error-message": string;
      success: string;
      "success-message": string;
    };
    updated: string;
    created: string;
    name: string;
    "something-went-wrong": string;
    email: string;
    "please-confirm": string;
    "not-available": string;
  };
  teams: {
    title: string;
    "add-team": string;
    "edit-team": string;
    "delete-team": string;
    "team-name": string;
    "age-group": string;
    season: string;
    players: string;
    coaches: string;
    "load-error": string;
    loading: string;
    "select-team": string;
    card: {
      gender: string;
      "age-description": string;
      "born-before": string;
      "born-after": string;
    };
  };
  validation: {
    required: string;
    "invalid-email": string;
    "password-mismatch": string;
    "min-length": string;
    "max-length": string;
  };
  "email-templates": {
    title: string;
    "add-template": string;
    "delete-confirm-title": string;
    "delete-warning": string;
    loading: string;
    "load-error": string;
    "no-templates": string;
    subject: string;
    "edit-template": string;
    "delete-template": string;
    "template-name": string;
    visibility: string;
    "visibility-private": string;
    "visibility-company": string;
    "visibility-description": string;
    body: string;
    "create-template": string;
    "no-subject": string;
  };
  events: {
    title: string;
    "create-single": string;
    "create-recurring": string;
    type: {
      single: string;
      recurring: string;
    };
    filters: {
      "start-date": string;
      "end-date": string;
      show: string;
    };
    hosts: {
      title: string;
      "add-placeholder": string;
      you: string;
    };
    invitations: {
      title: string;
      "load-error": string;
      loading: string;
      retry: string;
    };
    form: {
      general: string;
      name: string;
      agenda: string;
      description: string;
      "description-placeholder": string;
      "location-address": string;
      "location-postcode": string;
      save: string;
      error: string;
      "agenda-options": {
        training: string;
        match: string;
        meeting: string;
        other: string;
      };
      "start-date-time": string;
      "end-date-time": string;
    };
    scheduling: {
      title: string;
      "start-time": string;
      "end-time": string;
      "start-from": string;
      type: string;
      "day-of-week": string;
      ends: string;
      "end-of-season": string;
      "custom-date": string;
      "end-date": string;
      types: {
        weekly: string;
        "bi-weekly": string;
        monthly: string;
      };
    };
    recurring: {
      "create-title": string;
      "loading-players": string;
      "loading-season": string;
      "load-players-error": string;
      "load-season-error": string;
      "edit-title": string;
    };
    single: {
      "create-title": string;
      "loading-players": string;
      "loading-season": string;
      "load-players-error": string;
      "load-season-error": string;
    };
  };
  financial: {
    integration: {
      "create-title": string;
      name: string;
      type: string;
      "api-key": string;
      submit: string;
      error: string;
      "already-exists": string;
      types: {
        gocardless: string;
        stripe: string;
      };
      item: {
        "menu-label": string;
        edit: string;
        delete: string;
      };
      view: {
        "add-button": string;
        loading: string;
        "load-error": string;
      };
      update: {
        title: string;
        "hidden-key": string;
      };
      delete: {
        title: string;
        error: string;
      };
      form: {
        "create-title": string;
        "edit-title": string;
      };
    };
  };
  sidebar: {
    menu: {
      "players-list": string;
      "training-center": string;
      "match-center": string;
      stats: string;
      "event-management": string;
      "club-admin": string;
      "teams-admin": string;
      "players-admin": string;
      "users-admin": string;
      finances: string;
      settings: string;
      general: string;
      "financial-integration": string;
      season: string;
      "email-templates": string;
      "player-stats": string;
      "team-stats": string;
    };
    title: string;
    "team-selector": {
      loading: string;
      "load-error": string;
      "active-team": string;
      "change-team": string;
    };
    "user-menu": {
      "open-menu": string;
      logout: string;
      language: {
        "switch-to-english": string;
        "switch-to-spanish": string;
      };
    };
  };
  organization: {
    settings: {
      title: string;
      general: {
        title: string;
        name: string;
        "display-name": string;
        slug: string;
        "contact-email": string;
        "privacy-policy": string;
      };
      applications: {
        title: string;
        "preview-form": string;
        "open-applications": string;
        headline: string;
        description: string;
        "closed-message": string;
        "success-message": string;
      };
      save: string;
      "not-found": string;
      update: {
        error: string;
        success: string;
      };
    };
  };
  permissions: {
    denied: string;
    "add-permission": string;
    role: string;
    "select-role": string;
    "permission-exists": string;
    "delete-permission-confirmation": string;
    deleting: string;
    "permission-deleted": string;
    "edit-teams": string;
    "permissions-updated": string;
    "delete-permission": string;
  };
  players: {
    status: {
      change: string;
      "change-title": string;
      "status-label": string;
      registration: {
        "start-process": string;
        "start-process-description": string;
      };
      error: string;
      cancel: string;
      save: string;
      "applicant-organization": string;
      "applicant-team": string;
      "trialist-invited": string;
      trialist: string;
      "trialist-unsuccessful": string;
      "registration-pending": string;
      "registered-matches": string;
      "registered-training": string;
      relocated: string;
      left: string;
      removed: string;
      active: string;
      inactive: string;
      "removed-team": string;
      "removed-organization": string;
    };
    filters: {
      status: {
        label: string;
        none: string;
      };
      "sort-by": {
        label: string;
        options: {
          "creation-date": string;
          names: string;
          age: string;
        };
      };
      search: string;
    };
    list: {
      loading: string;
      "load-error": string;
      "no-players": string;
      "showing-count": string;
      "filters-note": string;
    };
    card: {
      born: string;
      "years-old": string;
      details: string;
      documents: string;
      "documents-count": string;
    };
    admin: {
      filters: {
        "born-after": string;
        "born-before": string;
        "clear-date-after": string;
        "clear-date-before": string;
        status: {
          label: string;
          none: string;
        };
        team: {
          label: string;
          none: string;
        };
        name: string;
        "clear-name": string;
        "sort-by": {
          label: string;
          options: {
            "creation-date": string;
            names: string;
            age: string;
          };
        };
        search: string;
      };
      card: {
        gender: string;
        born: string;
        "years-old": string;
        "teams-count": string;
        details: string;
        documents: string;
        "documents-count": string;
      };
      list: {
        loading: string;
        "load-error": string;
        "no-players": string;
        "showing-count": string;
        "loading-more": string;
        "load-more": string;
      };
      email: {
        "send-to": string;
        targets: {
          players: string;
          guardians: string;
        };
      };
    };
    "assign-to-team": string;
    "assign-player-to-team": string;
    assign: string;
    details: {
      title: string;
      contact: string;
      guardian: string;
      teams: string;
      "no-teams": string;
      "additional-info": string;
      address: string;
      postcode: string;
      phone: string;
      email: string;
      "copy-email": string;
      "send-email": string;
      name: string;
      born: string;
      "remove-from-team": string;
      "remove-confirmation": string;
      "playing-experience": string;
      "medical-conditions": string;
      none: string;
      "preferred-position": string;
      "not-available": string;
      close: string;
      "remove-from-organization": string;
      "remove-from-organization-confirmation": string;
      "remove-from-organization-dialog-title": string;
      "remove-from-team-dialog-title": string;
    };
    documents: {
      photo: string;
      "photo-alt": string;
      "id-card": string;
      "view-document": string;
    };
    profile: {
      title: string;
      tabs: {
        profile: string;
        stats: string;
      };
      strengths: string;
      weaknesses: string;
      improvements: string;
      "recent-sessions": string;
      stats: {
        goals: string;
        assists: string;
        position: string;
        rating: string;
      };
      close: string;
    };
    attendance: {
      attended: string;
      late: string;
      "missing-with-reason": string;
      "missing-no-reason": string;
    };
    performance: {
      abysmal: string;
      bad: string;
      neutral: string;
      good: string;
      outstanding: string;
    };
  };
  seasons: {
    loading: string;
    "load-error": string;
    objective: string;
    "start-date": string;
    "end-date": string;
    "edit-season": string;
    "loading-seasons": string;
    count: string;
    "add-season": string;
    "list-navigation": string;
    "edit-season-title": string;
    "add-new-season": string;
    "back-to-seasons": string;
    name: string;
    "objective-placeholder": string;
    "start-date-label": string;
    "end-date-label": string;
  };
  email: {
    send: {
      title: string;
      subject: string;
      template: {
        label: string;
        none: string;
        loading: string;
        "load-error": string;
      };
      success: string;
      error: string;
      close: string;
      send: string;
    };
  };
  matches: {
    edit: {
      loading: string;
      "load-error": string;
    };
    card: {
      description: string;
      competition: string;
      date: string;
      league: string;
      cup: string;
      friendly: string;
    };
    actions: {
      "add-review": string;
      "edit-match": string;
    };
    list: {
      loading: string;
      "load-error": string;
      "permission-denied": string;
      title: string;
      "add-new": string;
    };
    form: {
      "edit-title": string;
      "create-title": string;
      "back-to-match-center": string;
      fields: {
        name: string;
        "opposition-name": string;
        host: {
          label: string;
          home: string;
          away: string;
          neutral: string;
        };
        description: string;
        "start-date": string;
        competition: {
          label: string;
          league: string;
          cup: string;
          friendly: string;
        };
        "learning-outcomes": string;
        "players-engagement": string;
        "coach-behaviours": string;
        "safety-considerations": string;
        "arrival-and-warm-up": string;
        "game-week": string;
        "league-position": string;
        "league-position-opponent": string;
        opponent: string;
      };
      save: string;
      error: string;
      "match-already-exists-in-game-week-error": string;
    };
    review: {
      loading: string;
      "load-error": string;
      "permission-denied": string;
      tabs: {
        overview: string;
        goals: string;
        players: string;
        attempts: string;
      };
      goals: {
        scored: {
          title: string;
          type: {
            normal: string;
            "own-goal": string;
          };
          fields: {
            minute: string;
            "added-time": string;
            half: {
              label: string;
              first: string;
              second: string;
              "extra-time-first": string;
              "extra-time-second": string;
              penalties: string;
            };
            situation: {
              label: string;
              "open-play": string;
              "set-piece": string;
              detailed: string;
              types: {
                corner: string;
                "direct-free-kick": string;
                "goal-kick": string;
                "indirect-free-kick": string;
                penalty: string;
                "throw-in": string;
              };
            };
            scorer: string;
            zone: string;
            "body-part": {
              label: string;
              "right-foot": string;
              "left-foot": string;
              head: string;
              chest: string;
              knee: string;
              shoulder: string;
              hip: string;
              back: string;
              hand: string;
              other: string;
            };
            assist: string;
            "assist-zone": string;
            "key-pass": string;
            "key-pass-zone": string;
          };
          save: string;
          error: string;
          success: string;
          updated: string;
          created: string;
        };
        conceded: {
          title: string;
          "error-leading-to-goal": string;
        };
      };
      match: {
        "back-to-match-center": string;
        title: string;
        fields: {
          "opposition-name": string;
          host: {
            label: string;
            home: string;
            away: string;
            neutral: string;
          };
          "goals-scored": string;
          "goals-conceded": string;
          outcome: {
            label: string;
            win: string;
            loss: string;
            draw: string;
          };
          rating: string;
          weather: {
            label: string;
            sunny: string;
            rain: string;
            wet: string;
            overcast: string;
            snow: string;
            fog: string;
          };
        };
        save: string;
        error: string;
        success: string;
        updated: string;
        created: string;
      };
      players: {
        review: {
          title: string;
          addReview: string;
          editReview: string;
          rating: {
            decrease: string;
            increase: string;
          };
          minutesPlayed: {
            decrease: string;
            increase: string;
          };
        };
        attendance: string;
        "attendance-types": {
          attended: string;
          late: string;
          "missing-with-reason": string;
          "missing-no-reason": string;
        };
        "player-attributes": {
          dribbling: string;
          technique: string;
          shooting: string;
          crossing: string;
          finishing: string;
          heading: string;
          passing: string;
          "first-touch": string;
          tackling: string;
          marking: string;
          cover: string;
          "defensive-balance": string;
          "recovery-runs": string;
          delaying: string;
          "throw-ins": string;
          "penalty-taking": string;
          "free-kick-taking": string;
          "corner-taking": string;
          aggression: string;
          anticipation: string;
          bravery: string;
          composure: string;
          concentration: string;
          decisions: string;
          determination: string;
          flair: string;
          leadership: string;
          "off-the-ball": string;
          positioning: string;
          "team-work": string;
          vision: string;
          "work-rate": string;
          scanning: string;
          movement: string;
          deception: string;
          timing: string;
          acceleration: string;
          agility: string;
          balance: string;
          "jumping-reach": string;
          "natural-fitness": string;
          pace: string;
          stamina: string;
          strength: string;
          temperament: string;
          adaptability: string;
          controversy: string;
          consistency: string;
          "important-matches": string;
          ambition: string;
          "injury-proneness": string;
          loyalty: string;
          dirtiness: string;
          pressure: string;
          professionalism: string;
          sportsmanship: string;
          versatility: string;
          "aerial-reach": string;
          "command-of-area": string;
          communication: string;
          eccentricity: string;
          handling: string;
          kicking: string;
          "one-on-ones": string;
          "tendency-to-punch": string;
          reflexes: string;
          "tendency-to-rush-out": string;
          throwing: string;
        };
        "tactical-positions": {
          goalkeeper: string;
          "right-back": string;
          "right-centre-back": string;
          "centre-back": string;
          "left-back": string;
          "left-centre-back": string;
          "right-wing-back": string;
          "left-wing-back": string;
          "central-defensive-midfielder": string;
          "defensive-midfielder": string;
          "central-midfielder": string;
          "central-attacking-midfielder": string;
          "left-midfielder": string;
          "right-midfielder": string;
          "right-winger": string;
          "left-winger": string;
          striker: string;
        };
        mainPosition: string;
        minutesPlayed: string;
        highlights: string;
        "back-to-match-center": string;
        title: string;
        save: string;
        error: string;
        success: string;
      };
    };
  };
  match: {
    minute: string;
    "added-time": string;
    half: string;
    "first-half": string;
    "second-half": string;
    "extra-time-first": string;
    "extra-time-second": string;
    penalties: string;
    situation: string;
    "open-play": string;
    "set-piece": string;
    "error-leading-to-goal": string;
    pressing: string;
    "review-updated": string;
    "goal-type": string;
    "goal-type-normal": string;
    "goal-type-own": string;
    "goal-scorer": string;
    zone: string;
    "body-part": string;
    "review-status": string;
    type: string;
    "type-normal": string;
    "type-own": string;
    "body-parts": {
      "right-foot": string;
      "left-foot": string;
      head: string;
      chest: string;
      knee: string;
      shoulder: string;
      hip: string;
      back: string;
      hand: string;
      other: string;
    };
    "key-pass": string;
    "key-pass-zone": string;
    "assist-zone": string;
    "assist-body-part": string;
    "through-ball": string;
    "early-goals": string;
    "late-goals": string;
    "mid-match-goals": string;
    save: string;
    error: string;
    success: string;
    updated: string;
    created: string;
    assist: string;
    "situation-detailed": string;
    "situation-detailed-options": {
      "error-leading-to-a-goal": string;
      pressing: string;
      "open-play": string;
      "set-piece": string;
    };
    "set-piece-sub-type": {
      corner: string;
      "direct-free-kick": string;
      "goal-kick": string;
      "indirect-free-kick": string;
      penalty: string;
      "throw-in": string;
    };
    "open-play-sub-type": {
      "counter-attack": string;
      "combination-play": string;
      cross: string;
      dribble: string;
      "long-shot": string;
      "one-to-one": string;
      rebound: string;
      "through-ball": string;
    };
    halves: {
      first: string;
      second: string;
      "extra-time-first": string;
      "extra-time-second": string;
      penalties: string;
    };
    review: string;
    "time-details": string;
    "goal-details": string;
    "key-pass-details": string;
    "situation-details": string;
    "assist-details": string;
    "goal-scored": string;
    "goal-conceded": string;
    "attempt-type-options": {
      "shot-on-goal": string;
      "shot-off-goal": string;
      "shot-blocked": string;
      goal: string;
    };
    "add-attempt": string;
    "add-attempt-against": string;
    attempts: string;
    "attempts-against": string;
    attempt: string;
    "attempt-details": string;
    shooter: string;
    "attempt-type": string;
    "delete-attempt-confirmation": string;
    "delete-attempt-confirmation-title": string;
  };
  stats: {
    "first-name": string;
    "last-name": string;
    appearances: string;
    "assists-per-match": string;
    assists: string;
    loading: string;
    "load-error": string;
    "goals-per-match": string;
    "goals-received-per-match": string;
    goals: string;
    "goals-per-half": string;
    "goal-times": string;
    "goals-came-from": string;
    "open-play-goals": string;
    "set-piece-goals": string;
    "goals-scored-with": string;
    "goals-by-zone": string;
    "goal-types": {
      first: string;
      second: string;
      "extra-time-first": string;
      "extra-time-second": string;
      "after-match-penalties": string;
      "0-15": string;
      "16-30": string;
      "31-45": string;
      "46-60": string;
      "61-75": string;
      "76-90": string;
      "91-105": string;
      "106-120": string;
      "open-play": string;
      "set-piece": string;
      "error-leading-to-a-goal": string;
      pressing: string;
      "counter-attack": string;
      "combination-play": string;
      "combined-play": string;
      "individual-play": string;
      "corner-kick": string;
      "free-kick": string;
      penalty: string;
      "throw-in": string;
      "right-foot": string;
      "left-foot": string;
      head: string;
      chest: string;
      knee: string;
      shoulder: string;
      hip: string;
      back: string;
      hand: string;
      other: string;
      "direct-free-kick": string;
      "goal-kick": string;
      "indirect-free-kick": string;
      "through-ball": string;
      "early-goals": string;
      "late-goals": string;
      "mid-match-goals": string;
    };
    "goals-received-per-half": string;
    "goals-received-came-from": string;
    "goals-received-scored-with": string;
    "goals-received-by-zone": string;
    "goals-received-times": string;
    "goals-set-piece-received": string;
    "key-passes-per-match": string;
    "key-passes": string;
    "average-rating": string;
    "tabs-label": string;
    "player-performance": string;
    match: string;
    "goals-scored": string;
    "goals-conceded": string;
    "game-week": string;
    "game-week-abbreviation": string;
  };
  training: {
    attendance: string;
    rating: string;
    highlights: string;
    "attendance-types": {
      attended: string;
      late: string;
      "missing-with-reason": string;
      "missing-no-reason": string;
    };
    ratings: {
      abysmal: string;
      bad: string;
      neutral: string;
      good: string;
      outstanding: string;
    };
    attributes: {
      acceleration: string;
      adaptability: string;
      aggression: string;
      agility: string;
      ambition: string;
      anticipation: string;
    };
    "learning-outcomes": string;
    "players-engagement": string;
    "coach-behaviours": string;
    "safety-considerations": string;
    "arrival-and-warm-up": string;
    summary: string;
    details: string;
    "expectations-achieved-rating": string;
    "review-player": string;
    "add-new-review": string;
    "review-created": string;
    "review-count": string;
    performance: string;
    "no-reviews-for-period": string;
    reviews: string;
    players: string;
    "average-rating": string;
    "average-attendance": string;
    "chart-view": string;
    "table-view": string;
    insights: {
      "goals-first-half": string;
      "defensive-improvement": string;
      "player-combination": string;
    };
    filters: {
      "next-30-days": string;
      "all-next": string;
      "past-30-days": string;
      "all-past": string;
    };
    "show-insights": string;
    "show-reviews": string;
    "loading-events": string;
    "failed-to-load-events": string;
    "no-trainings-yet": string;
    "player-not-found": string;
    "edit-review": string;
    "add-review": string;
    plan: string;
    "review-details": string;
    "back-to-training-center": string;
    "session-review": string;
    "review-tabs-label": string;
    "player-reviews": string;
    "training-center": string;
    "create-new": string;
    "upload-new": string;
    "select-from-library": string;
    "add-practice": string;
  };
  team: {
    open: string;
    "loading-teams": string;
    "failed-to-load-teams": string;
    "teams-list": string;
    "teams-list-navigation": string;
    "loading-team": string;
    "failed-to-load-team": string;
    "delete-team-title": string;
    "edit-team": string;
    "delete-team": string;
    "delete-team-confirmation": string;
    "teams-count": string;
    "add-team": string;
    "edit-team-title": string;
    "add-new-team": string;
    "back-to-teams": string;
    name: string;
    "age-description": string;
    "age-description-placeholder": string;
    gender: string;
    genders: {
      male: string;
      female: string;
      mixed: string;
    };
    "players-born-after": string;
    "players-born-before": string;
    slogan: string;
    "sync-with-financial-system": string;
    fee: string;
    currency: string;
    "fee-interval": string;
    "fee-intervals": {
      monthly: string;
      yearly: string;
    };
  };
  users: {
    "email-already-registered": string;
    permissions: string;
    roles: {
      owner: string;
    };
    send: string;
    invite: string;
    "invite-new-member": string;
    "delete-invite-confirmation": string;
    "invite-deleted": string;
    "failed-to-delete-invite": string;
    extend: string;
    "extend-invite-confirmation": string;
    "invite-extended": string;
    "failed-to-extend-invite": string;
    invitee: string;
    "invited-by": string;
    "invited-on": string;
    "expires-on": string;
    expired: string;
    "fetching-invites": string;
    "failed-to-load-invites": string;
    "no-unredeemed-invites": string;
    status: string;
    all: string;
    pending: string;
    me: string;
    "joined-on": string;
    "no-permissions": string;
    "remove-member": string;
    owner: string;
    "teams-count": string;
    "loading-users": string;
    "failed-to-load-users": string;
    "users-admin": string;
    "user-admin-tabs": string;
    users: string;
    invites: string;
    "permission-roles": string;
  };
  roles: {
    "add-role": string;
    "create-new-role": string;
    "create-role": string;
    "role-created": string;
    "delete-role-confirmation": string;
    "role-deleted": string;
    "failed-to-delete-role": string;
    "edit-role": string;
    "role-updated": string;
    "loading-roles": string;
    "failed-to-load-roles": string;
    "delete-role": string;
    name: string;
    description: string;
    "role-already-exists": string;
  };
}
