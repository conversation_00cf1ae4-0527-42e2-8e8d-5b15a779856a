import { TranslationSchema } from "../types";

export const translation: TranslationSchema = {
  auth: {
    login: "Login",
    register: "Register",
    "forgot-password": "Forgot Password?",
    "reset-password": "Reset Password",
    "join-organization": "Join Organization",
    "email-placeholder": "Email",
    "password-placeholder": "Password",
    "confirm-password-placeholder": "Confirm Password",
    "no-account": "Don't have an account?",
    "have-account": "Already have an account?",
    "sign-up": "Sign Up",
    "sign-in": "Sign In",
    "reset-instructions":
      "Enter your email address and we'll send you instructions to reset your password.",
    "invalid-credentials": "Invalid email or password",
    "registration-error": "Registration failed. Please try again.",
    "permission-denied": "You don't have permission to access this resource",
    "load-error": "Failed to load data",
    "welcome-to-team-assist": "Welcome to Team Assist",
    welcome: "Welcome",
    "team-assist": "Team Assist",
    "join-organization-title": "Join {{orgName}} organization",
    "invite-fetch-error": "Failed to fetch invite",
    "invalid-invite": "Invalid invite",
    "expired-invite": "Invite has expired or was already used",
    "generic-error": "Something went wrong",
    "accept-invite": "Accept invite",
    "registration-page": "Team Assist Registration page",
    "enter-email": "Enter your email",
    "back-to-login": "Back to login",
    "enter-new-password": "Enter your new password",
  },
  common: {
    submit: "Submit",
    cancel: "Cancel",
    save: "Save",
    delete: "Delete",
    edit: "Edit",
    create: "Create",
    back: "Back",
    open: "Open",
    close: "Close",
    loading: "Loading...",
    error: "Something went wrong",
    success: "Success!",
    confirm: "Confirm",
    actions: "Actions",
    continue: "Continue",
    "permission-denied": "Permission denied",
    confirmation: {
      cancel: "Cancel",
      confirm: "Confirm",
      error: "Error",
      "error-message": "Something went wrong. Please try again or contact support",
      success: "Success",
      "success-message": "Changes saved successfully",
    },
    updated: "updated",
    created: "created",
    name: "Name",
    "something-went-wrong": "Something went wrong",
    email: "Email",
    "please-confirm": "Please confirm",
    "not-available": "N/A",
  },
  teams: {
    title: "Teams",
    "add-team": "Add Team",
    "edit-team": "Edit Team",
    "delete-team": "Delete Team",
    "team-name": "Team Name",
    "age-group": "Age Group",
    season: "Season",
    players: "Players",
    coaches: "Coaches",
    "load-error": "Failed to load teams",
    loading: "Loading teams...",
    "select-team": "Team",
    card: {
      gender: "Gender",
      "age-description": "Age description",
      "born-before": "Players born before",
      "born-after": "Players born after",
    },
  },
  validation: {
    required: "This field is required",
    "invalid-email": "Invalid email address",
    "password-mismatch": "Passwords do not match",
    "min-length": "Must be at least {{length}} characters",
    "max-length": "Must be less than {{length}} characters",
  },
  "email-templates": {
    title: "Email templates",
    "add-template": "Add email template",
    "delete-confirm-title": "Delete email template?",
    "delete-warning": "This action cannot be undone.",
    loading: "Loading email templates...",
    "load-error": "Failed to load email templates.",
    "no-templates": "No email templates here yet.",
    subject: "Subject",
    "edit-template": "Edit template",
    "delete-template": "Delete template",
    "template-name": "Template name",
    visibility: "Visibility",
    "visibility-private": "Private",
    "visibility-company": "Club",
    "visibility-description":
      "'Private' will ensure that only you can view and edit this template. 'Club' will allow all members of the club to view end edit this template.",
    body: "Body",
    "create-template": "Create email template",
    "no-subject": "No subject",
  },
  events: {
    title: "Events Management",
    "create-single": "Create Single",
    "create-recurring": "Create Recurring",
    type: {
      single: "Single",
      recurring: "Recurring",
    },
    filters: {
      "start-date": "Start date",
      "end-date": "End date",
      show: "Show",
    },
    hosts: {
      title: "Hosts",
      "add-placeholder": "Add hosts...",
      you: "You",
    },
    invitations: {
      title: "Invited players",
      "load-error": "Failed to load team players.",
      loading: "Loading team players...",
      retry: "Retry",
    },
    form: {
      general: "General",
      name: "Name",
      agenda: "Agenda",
      description: "Description",
      "description-placeholder": "A training session...",
      "location-address": "Location address",
      "location-postcode": "Location postcode",
      save: "Save",
      error: "Something went wrong",
      "agenda-options": {
        training: "Training session",
        match: "Match",
        meeting: "Team meeting",
        other: "Other",
      },
      "start-date-time": "Start date and time",
      "end-date-time": "End date and time",
    },
    scheduling: {
      title: "Scheduling",
      "start-time": "Start time",
      "end-time": "End time",
      "start-from": "Start from",
      type: "Type",
      "day-of-week": "Day of week",
      ends: "Ends",
      "end-of-season": "End of Season",
      "custom-date": "Custom date",
      "end-date": "End date",
      types: {
        weekly: "Weekly",
        "bi-weekly": "Bi-Weekly",
        monthly: "Monthly",
      },
    },
    recurring: {
      "create-title": "Create new recurring event",
      "loading-players": "Loading team players...",
      "loading-season": "Loading current season...",
      "load-players-error": "Failed to load team players.",
      "load-season-error": "Failed to load current season.",
      "edit-title": "Edit event",
    },
    single: {
      "create-title": "Create new single event",
      "loading-players": "Loading team players...",
      "loading-season": "Loading current season...",
      "load-players-error": "Failed to load team players.",
      "load-season-error": "Failed to load current season.",
    },
  },
  financial: {
    integration: {
      "create-title": "Create integration form",
      name: "Name",
      type: "Integration type",
      "api-key": "API key",
      submit: "Submit",
      error: "Something went wrong",
      "already-exists": "Integration of that type already exists",
      types: {
        gocardless: "GoCardless",
        stripe: "Stripe",
      },
      item: {
        "menu-label": "Open integration item menu",
        edit: "Edit",
        delete: "Delete",
      },
      view: {
        "add-button": "+ Add integration",
        loading: "Loading integrations...",
        "load-error": "Failed to fetch integrations",
      },
      update: {
        title: "Update integration form",
        "hidden-key": "********",
      },
      delete: {
        title: "Delete integration?",
        error: "Couldn't delete the integration",
      },
      form: {
        "create-title": "Create a new Integration",
        "edit-title": "Edit {{name}} Integration",
      },
    },
  },
  sidebar: {
    menu: {
      "players-list": "Players list",
      "training-center": "Training center",
      "match-center": "Match center",
      stats: "Stats",
      "event-management": "Event management",
      "club-admin": "Club admin",
      "teams-admin": "Teams admin",
      "players-admin": "Players admin",
      "users-admin": "Users admin",
      finances: "Finances",
      settings: "Settings",
      general: "General",
      "financial-integration": "Financial integration",
      season: "Season",
      "email-templates": "Email Templates",
      "player-stats": "Player stats",
      "team-stats": "Team stats",
    },
    title: "Team Assist",
    "team-selector": {
      loading: "Loading teams...",
      "load-error": "Failed to load teams",
      "active-team": "Active team",
      "change-team": "Change the active team to {{name}}",
    },
    "user-menu": {
      "open-menu": "Open profile menu",
      logout: "Log out",
      language: {
        "switch-to-english": "Switch to English",
        "switch-to-spanish": "Cambiar a Español",
      },
    },
  },
  organization: {
    settings: {
      title: "Organization settings",
      general: {
        title: "General",
        name: "Name",
        "display-name": "Display name",
        slug: "Slug",
        "contact-email": "Contact email",
        "privacy-policy": "Privacy policy",
      },
      applications: {
        title: "Applications",
        "preview-form": "Preview form",
        "open-applications": "Open applications",
        headline: "Headline",
        description: "Description",
        "closed-message": "Applications closed message",
        "success-message": "Application success message",
      },
      save: "Save",
      "not-found": "Organization not found",
      update: {
        error: "Something went wrong",
        success: "Organization updated",
      },
    },
  },
  permissions: {
    denied: "You don't have permissions to view this resource.",
    "add-permission": "Add permission",
    role: "Role",
    "select-role": "Please select a Permission Role",
    "permission-exists": "Permission already exists.",
    "delete-permission-confirmation": "Delete permission?",
    deleting: "Deleting...",
    "permission-deleted": "Permission deleted.",
    "edit-teams": "Edit teams",
    "permissions-updated": "Permissions updated.",
    "delete-permission": "Delete permission",
  },
  players: {
    status: {
      change: "Change status",
      "change-title": "Change {{name}}'s status",
      "status-label": "Status:",
      registration: {
        "start-process": "Start registration process?",
        "start-process-description": "(request documents, photos and payment from player)",
      },
      error: "Something went wrong",
      cancel: "Cancel",
      save: "Save",
      "applicant-organization": "Organization Applicant",
      "applicant-team": "Team Applicant",
      "trialist-invited": "Invited Trialist",
      trialist: "Trialist",
      "trialist-unsuccessful": "Unsuccessful Trialist",
      "registration-pending": "Pending registration",
      "registered-matches": "Registered for matches",
      "registered-training": "Training only",
      relocated: "Relocated to another team",
      left: "Left the team",
      removed: "Removed from team",
      active: "Active",
      inactive: "Inactive",
      "removed-team": "Removed from Team",
      "removed-organization": "Removed from Organization",
    },
    filters: {
      status: {
        label: "Player status",
        none: "None",
      },
      "sort-by": {
        label: "Sort by",
        options: {
          "creation-date": "Date of application",
          names: "Player names",
          age: "Player age",
        },
      },
      search: "Search",
    },
    list: {
      loading: "Loading players...",
      "load-error": "Failed to load players.",
      "no-players": "There are no players in this team ({{name}}). Filters may impact this result.",
      "showing-count": "Showing {{count}} players.",
      "filters-note": "Filters may impact results.",
    },
    card: {
      born: "Born {{date}}",
      "years-old": "({{age}} years old)",
      details: "Details",
      documents: "Documents",
      "documents-count": "Documents ({{count}})",
    },
    admin: {
      filters: {
        "born-after": "Players born after",
        "born-before": "Players born before",
        "clear-date-after": "Clear 'Player borns after' date filter",
        "clear-date-before": "Clear 'Player borns before' date filter",
        status: {
          label: "Player status",
          none: "None",
        },
        team: {
          label: "Team",
          none: "None",
        },
        name: "Player name",
        "clear-name": "Clear name filter",
        "sort-by": {
          label: "Sort by",
          options: {
            "creation-date": "Date of application",
            names: "Player names",
            age: "Player age",
          },
        },
        search: "Search",
      },
      card: {
        gender: "Gender",
        born: "Born {{date}}",
        "years-old": "({{age}} years old)",
        "teams-count": "Part of {{count}} teams",
        details: "Details",
        documents: "Documents",
        "documents-count": "Documents ({{count}})",
      },
      list: {
        loading: "Loading players...",
        "load-error": "Failed to load players.",
        "no-players": "No players found.",
        "showing-count": "Showing {{count}} out of {{total}} players",
        "loading-more": "Fetching more players...",
        "load-more": "Load more",
      },
      email: {
        "send-to": "Send email to",
        targets: {
          players: "Players",
          guardians: "Guardians",
        },
      },
    },
    "assign-to-team": "Assign to team",
    "assign-player-to-team": "Assign {{name}} to a team",
    assign: "Assign",
    details: {
      title: "{{name}}'s details",
      contact: "Contact",
      guardian: "Guardian",
      teams: "Player teams",
      "no-teams": "No teams yet",
      "additional-info": "Additional information",
      address: "Address",
      postcode: "Postcode",
      phone: "Phone",
      email: "Email",
      "copy-email": "Copy email",
      "send-email": "Send email",
      name: "Name",
      born: "Born",
      "remove-from-team": "Remove from team",
      "remove-confirmation":
        "Are you sure you want to remove the player from the team? This action is irreversible",
      "playing-experience": "Playing experience",
      "medical-conditions": "Medical conditions",
      none: "None",
      "preferred-position": "Preferred playing position",
      "not-available": "N/A",
      close: "Close",
      "remove-from-organization": "Remove from organization",
      "remove-from-organization-confirmation":
        "Are you sure you want to remove the player from the organization? This action is irreversible",
      "remove-from-organization-dialog-title": "Remove player from organization?",
      "remove-from-team-dialog-title": "Remove player from team?",
    },
    documents: {
      photo: "Photo",
      "photo-alt": "Player photo",
      "id-card": "ID card / Passport",
      "view-document": "View document",
    },
    profile: {
      title: "{{name}}'s details",
      tabs: {
        profile: "Profile",
        stats: "Stats",
      },
      strengths: "Strengths:",
      weaknesses: "Weaknesses:",
      improvements: "Has shown improvements in:",
      "recent-sessions": "Recent sessions:",
      stats: {
        goals: "Goals",
        assists: "Assists",
        position: "Position",
        rating: "Avg rating",
      },
      close: "Close",
    },
    attendance: {
      attended: "Attended",
      late: "Late",
      "missing-with-reason": "D.N.A (with reason)",
      "missing-no-reason": "D.N.A (without reason)",
    },
    performance: {
      abysmal: "Abysmal",
      bad: "Bad",
      neutral: "Neutral",
      good: "Good",
      outstanding: "Outstanding",
    },
  },
  seasons: {
    loading: "Loading season",
    "load-error": "Failed to load season",
    objective: "Objective",
    "start-date": "Start date",
    "end-date": "End date",
    "edit-season": "Edit season",
    "loading-seasons": "Loading seasons...",
    count: "{{count}} Seasons",
    "add-season": "Add season",
    "list-navigation": "Seasons list navigation",
    "edit-season-title": 'Edit "{{name}}" season',
    "add-new-season": "Add new season",
    "back-to-seasons": "Back to seasons",
    name: "Name",
    "objective-placeholder": "Win all leagues...",
    "start-date-label": "Season starts on:",
    "end-date-label": "Season ends on:",
  },
  email: {
    send: {
      title: "Send email",
      subject: "Subject",
      template: {
        label: "Email template",
        none: "None",
        loading: "Loading email templates...",
        "load-error": "Failed to load email templates.",
      },
      success: "Email sent",
      error: "Sending email failed",
      close: "Close",
      send: "Send",
    },
  },
  matches: {
    edit: {
      loading: "Loading match",
      "load-error": "Failed to load match",
    },
    card: {
      description: "Description",
      competition: "Competition",
      date: "Date",
      league: "League",
      cup: "Cup",
      friendly: "Friendly",
    },
    actions: {
      "add-review": "Add review",
      "edit-match": "Edit match",
    },
    list: {
      loading: "Loading matches...",
      "load-error": "Failed to load matches",
      "permission-denied": "Permission denied",
      title: "{{count}} football matches",
      "add-new": "Add new match plan",
    },
    form: {
      "edit-title": "Edit {{name}} plan",
      "create-title": "Add new football match plan",
      "back-to-match-center": "Back to match center",
      fields: {
        name: "Name",
        "opposition-name": "Opposition Name",
        host: {
          label: "Hosted by",
          home: "Home",
          away: "Away",
          neutral: "Neutral",
        },
        description: "Description",
        "start-date": "Start date and time",
        competition: {
          label: "Competition",
          league: "League",
          cup: "Cup",
          friendly: "Friendly",
        },
        "learning-outcomes": "Learning outcomes",
        "players-engagement": "Players engagement",
        "coach-behaviours": "Coach behaviours",
        "safety-considerations": "Safety considerations",
        "arrival-and-warm-up": "Arrival and warm up",
        "game-week": "Game week",
        "league-position": "League position",
        "league-position-opponent": "League position of opponent",
        opponent: "opponent",
      },
      save: "Save",
      error: "Something went wrong",
      "match-already-exists-in-game-week-error": "Match already exists in game week",
    },
    review: {
      loading: "Loading review and players...",
      "load-error": "Failed to load review",
      "permission-denied": "Permission denied",
      tabs: {
        overview: "Overview",
        goals: "Goals",
        players: "Players",
        attempts: "Attempts",
      },
      goals: {
        scored: {
          title: "Goals scored",
          type: {
            normal: "Normal",
            "own-goal": "Own goal",
          },
          fields: {
            minute: "Minute",
            "added-time": "Added time",
            half: {
              label: "Half",
              first: "First",
              second: "Second",
              "extra-time-first": "Extra time first",
              "extra-time-second": "Extra time second",
              penalties: "After match penalties",
            },
            situation: {
              label: "Situation",
              "open-play": "Open play",
              "set-piece": "Set piece",
              detailed: "Situation detailed",
              types: {
                corner: "Corner",
                "direct-free-kick": "Direct free kick",
                "goal-kick": "Goal kick",
                "indirect-free-kick": "Indirect free kick",
                penalty: "Penalty",
                "throw-in": "Throw in",
              },
            },
            scorer: "Goal scorer",
            zone: "Zone",
            "body-part": {
              label: "Body part",
              "right-foot": "Right foot",
              "left-foot": "Left foot",
              head: "Head",
              chest: "Chest",
              knee: "Knee",
              shoulder: "Shoulder",
              hip: "Hip",
              back: "Back",
              hand: "Hand",
              other: "Other",
            },
            assist: "Assist",
            "assist-zone": "Assist zone",
            "key-pass": "Key pass",
            "key-pass-zone": "Key pass zone",
          },
          save: "Save",
          error: "Something went wrong",
          success: "Review updated successfully",
          updated: "Review updated",
          created: "Review created",
        },
        conceded: {
          title: "Goals conceded",
          "error-leading-to-goal": "Error leading to a goal",
        },
      },
      match: {
        "back-to-match-center": "Back to match center",
        title: "Review",
        fields: {
          "opposition-name": "Opposition name",
          host: {
            label: "Hosted by",
            home: "Home",
            away: "Away",
            neutral: "Neutral",
          },
          "goals-scored": "Goals scored",
          "goals-conceded": "Goals conceded",
          outcome: {
            label: "Outcome",
            win: "Win",
            loss: "Loss",
            draw: "Draw",
          },
          rating: "Match rating",
          weather: {
            label: "Weather conditions",
            sunny: "Sunny",
            rain: "Rain",
            wet: "Wet",
            overcast: "Overcast",
            snow: "Snow",
            fog: "Fog",
          },
        },
        save: "Save",
        error: "Something went wrong",
        success: "Review updated successfully",
        updated: "Review updated",
        created: "Review created",
      },
      players: {
        review: {
          title: "Review",
          addReview: "Add player review",
          editReview: "Edit player review",
          rating: {
            decrease: "Decrease player rating",
            increase: "Increase player rating",
          },
          minutesPlayed: {
            decrease: "Decrease player minutes played",
            increase: "Increase player minutes played",
          },
        },
        attendance: "Attendance",
        "attendance-types": {
          attended: "Attended",
          late: "Late",
          "missing-with-reason": "Missing with reason",
          "missing-no-reason": "Missing without reason",
        },
        "player-attributes": {
          dribbling: "Dribbling",
          technique: "Technique",
          shooting: "Shooting",
          crossing: "Crossing",
          finishing: "Finishing",
          heading: "Heading",
          passing: "Passing",
          "first-touch": "First Touch",
          tackling: "Tackling",
          marking: "Marking",
          cover: "Cover",
          "defensive-balance": "Defensive Balance",
          "recovery-runs": "Recovery Runs",
          delaying: "Delaying",
          "throw-ins": "Throw-ins",
          "penalty-taking": "Penalty Taking",
          "free-kick-taking": "Free Kick Taking",
          "corner-taking": "Corner Taking",
          aggression: "Aggression",
          anticipation: "Anticipation",
          bravery: "Bravery",
          composure: "Composure",
          concentration: "Concentration",
          decisions: "Decisions",
          determination: "Determination",
          flair: "Flair",
          leadership: "Leadership",
          "off-the-ball": "Off the Ball",
          positioning: "Positioning",
          "team-work": "Team Work",
          vision: "Vision",
          "work-rate": "Work Rate",
          scanning: "Scanning",
          movement: "Movement",
          deception: "Deception",
          timing: "Timing",
          acceleration: "Acceleration",
          agility: "Agility",
          balance: "Balance",
          "jumping-reach": "Jumping Reach",
          "natural-fitness": "Natural Fitness",
          pace: "Pace",
          stamina: "Stamina",
          strength: "Strength",
          temperament: "Temperament",
          adaptability: "Adaptability",
          controversy: "Controversy",
          consistency: "Consistency",
          "important-matches": "Important Matches",
          ambition: "Ambition",
          "injury-proneness": "Injury Proneness",
          loyalty: "Loyalty",
          dirtiness: "Dirtiness",
          pressure: "Pressure",
          professionalism: "Professionalism",
          sportsmanship: "Sportsmanship",
          versatility: "Versatility",
          "aerial-reach": "Aerial Reach",
          "command-of-area": "Command of Area",
          communication: "Communication",
          eccentricity: "Eccentricity",
          handling: "Handling",
          kicking: "Kicking",
          "one-on-ones": "One on Ones",
          "tendency-to-punch": "Tendency to Punch",
          reflexes: "Reflexes",
          "tendency-to-rush-out": "Tendency to Rush Out",
          throwing: "Throwing",
        },
        "tactical-positions": {
          goalkeeper: "Goalkeeper",
          "right-back": "Right Back",
          "right-centre-back": "Right Centre Back",
          "centre-back": "Centre Back",
          "left-back": "Left Back",
          "left-centre-back": "Left Centre Back",
          "right-wing-back": "Right Wing Back",
          "left-wing-back": "Left Wing Back",
          "central-defensive-midfielder": "Central Defensive Midfielder",
          "defensive-midfielder": "Defensive Midfielder",
          "central-midfielder": "Central Midfielder",
          "central-attacking-midfielder": "Central Attacking Midfielder",
          "left-midfielder": "Left Midfielder",
          "right-midfielder": "Right Midfielder",
          "right-winger": "Right Winger",
          "left-winger": "Left Winger",
          striker: "Striker",
        },
        mainPosition: "Main Position",
        minutesPlayed: "Minutes Played",
        highlights: "Highlights",
        "back-to-match-center": "Back to match center",
        title: "Players reviews",
        save: "Save",
        error: "Something went wrong",
        success: "Review created!",
      },
    },
  },
  match: {
    minute: "Minute",
    "added-time": "Added time",
    half: "Half",
    "first-half": "First",
    "second-half": "Second",
    "extra-time-first": "Extra time first",
    "extra-time-second": "Extra time second",
    penalties: "After match penalties",
    situation: "Situation",
    "open-play": "Open play",
    "set-piece": "Set piece",
    "error-leading-to-goal": "Error leading to goal",
    pressing: "Pressing",
    "review-updated": "Review updated successfully",
    "goal-type": "Type",
    "goal-type-normal": "Normal",
    "goal-type-own": "Own goal",
    "goal-scorer": "Goal scorer",
    zone: "Zone",
    "body-part": "Body part",
    "review-status": "Review {{action}}",
    "body-parts": {
      "right-foot": "Right foot",
      "left-foot": "Left foot",
      head: "Head",
      chest: "Chest",
      knee: "Knee",
      shoulder: "Shoulder",
      hip: "Hip",
      back: "Back",
      hand: "Hand",
      other: "Other",
    },
    assist: "Assist",
    save: "Save",
    error: "Something went wrong",
    success: "¡Review created!",
    updated: "Review updated",
    created: "Review created",
    "situation-detailed": "Situation detailed",
    "situation-detailed-options": {
      "error-leading-to-a-goal": "Error leading to a goal",
      pressing: "Pressing",
      "open-play": "Open play",
      "set-piece": "Set piece",
    },
    "set-piece-sub-type": {
      corner: "Corner",
      "direct-free-kick": "Direct free kick",
      "goal-kick": "Goal kick",
      "indirect-free-kick": "Indirect free kick",
      penalty: "Penalty",
      "throw-in": "Throw in",
    },
    "open-play-sub-type": {
      "counter-attack": "Counter attack",
      "combination-play": "Combination play",
      cross: "Cross",
      dribble: "Dribble",
      "long-shot": "Long shot",
      "one-to-one": "One to one",
      rebound: "Rebound",
      "through-ball": "Through ball",
    },
    halves: {
      first: "First",
      second: "Second",
      "extra-time-first": "Extra time first",
      "extra-time-second": "Extra time second",
      penalties: "After match penalties",
    },
    review: "Review",
    "key-pass": "Key pass",
    "key-pass-zone": "Key pass zone",
    "assist-zone": "Assist zone",
    "assist-body-part": "Assist body part",
    "through-ball": "Through ball",
    "early-goals": "Early goals",
    "late-goals": "Late goals",
    "mid-match-goals": "Mid-match goals",
    "time-details": "Time details",
    "goal-details": "Goal details",
    "key-pass-details": "Key pass details",
    "situation-details": "Situation details",
    "assist-details": "Assist details",
    type: "Type",
    "type-normal": "Normal",
    "type-own": "Own goal",
    "goal-scored": "Goal scored",
    "goal-conceded": "Goal conceded",
    "attempt-type": "Attempt type",
    "attempt-type-options": {
      "shot-on-goal": "Shot on goal",
      "shot-off-goal": "Shot off goal",
      "shot-blocked": "Shot blocked",
      goal: "Goal",
    },
    "add-attempt": "Add attempt",
    "add-attempt-against": "Add attempt against",
    attempt: "Attempt",
    "attempt-details": "Attempt Details",
    shooter: "Shooter",
    attempts: "Attempts",
    "attempts-against": "Attempts against",
    "delete-attempt-confirmation": "Are you sure you want to delete this attempt?",
    "delete-attempt-confirmation-title": "Delete attempt",
  },
  stats: {
    "first-name": "First name",
    "last-name": "Last name",
    appearances: "Appearances",
    "assists-per-match": "Assists per Match",
    assists: "Assists",
    loading: "Loading stats...",
    "load-error": "Failed to load stats",
    "goals-per-match": "Goals per Match",
    "goals-received-per-match": "Goals received per match",
    goals: "Goals",
    "goals-per-half": "Goals per half",
    "goal-times": "Goal times",
    "goals-came-from": "Goals came from",
    "open-play-goals": "Open play goals",
    "set-piece-goals": "Set piece goals",
    "goals-scored-with": "Goals scored with",
    "goals-by-zone": "Goals by zone",
    "goal-types": {
      first: "First half",
      second: "Second half",
      "extra-time-first": "First extra time",
      "extra-time-second": "Second extra time",
      "after-match-penalties": "Penalties",
      "0-15": "0-15 min",
      "16-30": "16-30 min",
      "31-45": "31-45 min",
      "46-60": "46-60 min",
      "61-75": "61-75 min",
      "76-90": "76-90 min",
      "91-105": "91-105 min",
      "106-120": "106-120 min",
      "open-play": "Open play",
      "set-piece": "Set piece",
      "error-leading-to-a-goal": "Error leading to goal",
      pressing: "Pressing",
      "counter-attack": "Counter attack",
      "combined-play": "Combined play",
      "individual-play": "Individual play",
      "corner-kick": "Corner kick",
      "free-kick": "Free kick",
      penalty: "Penalty",
      "throw-in": "Throw in",
      "right-foot": "Right foot",
      "left-foot": "Left foot",
      head: "Head",
      chest: "Chest",
      knee: "Knee",
      shoulder: "Shoulder",
      hip: "Hip",
      back: "Back",
      hand: "Hand",
      other: "Other",
      "direct-free-kick": "Direct free kick",
      "goal-kick": "Goal kick",
      "indirect-free-kick": "Indirect free kick",
      "through-ball": "Through ball",
      "combination-play": "Combination play",
      "early-goals": "Early goals",
      "late-goals": "Late goals",
      "mid-match-goals": "Mid-match goals",
    },
    "goals-received-per-half": "Goals received per half",
    "goals-received-came-from": "Goals received came from",
    "goals-received-scored-with": "Goals received scored with",
    "goals-received-by-zone": "Goals received by zone",
    "goals-received-times": "Goals received times",
    "goals-set-piece-received": "Goals set piece goals received",
    "key-passes-per-match": "Key Passes per Match",
    "key-passes": "Key Passes",
    "average-rating": "Average Rating",
    "tabs-label": "Stats tabs",
    "player-performance": "Player Performance",
    match: "Match",
    "goals-scored": "Goals scored",
    "goals-conceded": "Goals conceded",
    "game-week": "Game week",
    "game-week-abbreviation": "GW",
  },
  training: {
    attendance: "Attendance",
    rating: "Rating",
    highlights: "Highlights",
    "attendance-types": {
      attended: "Attended",
      late: "Late",
      "missing-with-reason": "Missing with reason",
      "missing-no-reason": "Missing without reason",
    },
    ratings: {
      abysmal: "Abysmal",
      bad: "Bad",
      neutral: "Neutral",
      good: "Good",
      outstanding: "Outstanding",
    },
    attributes: {
      acceleration: "Acceleration",
      adaptability: "Adaptability",
      aggression: "Aggression",
      agility: "Agility",
      ambition: "Ambition",
      anticipation: "Anticipation",
    },
    "learning-outcomes": "Learning Outcome(s)",
    "players-engagement": "Players Engagement",
    "coach-behaviours": "Coach Behaviours",
    "safety-considerations": "Safety Considerations",
    "arrival-and-warm-up": "Arrival and Warmup",
    summary: "Summary",
    details: "Details",
    "expectations-achieved-rating": "Expectations Achieved Rating",
    "review-player": "Review {{name}}",
    "add-new-review": "Add new review",
    "review-created": "Review created!",
    "review-count": "{{count}} reviews",
    performance: "Performance",
    "no-reviews-for-period": "No reviews for the selected period.",
    reviews: "Reviews",
    players: "Players",
    "average-rating": "Average rating",
    "average-attendance": "Average attendance",
    "chart-view": "Chart view",
    "table-view": "Table view",
    insights: {
      "goals-first-half": "You have scored {{percent}}% of your goals in the first half.",
      "defensive-improvement":
        'You have worked on "Defensive principles of play" in the last {{weeks}} weeks and have conceded {{percent}}% fewer goals compared to the previous period working on other topics.',
      "player-combination":
        "{{percent}}% of your goals have come from combinations between {{player1}} and {{player2}}.",
    },
    filters: {
      "next-30-days": "Next 30 days",
      "all-next": "All next",
      "past-30-days": "Past 30 days",
      "all-past": "All past",
    },
    "show-insights": "Show Insights",
    "show-reviews": "Show Reviews",
    "loading-events": "Loading events...",
    "failed-to-load-events": "Failed to load events.",
    "no-trainings-yet": "No trainings yet.",
    "player-not-found": "Player not found.",
    "edit-review": "Edit Review",
    "add-review": "Add Review",
    plan: "Plan",
    "review-details": "Review Details",
    "back-to-training-center": "Back to Training Center",
    "session-review": "Session review",
    "review-tabs-label": "Training Center review tabs",
    "player-reviews": "Player Reviews",
    "training-center": "Training Center",
    "create-new": "Create new",
    "upload-new": "Upload new",
    "select-from-library": "Select from library",
    "add-practice": "Add practice",
  },
  team: {
    open: "Open",
    "loading-teams": "Loading teams...",
    "failed-to-load-teams": "Failed to load teams.",
    "teams-list": "Teams list",
    "teams-list-navigation": "Teams list navigation",
    "loading-team": "Loading team",
    "failed-to-load-team": "Failed to load team",
    "delete-team-title": 'Delete "{{name}}" team',
    "edit-team": "Edit team",
    "delete-team": "Delete team",
    "delete-team-confirmation":
      "All players data for this team will be lost and the action is irreversible. Are you sure you want to delete this team?",
    "teams-count": "{{count}} Teams",
    "add-team": "Add team",
    "edit-team-title": "Edit {{name}} team",
    "add-new-team": "Add new team",
    "back-to-teams": "Back to teams",
    name: "Name",
    "age-description": "Age Description",
    "age-description-placeholder": "Under 15 Boys",
    gender: "Gender",
    genders: {
      male: "Male",
      female: "Female",
      mixed: "Mixed",
    },
    "players-born-after": "Players must be born after",
    "players-born-before": "Players must be born before",
    slogan: "Team slogan",
    "sync-with-financial-system": "Sync with financial system",
    fee: "Fee",
    currency: "Currency",
    "fee-interval": "Fee interval",
    "fee-intervals": {
      monthly: "Monthly",
      yearly: "Yearly",
    },
  },
  users: {
    "email-already-registered": "User with that email has already registered",
    permissions: "Permissions",
    roles: {
      owner: "Owner",
    },
    send: "Send",
    invite: "Invite",
    "invite-new-member": "Invite new member",
    "delete-invite-confirmation": "Delete the invite for {{email}}?",
    "invite-deleted": "Invite deleted.",
    "failed-to-delete-invite": "Couldn't delete the invite.",
    extend: "Extend",
    "extend-invite-confirmation": "Extend invite for {{email}}?",
    "invite-extended": "Invite extended.",
    "failed-to-extend-invite": "Couldn't extend the invite.",
    invitee: "Invitee",
    "invited-by": "Invited by",
    "invited-on": "Invited on",
    "expires-on": "Expires on",
    expired: "Expired",
    "fetching-invites": "Fetching invites...",
    "failed-to-load-invites": "Failed to load invites.",
    "no-unredeemed-invites": "No unredeemed invites.",
    status: "Status",
    all: "All",
    pending: "Pending",
    me: "(me)",
    "joined-on": "Joined on",
    "no-permissions": "No permissions.",
    "remove-member": "Remove member",
    owner: "Owner",
    "teams-count": "{{count}} teams",
    "loading-users": "Loading users...",
    "failed-to-load-users": "Failed to load users.",
    "users-admin": "Users admin",
    "user-admin-tabs": "User admin tabs",
    users: "Users",
    invites: "Invites",
    "permission-roles": "Permission roles",
  },
  roles: {
    "add-role": "Add role",
    "create-new-role": "Create new role",
    "create-role": "Create role",
    "role-created": "Role created.",
    "delete-role-confirmation": "Delete {{name}} role?",
    "role-deleted": "Role deleted.",
    "failed-to-delete-role": "Failed to delete role.",
    "edit-role": "Edit {{name}} role",
    "role-updated": "Role updated.",
    "loading-roles": "Loading roles...",
    "failed-to-load-roles": "Failed to load roles.",
    "delete-role": "Delete {{name}} role",
    name: "Name",
    description: "Description",
    "role-already-exists": "Role with the same name already exists.",
  },
};

export default translation;
