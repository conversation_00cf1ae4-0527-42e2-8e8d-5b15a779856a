import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";
import { TranslationSchema } from "./locales/types";

import en from "./locales/en/translation";
import es from "./locales/es/translation";

declare module "i18next" {
  interface CustomTypeOptions {
    resources: {
      translation: TranslationSchema;
    };
  }
}

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources: {
      en: { translation: en },
      es: { translation: es },
    },
    fallbackLng: "en",
    detection: {
      order: ["backend", "localStorage", "navigator"],
      caches: ["localStorage"],
    },
    load: "languageOnly",
    interpolation: {
      escapeValue: false,
    },
  });

export default i18n;
