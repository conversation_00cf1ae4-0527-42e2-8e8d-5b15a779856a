import { FC } from "react";
import { startCase } from "lodash/fp";

import { Box, Typography, Card, Stack, Chip } from "@mio/ui";
import { CustomDate, GCSubscription } from "@mio/helpers";

type Props = {
  gcSubscription: GCSubscription;
};

export const GCSubscriptionItem: FC<Props> = ({ gcSubscription }) => {
  return (
    <Card sx={{ p: 2 }}>
      <Stack direction="row" justifyContent="space-between" flexWrap="wrap">
        <Typography component="h2" variant="h5" fontWeight="bold">
          {gcSubscription.name}
        </Typography>

        <Stack alignItems="center" gap={2}>
          <Chip label={gcSubscription.currency} />
          <Box>{gcSubscription.amount / 100}</Box>
        </Stack>

        <Stack alignItems="center" gap={2}>
          <Chip label="Start date" />
          <Box>{CustomDate.toDisplayDate(gcSubscription.start_date)}</Box>
        </Stack>

        <Stack alignItems="center" gap={2}>
          <Chip label="End date" />
          <Box>{CustomDate.toDisplayDate(gcSubscription.end_date)}</Box>
        </Stack>

        <Stack alignItems="center" gap={2}>
          <Chip label="Interval" />
          <Box>{startCase(gcSubscription.interval_unit || "")}</Box>
        </Stack>

        <Stack alignItems="center" gap={2}>
          <Chip label="Status" />
          <Box>{startCase(gcSubscription.status)}</Box>
        </Stack>
      </Stack>
    </Card>
  );
};
