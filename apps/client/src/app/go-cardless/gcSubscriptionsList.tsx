import { FC } from "react";

import {
  <PERSON>,
  Stack,
  <PERSON>po<PERSON>,
  PageError,
  PageLoader,
  useOrganization,
  useGetGCSubscriptions,
} from "@mio/ui";
import { ErrorMessages } from "@mio/helpers";

import { SidebarLayout } from "../layouts";
import { GCSubscriptionItem } from "./gcSubscriptionItem";

export const GCSubscriptionsList: FC = () => {
  const organization = useOrganization();

  const gcSubscriptions = useGetGCSubscriptions(organization.id).query;

  if (gcSubscriptions.status === "loading") {
    return (
      <SidebarLayout>
        <PageLoader message="Loading subscriptions..." />
      </SidebarLayout>
    );
  }

  if (gcSubscriptions.status === "error") {
    return (
      <SidebarLayout>
        <PageError
          message={
            gcSubscriptions.error.message === ErrorMessages.PermissionDenied
              ? "Permission denied"
              : "Failed to load subscriptions"
          }
        />
      </SidebarLayout>
    );
  }

  if (gcSubscriptions.status === "success") {
    return (
      <SidebarLayout>
        <Box>
          <Stack
            component="header"
            flexDirection="row"
            justifyContent="space-between"
            mb={5}
            gap={2}
            flexWrap="wrap"
          >
            <Box>
              <Typography variant="h4" component="h1">
                {gcSubscriptions.data.length} Subscriptions
              </Typography>
            </Box>
          </Stack>

          <Stack gap={4}>
            {gcSubscriptions.data.map((item) => (
              <GCSubscriptionItem key={item.id} gcSubscription={item} />
            ))}
          </Stack>
        </Box>
      </SidebarLayout>
    );
  }

  return null;
};
