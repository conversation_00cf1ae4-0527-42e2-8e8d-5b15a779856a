import { useTranslation } from "react-i18next";
import { Attendance, PlayerPerformanceBasicRatings } from "@mio/helpers";

export const usePlayerReviews = () => {
  const { t } = useTranslation();

  const toAttendanceLabel = {
    [Attendance.Attended]: t("players.attendance.attended"),
    [Attendance.Late]: t("players.attendance.late"),
    [Attendance.MissingWithReason]: t("players.attendance.missing-with-reason"),
    [Attendance.MissingNoReason]: t("players.attendance.missing-no-reason"),
  } as const;

  const toPerformanceLabel = {
    [PlayerPerformanceBasicRatings.Abysmal]: t("players.performance.abysmal"),
    [PlayerPerformanceBasicRatings.Bad]: t("players.performance.bad"),
    [PlayerPerformanceBasicRatings.Neutral]: t("players.performance.neutral"),
    [PlayerPerformanceBasicRatings.Good]: t("players.performance.good"),
    [PlayerPerformanceBasicRatings.Outstanding]: t("players.performance.outstanding"),
  } as const;

  const toDisplayTrainingSessionAttendanceColor = {
    [Attendance.Attended]: "success" as const,
    [Attendance.Late]: "error" as const,
    [Attendance.MissingNoReason]: "error" as const,
    [Attendance.MissingWithReason]: "warning" as const,
  } as const;

  const toDisplayPlayerPerformanceBasicRatingsColor = {
    [PlayerPerformanceBasicRatings.Abysmal]: "error",
    [PlayerPerformanceBasicRatings.Bad]: "error",
    [PlayerPerformanceBasicRatings.Neutral]: undefined,
    [PlayerPerformanceBasicRatings.Good]: "success",
    [PlayerPerformanceBasicRatings.Outstanding]: "success",
  } as const;

  return {
    toAttendanceLabel,
    toPerformanceLabel,
    toDisplayTrainingSessionAttendanceColor,
    toDisplayPlayerPerformanceBasicRatingsColor,
  };
};
