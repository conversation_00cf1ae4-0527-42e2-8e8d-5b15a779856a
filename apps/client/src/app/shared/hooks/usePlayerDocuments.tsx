import { useState } from "react";
import { useTranslation } from "react-i18next";

import { PopulatedPlayer } from "@mio/helpers";
import {
  Stack,
  Card,
  Typography,
  AccountCircleIcon,
  UploadedImage,
  FolderSharedIcon,
  Modal,
} from "@mio/ui";

export const usePlayerDocuments = () => {
  const { t } = useTranslation();
  const [player, setPlayer] = useState<PopulatedPlayer | undefined>();

  const DocumentsUI = (
    <Modal
      fullWidth
      onClose={() => setPlayer(undefined)}
      open={!!player}
      content={
        <Stack direction="row" gap={3} flexWrap="wrap">
          <Card
            sx={{
              p: 2,
              display: "flex",
              flexDirection: "column",
              gap: 3,
              maxWidth: { xs: "100%", md: "250px" },
            }}
          >
            <Stack direction="row" gap={2} alignItems="center">
              <AccountCircleIcon fontSize="large" />
              <Typography variant="h6" component="h2">
                {t("players.documents.photo")}
              </Typography>
            </Stack>

            {player?.photo && (
              <UploadedImage
                image={player.photo}
                altText={t("players.documents.photo-alt")}
                imageAction={
                  <a href={player.photo.url} target="_blank" rel="noreferrer">
                    {player.photo.name}
                  </a>
                }
              />
            )}
          </Card>

          {player?.document_images?.map((image) => (
            <Card
              key={image.id}
              sx={{
                p: 2,
                display: "flex",
                flexDirection: "column",
                gap: 3,
                maxWidth: { xs: "100%", md: "250px" },
              }}
            >
              <Stack direction="row" gap={2} alignItems="center">
                <FolderSharedIcon fontSize="large" />
                <Typography variant="h6" component="h2">
                  {t("players.documents.id-card")}
                </Typography>
              </Stack>

              <UploadedImage
                image={image}
                altText={t("players.documents.photo-alt")}
                imageAction={
                  <a href={image.url} target="_blank" rel="noreferrer">
                    {image.name}
                  </a>
                }
              />
            </Card>
          ))}
        </Stack>
      }
    />
  );

  return { DocumentsUI, show: setPlayer };
};
