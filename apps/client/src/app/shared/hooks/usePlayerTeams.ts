import { keyBy } from "lodash/fp";

import { PlayerTeamStatus, PopulatedPlayer, AssignedPlayerTeamProfile, Team } from "@mio/helpers";
import { useOrganization, useGetAdminTeams } from "@mio/ui";

export const usePlayerTeams = (
  player: PopulatedPlayer,
): Array<{ team: Team; profile: AssignedPlayerTeamProfile }> => {
  const organization = useOrganization();
  const teamsQuery = useGetAdminTeams(organization.id);

  const profilesWithTeams = player.profiles.filter(
    (profile) => profile.status !== PlayerTeamStatus.ApplicantOrganization,
  ) as AssignedPlayerTeamProfile[];

  const playerTeamIds = profilesWithTeams.map((profile) => profile.teamId);

  const playerProfilesPerTeamId = keyBy("teamId", profilesWithTeams);

  const teams = teamsQuery.data
    ? teamsQuery.data.filter((team) => playerTeamIds.includes(team.id))
    : [];

  return teams.map((team) => ({ team, profile: playerProfilesPerTeamId[team.id] }));
};
