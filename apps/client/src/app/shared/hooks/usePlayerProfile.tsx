import { FC, SyntheticEvent, useState } from "react";
import { useTranslation } from "react-i18next";

import {
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Stack,
  Chip,
  Typography,
  useTheme,
  useMediaQuery,
  Avatar,
  TabContext,
  TabList,
  Tab,
  TabPanel,
} from "@mio/ui";
import { PopulatedPlayer, Player } from "@mio/helpers";

const PlayerProfile: FC<{
  player: any;
}> = ({ player }) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<"Profile" | "Details">("Profile");

  const handleChange = (_: SyntheticEvent, newValue: "Profile" | "Details") => {
    setActiveTab(newValue);
  };

  return (
    <Stack gap={3} sx={{ height: "80vh" }}>
      <Box>
        <Avatar alt="<PERSON> Crouch" src="/assets/PeterCrouch.jpg" sx={{ width: 100, height: 100 }} />
      </Box>

      <Box>
        <TabContext value={activeTab}>
          <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
            <TabList
              onChange={handleChange}
              aria-label="Training Center review tabs"
              variant="fullWidth"
              centered
            >
              <Tab label={t("players.profile.tabs.profile")} value="Profile" />
              <Tab label={t("players.profile.tabs.stats")} value="Stats" />
            </TabList>
          </Box>
          <TabPanel value="Profile">
            <Stack gap={2} flexWrap="wrap">
              <Box>
                <Typography>{t("players.profile.strengths")}</Typography>
                <Stack direction="row" gap={2} mt={2}>
                  {player.strengths.map((strength: any) => (
                    <Chip key={strength} label={strength} sx={{ mb: 2 }} color="success" />
                  ))}
                </Stack>
              </Box>

              <Box>
                <Typography>{t("players.profile.weaknesses")}</Typography>
                <Stack direction="row" gap={2} mt={2}>
                  {player.weaknesses.map((weakness: any) => (
                    <Chip key={weakness} label={weakness} sx={{ mb: 2 }} color="error" />
                  ))}
                </Stack>
              </Box>

              <Box>
                <Typography>{t("players.profile.improvements")}</Typography>
                <Stack direction="row" gap={2} mt={2}>
                  {player.improvedOn.map((item: any) => (
                    <Chip key={item} label={item} sx={{ mb: 2 }} color="info" />
                  ))}
                </Stack>
              </Box>

              <Box>
                <Typography>{t("players.profile.recent-sessions")}</Typography>
                <Stack direction="row" gap={2} mt={2}>
                  {player.lastTwoWeeks.map((item: any) => (
                    <Chip key={item} label={item} sx={{ mb: 2 }} color="primary" />
                  ))}
                </Stack>
              </Box>
            </Stack>
          </TabPanel>
          <TabPanel value="Stats">
            <Stack gap={4} direction="row" flexWrap="wrap">
              <Stack alignItems="center">
                <Typography>{t("players.profile.stats.goals")}</Typography>
                <Chip label="15" sx={{ mb: 2 }} color="primary" />
              </Stack>
              <Stack alignItems="center">
                <Typography>{t("players.profile.stats.assists")}</Typography>
                <Chip label="9" sx={{ mb: 2 }} color="info" />
              </Stack>
              <Stack alignItems="center">
                <Typography>{t("players.profile.stats.position")}</Typography>
                <Chip label="ST" sx={{ mb: 2 }} color="default" />
              </Stack>
              <Stack alignItems="center">
                <Typography>{t("players.profile.stats.rating")}</Typography>
                <Chip label="8.2" sx={{ mb: 2 }} color="secondary" />
              </Stack>
            </Stack>
          </TabPanel>
        </TabContext>
      </Box>
    </Stack>
  );
};

type Props = {
  player: PopulatedPlayer;
  onClose: () => void;
};

const PlayerProfileModal: FC<Props> = ({ player, onClose }) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const isOnSmallScreen = useMediaQuery(theme.breakpoints.down("md"));

  return (
    <Dialog open={!!player} fullWidth={!isOnSmallScreen} fullScreen={isOnSmallScreen}>
      <DialogTitle id="confirmation-dialog-title" sx={{ mb: 2 }}>
        {t("players.profile.title", { name: Player.getFullName(player) })}
      </DialogTitle>

      <DialogContent>
        <PlayerProfile player={player} />
      </DialogContent>
      <DialogActions>
        <Button variant="outlined" onClick={onClose}>
          {t("players.profile.close")}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export const usePlayerProfile = () => {
  const [player, setPlayer] = useState<any>();

  return {
    PlayerProfileUI: () =>
      player ? <PlayerProfileModal player={player} onClose={() => setPlayer(undefined)} /> : null,
    show: (player: any) => setPlayer(player),
  };
};
