import { FC, useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

import {
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Typography,
  LoadingButton,
  Snackbar,
} from "@mio/ui";

type Props = {
  message?: string;
  title: string;
  onCancel?: () => void;
  onConfirm: (data: unknown) => void;
  isLoading: boolean;
  isError: boolean;
  isSuccess: boolean;
};

const ConfirmationModal: FC<Props> = ({
  message,
  title,
  onCancel,
  onConfirm,
  isLoading,
  isError,
  isSuccess,
}) => {
  const { t } = useTranslation();

  return (
    <Dialog open={!!message} fullWidth>
      <DialogTitle id="confirmation-dialog-title" sx={{ mb: 2 }}>
        {title}
      </DialogTitle>

      <DialogContent>
        <Typography>{message}</Typography>
        {isError && (
          <Box mb={2}>
            <Alert severity="error">{t("common.confirmation.error-message")}</Alert>
          </Box>
        )}
        {isSuccess && (
          <Box mb={2}>
            <Alert severity="success">{t("common.confirmation.success-message")}</Alert>
            <Snackbar open={isSuccess} onClose={onCancel} message="Successfully completed!" />
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        <Button variant="outlined" onClick={onCancel}>
          {t("common.confirmation.cancel")}
        </Button>

        <LoadingButton
          variant="outlined"
          onClick={onConfirm}
          loading={isLoading}
          disabled={isSuccess}
        >
          {t("common.confirmation.confirm")}
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
};

export const useConfirmationModal = (props: Props) => {
  const [message, setMessage] = useState<string | undefined>(props.message);

  useEffect(() => {
    if (props.isSuccess) {
      setMessage(undefined);
    }
  }, [props.isSuccess]);

  return {
    ConfirmationModalUI: () =>
      message ? (
        <ConfirmationModal
          {...props}
          message={message}
          onCancel={() => {
            setMessage(undefined);
            props.onCancel && props.onCancel();
          }}
        />
      ) : null,
    show: (message: string | undefined) => setMessage(message),
  };
};
