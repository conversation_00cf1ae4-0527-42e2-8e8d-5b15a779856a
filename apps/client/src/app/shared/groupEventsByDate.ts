import { TeamEvent } from "@mio/helpers";

export const groupEventsByDate = (events: TeamEvent.ExtendedTeamEvent[]) => {
  const grouped = events.reduce((acc, item) => {
    const key = item.startDateTime.toDateString();

    acc[key] = acc[key] || [];
    acc[key].push(item);

    return acc;
  }, {} as Record<string, TeamEvent.ExtendedTeamEvent[]>);

  return Object.entries(grouped)
    .map(([key, value]) => {
      return {
        date: key,
        events: value,
      };
    })
    .sort((a, b) => {
      return new Date(a.date).getTime() - new Date(b.date).getTime();
    });
};
