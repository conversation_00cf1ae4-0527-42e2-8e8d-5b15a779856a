import { useEffect, useState, FC, CSSProperties } from "react";
import { ContentState, convertToRaw, EditorState } from "draft-js";
import draftToHtml from "draftjs-to-html";
import htmlToDraft from "html-to-draftjs";
import { Editor } from "react-draft-wysiwyg";
import "react-draft-wysiwyg/dist/react-draft-wysiwyg.css";

type Props = {
  value: string;
  onChange: (newValue: string) => void;
  customStyle?: CSSProperties | undefined;
};

const defaultStyles: CSSProperties = {
  border: "1px solid lightgrey",
};

const RichTextEditor: FC<Props> = ({ onChange, value, customStyle = defaultStyles }) => {
  const [editorState, setEditorState] = useState(EditorState.createEmpty());
  const [updated, setUpdated] = useState(false);

  useEffect(() => {
    if (!updated) {
      const defaultValue = value ? value : "";
      const blocksFromHtml = htmlToDraft(defaultValue);
      const contentState = ContentState.createFromBlockArray(
        blocksFromHtml.contentBlocks,
        blocksFromHtml.entityMap,
      );
      const newEditorState = EditorState.createWithContent(contentState);
      setEditorState(newEditorState);
    }
  }, [value, updated]);

  const onEditorStateChange = (editorState: EditorState) => {
    setUpdated(true);
    setEditorState(editorState);

    return onChange(draftToHtml(convertToRaw(editorState.getCurrentContent())));
  };

  const toolbar = {
    options: ["link", "inline"],
    inline: {
      options: ["bold", "italic", "underline"],
    },
  };

  return (
    <Editor
      spellCheck
      editorState={editorState}
      onEditorStateChange={onEditorStateChange}
      toolbar={toolbar}
      editorStyle={customStyle}
    />
  );
};

export default RichTextEditor;
