import { FC, PropsWithChildren } from "react";
import { <PERSON> } from "react-router-dom";

import { ArrowBackIcon, MUILink } from "@mio/ui";

type Props = PropsWithChildren<{
  path: string;
}>;

export const BackButton: FC<Props> = ({ path, children }) => {
  return (
    <MUILink component={Link} to={path} sx={{ display: "flex", alignItems: "center" }}>
      <ArrowBackIcon sx={{ mr: 1 }} aria-hidden={true} />
      <span>{children}</span>
    </MUILink>
  );
};
