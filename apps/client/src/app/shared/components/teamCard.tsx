import { FC } from "react";
import { startCase } from "lodash/fp";
import { useTranslation } from "react-i18next";

import { Card, Typography, Stack, Box, Chip } from "@mio/ui";
import { CustomDate, Team } from "@mio/helpers";

type Props = {
  team: Team;
  children: JSX.Element;
};

const gridColumns = "1.5fr 1fr 1fr 1fr 1fr 1fr";

export const TeamCard: FC<Props> = ({ team, children }) => {
  const { t } = useTranslation();

  return (
    <Card sx={{ p: 2 }}>
      <Stack
        sx={{
          display: {
            md: "grid",
            xs: "flex",
          },
          flexWrap: "wrap",
          flexDirection: "row",
          gap: {
            xs: 2,
            sm: 4,
          },
          gridTemplateColumns: {
            md: gridColumns,
          },
        }}
      >
        <Typography component="h2" variant="h5" fontWeight="bold">
          {team.name}
        </Typography>

        <Box>
          <Chip label={startCase(team.gender)} />
        </Box>

        <Stack alignItems="center" gap={2}>
          <Chip label={t("teams.card.age-description")} />
          <Box>{team.ageDescription}</Box>
        </Stack>

        <Stack alignItems="center" gap={2}>
          <Chip label={t("teams.card.born-before")} />
          <Box>{CustomDate.toDisplayDate(team.playersBornBefore)}</Box>
        </Stack>

        <Stack alignItems="center" gap={2}>
          <Chip label={t("teams.card.born-after")} />
          <Box>{CustomDate.toDisplayDate(team.playersBornAfter)}</Box>
        </Stack>

        <Box>{children}</Box>
      </Stack>
    </Card>
  );
};
