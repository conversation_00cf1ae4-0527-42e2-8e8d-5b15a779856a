import { Player, Team, WITHIN_RANGE_MSG } from "@mio/helpers";
import { Alert, Tooltip, WarningIcon } from "@mio/ui";

type Props = {
  team: Team;
  player: Player;
  /** Display the warning as an icon with Tooltip, rather than Alert component */
  condensed?: boolean;
};

const PlayerAgeAlert = ({ team, player, condensed }: Props) => {
  const message = Team.getPlayerAgeCompatibilityLabel(team, player);

  if (message === WITHIN_RANGE_MSG) {
    return null;
  }

  if (condensed) {
    return (
      <Tooltip title={message}>
        <WarningIcon color="warning" />
      </Tooltip>
    );
  }

  return <Alert severity="warning">{message}</Alert>;
};

export { PlayerAgeAlert };
