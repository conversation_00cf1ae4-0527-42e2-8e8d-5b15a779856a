import { FC } from "react";
import { Navigate, Route, Routes } from "react-router-dom";

import {
  CurrentPermissionsProvider,
  CurrentProfileProvider,
  CurrentTeamProvider,
  CurrentUserProvider,
  OrganizationResolver,
} from "@mio/ui";
import { PermissionsModule, sharedUrls, UrlParams } from "@mio/helpers";

import { LoginPage, RegisterPage, RequestPasswordReset, ResetPassword } from "./auth";
import { AdminTeamsList } from "./teams-admin/teams-list";
import { AddTeam } from "./teams-admin/add-team";
import { TeamsList } from "./team/teamsList";
import { PlayersList } from "./players";
import { TrainingCenter, TrainingReviewDetails } from "./team/training-center";
import { AdminPlayersList } from "./players-admin/playersList";
import { OrganizationSettings } from "./organization-settings";
import { EditTeam } from "./teams-admin/edit-team";
import { WithLoadedTeams } from "./team";
import { CanAccess } from "./permissions";
import { UsersAdmin } from "./users-admin";
import { AppPrivateRoute, AppPublicRoute } from "./auth.guards";
import { SeasonsList, AddSeason, EditSeason } from "./seasons";
import { FinancialIntegrationsView } from "./financial-integration/financialIntegrationsView";
import { EventsRoot } from "./events-management";
import { GCSubscriptionsList } from "./go-cardless/gcSubscriptionsList";
import JoinOrganization from "./auth/joinOrganization";
import { EmailTemplates } from "./email-templates";
import { MatchPlanList } from "./team/match-center/match-plan/matchPlansList";
import { AddMatchPlan } from "./team/match-center/match-plan/addMatchPlan";
import { EditMatchPlan } from "./team/match-center/match-plan/editMatchPlan";
import { MatchReviewCenter } from "./team/match-center/match-review/matchReviewCenter";
import { StatsCenter } from "./team/player-stats/statsCenter";
import { TeamStatsCenter } from "./team/team-stats/teamStatsCenter";
import { AddTrainingSessionPlan } from "./team/training-center/addTrainingSessionPlan";

export const AppRouting: FC = () => {
  return (
    <Routes>
      <Route
        path="/login"
        element={
          <AppPublicRoute>
            <LoginPage />
          </AppPublicRoute>
        }
      />
      <Route
        path={`/register/:${UrlParams.InviteId}`}
        element={
          <AppPublicRoute>
            <RegisterPage />
          </AppPublicRoute>
        }
      />
      <Route
        path={`/join/:${UrlParams.OrganizationSlug}/invite/:${UrlParams.InviteId}`}
        element={<JoinOrganization />}
      />

      <Route
        path="/auth/password-reset"
        element={
          <AppPublicRoute>
            <RequestPasswordReset />
          </AppPublicRoute>
        }
      />

      <Route
        path={"/" + sharedUrls.passwordReset}
        element={
          <AppPublicRoute>
            <ResetPassword />
          </AppPublicRoute>
        }
      />

      <Route
        path={`/:${UrlParams.OrganizationId}/*`}
        element={
          <AppPrivateRoute>
            <CurrentUserProvider>
              <CurrentProfileProvider>
                <CurrentPermissionsProvider>
                  <Routes>
                    <Route
                      path="teams-admin/*"
                      element={
                        <CanAccess actions={[PermissionsModule.Action.Actions.ManageTeams]}>
                          <Routes>
                            <Route path="create" element={<AddTeam />} />
                            <Route path={`edit/:${UrlParams.TeamId}`} element={<EditTeam />} />
                            <Route path="" element={<AdminTeamsList />} />
                          </Routes>
                        </CanAccess>
                      }
                    />

                    <Route
                      path="players-admin/*"
                      element={
                        <CanAccess actions={[PermissionsModule.Action.Actions.ManagePlayers]}>
                          <Routes>
                            <Route path="" element={<AdminPlayersList />} />
                          </Routes>
                        </CanAccess>
                      }
                    />

                    <Route
                      path="finances/*"
                      element={
                        //TODO: Add the appropriate new permission
                        <CanAccess actions={[PermissionsModule.Action.Actions.ManageOrganization]}>
                          <Routes>
                            <Route path="" element={<GCSubscriptionsList />} />
                          </Routes>
                        </CanAccess>
                      }
                    />

                    <Route
                      path="settings/*"
                      element={
                        <CanAccess actions={[PermissionsModule.Action.Actions.ManageOrganization]}>
                          <Routes>
                            <Route path="general" element={<OrganizationSettings />} />
                            <Route
                              path="financial-integrations"
                              element={<FinancialIntegrationsView />}
                            />
                            <Route path="seasons" element={<SeasonsList />} />
                            <Route path="seasons/create" element={<AddSeason />} />
                            <Route
                              path={`seasons/edit/:${UrlParams.SeasonId}`}
                              element={<EditSeason />}
                            />
                            <Route path="email-templates" element={<EmailTemplates />} />
                          </Routes>
                        </CanAccess>
                      }
                    />

                    <Route
                      path="users-admin/*"
                      element={
                        <CanAccess actions={[PermissionsModule.Action.Actions.ManageUsers]}>
                          <Routes>
                            <Route path="" element={<UsersAdmin />} />
                          </Routes>
                        </CanAccess>
                      }
                    />

                    <Route
                      path="teams/*"
                      element={
                        <WithLoadedTeams>
                          <Routes>
                            <Route path={`:${UrlParams.TeamId}`} element={<PlayersList />} />

                            <Route
                              path={`:${UrlParams.TeamId}/players-list`}
                              element={<PlayersList />}
                            />

                            <Route
                              path={`:${UrlParams.TeamId}/training-center/*`}
                              element={
                                <Routes>
                                  <Route
                                    path={`reviews/:${UrlParams.TrainingSessionReviewId}`}
                                    element={<TrainingReviewDetails />}
                                  />
                                  <Route path="" element={<TrainingCenter />} />
                                  <Route path="create" element={<AddTrainingSessionPlan />} />
                                </Routes>
                              }
                            />

                            {/* TODO: Add the appropriate new permission - not sure who has access to this - maybe everyone? */}
                            <Route
                              path={`:${UrlParams.TeamId}/match-center/*`}
                              element={
                                <Routes>
                                  <Route
                                    path={`review/:${UrlParams.FootballMatchPlanId}`}
                                    element={<MatchReviewCenter />}
                                  />

                                  <Route path="create" element={<AddMatchPlan />} />
                                  <Route
                                    path={`edit/:${UrlParams.FootballMatchPlanId}`}
                                    element={<EditMatchPlan />}
                                  />
                                  <Route path="" element={<MatchPlanList />} />
                                </Routes>
                              }
                            />

                            <Route
                              path={`:${UrlParams.TeamId}/player-stats`}
                              element={<StatsCenter />}
                            />

                            <Route
                              path={`:${UrlParams.TeamId}/team-stats`}
                              element={<TeamStatsCenter />}
                            />

                            <Route
                              path={`:${UrlParams.TeamId}/event-management/*`}
                              element={
                                <CanAccess
                                  actions={[PermissionsModule.Action.Actions.ManageEvents]}
                                >
                                  <Routes>
                                    <Route path="/*" element={<EventsRoot />} />
                                  </Routes>
                                </CanAccess>
                              }
                            />

                            {/* if there is only one Team for the User - redirects to it ( lands on :teamId/players-list).
                        Otherwise it renders the TeamsList so the User can pick
                        the team manually */}
                            <Route
                              path="/"
                              element={
                                <CurrentTeamProvider>
                                  <TeamsList />
                                </CurrentTeamProvider>
                              }
                            />
                          </Routes>
                        </WithLoadedTeams>
                      }
                    />

                    <Route path="" element={<Navigate to="teams" />} />
                  </Routes>
                </CurrentPermissionsProvider>
              </CurrentProfileProvider>
            </CurrentUserProvider>
          </AppPrivateRoute>
        }
      />

      {/* if there is no organizationId in the url - will pick Profile.organizations[0]
      as the default one and add it to the url. We can add more thought-out method
      for picking organization later on  */}
      <Route
        path="/*"
        element={
          <AppPrivateRoute>
            <CurrentUserProvider>
              <CurrentProfileProvider>
                <OrganizationResolver />
              </CurrentProfileProvider>
            </CurrentUserProvider>
          </AppPrivateRoute>
        }
      />
    </Routes>
  );
};
