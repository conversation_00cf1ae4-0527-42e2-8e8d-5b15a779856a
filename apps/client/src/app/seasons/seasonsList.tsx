import { FC } from "react";
import { <PERSON> } from "react-router-dom";
import { useTranslation } from "react-i18next";

import {
  Box,
  Stack,
  Typography,
  PageError,
  PageLoader,
  Button,
  AddIcon,
  useOrganization,
  useGetSeasons,
} from "@mio/ui";
import { ErrorMessages } from "@mio/helpers";
import { SidebarLayout } from "../layouts";
import { SeasonItem } from "./seasonItem";

export const SeasonsList: FC = () => {
  const { t } = useTranslation();
  const organization = useOrganization();
  const seasons = useGetSeasons(organization.id);

  if (seasons.status === "loading") {
    return <PageLoader message={t("seasons.loading-seasons")} />;
  }

  if (seasons.status === "error") {
    return (
      <SidebarLayout>
        <PageError
          message={
            seasons.error.message === ErrorMessages.PermissionDenied
              ? t("common.permission-denied")
              : t("seasons.load-error")
          }
        />
      </SidebarLayout>
    );
  }

  if (seasons.status === "success") {
    return (
      <SidebarLayout>
        <Box sx={{ width: "100%", bgcolor: "background.paper", padding: { sm: 2 } }}>
          <Stack
            component="header"
            flexDirection="row"
            justifyContent="space-between"
            mb={5}
            gap={2}
            flexWrap="wrap"
          >
            <Box>
              <Typography variant="h4" component="h1">
                {t("seasons.count", { count: seasons.data.length })}
              </Typography>
            </Box>

            <Box>
              <Button
                component={Link}
                to="create"
                variant="contained"
                size="large"
                color="secondary"
                startIcon={<AddIcon />}
              >
                {t("seasons.add-season")}
              </Button>
            </Box>
          </Stack>
          <Stack aria-label={t("seasons.list-navigation")} component="nav" gap={3}>
            {seasons.data.map((season) => (
              <SeasonItem season={season} key={season.id} />
            ))}
          </Stack>
        </Box>
      </SidebarLayout>
    );
  }

  return null;
};
