import { FC } from "react";
import { Navigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { isError, Season } from "@mio/helpers";
import {
  PageError,
  PageLoader,
  useGetSeason,
  useOrganization,
  useOrganizationId,
  useRequiredSeasonId,
  useUpdateSeason,
} from "@mio/ui";

import { UpsertSeasonForm } from "./upsertForm";
import { SidebarLayout } from "../layouts";

export const EditSeason: FC = () => {
  const { t } = useTranslation();
  const seasonId = useRequiredSeasonId();
  const organization = useOrganization();
  const seasonQuery = useGetSeason(organization.id, seasonId);
  const updateSeason = useUpdateSeason();
  const organizationId = useOrganizationId();

  const handleSubmit = (data: unknown) => {
    const validated = Season.toUpdateDto(data);

    if (!isError(validated)) {
      updateSeason.mutate(validated);
    }
  };

  if (seasonQuery.status === "loading") {
    return (
      <SidebarLayout>
        <PageLoader message={t("seasons.loading")} />
      </SidebarLayout>
    );
  }

  if (seasonQuery.status === "error") {
    return (
      <SidebarLayout>
        <PageError message={t("seasons.load-error")} />
      </SidebarLayout>
    );
  }

  if (updateSeason.isSuccess) {
    return <Navigate to={`/${organizationId}/settings/seasons`} />;
  }

  if (seasonQuery.status === "success") {
    return (
      <SidebarLayout>
        <UpsertSeasonForm
          onSubmit={handleSubmit}
          season={seasonQuery.data}
          loading={updateSeason.isLoading}
          serverError={updateSeason.error}
        />
      </SidebarLayout>
    );
  }

  return <Navigate to={`/${organizationId}/settings/seasons`} />;
};
