import { FC } from "react";
import { <PERSON> } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { CustomDate, Season } from "@mio/helpers";
import { Typography, Card, Stack, Chip, Box, EditIcon, IconButton, Tooltip } from "@mio/ui";

type Props = {
  season: Season;
};

export const SeasonItem: FC<Props> = ({ season }) => {
  const { t } = useTranslation();

  return (
    <Card sx={{ p: 2 }}>
      <Stack direction="row" justifyContent="space-between" flexWrap="wrap" gap={2}>
        <Typography component="h2" variant="h5">
          {season.name}
        </Typography>

        <Stack alignItems="center" gap={1}>
          <Chip label={t("seasons.objective")} />
          <Box>{season.objective}</Box>
        </Stack>

        <Stack alignItems="center" gap={1}>
          <Chip label={t("seasons.start-date")} />
          <Box>{CustomDate.toDisplayDate(season.startDate)}</Box>
        </Stack>

        <Stack alignItems="center" gap={1}>
          <Chip label={t("seasons.end-date")} />
          <Box>{CustomDate.toDisplayDate(season.endDate)}</Box>
        </Stack>

        <Box>
          <Tooltip title={t("seasons.edit-season")}>
            <IconButton
              component={Link}
              to={`edit/${season.id}`}
              color="primary"
              aria-label={t("seasons.edit-season")}
            >
              <EditIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Stack>
    </Card>
  );
};
