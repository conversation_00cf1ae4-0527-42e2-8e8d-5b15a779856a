import { FC } from "react";
import { Navigate } from "react-router-dom";

import { isError, Season } from "@mio/helpers";
import { useOrganization, useCreateSeason } from "@mio/ui";
import { UpsertSeasonForm } from "./upsertForm";
import { SidebarLayout } from "../layouts";

export const AddSeason: FC = () => {
  const createSeason = useCreateSeason();
  const organization = useOrganization();

  const handleSubmit = (data: unknown) => {
    const validated = Season.toCreateDto(data);

    if (!isError(validated)) {
      createSeason.mutate(validated);
    }
  };

  if (createSeason.isSuccess) {
    return <Navigate to={`/${organization.id}/settings/seasons`} />;
  }

  return (
    <SidebarLayout>
      <UpsertSeasonForm
        onSubmit={handleSubmit}
        loading={createSeason.isLoading}
        serverError={createSeason.error}
      />
    </SidebarLayout>
  );
};
