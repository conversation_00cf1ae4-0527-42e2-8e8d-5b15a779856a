import { FC, ReactNode } from "react";

import { Box, useScreenSize, Stack } from "@mio/ui";

type Props = {
  children: ReactNode;
  imageUrl: string;
};

export const BackgroundLayout: FC<Props> = ({ children, imageUrl }) => {
  const { smallScreen } = useScreenSize();

  if (smallScreen) {
    return (
      <Stack height="100%">
        <Box
          sx={{
            background: `url(${imageUrl}) no-repeat center center`,
            backgroundSize: "cover",
            height: 0,
            paddingBottom: "56.25%" /* 16:9 ratio */,
          }}
        />
        <Box>{children}</Box>
      </Stack>
    );
  }

  return (
    <Box
      sx={{
        height: "100%",
        backgroundImage: `url(${imageUrl})`,
        backgroundSize: "cover",
        backgroundPosition: "center center",
        backgroundRepeat: "no-repeat",
      }}
    >
      {children}
    </Box>
  );
};
