import { FC } from "react";
import { useTranslation } from "react-i18next";

import { Box, MenuItem, Typography, TranslateIcon } from "@mio/ui";

export const LanguageSelector: FC = () => {
  const { i18n } = useTranslation();

  const handleLanguageChange = () => {
    const newLang = i18n.language === "en" ? "es" : "en";
    i18n.changeLanguage(newLang);
  };

  return (
    <MenuItem onClick={handleLanguageChange}>
      <Box sx={{ display: "flex", alignItems: "center" }}>
        <Box mr={1} component={TranslateIcon} />
        <Typography>
          {i18n.language === "en" ? "Cambiar a Español" : "Switch to English"}
        </Typography>
      </Box>
    </MenuItem>
  );
};
