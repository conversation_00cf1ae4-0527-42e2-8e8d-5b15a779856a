import { Link, useLocation } from "react-router-dom";
import { useState } from "react";
import { useTranslation } from "react-i18next";

import { PermissionsModule } from "@mio/helpers";
import {
  Box,
  Button,
  Collapse,
  DataThresholdingIcon,
  DirectionsRunIcon,
  EventAvailableIcon,
  ExpandLessIcon,
  ExpandMoreIcon,
  Divider,
  GroupsIcon,
  SettingsIcon,
  Stack,
  useOrganizationId,
  useTeamId,
  ScoreboardIcon,
  ContactEmergencyIcon,
} from "@mio/ui";

import { CanSee } from "../../permissions";
import { TeamSelector } from "./teamSelector";
import { UserMenu } from "./userMenu";
import { environment } from "../../../environments/environment";

const buttonStyles = (isActive: boolean) => ({
  width: "100%",
  justifyContent: "start",
  backgroundColor: isActive ? "primary.main" : "initial",
  color: "inherit",
  paddingLeft: 2,
  "&:hover": {
    backgroundColor: isActive ? "primary.main" : "primary.main",
  },
});

const matchingPath = (currentPath: string) => (matchingPath: string) =>
  currentPath.includes(matchingPath);

export const SidebarContent = () => {
  const { t } = useTranslation();
  const organizationId = useOrganizationId();
  const teamId = useTeamId();
  const { pathname } = useLocation();
  const isPathMatch = matchingPath(pathname);

  const baseUrl = `/${organizationId}/teams/${teamId}`;

  const playerListUrl = baseUrl + "/players-list";
  const trainingCenterUrl = baseUrl + "/training-center";
  const matchCenterUrl = baseUrl + "/match-center";
  const playerStatsUrl = baseUrl + "/player-stats";
  const teamStatsUrl = baseUrl + "/team-stats";
  const eventManagementUrl = baseUrl + "/event-management";

  const teamAdminUrl = `/${organizationId}/teams-admin`;
  const playerAdminUrl = `/${organizationId}/players-admin`;
  const userAdminUrl = `/${organizationId}/users-admin`;
  const financeAdminUrl = `/${organizationId}/finances`;

  const generalOrgSettings = `/${organizationId}/settings/general`;
  const financialIntegrationSettings = `/${organizationId}/settings/financial-integrations`;
  const seasonsSettings = `/${organizationId}/settings/seasons`;
  const emailTemplateSettings = `/${organizationId}/settings/email-templates`;

  const adminUrls = [teamAdminUrl, playerAdminUrl, userAdminUrl, financeAdminUrl];
  const settingsUrls = [
    generalOrgSettings,
    financialIntegrationSettings,
    seasonsSettings,
    emailTemplateSettings,
  ];

  const openListDefault = settingsUrls.some((url) => isPathMatch(url))
    ? "settings"
    : adminUrls.some((url) => isPathMatch(url))
    ? "admin"
    : null;

  const [openList, setOpenList] = useState<"admin" | "settings" | null>(openListDefault);

  const onOpenList = (list: typeof openList) => {
    setOpenList(openList === list ? null : list);
  };

  const TeamMenu = (
    <Stack px={2} sx={{ backgroundColor: "primary.dark" }}>
      <Box mb={3}>
        <TeamSelector />
      </Box>

      {teamId && (
        <Stack gap={2}>
          <Button
            sx={buttonStyles(isPathMatch(playerListUrl))}
            startIcon={<ContactEmergencyIcon />}
            to={playerListUrl}
            component={Link}
            variant="text"
            color="inherit"
          >
            {t("sidebar.menu.players-list")}
          </Button>

          <Button
            sx={buttonStyles(isPathMatch(trainingCenterUrl))}
            startIcon={<DirectionsRunIcon />}
            to={trainingCenterUrl}
            component={Link}
            variant="text"
            color="inherit"
          >
            {t("sidebar.menu.training-center")}
          </Button>

          <Button
            sx={buttonStyles(isPathMatch(matchCenterUrl))}
            startIcon={<ScoreboardIcon />}
            to={matchCenterUrl}
            component={Link}
            variant="text"
            color="inherit"
          >
            {t("sidebar.menu.match-center")}
          </Button>

          {!environment.demoMode && (
            <Button
              sx={buttonStyles(isPathMatch(teamStatsUrl))}
              startIcon={<DataThresholdingIcon />}
              to={teamStatsUrl}
              component={Link}
              variant="text"
              color="inherit"
            >
              {t("sidebar.menu.team-stats")}
            </Button>
          )}

          {!environment.demoMode && (
            <Button
              sx={buttonStyles(isPathMatch(playerStatsUrl))}
              startIcon={<DataThresholdingIcon />}
              to={playerStatsUrl}
              component={Link}
              variant="text"
              color="inherit"
            >
              {t("sidebar.menu.player-stats")}
            </Button>
          )}

          <CanSee actions={[PermissionsModule.Action.Actions.ManageEvents]}>
            <Button
              sx={buttonStyles(isPathMatch(eventManagementUrl))}
              startIcon={<EventAvailableIcon fontSize="large" />}
              to={eventManagementUrl}
              component={Link}
              variant="text"
              color="inherit"
            >
              {t("sidebar.menu.event-management")}
            </Button>
          </CanSee>
        </Stack>
      )}
    </Stack>
  );

  return (
    <Box sx={{ display: "flex", flexDirection: "column", height: "100%", mt: 2 }}>
      <Box sx={{ flexGrow: 1 }}>
        <Stack component="nav" gap={2}>
          {TeamMenu}

          <Divider sx={{ borderColor: "primary.main" }} />

          <Stack px={2}>
            <Button
              onClick={() => onOpenList("admin")}
              sx={buttonStyles(false)}
              startIcon={<GroupsIcon />}
              endIcon={openList === "admin" ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              variant="text"
              color="inherit"
            >
              {t("sidebar.menu.club-admin")}
            </Button>

            <Collapse in={openList === "admin"}>
              <Stack component="nav" gap={2} ml={1} mt={2}>
                <CanSee actions={[PermissionsModule.Action.Actions.ManageTeams]}>
                  <Button
                    sx={buttonStyles(isPathMatch(teamAdminUrl))}
                    to={teamAdminUrl}
                    component={Link}
                    variant="text"
                    color="inherit"
                  >
                    {t("sidebar.menu.teams-admin")}
                  </Button>
                </CanSee>

                <CanSee actions={[PermissionsModule.Action.Actions.ManagePlayers]}>
                  <Button
                    sx={buttonStyles(isPathMatch(playerAdminUrl))}
                    to={playerAdminUrl}
                    component={Link}
                    variant="text"
                    color="inherit"
                  >
                    {t("sidebar.menu.players-admin")}
                  </Button>
                </CanSee>

                <CanSee actions={[PermissionsModule.Action.Actions.ManageUsers]}>
                  <Button
                    sx={buttonStyles(isPathMatch(userAdminUrl))}
                    to={userAdminUrl}
                    component={Link}
                    variant="text"
                    color="inherit"
                  >
                    {t("sidebar.menu.users-admin")}
                  </Button>
                </CanSee>

                <CanSee actions={[PermissionsModule.Action.Actions.ManageOrganization]}>
                  <Button
                    sx={buttonStyles(isPathMatch(financeAdminUrl))}
                    to={financeAdminUrl}
                    component={Link}
                    variant="text"
                    color="inherit"
                  >
                    {t("sidebar.menu.finances")}
                  </Button>
                </CanSee>
              </Stack>
            </Collapse>
          </Stack>

          <CanSee actions={[PermissionsModule.Action.Actions.ManageOrganization]}>
            <Stack px={2}>
              <Button
                onClick={() => onOpenList("settings")}
                sx={buttonStyles(false)}
                startIcon={<SettingsIcon />}
                endIcon={openList === "settings" ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                variant="text"
                color="inherit"
              >
                {t("sidebar.menu.settings")}
              </Button>

              <Collapse in={openList === "settings"}>
                <Stack component="nav" gap={2} ml={1} mt={2}>
                  <Button
                    sx={buttonStyles(isPathMatch(generalOrgSettings))}
                    to={generalOrgSettings}
                    component={Link}
                    variant="text"
                    color="inherit"
                  >
                    {t("sidebar.menu.general")}
                  </Button>

                  {!environment.demoMode && (
                    <Button
                      sx={buttonStyles(isPathMatch(financialIntegrationSettings))}
                      to={financialIntegrationSettings}
                      component={Link}
                      variant="text"
                      color="inherit"
                    >
                      {t("sidebar.menu.financial-integration")}
                    </Button>
                  )}

                  <Button
                    sx={buttonStyles(isPathMatch(seasonsSettings))}
                    to={seasonsSettings}
                    component={Link}
                    variant="text"
                    color="inherit"
                  >
                    {t("sidebar.menu.season")}
                  </Button>

                  <Button
                    sx={buttonStyles(isPathMatch(emailTemplateSettings))}
                    to={emailTemplateSettings}
                    component={Link}
                    variant="text"
                    color="inherit"
                  >
                    {t("sidebar.menu.email-templates")}
                  </Button>
                </Stack>
              </Collapse>
            </Stack>
          </CanSee>
        </Stack>
      </Box>

      <Box p={2}>
        <UserMenu />
      </Box>
    </Box>
  );
};
