import { FC, PropsWithChildren } from "react";
import { useTranslation } from "react-i18next";

import { Box, SidebarLayout as SharedSidebarLayout, Typography, visuallyHidden } from "@mio/ui";

import { SidebarContent } from "./sidebarContent";
import { LogoWithText } from "../../shared";

export const SidebarLayout: FC<PropsWithChildren> = ({ children }) => {
  const { t } = useTranslation();

  return (
    <SharedSidebarLayout
      title={
        <Box>
          <Typography component="h1" sx={{ ...visuallyHidden }}>
            {t("sidebar.title")}
          </Typography>

          <Box width={{ xs: "75px", md: "100px" }}>
            <LogoWithText variant="light" />
          </Box>
        </Box>
      }
      content={<SidebarContent />}
    >
      {children}
    </SharedSidebarLayout>
  );
};
