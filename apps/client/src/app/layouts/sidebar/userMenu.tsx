import { FC, useState, MouseEvent } from "react";
import { useTranslation } from "react-i18next";

import {
  Avatar,
  Box,
  Typography,
  Menu,
  MenuItem,
  Button,
  LogoutIcon,
  useAuth,
  useProvidedCurrentProfile,
  useProvidedCurrentUser,
  Stack,
  useOrganization,
  Divider,
} from "@mio/ui";

import OrganizationSelector from "./organizationSelector";
import { LanguageSelector } from "./languageSelector";

export const UserMenu: FC = () => {
  const { t } = useTranslation();
  const { logout } = useAuth();
  const user = useProvidedCurrentUser();
  const profile = useProvidedCurrentProfile();
  const organization = useOrganization();

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleOpen = (event: MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const avatar = profile.firstName.charAt(0) + profile.lastName.charAt(0);

  return (
    <>
      <Box>
        <Stack direction="row" gap={2} alignItems="center" flexWrap="wrap">
          <Button
            variant="text"
            id="menu-button"
            aria-controls={open ? "user-menu" : undefined}
            aria-haspopup="true"
            aria-label={t("sidebar.user-menu.open-menu")}
            aria-expanded={open ? "true" : undefined}
            onClick={handleOpen}
            sx={{ color: "inherit" }}
          >
            <Avatar sx={{ bgcolor: "secondary.main", textTransform: "capitalize" }}>
              {avatar}
            </Avatar>
          </Button>

          <Box>
            <Stack sx={{ textAlign: "left" }}>
              <Typography
                sx={{
                  color: "inherit",
                  fontWeight: "bold",
                  fontSize: "0.8em",
                  wordBreak: "break-all",
                }}
              >
                {user.authentication.email}
              </Typography>

              {profile.organizations.length < 2 && (
                <Typography sx={{ color: "inherit", fontWeight: "bold", fontSize: "0.8em" }}>
                  {organization.name}
                </Typography>
              )}
            </Stack>
          </Box>
        </Stack>

        {profile.organizations.length > 1 && (
          <Box mt={1} pb={2}>
            <OrganizationSelector />
          </Box>
        )}
      </Box>

      <Menu
        id="user-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          "aria-labelledby": "menu-button",
        }}
      >
        <Divider />
        <LanguageSelector />
        <Divider />
        <MenuItem
          onClick={() => {
            handleClose();
            logout();
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <Box mr={1} component={LogoutIcon} />
            <Typography>{t("sidebar.user-menu.logout")}</Typography>
          </Box>
        </MenuItem>
      </Menu>
    </>
  );
};
