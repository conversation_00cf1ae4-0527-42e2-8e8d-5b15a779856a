import { FC } from "react";

import {
  MenuItem,
  Select,
  Typography,
  useOrganization,
  usePersistedActiveOrganization,
  useProvidedCurrentProfile,
} from "@mio/ui";
import { OrganizationId } from "@mio/helpers";

const OrganizationSelector: FC = () => {
  const profile = useProvidedCurrentProfile();
  const activeOrganization = useOrganization();
  const { setActiveOrganization } = usePersistedActiveOrganization();

  return (
    <Select
      fullWidth
      aria-label="Organization"
      onChange={(event) => setActiveOrganization(event.target.value as OrganizationId)}
      value={activeOrganization.id}
      sx={{
        color: "white",
        ".MuiOutlinedInput-notchedOutline": {
          borderColor: "rgba(228, 219, 233, 0.25)",
        },
        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
          borderColor: "rgba(228, 219, 233, 0.25)",
        },
        "&:hover .MuiOutlinedInput-notchedOutline": {
          borderColor: "rgba(228, 219, 233, 0.25)",
        },
        ".MuiSvgIcon-root ": {
          fill: "white !important",
        },
      }}
      renderValue={() => <Typography color="white">{activeOrganization.name}</Typography>}
    >
      {profile.organizations.map((elem) => (
        <MenuItem value={elem.id} key={elem.id}>
          <Typography>{elem.name}</Typography>
        </MenuItem>
      ))}
    </Select>
  );
};

export default OrganizationSelector;
