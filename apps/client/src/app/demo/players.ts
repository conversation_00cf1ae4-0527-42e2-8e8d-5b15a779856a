import { sample } from "lodash";

import {
  CustomDate,
  OrganizationId,
  Player,
  PlayerSorting,
  PlayerTeamProfile,
  PlayerTeamStatus,
  PlayerTeamStatusWithTeam,
  PopulatedPlayer,
  TeamId,
} from "@mio/helpers";
import { GetTeamPlayerParams, QueryPlayersParams } from "@mio/ui";

const players: PopulatedPlayer[] = [];

const hasGuardian = (sample([1, 2]) || 1) % 2 === 1;

export const createPlayers = (
  organizationId: OrganizationId,
  teamId: TeamId,
  applicants = 1,
  registered = 1,
  random = 1,
) => {
  Array.from(Array(applicants)).forEach(() => {
    const player = hasGuardian ? Player.toDemoWithGuardianInstance() : Player.toDemoInstance();

    const playerProfile = PlayerTeamProfile.toApplicantDemoInstance({
      organizationId,
      playerId: player.id,
    });

    const populatedPlayer = Player.toPopulatedPlayer(player, [playerProfile]);

    players.push(populatedPlayer);
  });

  Array.from(Array(registered)).forEach(() => {
    const player = hasGuardian ? Player.toDemoWithGuardianInstance() : Player.toDemoInstance();

    const playerProfile = PlayerTeamProfile.toDemoInstance({
      organizationId,
      playerId: player.id,
      teamId,
    });

    const populatedPlayer = Player.toPopulatedPlayer(player, [playerProfile]);

    players.push(populatedPlayer);
  });

  Array.from(Array(random)).forEach(() => {
    const player = hasGuardian ? Player.toDemoWithGuardianInstance() : Player.toDemoInstance();

    const playerProfile = PlayerTeamProfile.toDemoInstance({
      organizationId,
      playerId: player.id,
      teamId,
      status:
        sample(
          Object.values(PlayerTeamStatusWithTeam).filter(
            (elem) => elem !== PlayerTeamStatus.RegisteredMatches,
          ),
        ) || PlayerTeamStatus.ApplicantTeam,
    });

    const populatedPlayer = Player.toPopulatedPlayer(player, [playerProfile]);

    players.push(populatedPlayer);
  });

  return undefined;
};

const filterTeamPlayer = (dto: GetTeamPlayerParams, player: PopulatedPlayer) => {
  return player.profiles.some(
    (profile) =>
      profile.status !== PlayerTeamStatus.ApplicantOrganization &&
      profile.teamId === dto.teamId &&
      profile.organizationId === dto.organizationId &&
      (dto.query?.searchName
        ? player.firstName.toLowerCase().includes(dto.query.searchName.toLowerCase())
        : true) &&
      (dto.query?.playerStatus ? profile.status === dto.query.playerStatus : true) &&
      (dto.query?.dobBefore ? CustomDate.isBefore(player.dob, dto.query.dobBefore) : true) &&
      (dto.query?.dobAfter ? CustomDate.isBefore(player.dob, dto.query.dobAfter) : true),
  );
};

const filterQueriedPlayer = (dto: QueryPlayersParams, player: PopulatedPlayer) => {
  return player.profiles.some(
    (profile) =>
      profile.organizationId === dto.organizationId &&
      (dto.query?.searchName
        ? player.firstName.toLowerCase().includes(dto.query.searchName.toLowerCase())
        : true) &&
      (dto.query?.playerStatus ? profile.status === dto.query.playerStatus : true) &&
      (dto.query?.dobBefore ? CustomDate.isBefore(player.dob, dto.query.dobBefore) : true) &&
      (dto.query?.dobAfter ? CustomDate.isBefore(player.dob, dto.query.dobAfter) : true),
  );
};

const sortPlayers = (dto: GetTeamPlayerParams | QueryPlayersParams, players: PopulatedPlayer[]) => {
  if (dto.query.sortBy === PlayerSorting.Names) {
    return players.sort((a, b) => a.firstName.charCodeAt(0) - b.firstName.charCodeAt(0));
  }

  if (dto.query.sortBy === PlayerSorting.Age) {
    return players.sort((a, b) => a.dob.getTime() - b.dob.getTime());
  }

  return players;
};

export const getTeamPlayers = (dto: GetTeamPlayerParams) =>
  sortPlayers(
    dto,
    players.filter((player) => filterTeamPlayer(dto, player)),
  );

export const queryPlayers = (dto: QueryPlayersParams) => {
  const result = sortPlayers(
    dto,
    players.filter((player) => filterQueriedPlayer(dto, player)),
  );

  return { data: result, limit: 100, skip: 0, total: result.length };
};
