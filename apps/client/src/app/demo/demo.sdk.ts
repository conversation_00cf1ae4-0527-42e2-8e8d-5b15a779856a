import { faker } from "@faker-js/faker";

import {
  AccessToken,
  CoachUser,
  EmailTemplate,
  EmailTemplateVisibility,
  Invite,
  Organization,
  OrganizationId,
  PermissionsModule,
  Profile,
  Season,
  Team,
  TeamId,
  UUID,
} from "@mio/helpers";
import { GetTeamPlayerParams, QueryPlayersParams, SDK } from "@mio/ui";

import * as players from "./players";
import { user } from "./user";
import { profile } from "./profile";
import { getEvents, createEvents } from "./events";

const teamId1 = UUID.parser<TeamId>().parse("10389a2a-9436-408c-a107-b138c2fe66cf");
const teamId2 = UUID.parser<TeamId>().parse("f9ecd314-cee2-42b5-8781-8ee8a5180814");
const orgId = UUID.parser<OrganizationId>().parse("d0be0e92-3e12-410e-aa2b-8a4ea26c78b0");

const organization = Organization.toPublicDemoInstance(undefined, orgId);

const publicProfile = {
  ...profile,
  organizations: [organization],
};

const coachRole = PermissionsModule.Role.Role.toCoachDemoInstance();
const adminRole = PermissionsModule.Role.Role.toAdminDemoInstance();

const roles = [coachRole, adminRole];

const ownerPermission = PermissionsModule.PermissionEntity.Permission.toDemoOwnerInstance(
  organization.id,
  publicProfile.id,
);

const coachPermission = PermissionsModule.PermissionEntity.Permission.toDemoCustomInstance(
  coachRole.id,
  undefined,
  undefined,
  undefined,
  [teamId1, teamId2],
);
const adminPermission = PermissionsModule.PermissionEntity.Permission.toDemoCustomInstance(
  adminRole.id,
);

const team1 = Team.toDemoInstance(undefined, teamId1);
const team2 = Team.toDemoInstance(undefined, teamId2);

const teams = [team1, team2];

const generateFullName = (type: "male" | "female") => ({
  firstName: faker.name.firstName(type),
  lastName: faker.name.lastName(type),
});

const user1Names = generateFullName("male");
const user2Names = generateFullName("female");
const user3Names = generateFullName("female");
const user4Names = generateFullName("male");

const user1 = CoachUser.toDemoInstance(undefined, undefined, user1Names.firstName);
const user2 = CoachUser.toDemoInstance(undefined, undefined, user2Names.firstName);
const user3 = CoachUser.toDemoInstance(undefined, undefined, user3Names.firstName);
const user4 = CoachUser.toDemoInstance(undefined, undefined, user4Names.firstName);

const populatedUser1 = CoachUser.toPopulatedUserDemoInstance(
  user1,
  Profile.toDemoInstance(undefined, undefined, user1Names.firstName, user1Names.lastName),
  Invite.toRedeemedDemoInstance({
    invitedBy: "system",
    email: user1.authentication.email,
    organization: organization.id,
  }),
  [ownerPermission],
);

const populatedUser2 = CoachUser.toPopulatedUserDemoInstance(
  user2,
  Profile.toDemoInstance(undefined, undefined, user2Names.firstName, user2Names.lastName),
  Invite.toRedeemedDemoInstance({
    invitedBy: user1.authentication.email,
    email: user2.authentication.email,
    organization: organization.id,
  }),
  [adminPermission],
);
const populatedUser3 = CoachUser.toPopulatedUserDemoInstance(
  user3,
  Profile.toDemoInstance(undefined, undefined, user3Names.firstName, user3Names.lastName),
  Invite.toRedeemedDemoInstance({
    invitedBy: user1.authentication.email,
    email: user3.authentication.email,
    organization: organization.id,
  }),
  [coachPermission],
);
const populatedUser4 = CoachUser.toPopulatedUserDemoInstance(
  user4,
  Profile.toDemoInstance(undefined, undefined, user4Names.firstName, user4Names.lastName),
  Invite.toRedeemedDemoInstance({
    invitedBy: user1.authentication.email,
    email: user4.authentication.email,
    organization: organization.id,
  }),
  [coachPermission],
);

const users = [populatedUser1, populatedUser2, populatedUser3, populatedUser4];

players.createPlayers(organization.id, team1.id, 5, 5, 5);
players.createPlayers(organization.id, team2.id, 3, 3, 3);

const invites = [
  Invite.toPendingDemoInstance(),
  Invite.toPendingDemoInstance(),
  Invite.toExpiredDemoInstance(),
];

const seasons = [Season.toDemoInstance()];

const emailTemplates = [
  EmailTemplate.toDemoInstance(EmailTemplateVisibility.Private),
  EmailTemplate.toDemoInstance(EmailTemplateVisibility.Company),
];

createEvents(teamId1);
createEvents(teamId2);

/* override only what we need, consumers are responsible to fill in the gaps */
const demoSDK: Partial<SDK> & { isDemo: true } = {
  isDemo: true,

  login: async () => ({
    token: "12345" as AccessToken,
  }),
  register: async () => undefined,

  getCurrentUser: async () => ({
    ...user,
    firstName: profile.firstName,
    lastName: profile.lastName,
  }),

  getCurrentProfile: async () => publicProfile,

  getCurrentPermissions: async () => [ownerPermission],

  getTeams: async () => teams,
  getAdminTeams: async () => teams,

  getTeam: async (_orgId, teamId) => teams.find((t) => t.id === teamId) as Team,

  getTeamPlayers: async (dto: GetTeamPlayerParams) => players.getTeamPlayers(dto),
  queryPlayers: async (dto: QueryPlayersParams) => players.queryPlayers(dto),

  getOrganizationUsers: async () => users,

  getRoles: async () => roles,

  getUnredeemedInvites: async () => invites,

  findGCSubscriptions: async () => [],

  getSeasons: async () => seasons,
  getCurrentSeason: async () => seasons[0],
  getSeason: async () => seasons[0],

  getEmailTemplates: async () => emailTemplates,

  getTeamEvents: async (_, teamId: TeamId) => getEvents(teamId),
};

export default demoSDK;
