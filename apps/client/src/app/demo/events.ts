import {
  CustomDate,
  EventScheduling,
  PositiveInteger,
  RecurringTeamEvent,
  TeamEvent,
  TeamId,
} from "@mio/helpers";

const events: TeamEvent.ExtendedTeamEvent[] = [];

export const getEvents = (teamId: TeamId) => events.filter((event) => event.teamId === teamId);

export const createEvents = (teamId: TeamId) => {
  const dates = RecurringTeamEvent.Entity.getWeeklyRecurringDates(
    EventScheduling.Type.Weekly,
    EventScheduling.DayOfWeek.Monday,
    CustomDate.now(),
    CustomDate.addDays(40 as PositiveInteger),
  );

  const recurringEvent = RecurringTeamEvent.Entity.toDemoInstance(
    undefined,
    undefined,
    undefined,
    teamId,
  );

  const instances = dates.map((date) => {
    return {
      ...TeamEvent.Entity.createRecurring(recurringEvent, date),
      recurringEvent,
    };
  });

  instances.forEach((event) => {
    events.push(event);
  });
};
