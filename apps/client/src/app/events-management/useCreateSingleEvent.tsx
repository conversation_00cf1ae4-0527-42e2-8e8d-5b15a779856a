import { FC, useState } from "react";
import { useTranslation } from "react-i18next";

import { isError, TeamEvent } from "@mio/helpers";
import {
  PageError,
  PageLoader,
  playersState,
  useCreateTeamEvent,
  useGetCurrentSeason,
  useOrganization,
  useRequiredTeam,
  Modal,
  Typography,
} from "@mio/ui";

import { SingleTeamEventForm } from "./single-event.form";

type Props = {
  onSuccess: (response: TeamEvent.SingleTeamEvent) => void;
};

const CreateSingleTeamEvent: FC<Props> = ({ onSuccess }) => {
  const { t } = useTranslation();
  const createTeamEvent = useCreateTeamEvent();
  const organization = useOrganization();
  const team = useRequiredTeam();
  const { query: teamPlayersQuery } = playersState.useTeamPlayers(team.id, organization.id);
  const currentSeason = useGetCurrentSeason();

  const handleSubmit = (data: unknown) => {
    const validated = TeamEvent.Entity.toCreateSingleDto(data);

    if (!isError(validated)) {
      createTeamEvent.mutate(validated, {
        onSuccess,
      });
    }
  };

  if (teamPlayersQuery.isLoading) {
    return <PageLoader message={t("events.single.loading-players")} />;
  }

  if (currentSeason.isLoading) {
    return <PageLoader message={t("events.single.loading-season")} />;
  }

  if (teamPlayersQuery.isError) {
    return <PageError message={t("events.single.load-players-error")} />;
  }

  if (currentSeason.isError) {
    return <PageError message={t("events.single.load-season-error")} />;
  }

  return (
    <SingleTeamEventForm
      onSubmit={handleSubmit}
      loading={createTeamEvent.isLoading}
      serverError={createTeamEvent.error}
      teamPlayers={teamPlayersQuery.data}
    />
  );
};

export const useCreateSingleEvent = (onSuccess: (event: TeamEvent.SingleTeamEvent) => void) => {
  const { t } = useTranslation();
  const [isActive, setActive] = useState(false);

  return {
    createEvent: () => setActive(true),
    CreateUI: (
      <Modal
        open={isActive}
        fullWidth
        onClose={() => setActive(false)}
        title={
          <Typography variant="h5" component="h2">
            {t("events.single.create-title")}
          </Typography>
        }
        content={
          <CreateSingleTeamEvent
            onSuccess={(event) => {
              setActive(false);
              onSuccess(event);
            }}
          />
        }
      />
    ),
  };
};
