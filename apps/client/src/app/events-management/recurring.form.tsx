import { FC } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, Controller, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";

import {
  Box,
  TextField,
  Stack,
  LocalError,
  FormLabel,
  Select,
  MenuItem,
  LoadingButton,
  FormControl,
  InputLabel,
  Card,
  Divider,
  useOrganization,
  useRequiredTeam,
  useProvidedCurrentProfile,
  useFormResolver,
} from "@mio/ui";
import {
  APIError,
  RecurringTeamEvent,
  PopulatedPlayer,
  EventShared,
  Season,
  ProfileId,
  PlayerId,
} from "@mio/helpers";

import Invitations from "./invitations";
import Hosts from "./hosts";
import SchedulingForm from "./scheduling.form";
import { allowedPlayerStatuses } from "./types";

type Props = {
  recurringEvent?: RecurringTeamEvent.RecurringTeamEvent;
  onSubmit: (data: RecurringTeamEvent.UpsertDto) => void;
  loading?: boolean;
  serverError?: APIError | null;
  teamPlayers: PopulatedPlayer[];
  currentSeason: Season | null;
};

export const RecurringTeamEventForm: FC<Props> = ({
  recurringEvent,
  onSubmit,
  loading,
  serverError,
  teamPlayers,
  currentSeason,
}) => {
  const { t } = useTranslation();
  const {
    formMethods,
    register,
    handleSubmit,
    control,
    errors,
    hosts,
    setHosts,
    invitations,
    addParticipant,
    removeParticipant,
  } = useEventForm(recurringEvent, teamPlayers);

  return (
    <Stack gap={3} component="form" onSubmit={handleSubmit(onSubmit)}>
      <Card component={Stack} gap={3} sx={{ p: 2 }}>
        <Divider component={FormLabel}>{t("events.form.general")}</Divider>

        <FormControl>
          <TextField
            fullWidth
            aria-required
            id="name"
            label={t("events.form.name")}
            name="name"
            inputProps={{ ...register("name") }}
            helperText={errors.name?.message || ""}
            error={!!errors.name?.message}
            aria-invalid={!!errors.name?.message}
          />
        </FormControl>

        <FormControl required>
          <InputLabel htmlFor="agenda">{t("events.form.agenda")}</InputLabel>
          <Controller<RecurringTeamEvent.UpsertDto>
            name="agenda"
            control={control}
            render={(params) => (
              <Select
                fullWidth
                label={t("events.form.agenda")}
                inputProps={{ ...params.field }}
                id="agenda"
              >
                <MenuItem value={EventShared.Agenda.TrainingSession}>
                  {t("events.form.agenda-options.training")}
                </MenuItem>
                <MenuItem value={EventShared.Agenda.Match}>
                  {t("events.form.agenda-options.match")}
                </MenuItem>
                <MenuItem value={EventShared.Agenda.Meeting}>
                  {t("events.form.agenda-options.meeting")}
                </MenuItem>
                <MenuItem value={EventShared.Agenda.Other}>
                  {t("events.form.agenda-options.other")}
                </MenuItem>
              </Select>
            )}
          />
        </FormControl>

        <FormControl>
          <TextField
            fullWidth
            aria-required
            multiline
            minRows={2}
            id="description"
            name="description"
            label={t("events.form.description")}
            placeholder={t("events.form.description-placeholder")}
            inputProps={{ ...register("description") }}
            helperText={errors.description?.message || ""}
            error={!!errors.description?.message}
            aria-invalid={!!errors.description?.message}
          />
        </FormControl>

        <Hosts onChange={setHosts} hosts={hosts} />

        <Invitations
          onAdd={addParticipant}
          onRemove={removeParticipant}
          selectedPlayers={invitations}
        />

        <FormControl>
          <TextField
            fullWidth
            multiline
            rows={3}
            label={<FormLabel>{t("events.form.location-address")}</FormLabel>}
            id="location.address"
            name="location.address"
            inputProps={{ ...register("location.address") }}
            helperText={errors.location?.address?.message || ""}
            error={!!errors.location?.address?.message}
            aria-invalid={!!errors.location?.address?.message}
          />
        </FormControl>

        <FormControl>
          <TextField
            fullWidth
            label={<FormLabel>{t("events.form.location-postcode")}</FormLabel>}
            id="location.postcode"
            name="location.postcode"
            inputProps={{ ...register("location.postcode") }}
            helperText={errors.location?.postcode?.message || ""}
            error={!!errors.location?.postcode?.message}
            aria-invalid={!!errors.location?.postcode?.message}
          />
        </FormControl>
      </Card>

      <Card>
        <FormProvider {...formMethods}>
          <SchedulingForm currentSeason={currentSeason} />
        </FormProvider>
      </Card>

      <Box gap={2} sx={{ display: "flex", justifyContent: "center" }}>
        <LoadingButton type="submit" variant="contained" loading={loading}>
          {t("events.form.save")}
        </LoadingButton>
      </Box>

      {serverError && <LocalError message={t("events.form.error")} />}
    </Stack>
  );
};

export const useEventForm = (
  event: RecurringTeamEvent.RecurringTeamEvent | undefined,
  teamPlayers: PopulatedPlayer[],
) => {
  const organization = useOrganization();
  const team = useRequiredTeam();
  const currentProfile = useProvidedCurrentProfile();
  const formResolver = useFormResolver<RecurringTeamEvent.UpsertDto>(
    RecurringTeamEvent.Entity.toUpsertDto,
  );

  const formMethods = useForm<RecurringTeamEvent.UpsertDto>({
    defaultValues: {
      id: event?.id,
      name: event?.name,
      description: event?.description,
      location: event?.location,
      status: RecurringTeamEvent.Status.Active,
      agenda: event?.agenda || EventShared.Agenda.TrainingSession,
      invitations:
        event?.invitations ||
        teamPlayers
          .filter((player) =>
            player.profiles.some((profile) => allowedPlayerStatuses.includes(profile.status)),
          )
          .map((elem) => elem.id),
      hosts: event?.hosts || [currentProfile.id],

      organizationId: organization.id,
      teamId: team.id,
      schedule: event?.schedule,
    },
    resolver: formResolver,
  });

  const { register, handleSubmit, setValue, watch, formState } = formMethods;

  const hosts = (watch("hosts") || []) as ProfileId[];
  const invitations = (watch("invitations") || []) as PlayerId[];

  const { errors, submitCount } = formState;
  const hasSubmitted = submitCount > 0;

  const addParticipant = (id: PlayerId) => {
    setValue("invitations", Array.from(new Set([...invitations, id])), {
      shouldValidate: true,
      shouldDirty: true,
      shouldTouch: true,
    });
  };

  const removeParticipant = (id: PlayerId) => {
    setValue(
      "invitations",
      invitations.filter((elem) => elem !== id),
      {
        shouldValidate: true,
        shouldDirty: true,
        shouldTouch: true,
      },
    );
  };

  return {
    register,
    handleSubmit,
    control: formMethods.control,
    errors: hasSubmitted ? errors : {},
    setValue,
    setHosts: (newValues: ProfileId[]) =>
      setValue("hosts", newValues, {
        shouldValidate: true,
        shouldDirty: true,
        shouldTouch: true,
      }),
    hosts,
    addParticipant,
    removeParticipant,
    invitations,
    formMethods,
  };
};
