import { FC } from "react";
import { Controller, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";

import {
  Box,
  TextField,
  Stack,
  LocalError,
  FormLabel,
  Select,
  MenuItem,
  LoadingButton,
  FormControl,
  InputLabel,
  Card,
  useOrganization,
  useRequiredTeam,
  useProvidedCurrentProfile,
  useFormResolver,
  DateTimePicker,
} from "@mio/ui";
import {
  APIError,
  PopulatedPlayer,
  EventShared,
  TeamEvent,
  PlayerId,
  ProfileId,
  CustomDate,
  PositiveInteger,
} from "@mio/helpers";

import Invitations from "./invitations";
import Hosts from "./hosts";

import { allowedPlayerStatuses } from "./types";

type Props = {
  event?: TeamEvent.SingleTeamEvent;
  onSubmit: (data: TeamEvent.UpsertSingleDto) => void;
  loading?: boolean;
  serverError?: APIError | null;
  teamPlayers: PopulatedPlayer[];
};

export const SingleTeamEventForm: FC<Props> = ({
  event,
  onSubmit,
  loading,
  serverError,
  teamPlayers,
}) => {
  const { t } = useTranslation();
  const {
    register,
    handleSubmit,
    control,
    errors,
    hosts,
    setHosts,
    invitations,
    addParticipant,
    removeParticipant,
  } = useEventForm(event, teamPlayers);

  return (
    <Stack gap={3} component="form" onSubmit={handleSubmit(onSubmit)}>
      <Card component={Stack} gap={3} sx={{ p: 2 }}>
        <FormControl>
          <TextField
            fullWidth
            aria-required
            id="name"
            label={t("events.form.name")}
            name="name"
            inputProps={{ ...register("name") }}
            helperText={errors.name?.message || ""}
            error={!!errors.name?.message}
            aria-invalid={!!errors.name?.message}
          />
        </FormControl>

        <FormControl required>
          <Controller<TeamEvent.UpsertSingleDto>
            name="startDateTime"
            control={control}
            defaultValue={CustomDate.now()}
            render={({ field: { onChange, ...restField } }) => (
              <DateTimePicker
                label={t("events.form.start-date-time")}
                onChange={(newValue) => {
                  onChange(CustomDate.validOrEmpty(newValue));
                }}
                renderInput={(params) => (
                  <TextField
                    fullWidth
                    helperText={errors.startDateTime?.message || ""}
                    error={!!errors.startDateTime?.message}
                    aria-invalid={!!errors.startDateTime?.message}
                    {...params}
                  />
                )}
                {...restField}
              />
            )}
          />
        </FormControl>

        <FormControl required>
          <Controller<TeamEvent.UpsertSingleDto>
            name="endDateTime"
            control={control}
            defaultValue={CustomDate.addMinutes(60 as PositiveInteger)}
            render={({ field: { onChange, ...restField } }) => (
              <DateTimePicker
                label={t("events.form.end-date-time")}
                onChange={(newValue) => {
                  onChange(CustomDate.validOrEmpty(newValue));
                }}
                renderInput={(params) => (
                  <TextField
                    fullWidth
                    helperText={errors.endDateTime?.message || ""}
                    error={!!errors.endDateTime?.message}
                    aria-invalid={!!errors.endDateTime?.message}
                    {...params}
                  />
                )}
                {...restField}
              />
            )}
          />
        </FormControl>

        <FormControl required>
          <InputLabel htmlFor="agenda">{t("events.form.agenda")}</InputLabel>
          <Controller<TeamEvent.UpsertSingleDto>
            name="agenda"
            control={control}
            render={(params) => (
              <Select
                fullWidth
                label={t("events.form.agenda")}
                inputProps={{ ...params.field }}
                id="agenda"
              >
                <MenuItem value={EventShared.Agenda.TrainingSession}>
                  {t("events.form.agenda-options.training")}
                </MenuItem>
                <MenuItem value={EventShared.Agenda.Match}>
                  {t("events.form.agenda-options.match")}
                </MenuItem>
                <MenuItem value={EventShared.Agenda.Meeting}>
                  {t("events.form.agenda-options.meeting")}
                </MenuItem>
                <MenuItem value={EventShared.Agenda.Other}>
                  {t("events.form.agenda-options.other")}
                </MenuItem>
              </Select>
            )}
          />
        </FormControl>

        <FormControl>
          <TextField
            fullWidth
            multiline
            minRows={2}
            id="description"
            name="description"
            label={t("events.form.description")}
            placeholder={t("events.form.description-placeholder")}
            inputProps={{ ...register("description") }}
            helperText={errors.description?.message || ""}
            error={!!errors.description?.message}
            aria-invalid={!!errors.description?.message}
          />
        </FormControl>

        <Hosts onChange={setHosts} hosts={hosts} />

        <Invitations
          onAdd={addParticipant}
          onRemove={removeParticipant}
          selectedPlayers={invitations}
        />

        <FormControl required>
          <TextField
            fullWidth
            multiline
            rows={3}
            label={<FormLabel>{t("events.form.location-address")}</FormLabel>}
            id="location.address"
            name="location.address"
            inputProps={{ ...register("location.address") }}
            helperText={errors.location?.address?.message || ""}
            error={!!errors.location?.address?.message}
            aria-invalid={!!errors.location?.address?.message}
          />
        </FormControl>

        <FormControl required>
          <TextField
            fullWidth
            label={<FormLabel>{t("events.form.location-postcode")}</FormLabel>}
            id="location.postcode"
            name="location.postcode"
            inputProps={{ ...register("location.postcode") }}
            helperText={errors.location?.postcode?.message || ""}
            error={!!errors.location?.postcode?.message}
            aria-invalid={!!errors.location?.postcode?.message}
          />
        </FormControl>
      </Card>

      <Box gap={2} sx={{ display: "flex", justifyContent: "center" }}>
        <LoadingButton type="submit" variant="contained" loading={loading}>
          {t("events.form.save")}
        </LoadingButton>
      </Box>

      {serverError && <LocalError message={t("events.form.error")} />}
    </Stack>
  );
};

export const useEventForm = (
  event: TeamEvent.SingleTeamEvent | undefined,
  teamPlayers: PopulatedPlayer[],
) => {
  const organization = useOrganization();
  const team = useRequiredTeam();
  const currentProfile = useProvidedCurrentProfile();
  const formResolver = useFormResolver<TeamEvent.UpsertSingleDto>(
    TeamEvent.Entity.toUpsertSingleDto,
  );

  const formMethods = useForm<TeamEvent.UpsertSingleDto>({
    defaultValues: {
      id: event?.id,
      organizationId: organization.id,
      teamId: team.id,

      name: event?.name,
      description: event?.description,
      location: event?.location,
      agenda: event?.agenda || EventShared.Agenda.TrainingSession,

      startDateTime: event?.startDateTime,
      endDateTime: event?.endDateTime,

      invitations:
        event?.invitations ||
        teamPlayers
          .filter((player) =>
            player.profiles.some((profile) => allowedPlayerStatuses.includes(profile.status)),
          )
          .map((elem) => elem.id),
      hosts: event?.hosts || [currentProfile.id],
    },
    resolver: formResolver,
  });

  const { register, handleSubmit, setValue, watch, formState } = formMethods;

  const hosts = (watch("hosts") || []) as ProfileId[];
  const invitations = (watch("invitations") || []) as PlayerId[];

  const { errors, submitCount } = formState;
  const hasSubmitted = submitCount > 0;

  const addParticipant = (id: PlayerId) => {
    setValue("invitations", Array.from(new Set([...invitations, id])), {
      shouldValidate: true,
      shouldDirty: true,
      shouldTouch: true,
    });
  };

  const removeParticipant = (id: PlayerId) => {
    setValue(
      "invitations",
      invitations.filter((elem) => elem !== id),
      {
        shouldValidate: true,
        shouldDirty: true,
        shouldTouch: true,
      },
    );
  };

  return {
    register,
    handleSubmit,
    control: formMethods.control,
    errors: hasSubmitted ? errors : {},
    setValue,
    setHosts: (newValues: ProfileId[]) =>
      setValue("hosts", newValues, {
        shouldValidate: true,
        shouldDirty: true,
        shouldTouch: true,
      }),
    hosts,
    addParticipant,
    removeParticipant,
    invitations,
    formMethods,
  };
};
