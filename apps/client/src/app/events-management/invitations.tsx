import { FC, useMemo } from "react";
import { useTranslation } from "react-i18next";

import { Player, PlayerId } from "@mio/helpers";
import {
  FormControl,
  Stack,
  FormGroup,
  FormLabel,
  FormControlLabel,
  Checkbox,
  playersState,
  useOrganization,
  useRequiredTeam,
  Button,
  LocalLoader,
  LocalError,
  Card,
} from "@mio/ui";
import { allowedPlayerStatuses } from "./types";

type Props = {
  selectedPlayers: PlayerId[];
  onAdd: (value: PlayerId) => void;
  onRemove: (value: PlayerId) => void;
};

const Invitations: FC<Props> = ({ onAdd, onRemove, selectedPlayers }) => {
  const { t } = useTranslation();
  const organization = useOrganization();
  const team = useRequiredTeam();
  const { query: teamPlayersQuery } = playersState.useTeamPlayers(team.id, organization.id);

  const selected = useMemo(() => {
    return new Set(selectedPlayers);
  }, [selectedPlayers]);

  return (
    <FormControl component="fieldset" sx={{ borderColor: "black", borderWidth: 1 }}>
      <FormLabel component="legend" sx={{ mb: 1 }}>
        {t("events.invitations.title")}
      </FormLabel>

      {teamPlayersQuery.isError && (
        <Stack gap={1}>
          <LocalError message={t("events.invitations.load-error")} />
          <Button variant="outlined" onClick={() => teamPlayersQuery.refetch()}>
            {t("events.invitations.retry")}
          </Button>
        </Stack>
      )}

      {teamPlayersQuery.isLoading && <LocalLoader message={t("events.invitations.loading")} />}

      {teamPlayersQuery.isSuccess && (
        <Stack component={FormGroup} direction="row" flexWrap="wrap" gap={2}>
          {teamPlayersQuery.data
            .filter((player) =>
              player.profiles.some((profile) => allowedPlayerStatuses.includes(profile.status)),
            )
            .map((player) => {
              return (
                <Card sx={{ p: 1 }} key={player.id}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={selected.has(player.id)}
                        value={player.id}
                        onChange={(event) => {
                          const isChecked = event.target.checked;

                          if (isChecked) {
                            onAdd(player.id);
                          } else {
                            onRemove(player.id);
                          }
                        }}
                      />
                    }
                    label={Player.getFullName(player)}
                  />
                </Card>
              );
            })}
        </Stack>
      )}
    </FormControl>
  );
};

export default Invitations;
