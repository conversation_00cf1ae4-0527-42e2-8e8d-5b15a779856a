import { useMemo } from "react";
import { useTranslation } from "react-i18next";

import { CustomDate, CustomNumber, TeamEvent } from "@mio/helpers";
import {
  <PERSON>Error,
  PageLoader,
  Stack,
  Typography,
  useGetTeamEvents,
  Card,
  Divider,
  useGetCurrentSeason,
  Button,
  AddIcon,
} from "@mio/ui";

import { useCreateRecurringEvent } from "./useCreateRecurringEvent";
import { useEditRecurringEvent } from "./useEditRecurringEvent";
import { TeamEventsFilters } from "./filters";
import { useCreateSingleEvent } from "./useCreateSingleEvent";
import EventItem from "./event-item";
import { groupEventsByDate } from "../shared";

export const EventsList = () => {
  const { t } = useTranslation();
  const currentSeason = useGetCurrentSeason();

  const initialDatePeriod = TeamEvent.Entity.constructFutureRange(
    CustomNumber.castToPositiveInteger(30),
  );
  const { query, setQuery, addEvent } = useGetTeamEvents({
    ...initialDatePeriod,
  });
  const { EditUI: EditRecurringUI } = useEditRecurringEvent();
  const { createEvent: createSingleEvent, CreateUI: CreateSingleUI } = useCreateSingleEvent(
    (event) => {
      addEvent(event);
      query.refetch();
    },
  );
  const { createEvent: createRecurringEvent, CreateUI: CreateRecurringUI } =
    useCreateRecurringEvent(() => {
      query.refetch();
    });

  const eventsByDate = useMemo(() => {
    if (query.data) {
      return groupEventsByDate(query.data);
    }
    return [] as const;
  }, [query.data]);

  if (query.isLoading || currentSeason.isLoading) {
    return <PageLoader message={t("common.loading")} />;
  }

  if (query.isError || currentSeason.isError) {
    return <PageError message={t("common.error")} />;
  }

  const handleSubmit = (data: TeamEvent.QueryDto) => {
    setQuery(data);
  };

  return (
    <Stack gap={4}>
      <Stack
        component="header"
        flexDirection="row"
        gap={5}
        mb={2}
        alignItems="center"
        flexWrap="wrap"
      >
        <Typography variant="h4" component="h1">
          {t("events.title")}
        </Typography>

        <Stack gap={2} direction="row">
          <Button variant="outlined" startIcon={<AddIcon />} onClick={createSingleEvent}>
            {t("events.create-single")}
          </Button>

          <Button variant="outlined" startIcon={<AddIcon />} onClick={createRecurringEvent}>
            {t("events.create-recurring")}
          </Button>
        </Stack>
      </Stack>

      <TeamEventsFilters onSubmit={handleSubmit} />

      <Stack gap={4}>
        {eventsByDate.map((item) => (
          <Card key={item.date} sx={{ p: 2 }}>
            <Divider>{CustomDate.toDisplayDate(item.date)}</Divider>

            <Stack gap={2}>
              {item.events.map((event) => (
                <EventItem event={event} />
              ))}
            </Stack>
          </Card>
        ))}
      </Stack>

      {CreateRecurringUI}

      {EditRecurringUI}

      {CreateSingleUI}
    </Stack>
  );
};
