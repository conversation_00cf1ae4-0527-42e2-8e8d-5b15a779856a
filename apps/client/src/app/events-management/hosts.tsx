import { FC, useMemo } from "react";
import { useTranslation } from "react-i18next";

import { ProfileId, Profile } from "@mio/helpers";
import {
  Autocomplete,
  FormControl,
  FormLabel,
  TextField,
  useProvidedCurrentProfile,
  useTeamCoaches,
} from "@mio/ui";

type Props = {
  hosts: ProfileId[];
  onChange: (hosts: ProfileId[]) => void;
};

const Hosts: FC<Props> = ({ hosts, onChange }) => {
  const { t } = useTranslation();
  const myProfile = useProvidedCurrentProfile();
  const teamCoaches = useTeamCoaches();

  const coaches = useMemo(() => teamCoaches.data || [], [teamCoaches.data]);

  return (
    <FormControl component="fieldset" sx={{ borderColor: "black", borderWidth: 1 }}>
      <FormLabel component="legend" sx={{ mb: 1 }} htmlFor="hosts-select">
        {t("events.hosts.title")}
      </FormLabel>

      <Autocomplete<Profile, true>
        multiple
        id="hosts-select"
        options={coaches}
        value={coaches.filter((elem) => hosts.includes(elem.id))}
        getOptionLabel={(profile) =>
          profile.id === myProfile.id ? t("events.hosts.you") : Profile.getFullName(profile)
        }
        isOptionEqualToValue={(option, value) => option.id === value.id}
        renderInput={(params) => (
          <TextField {...params} placeholder={t("events.hosts.add-placeholder")} />
        )}
        onChange={(_, newValue) => {
          onChange(newValue.map((elem) => elem.id));
        }}
      />
    </FormControl>
  );
};

export default Hosts;
