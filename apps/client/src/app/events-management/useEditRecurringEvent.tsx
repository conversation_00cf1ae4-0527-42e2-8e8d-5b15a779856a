import { FC, useState } from "react";
import { useTranslation } from "react-i18next";

import { isError, RecurringTeamEvent } from "@mio/helpers";
import {
  Modal,
  PageError,
  PageLoader,
  playersState,
  Typography,
  useGetCurrentSeason,
  useOrganization,
  useRequiredTeam,
  useUpdateRecurringTeamEvent,
} from "@mio/ui";

import { RecurringTeamEventForm } from "./recurring.form";

type Props = {
  event: RecurringTeamEvent.RecurringTeamEvent;
  onSuccess: (response: RecurringTeamEvent.RecurringTeamEvent) => void;
};

const EditRecurringTeamEvent: FC<Props> = ({ onSuccess, event }) => {
  const { t } = useTranslation();
  const organization = useOrganization();
  const team = useRequiredTeam();

  const { query: teamPlayersQuery } = playersState.useTeamPlayers(team.id, organization.id);
  const currentSeason = useGetCurrentSeason();

  const updateRecurringTeamEvent = useUpdateRecurringTeamEvent();

  const handleSubmit = (data: unknown) => {
    const validated = RecurringTeamEvent.Entity.toUpdateDto(data);

    if (!isError(validated)) {
      updateRecurringTeamEvent.mutate(validated, {
        onSuccess,
      });
    }
  };

  if (teamPlayersQuery.isLoading) {
    return <PageLoader message={t("events.recurring.loading-players")} />;
  }

  if (currentSeason.isLoading) {
    return <PageLoader message={t("events.recurring.loading-season")} />;
  }

  if (currentSeason.isError) {
    return <PageError message={t("events.recurring.load-season-error")} />;
  }

  if (teamPlayersQuery.isError) {
    return <PageError message={t("events.recurring.load-players-error")} />;
  }

  return (
    <RecurringTeamEventForm
      onSubmit={handleSubmit}
      recurringEvent={event}
      loading={updateRecurringTeamEvent.isLoading}
      serverError={updateRecurringTeamEvent.error}
      teamPlayers={teamPlayersQuery.data}
      currentSeason={currentSeason.data}
    />
  );
};

export const useEditRecurringEvent = () => {
  const { t } = useTranslation();
  const [event, setEvent] = useState<RecurringTeamEvent.RecurringTeamEvent | undefined>(undefined);

  return {
    editEvent: (event: RecurringTeamEvent.RecurringTeamEvent) => setEvent(event),
    EditUI: (
      <Modal
        open={!!event}
        fullWidth
        onClose={() => setEvent(undefined)}
        title={
          <Typography variant="h5" component="h2">
            {t("events.recurring.edit-title")}
          </Typography>
        }
        content={
          event ? (
            <EditRecurringTeamEvent onSuccess={() => setEvent(undefined)} event={event} />
          ) : undefined
        }
      />
    ),
  };
};
