import { FC, useState } from "react";
import { useTranslation } from "react-i18next";

import { isError, RecurringTeamEvent } from "@mio/helpers";
import {
  PageError,
  PageLoader,
  playersState,
  useCreateRecurringTeamEvent,
  useGetCurrentSeason,
  useOrganization,
  useRequiredTeam,
  Modal,
  Typography,
} from "@mio/ui";

import { RecurringTeamEventForm } from "./recurring.form";

type Props = {
  onSuccess: (response: RecurringTeamEvent.RecurringTeamEvent) => void;
};

const AddRecurringTeamEvent: FC<Props> = ({ onSuccess }) => {
  const { t } = useTranslation();
  const createTeamEvent = useCreateRecurringTeamEvent();
  const organization = useOrganization();
  const team = useRequiredTeam();
  const { query: teamPlayersQuery } = playersState.useTeamPlayers(team.id, organization.id);
  const currentSeason = useGetCurrentSeason();

  const handleSubmit = (data: unknown) => {
    const validated = RecurringTeamEvent.Entity.toCreateDto(data);

    if (!isError(validated)) {
      createTeamEvent.mutate(validated, {
        onSuccess,
      });
    }
  };

  if (teamPlayersQuery.isLoading) {
    return <PageLoader message={t("events.recurring.loading-players")} />;
  }

  if (currentSeason.isLoading) {
    return <PageLoader message={t("events.recurring.loading-season")} />;
  }

  if (teamPlayersQuery.isError) {
    return <PageError message={t("events.recurring.load-players-error")} />;
  }

  if (currentSeason.isError) {
    return <PageError message={t("events.recurring.load-season-error")} />;
  }

  return (
    <RecurringTeamEventForm
      onSubmit={handleSubmit}
      loading={createTeamEvent.isLoading}
      serverError={createTeamEvent.error}
      teamPlayers={teamPlayersQuery.data}
      currentSeason={currentSeason.data}
    />
  );
};

export const useCreateRecurringEvent = (
  onSuccess: (event: RecurringTeamEvent.RecurringTeamEvent) => void,
) => {
  const { t } = useTranslation();
  const [isActive, setActive] = useState(false);

  return {
    createEvent: () => setActive(true),
    CreateUI: (
      <Modal
        open={isActive}
        fullWidth
        onClose={() => setActive(false)}
        title={
          <Typography variant="h5" component="h2">
            {t("events.recurring.create-title")}
          </Typography>
        }
        content={
          <AddRecurringTeamEvent
            onSuccess={(event) => {
              setActive(false);
              onSuccess(event);
            }}
          />
        }
      />
    ),
  };
};
