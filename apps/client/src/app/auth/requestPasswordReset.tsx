import { FC } from "react";
import { useTranslation } from "react-i18next";

import { AppLink, Box, Container, PasswordReset, Typography, Card, Stack } from "@mio/ui";
import { UserTypes } from "@mio/helpers";

import { BackgroundLayout } from "../layouts";
import { LogoWithText } from "../shared";

export const RequestPasswordReset: FC = () => {
  const { t } = useTranslation();

  return (
    <BackgroundLayout imageUrl="/assets/stadium2.png">
      <Container
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          flexDirection: "column",
          height: "100%",
          pt: { xs: 0, md: 5 },
          pl: { xs: 0 },
          pr: { xs: 0 },
        }}
      >
        <Card sx={{ width: { md: "50%", xs: "100%" }, boxShadow: { md: 3, xs: 0 } }}>
          <Stack
            sx={{
              minWidth: [1, 1, 1 / 2],
              padding: 2,
              gap: { md: 3, xs: 2 },
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Box width="200px">
              <LogoWithText variant="dark" />
            </Box>

            <Typography variant="h4" component="h1">
              {t("auth.reset-password")}
            </Typography>

            <Typography variant="h5" component="h2">
              {t("auth.enter-email")}
            </Typography>

            <Box width={1}>
              <PasswordReset.Request.Container type={UserTypes.Coach} />
            </Box>

            <Box>
              <AppLink to="/login">{t("auth.back-to-login")}</AppLink>
            </Box>
          </Stack>
        </Card>
      </Container>
    </BackgroundLayout>
  );
};
