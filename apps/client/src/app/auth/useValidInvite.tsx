import { useParams } from "react-router-dom";

import { Invite, InviteId, isError, UUID, UrlParams } from "@mio/helpers";
import { useInvite } from "@mio/ui";

/* ensures we have valid UUID param in the current URL */
const useInviteId = () => {
  const params = useParams();

  const validated = UUID.parse<InviteId>(params[UrlParams.InviteId]);

  return validated;
};

export const useValidInvite = () => {
  const id = useInviteId();

  const inviteQuery = useInvite({ id: isError(id) ? undefined : id });

  if (inviteQuery.status === "error" || inviteQuery.status === "loading") {
    return { status: inviteQuery.status } as const;
  }

  if (!inviteQuery.data) {
    return { status: "invalidId" } as const;
  }

  /* Non-expired/redeemed invites only */
  if (Invite.isPending(inviteQuery.data)) {
    return { invite: inviteQuery.data, status: inviteQuery.status } as const;
  }

  /* Expired/redeemed invites go here */
  return { status: "invalid" } as const;
};
