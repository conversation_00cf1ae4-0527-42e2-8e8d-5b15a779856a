import { FC } from "react";
import { useTranslation } from "react-i18next";

import {
  Typo<PERSON>,
  <PERSON>Loader,
  PageError,
  CenteredLayout,
  Stack,
  usePublicOrganization,
  LoadingButton,
  useJoinOrganization,
  Box,
} from "@mio/ui";
import { InviteId } from "@mio/helpers";

import { useValidInvite } from "./useValidInvite";

const JoinOrganization: FC = () => {
  const { t } = useTranslation();
  const inviteQuery = useValidInvite();
  const { query } = usePublicOrganization();
  const joinOrganization = useJoinOrganization();

  const handleAccept = (inviteId: InviteId) => {
    joinOrganization.mutate(inviteId, {
      onSuccess: () => {
        window.location.assign("");
      },
    });
  };

  return (
    <CenteredLayout>
      <Typography component="h1" variant="h2" mb={5} mt={2} textAlign="center">
        {t("auth.team-assist")}
      </Typography>

      <Stack
        sx={{ minWidth: [1, 1, 1 / 2], padding: 2, border: `1px solid gray`, textAlign: "center" }}
        gap={3}
      >
        {query.isSuccess && (
          <Typography variant="h4" component="h2">
            {t("auth.join-organization-title", { orgName: query.data?.displayName })}
          </Typography>
        )}

        {inviteQuery.status === "loading" && <PageLoader />}

        {inviteQuery.status === "error" && <PageError message={t("auth.invite-fetch-error")} />}

        {inviteQuery.status === "invalidId" && <PageError message={t("auth.invalid-invite")} />}

        {inviteQuery.status === "invalid" && <PageError message={t("auth.expired-invite")} />}

        {joinOrganization.isError && <PageError message={t("auth.generic-error")} />}

        {inviteQuery.status === "success" && (
          <Box>
            <LoadingButton
              variant="contained"
              onClick={() => handleAccept(inviteQuery.invite.id)}
              loading={joinOrganization.isLoading}
            >
              {t("auth.accept-invite")}
            </LoadingButton>
          </Box>
        )}
      </Stack>
    </CenteredLayout>
  );
};

export default JoinOrganization;
