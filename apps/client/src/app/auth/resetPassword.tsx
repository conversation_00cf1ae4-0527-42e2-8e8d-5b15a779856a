import { FC, useState } from "react";
import { useTranslation } from "react-i18next";

import { AppLink, Box, PasswordReset, Typography, Card, Stack } from "@mio/ui";
import { UserTypes } from "@mio/helpers";

import { BackgroundLayout } from "../layouts";
import { LogoWithText } from "../shared";

const imageUrls = ["/assets/stadium.png", "/assets/stadium2.png"];

export const ResetPassword: FC = () => {
  const { t } = useTranslation();
  const [visiblePassword, toggleVisiblePassword] = useState(false);

  const bgImageUrl = visiblePassword ? imageUrls[0] : imageUrls[1];

  return (
    <BackgroundLayout imageUrl={bgImageUrl}>
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          flexDirection: "column",
          height: "100%",
          pt: { xs: 0, md: 5 },
          pl: { xs: 0 },
          pr: { xs: 0 },
        }}
      >
        <Card sx={{ width: { md: "50%", xs: "100%" }, boxShadow: { md: 3, xs: 0 } }}>
          <Stack
            sx={{
              minWidth: [1, 1, 1 / 2],
              padding: 2,
              justifyContent: "center",
              alignItems: "center",
              gap: { xs: 2, md: 3 },
            }}
          >
            <Box width="200px">
              <LogoWithText variant="dark" />
            </Box>

            <Typography variant="h4" component="h1">
              {t("auth.reset-password")}
            </Typography>

            <Typography variant="h5" mb={3} component="h2">
              {t("auth.enter-new-password")}
            </Typography>

            <Box width={1}>
              <PasswordReset.Update.Container
                type={UserTypes.Coach}
                onPasswordToggle={toggleVisiblePassword}
              />
            </Box>

            <Box mt={2}>
              <AppLink to="/login">{t("auth.back-to-login")}</AppLink>
            </Box>
          </Stack>
        </Card>
      </Box>
    </BackgroundLayout>
  );
};
