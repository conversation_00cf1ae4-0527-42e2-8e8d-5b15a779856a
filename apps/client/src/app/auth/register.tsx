import { FC, useState } from "react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";

import {
  Typography,
  Container,
  Box,
  visuallyHidden,
  CircularProgress,
  Alert,
  Register,
  Card,
  Button,
} from "@mio/ui";

import { LogoWithText } from "../shared";
import { useValidInvite } from "./useValidInvite";
import { BackgroundLayout } from "../layouts";

const imageUrls = ["/assets/stadium.png", "/assets/stadium2.png"];

export const RegisterPage: FC = () => {
  const { t } = useTranslation();
  const inviteQuery = useValidInvite();
  const [success, setResult] = useState(false);
  const [visiblePassword, toggleVisiblePassword] = useState(false);

  const bgImageUrl = visiblePassword ? imageUrls[0] : imageUrls[1];

  return (
    <BackgroundLayout imageUrl={bgImageUrl}>
      <Container
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "flex-start",
          flexDirection: "column",
          height: "100%",
          pt: { xs: 0, md: 5 },
          pl: { xs: 0 },
          pr: { xs: 0 },
        }}
      >
        <Typography component="h1" variant="h2" mb={5} sx={{ ...visuallyHidden }}>
          {t("auth.registration-page")}
        </Typography>
        <Card sx={{ width: { md: "50%", xs: "100%" }, boxShadow: { md: 3, xs: 0 } }}>
          <Box
            sx={{
              minWidth: [1, 1, 1 / 2],
              padding: 2,
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              gap: { md: 3, xs: 2 },
            }}
          >
            <Box width="200px">
              <LogoWithText variant="dark" />
            </Box>

            <Typography variant="h5" component="h2" mb={3}>
              {t("auth.register")}
            </Typography>

            {inviteQuery.status === "loading" && (
              <Box mb={3}>
                <CircularProgress />
              </Box>
            )}

            {inviteQuery.status === "error" && (
              <Box mb={3}>
                <Alert severity="error">
                  <Typography variant="body1">{t("auth.invite-fetch-error")}</Typography>
                </Alert>
              </Box>
            )}

            {inviteQuery.status === "invalidId" && (
              <Box mb={3}>
                <Alert severity="error">
                  <Typography variant="body1">{t("auth.invalid-invite")}</Typography>
                </Alert>
              </Box>
            )}

            {inviteQuery.status === "invalid" && (
              <Box mb={3}>
                <Alert severity="error">
                  <Typography variant="body1">{t("auth.expired-invite")}</Typography>
                </Alert>
              </Box>
            )}

            {inviteQuery.status === "success" && (
              <Box sx={{ width: "100%" }}>
                <Register.Container
                  email={inviteQuery.invite.email}
                  invite={inviteQuery.invite.id}
                  onSuccess={() => setResult(true)}
                  onPasswordToggle={toggleVisiblePassword}
                />
              </Box>
            )}

            {success && (
              <Box mt={2} sx={{ display: "flex", justifyContent: "center" }}>
                <Button component={Link} to="/login" variant="contained" size="large">
                  {t("auth.login")}
                </Button>
              </Box>
            )}
          </Box>
        </Card>
      </Container>
    </BackgroundLayout>
  );
};
