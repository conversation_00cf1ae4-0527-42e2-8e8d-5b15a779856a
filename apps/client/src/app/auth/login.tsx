import { FC, useEffect, useState } from "react";
import { useAudio } from "react-use";
import { useTranslation } from "react-i18next";

import {
  Typography,
  Container,
  Login,
  Box,
  AppLink,
  Stack,
  Card,
  useScreenSize,
  visuallyHidden,
} from "@mio/ui";
import { UserTypes } from "@mio/helpers";

import { LogoWithText } from "../shared";
import { BackgroundLayout } from "../layouts";

const preloadImages = (urls: string[]) => {
  urls.forEach((url) => {
    const img = new Image();
    img.src = url;
  });
};

const imageUrls = ["/assets/stadium.png", "/assets/stadium2.png"];

export const LoginPage: FC = () => {
  const { t } = useTranslation();
  const [didLogin, setDidLogin] = useState(false);
  const [visiblePassword, toggleVisiblePassword] = useState(false);
  const [audio, , controls] = useAudio({
    src: "/assets/crowd-cheer.mp3",
    autoPlay: false,
    preload: "auto",
  });
  const { smallScreen } = useScreenSize();

  useEffect(() => {
    preloadImages([imageUrls[0]]);
  }, []);

  const bgImageUrl = didLogin || visiblePassword ? imageUrls[0] : imageUrls[1];

  return (
    <BackgroundLayout imageUrl={bgImageUrl}>
      <Container
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "flex-start",
          flexDirection: "column",
          height: "100%",
          pt: { xs: 0, md: 5 },
          pl: { xs: 0 },
          pr: { xs: 0 },
        }}
      >
        {audio}

        <Typography sx={{ ...visuallyHidden }} variant="h1">
          {t("auth.welcome-to-team-assist")}
        </Typography>

        <Typography
          aria-hidden={true}
          variant={smallScreen ? "h5" : "h3"}
          maxWidth={smallScreen ? "auto" : "30vw"}
          component="h1"
          color={{ sx: "primaryText", md: "primary.contrastText" }}
          fontWeight="bold"
          sx={{
            p: { xs: 1, md: 0 },
            transition: "opacity 0.3s linear",
            transitionDelay: "0.3s",
            opacity: didLogin ? 1 : 0,
          }}
        >
          {t("auth.welcome")}
        </Typography>

        <Card
          sx={{
            width: { md: "50%", xs: "100%" },
            boxShadow: { md: 3, xs: 0 },
            transition: "opacity 0.3s linear",
            opacity: didLogin ? 0 : 1,
          }}
        >
          <Stack
            sx={{ minWidth: [1, 1, 1 / 2], padding: 2 }}
            gap={{ md: 3, xs: 2 }}
            alignItems="center"
          >
            <Box width="200px">
              <LogoWithText variant="dark" />
            </Box>

            <Typography variant="h5" component="h2" textAlign="center">
              {t("auth.login")}
            </Typography>

            <Box sx={{ width: "100%" }}>
              <Login.Container
                type={UserTypes.Coach}
                delayLogin={1200}
                onPasswordToggle={toggleVisiblePassword}
                onSuccess={() => {
                  setDidLogin(true);
                  controls.volume(0.08);
                  controls.play();
                }}
              />
            </Box>

            <Box>
              <AppLink to="/auth/password-reset">{t("auth.reset-password")}</AppLink>
            </Box>
          </Stack>
        </Card>
      </Container>
    </BackgroundLayout>
  );
};
