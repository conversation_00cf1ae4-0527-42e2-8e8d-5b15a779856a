import { FC, PropsWithChildren } from "react";
import { useNavigate } from "react-router-dom";
import { z } from "zod";

import {
  SDK<PERSON>rovider,
  ThemeProvider,
  EnvConfigGuard,
  StateProvider,
  DateProvider,
  createCustomTheme,
  useSDKConfig,
  useAuthToken,
  TokenContextProvider,
  configParser,
} from "@mio/ui";
import { AccessToken } from "@mio/helpers";

import { environment } from "../environments/environment";
import "../i18n"; // Import i18n configuration

const Providers: FC<PropsWithChildren> = ({ children }) => {
  const baseURL = environment.BASE_URL;
  const { deleteToken, setToken } = useAuthToken();
  const navigate = useNavigate();
  const theme = createCustomTheme();

  const sdk = useSDKConfig({
    baseURL,
    onUnauthorized: () => {
      deleteToken();
      navigate("/login");
    },
    getAccessToken: () => localStorage.getItem("token") as AccessToken,
  });

  const resolvedSdk = environment.demoMode
    ? import("./demo/demo.sdk").then((module) => ({ ...sdk, ...module.default }))
    : Promise.resolve(sdk);

  if (environment.demoMode) {
    setToken("Fake token" as AccessToken);
  }

  return (
    <StateProvider>
      <SDKProvider sdk={resolvedSdk}>
        <ThemeProvider theme={theme}>
          <DateProvider>{children}</DateProvider>
        </ThemeProvider>
      </SDKProvider>
    </StateProvider>
  );
};

export const AppProviders: FC<PropsWithChildren> = ({ children }) => {
  const appConfigParser = configParser.merge(
    z.object({
      playerPortalUrl: z.string(),
    }),
  );

  return (
    <EnvConfigGuard env={environment} parser={appConfigParser}>
      <TokenContextProvider>
        <Providers>{children}</Providers>
      </TokenContextProvider>
    </EnvConfigGuard>
  );
};
