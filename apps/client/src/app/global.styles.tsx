import { FC, PropsWithChildren } from "react";

import { Global, css } from "@emotion/react";
import { CssBaseline } from "@mui/material";

export const GlobalStyles: FC<PropsWithChildren> = ({ children }) => {
  return (
    <>
      <Global
        styles={css`
          html,
          body,
          #root {
            height: 100%;
          }
          input[type="search"]::-webkit-search-decoration,
          input[type="search"]::-webkit-search-cancel-button,
          input[type="search"]::-webkit-search-results-button,
          input[type="search"]::-webkit-search-results-decoration {
            display: none;
          }
        `}
      />
      <CssBaseline />
      {children}
    </>
  );
};
