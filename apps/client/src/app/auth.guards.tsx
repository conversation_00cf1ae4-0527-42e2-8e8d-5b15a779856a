import { FC, PropsWithChildren, useCallback } from "react";

import { PrivateRoute, PublicRoute, useAuth } from "@mio/ui";

export const AppPublicRoute: FC<PropsWithChildren> = ({ children }) => {
  const { login, isLogged } = useAuth();

  const authFunction = useCallback(() => {
    return { isLoading: login.isLoading, isLogged };
  }, [login, isLogged]);

  return (
    <PublicRoute redirectTo="/" authFunction={authFunction}>
      {children}
    </PublicRoute>
  );
};

export const AppPrivateRoute: FC<PropsWithChildren> = ({ children }) => {
  const { login, isLogged } = useAuth();

  const authFunction = useCallback(() => {
    return { isLoading: login.isLoading, isLogged };
  }, [login, isLogged]);

  return (
    <PrivateRoute redirectTo="/login" authFunction={authFunction}>
      {children}
    </PrivateRoute>
  );
};
