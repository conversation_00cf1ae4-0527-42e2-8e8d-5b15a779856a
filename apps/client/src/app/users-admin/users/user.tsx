import { FC } from "react";
import { useTranslation } from "react-i18next";

import { CustomDate, PermissionsModule, Coach } from "@mio/helpers";
import {
  <PERSON><PERSON>,
  Box,
  Button,
  Chip,
  DeleteIcon,
  Divider,
  IconButton,
  Stack,
  Tooltip,
  Typography,
  useProvidedCurrentProfile,
} from "@mio/ui";
import AddPermission from "./addPermission";

type Props = {
  user: Coach<"populated">;
  roles: PermissionsModule.Role.Role[];
  onDelete: (id: PermissionsModule.PermissionEntity.PermissionId) => void;
  onEdit: (permission: PermissionsModule.PermissionEntity.CustomPermission) => void;
};

const UserItem: FC<Props> = ({ user, roles, onDelete, onEdit }) => {
  const { t } = useTranslation();
  const profile = useProvidedCurrentProfile();
  const isMe = profile.id === user.profile.id;

  return (
    <Stack p={2} gap={2}>
      <Stack direction="row" justifyContent="space-between" alignItems="center">
        <Stack gap={2}>
          <Typography>
            {user.authentication.email}{" "}
            {isMe ? (
              <Box component="span" sx={{ fontWeight: "bold" }}>
                {t("users.me")}
              </Box>
            ) : null}
          </Typography>
          <Typography>
            {user.profile.firstName} {user.profile.lastName}
          </Typography>
        </Stack>

        <Stack gap={2}>
          <Typography>
            {t("users.invited-by")}: {user.invite.invitedBy}
          </Typography>
          <Typography>
            {t("users.joined-on")}: {CustomDate.toDisplayDate(user.invite.redeemed)}
          </Typography>
        </Stack>
      </Stack>

      <Divider />

      <Stack width={"100%"} gap={1}>
        {user.permissions.length === 0 && (
          <Alert severity="warning">
            <Stack gap={1} direction="row" alignItems="center">
              <Typography>{t("users.no-permissions")}</Typography>
              <Box>
                <Button variant="text" color="warning">
                  {t("users.remove-member")}
                </Button>
              </Box>
            </Stack>
          </Alert>
        )}

        {user.permissions.map((permission) => {
          if (PermissionsModule.PermissionEntity.Permission.isOwnerType(permission)) {
            return (
              <Box key={permission.id}>
                <Chip label={t("users.owner")} color="secondary" />
              </Box>
            );
          }
          const role = roles.find((r) => r.id === permission.roleId);

          return (
            <Stack
              key={permission.id}
              direction="row"
              justifyContent="space-between"
              alignItems="center"
            >
              <Stack gap={1} direction="row" alignItems="center">
                <Typography>{role?.name || t("common.not-available")}</Typography>
                <Box>
                  <Tooltip title={t("permissions.delete-permission")}>
                    <IconButton onClick={() => onDelete(permission.id)}>
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </Box>
              </Stack>

              <Stack>
                <Button variant="text" onClick={() => onEdit(permission)}>
                  {t("users.teams-count", { count: permission.teamIds?.length || 0 })}
                </Button>
              </Stack>
            </Stack>
          );
        })}

        {!isMe && (
          <>
            <Divider />
            <AddPermission roles={roles} user={user} />
          </>
        )}
      </Stack>
    </Stack>
  );
};

export default UserItem;
