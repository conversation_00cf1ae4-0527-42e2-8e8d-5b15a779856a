import { FC, useState } from "react";
import { useTranslation } from "react-i18next";

import {
  Confirmation,
  LocalLoader,
  ToggleSnackbar,
  Alert,
  useDeletePermission,
  useOrganization,
  useOrganizationUsersCrud,
} from "@mio/ui";
import { PermissionsModule, Coach } from "@mio/helpers";

type PermissionId = PermissionsModule.PermissionEntity.PermissionId;

type Params = {
  user: Coach<"populated">;
  permissionId: PermissionId;
};

export const useDeleteUserPermission = () => {
  const [params, setParams] = useState<Params | undefined>(undefined);
  const { t } = useTranslation();

  return {
    deletePermission: (params: Params) => {
      setParams(params);
    },
    DeleteUI: (
      <DeletePermission
        permissionId={params?.permissionId}
        user={params?.user}
        onClose={() => setParams(undefined)}
      />
    ),
  };
};

type Props = {
  user?: Coach<"populated">;
  permissionId?: PermissionId;
  onClose: () => void;
};

const DeletePermission: FC<Props> = ({ user, permissionId, onClose }) => {
  const { t } = useTranslation();
  const deletePermission = useDeletePermission();
  const organization = useOrganization();
  const { removePermission } = useOrganizationUsersCrud(organization.id);

  const handleDelete = () => {
    if (permissionId && user) {
      deletePermission.mutate(
        {
          permissionId,
          organizationId: organization.id,
        },
        {
          onSuccess: () => {
            removePermission(user, permissionId);
          },
        },
      );
    }
  };

  return (
    <>
      <Confirmation
        open={!!permissionId}
        title={t("common.please-confirm")}
        content={t("permissions.delete-permission-confirmation")}
        onConfirm={handleDelete}
        onClose={onClose}
      />

      {deletePermission.isLoading && <LocalLoader message={t("permissions.deleting")} />}

      <ToggleSnackbar open={deletePermission.isSuccess}>
        <Alert severity="success">{t("permissions.permission-deleted")}</Alert>
      </ToggleSnackbar>

      <ToggleSnackbar open={deletePermission.isError}>
        <Alert severity="error">{t("common.something-went-wrong")}</Alert>
      </ToggleSnackbar>
    </>
  );
};
