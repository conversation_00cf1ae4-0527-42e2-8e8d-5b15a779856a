import { FC } from "react";
import { useTranslation } from "react-i18next";

import {
  <PERSON>ack,
  Card,
  LocalLoader,
  LocalError,
  useOrganization,
  useOrganizationUsers,
  useRoles,
} from "@mio/ui";

import UserItem from "./user";
import { useDeleteUserPermission } from "./deletePermission";
import { useTeamsEdit } from "./editPermission";

const UsersList: FC = () => {
  const { t } = useTranslation();
  const organization = useOrganization();
  const { query } = useOrganizationUsers(organization.id);
  const { query: rolesQuery } = useRoles(organization.id);
  const { DeleteUI, deletePermission } = useDeleteUserPermission();
  const { EditUI, editPermission } = useTeamsEdit();

  if (query.isLoading || rolesQuery.isLoading) {
    return <LocalLoader message={t("users.loading-users")} />;
  }

  if (query.isError || rolesQuery.isError) {
    return <LocalError message={t("users.failed-to-load-users")} />;
  }

  return (
    <Stack gap={4}>
      {query.data.map((user) => (
        <Card key={user.id}>
          <UserItem
            user={user}
            roles={rolesQuery.data}
            onDelete={(permissionId) => {
              deletePermission({
                user,
                permissionId,
              });
            }}
            onEdit={(permission) => {
              editPermission({ user, permission });
            }}
          />
        </Card>
      ))}

      {DeleteUI}

      {EditUI}
    </Stack>
  );
};

export default UsersList;
