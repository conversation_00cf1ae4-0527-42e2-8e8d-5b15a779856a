import { FC } from "react";
import { useTranslation } from "react-i18next";

import { Card, LocalError, LocalLoader, Stack, Alert, Box } from "@mio/ui";
import InviteItem from "./invite";
import { useFilteredInvites } from "./useFilteredInvites";
import CreateInvite from "./createInvite";

const InvitesList: FC = () => {
  const { t } = useTranslation();
  const { invitesQuery, FiltersUI } = useFilteredInvites();

  if (invitesQuery.isLoading) {
    return <LocalLoader message={t("users.fetching-invites")} />;
  }

  if (invitesQuery.isError) {
    return <LocalError message={t("users.failed-to-load-invites")} />;
  }

  return (
    <Stack gap={4}>
      <Stack direction="row" justifyContent="space-between" flexWrap="wrap" gap={2}>
        <Box>
          <FiltersUI />
        </Box>

        <Box>
          <CreateInvite />
        </Box>
      </Stack>
      {invitesQuery.data.map((invite) => (
        <Card key={invite.id}>
          <InviteItem invite={invite} />
        </Card>
      ))}

      {invitesQuery.data.length === 0 && (
        <Alert severity="info">{t("users.no-unredeemed-invites")}</Alert>
      )}
    </Stack>
  );
};

export default InvitesList;
