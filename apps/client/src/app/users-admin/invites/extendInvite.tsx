import { FC, useState } from "react";
import { useTranslation } from "react-i18next";

import { Invite } from "@mio/helpers";
import {
  Confirmation,
  Snackbar,
  Button,
  Alert,
  AddIcon,
  useCreateInvite,
  useOrganization,
  useUnredeemedInvites,
} from "@mio/ui";

type Props = {
  invite: Invite;
};

const ExtendInvite: FC<Props> = ({ invite }) => {
  const { t } = useTranslation();
  const [showConfirm, toggleConfirm] = useState(false);
  const [result, setResult] = useState<"success" | "fail" | undefined>();

  const organization = useOrganization();
  const invites = useUnredeemedInvites(organization.id);
  const extendInvite = useCreateInvite();

  const handleConfirm = () => {
    extendInvite.mutate(
      {
        email: invite.email,
        organization: invite.organization,
        invitedBy: invite.invitedBy,
      },
      {
        onSuccess: () => {
          invites.refetch();
          setResult("success");
        },
        onError: () => {
          setResult("fail");
        },
      },
    );
  };

  return (
    <>
      <Button variant="outlined" onClick={() => toggleConfirm(true)} startIcon={<AddIcon />}>
        {t("users.extend")}
      </Button>

      <Confirmation
        open={showConfirm}
        onConfirm={handleConfirm}
        onClose={() => toggleConfirm(false)}
        title={t("common.please-confirm")}
        content={t("users.extend-invite-confirmation", { email: invite.email })}
      />

      <Snackbar
        open={result === "success"}
        autoHideDuration={2000}
        onClose={() => setResult(undefined)}
      >
        <Alert severity="success">{t("users.invite-extended")}</Alert>
      </Snackbar>

      <Snackbar
        open={result === "fail"}
        autoHideDuration={2000}
        onClose={() => setResult(undefined)}
      >
        <Alert severity="error">{t("users.failed-to-extend-invite")}</Alert>
      </Snackbar>
    </>
  );
};

export default ExtendInvite;
