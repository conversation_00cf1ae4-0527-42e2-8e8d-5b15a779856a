import { FC } from "react";
import { useTranslation } from "react-i18next";

import { ExpiredInvite, PendingInvite, CustomDate, Invite } from "@mio/helpers";
import { Stack, Typography, Chip, Box } from "@mio/ui";
import DeleteInvite from "./deleteInvite";
import { useFilteredInvites } from "./useFilteredInvites";
import ExtendInvite from "./extendInvite";

type Props = {
  invite: PendingInvite | ExpiredInvite;
};

const InviteItem: FC<Props> = ({ invite }) => {
  const { t } = useTranslation();
  const { invitesQuery } = useFilteredInvites();

  return (
    <Stack direction="row" justifyContent="space-between" p={2} gap={1} flexWrap="wrap">
      <Stack gap={1} flexWrap="wrap">
        <Stack direction="row" gap={1}>
          <Typography>{t("users.invitee")}:</Typography>
          <Typography>{invite.email}</Typography>
        </Stack>

        <Stack direction="row" gap={1}>
          <Typography>{t("users.invited-by")}:</Typography>
          <Typography>{invite.invitedBy}</Typography>
        </Stack>
      </Stack>

      <Stack gap={1} flexWrap="wrap">
        <Stack direction="row" gap={1}>
          <Typography>{t("users.invited-on")}:</Typography>
          <Typography>{CustomDate.toDisplayDate(invite.created)}</Typography>
        </Stack>

        <Stack direction="row" gap={1} alignItems="center" flexWrap="wrap">
          <Typography>{t("users.expires-on")}:</Typography>
          <Typography>{CustomDate.toDisplayDate(invite.expires)}</Typography>
          {Invite.isExpired(invite) ? (
            <Box>
              <Chip label={t("users.expired")} color="warning" sx={{ mr: 1 }} />
              <ExtendInvite invite={invite} />
            </Box>
          ) : null}
        </Stack>
      </Stack>

      <Stack gap={1}>
        <Box>
          <DeleteInvite
            invite={invite}
            orgId={invite.organization}
            onDelete={() => {
              invitesQuery.refetch();
            }}
          />
        </Box>
      </Stack>
    </Stack>
  );
};

export default InviteItem;
