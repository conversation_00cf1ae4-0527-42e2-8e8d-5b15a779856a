import { FC, useState } from "react";
import { useTranslation } from "react-i18next";

import {
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  useOrganization,
  useUnredeemedInvites,
} from "@mio/ui";
import { Invite } from "@mio/helpers";

const Filters = {
  All: "all",
  Pending: "pending",
  Expired: "expired",
} as const;

type Filters = typeof Filters[keyof typeof Filters];

type Props = { onChange: (value: Filters) => void; value: Filters };

const FiltersUI: FC<Props> = ({ onChange, value }) => {
  const { t } = useTranslation();

  return (
    <FormControl sx={{ minWidth: 300 }}>
      <InputLabel>{t("users.status")}</InputLabel>
      <Select<Filters>
        label={t("users.status")}
        value={value}
        onChange={(event) => onChange(event.target.value as Filters)}
      >
        <MenuItem value={Filters.All}>{t("users.all")}</MenuItem>
        <MenuItem value={Filters.Pending}>{t("users.pending")}</MenuItem>
        <MenuItem value={Filters.Expired}>{t("users.expired")}</MenuItem>
      </Select>
    </FormControl>
  );
};

export const useFilteredInvites = () => {
  const organization = useOrganization();
  const [filter, setFilter] = useState<Filters>(Filters.All);

  const invitesQuery = useUnredeemedInvites(organization.id);

  return {
    FiltersUI: () => <FiltersUI onChange={setFilter} value={filter} />,
    invitesQuery: {
      ...invitesQuery,
      data:
        invitesQuery.data?.filter((elem) => {
          if (filter === Filters.Pending) {
            return Invite.isPending(elem);
          }

          if (filter === Filters.Expired) {
            return Invite.isExpired(elem);
          }

          return true;
        }) || [],
    },
  };
};
