import { FC, useState } from "react";
import { useTranslation } from "react-i18next";
import i18n from "../../../i18n";

import {
  Dialog,
  DialogTitle,
  DialogContent,
  Button,
  Stack,
  IconButton,
  CloseIcon,
  Typography,
  AddIcon,
  Box,
  useCreateInvite,
  useOrganization,
  useUnredeemedInvites,
  useRoles,
  LocalLoader,
  LocalError,
} from "@mio/ui";
import { Invite, isError } from "@mio/helpers";
import CreateInviteForm from "./createInvite.form";

const CreateInvite: FC = () => {
  const { t } = useTranslation();
  const [active, setActive] = useState(false);
  const organization = useOrganization();
  const permissionRoles = useRoles(organization.id);
  const createInvite = useCreateInvite();
  const invitesList = useUnredeemedInvites(organization.id);

  const handleSubmit = (data: unknown) => {
    const parsed = Invite.parseCreateDto({
      ...(data as { email: string; role: string }),
      locale: i18n.resolvedLanguage,
    });

    if (!isError(parsed)) {
      createInvite.mutate(parsed, {
        onSuccess: () => {
          invitesList.refetch();
          setActive(false);
        },
      });
    }
  };

  return (
    <>
      <Button
        onClick={() => setActive(true)}
        variant="contained"
        size="large"
        color="secondary"
        startIcon={<AddIcon />}
      >
        {t("users.invite")}
      </Button>
      <Dialog open={active} fullWidth>
        <DialogTitle>
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Typography component="h1" variant="h4">
              {t("users.invite-new-member")}
            </Typography>
            <IconButton
              aria-label={t("common.close")}
              onClick={() => setActive(false)}
              sx={{ ml: 2 }}
            >
              <CloseIcon />
            </IconButton>
          </Stack>
        </DialogTitle>
        <DialogContent>
          <Box pt={2}>
            {permissionRoles.query.isLoading && <LocalLoader />}
            {permissionRoles.query.isError && <LocalError />}
            {permissionRoles.query.isSuccess && (
              <CreateInviteForm
                onSubmit={handleSubmit}
                loading={createInvite.isLoading}
                serverError={createInvite.error}
                permissionRoles={permissionRoles.query.data}
              />
            )}
          </Box>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default CreateInvite;
