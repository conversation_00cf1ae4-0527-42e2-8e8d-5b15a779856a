import { useForm } from "react-hook-form";

import { Email, Invite, CreateInviteDto, OrganizationId, Primitive } from "@mio/helpers";
import { useFormResolver } from "@mio/ui";

export type DataShape = Primitive<CreateInviteDto>;

export const useInviteForm = (organizationId: OrganizationId, invitedBy: Email) => {
  const formResolver = useFormResolver<DataShape>(Invite.parseCreateDto);

  const formMethods = useForm<DataShape>({
    defaultValues: {
      organization: organizationId,
      invitedBy: invitedBy,
    },
    resolver: formResolver,
    mode: "onChange",
  });

  const hasSubmitted = formMethods.formState.submitCount > 0;
  const { errors } = formMethods.formState;

  return {
    formMethods,
    errors: hasSubmitted ? errors : {},
    control: formMethods.control,
  };
};
