import { FC, useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

import {
  Modal,
  ToggleSnackbar,
  Alert,
  Typography,
  Button,
  Box,
  AddIcon,
  useCreateRole,
  useOrganization,
  useRoles,
} from "@mio/ui";
import { PermissionsModule, isError } from "@mio/helpers";
import { SaveRoleForm } from "./save.form";

const CreateRole: FC = () => {
  const { t } = useTranslation();
  const [showForm, toggleForm] = useState(false);
  const organization = useOrganization();
  const createRole = useCreateRole();
  const { addRole } = useRoles(organization.id);

  const handleSubmit = (data: unknown) => {
    const parsed = PermissionsModule.Role.Role.toSaveDto(data);

    if (!isError(parsed)) {
      createRole.mutate(parsed, {
        onSuccess: (role) => {
          addRole(role);
          toggleForm(false);
        },
      });
    }
  };

  useEffect(() => {
    if (!showForm) {
      createRole.reset();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [showForm]);

  return (
    <>
      <Button
        onClick={() => toggleForm(true)}
        variant="contained"
        size="large"
        color="secondary"
        startIcon={<AddIcon />}
      >
        {t("roles.add-role")}
      </Button>

      <Modal
        open={showForm}
        onClose={() => toggleForm(false)}
        title={<Typography>{t("roles.create-new-role")}</Typography>}
        fullWidth={true}
        content={
          <Box pt={1}>
            <SaveRoleForm
              serverError={createRole.error}
              loading={createRole.isLoading}
              formTitle={t("roles.create-role")}
              onSubmit={handleSubmit}
            />
          </Box>
        }
      />

      <ToggleSnackbar open={createRole.isSuccess}>
        <Alert severity="success">{t("roles.role-created")}</Alert>
      </ToggleSnackbar>
    </>
  );
};

export default CreateRole;
