import { FC } from "react";
import { useTranslation } from "react-i18next";
import { startCase } from "lodash/fp";

import { PermissionsModule } from "@mio/helpers";
import {
  Card,
  CheckIcon,
  Stack,
  Typography,
  Box,
  IconButton,
  DeleteIcon,
  EditIcon,
  Tooltip,
} from "@mio/ui";

type Props = {
  actions: PermissionsModule.Action.Actions[];
  displayName: string;
  onDelete: () => void;
  onEdit: () => void;
};

const RoleItem: FC<Props> = ({ actions, displayName, onDelete, onEdit }) => {
  const { t } = useTranslation();

  return (
    <Card sx={{ p: 3, width: "100%" }}>
      <Stack gap={2}>
        <Stack direction="row" gap={1} alignItems="center">
          <Typography variant="h5" component="h2" fontWeight="bold" mr={2}>
            {displayName}
          </Typography>

          <Tooltip title={t("roles.edit-role", { name: displayName })}>
            <IconButton aria-label={t("roles.edit-role", { name: displayName })} onClick={onEdit}>
              <EditIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title={t("roles.delete-role", { name: displayName })}>
            <IconButton
              aria-label={t("roles.delete-role", { name: displayName })}
              onClick={onDelete}
            >
              <DeleteIcon />
            </IconButton>
          </Tooltip>
        </Stack>

        <Box component="ul" sx={{ listStyle: "none", paddingInlineStart: 0 }}>
          {actions.map((elem) => (
            <Box key={elem} component="li" sx={{ mb: 2 }}>
              <Stack direction="row" gap={1} alignItems="center">
                <CheckIcon color="success" />
                <Typography>{startCase(elem)}</Typography>
              </Stack>
            </Box>
          ))}
        </Box>
      </Stack>
    </Card>
  );
};

export default RoleItem;
