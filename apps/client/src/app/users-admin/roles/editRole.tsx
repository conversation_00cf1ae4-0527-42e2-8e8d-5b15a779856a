import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

import {
  <PERSON><PERSON>,
  Modal,
  ToggleSnackbar,
  Typography,
  useUpdateRole,
  useOrganization,
  useRoles,
} from "@mio/ui";
import { PermissionsModule, isError } from "@mio/helpers";
import { SaveRoleForm } from "./save.form";

export const useEditRole = () => {
  const { t } = useTranslation();
  const [role, setRole] = useState<PermissionsModule.Role.Role | undefined>();
  const organization = useOrganization();
  const saveRole = useUpdateRole();
  const { updateRole } = useRoles(organization.id);

  useEffect(() => {
    if (!role) {
      saveRole.reset();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [role]);

  const handleUpdate = (payload: unknown) => {
    const parsed = PermissionsModule.Role.Role.toSaveDto(payload);

    if (!isError(parsed) && role) {
      saveRole.mutate(
        {
          roleId: role.id,
          organizationId: role.organizationId,
          dto: parsed,
        },
        {
          onSuccess: (updatedRole) => {
            updateRole(updatedRole);
            setRole(undefined);
          },
        },
      );
    }
  };

  return {
    editRole: (role: PermissionsModule.Role.Role) => {
      setRole(role);
    },
    EditUI: (
      <>
        <Modal
          open={!!role}
          onClose={() => setRole(undefined)}
          title={<Typography>{t("roles.edit-role", { name: role?.name })}</Typography>}
          fullWidth={true}
          content={
            <SaveRoleForm
              loading={saveRole.isLoading}
              serverError={saveRole.error}
              formTitle={t("roles.edit-role", { name: role?.name })}
              onSubmit={handleUpdate}
              role={role}
            />
          }
        />

        <ToggleSnackbar open={saveRole.isSuccess}>
          <Alert severity="success">{t("roles.role-updated")}</Alert>
        </ToggleSnackbar>
      </>
    ),
  };
};
