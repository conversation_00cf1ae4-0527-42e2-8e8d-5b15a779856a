import { useState } from "react";
import { useTranslation } from "react-i18next";

import {
  Alert,
  Confirmation,
  ToggleSnackbar,
  useDeleteRole as useDeleteRoleMutation,
  useOrganization,
  useRoles,
} from "@mio/ui";
import { PermissionsModule } from "@mio/helpers";

export const useDeleteRole = () => {
  const { t } = useTranslation();
  const [role, setRole] = useState<PermissionsModule.Role.Role | undefined>();
  const organization = useOrganization();
  const deleteRole = useDeleteRoleMutation();
  const { removeRole } = useRoles(organization.id);

  const handleDelete = () => {
    if (role) {
      deleteRole.mutate(
        {
          organizationId: role.organizationId,
          roleId: role.id,
        },
        {
          onSuccess: () => {
            removeRole(role);
            setRole(undefined);
          },
        },
      );
    }
  };

  return {
    deleteRole: (role: PermissionsModule.Role.Role) => {
      setRole(role);
    },
    DeleteUI: (
      <>
        <Confirmation
          open={!!role}
          onClose={() => setRole(undefined)}
          onConfirm={handleDelete}
          title={t("common.please-confirm")}
          content={t("roles.delete-role-confirmation", { name: role?.name })}
        />

        <ToggleSnackbar open={deleteRole.isSuccess}>
          <Alert severity="success">{t("roles.role-deleted")}</Alert>
        </ToggleSnackbar>

        <ToggleSnackbar open={deleteRole.isError}>
          <Alert severity="error">{t("roles.failed-to-delete-role")}</Alert>
        </ToggleSnackbar>
      </>
    ),
  };
};
