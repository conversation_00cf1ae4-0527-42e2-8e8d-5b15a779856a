import { FC } from "react";
import { useTranslation } from "react-i18next";

import { LocalError, LocalLoader, Stack, useOrganization, useRoles } from "@mio/ui";

import RoleItem from "./roleItem";
import CreateRole from "./createRole";
import { useDeleteRole } from "./deleteRole";
import { useEditRole } from "./editRole";

const RolesList: FC = () => {
  const { t } = useTranslation();
  const organization = useOrganization();
  const { query } = useRoles(organization.id);
  const { deleteRole, DeleteUI } = useDeleteRole();
  const { editRole, EditUI } = useEditRole();

  return (
    <Stack>
      <Stack mb={4} direction="row-reverse">
        <CreateRole />
      </Stack>
      {query.isLoading && <LocalLoader message={t("roles.loading-roles")} />}
      {query.isError && <LocalError message={t("roles.failed-to-load-roles")} />}
      {query.isSuccess && (
        <>
          <Stack gap={4}>
            {query.data.map((elem) => (
              <RoleItem
                key={elem.id}
                displayName={elem.name}
                actions={elem.actions}
                onDelete={() => deleteRole(elem)}
                onEdit={() => editRole(elem)}
              />
            ))}
          </Stack>
          {DeleteUI}
          {EditUI}
        </>
      )}
    </Stack>
  );
};

export default RolesList;
