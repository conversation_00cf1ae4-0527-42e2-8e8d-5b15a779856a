import { FC, useState, SyntheticEvent } from "react";
import { useTranslation } from "react-i18next";

import { TabPanel, Tab, TabContext, TabList, Stack, Typography, Box } from "@mio/ui";
import { SidebarLayout } from "../layouts";
import UsersList from "./users/usersList";
import InvitesList from "./invites/invitesList";
import RolesList from "./roles";

enum Panels {
  Users = "users",
  Roles = "roles",
  Invites = "invites",
}

export const UsersAdmin: FC = () => {
  const { t } = useTranslation();
  const [value, setValue] = useState<Panels>(Panels.Users);

  const handleChange = (_: SyntheticEvent, newValue: Panels) => {
    setValue(newValue);
  };

  return (
    <SidebarLayout>
      <Stack component="header" flexDirection="row" justifyContent="space-between" mb={5}>
        <Typography variant="h4" component="h1">
          {t("users.users-admin")}
        </Typography>
      </Stack>

      <TabContext value={value}>
        <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
          <TabList
            onChange={handleChange}
            aria-label={t("users.user-admin-tabs")}
            variant="fullWidth"
            centered
          >
            <Tab label={t("users.users")} value={Panels.Users} />
            <Tab label={t("users.invites")} value={Panels.Invites} />
            <Tab label={t("users.permission-roles")} value={Panels.Roles} />
          </TabList>
        </Box>
        <TabPanel value={Panels.Users}>
          <UsersList />
        </TabPanel>
        <TabPanel value={Panels.Invites}>
          <InvitesList />
        </TabPanel>
        <TabPanel value={Panels.Roles}>
          <RolesList />
        </TabPanel>
      </TabContext>
    </SidebarLayout>
  );
};
