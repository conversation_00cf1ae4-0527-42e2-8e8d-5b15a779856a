import { FC } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";

import {
  visuallyHidden,
  Box,
  TextField,
  FormLabel,
  Stack,
  Alert,
  Select,
  MenuItem,
  FormControl,
  LoadingButton,
  useFormResolver,
} from "@mio/ui";
import {
  APIError,
  FinancialIntegration,
  FinancialIntegrationType,
  Primitive,
  PublicIntegration,
  UpdateFinancialIntegrationDto,
} from "@mio/helpers";

type DataShape = Primitive<UpdateFinancialIntegrationDto>;

type Props = {
  onSubmit: (data: unknown) => void;
  loading?: boolean;
  serverError?: APIError | null;
  data: PublicIntegration;
};

export const UpdateIntegrationForm: FC<Props> = ({ onSubmit, loading, serverError, data }) => {
  const { t } = useTranslation();
  const formResolver = useFormResolver<DataShape>(FinancialIntegration.toUpdateDto);

  const formMethods = useForm<DataShape>({
    defaultValues: {
      id: data.id,
      name: data.name,
    },
    resolver: formResolver,
    mode: "onChange",
  });

  const hasSubmitted = formMethods.formState.submitCount > 0;

  const { errors } = formMethods.formState;

  return (
    <form onSubmit={formMethods.handleSubmit(onSubmit)}>
      <Box component="legend" sx={visuallyHidden}>
        {t("financial.integration.update.title")}
      </Box>

      <Box mb={2}>
        <FormControl fullWidth>
          <FormLabel>{t("financial.integration.type")}</FormLabel>
          <Select
            fullWidth
            required
            labelId="integration-type-label"
            id="integration-type"
            value={data.type}
            disabled={true}
          >
            <MenuItem value={FinancialIntegrationType.GoCardless}>
              {t("financial.integration.types.gocardless")}
            </MenuItem>
          </Select>
        </FormControl>
      </Box>

      <Box mb={2}>
        <TextField
          fullWidth
          required
          label={<FormLabel required={false}>{t("financial.integration.name")}</FormLabel>}
          id="name"
          name="name"
          inputProps={{ ...formMethods.register("name") }}
          helperText={(hasSubmitted && errors.name?.message) || ""}
          error={hasSubmitted && !!errors.name?.message}
          aria-invalid={hasSubmitted && !!errors.name?.message}
        />
      </Box>

      <Box mb={2}>
        <TextField
          fullWidth
          type="password"
          label={<FormLabel>{t("financial.integration.api-key")}</FormLabel>}
          id="key"
          name="key"
          disabled={true}
          defaultValue={t("financial.integration.update.hidden-key")}
        />
      </Box>

      <Stack mt={4} alignItems="center">
        {serverError && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {t("financial.integration.error")}
          </Alert>
        )}
        <LoadingButton
          sx={{ width: 1 / 2 }}
          type="submit"
          loading={loading}
          variant="contained"
          size="large"
          color="primary"
        >
          {t("financial.integration.submit")}
        </LoadingButton>
      </Stack>
    </form>
  );
};
