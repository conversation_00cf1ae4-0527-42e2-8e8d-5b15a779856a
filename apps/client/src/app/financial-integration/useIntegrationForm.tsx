import { useState, FC } from "react";
import { isObject } from "lodash/fp";
import { useTranslation } from "react-i18next";

import {
  Box,
  CloseIcon,
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
  Typography,
  useCreateFinancialIntegration,
  useOrganizationId,
  useUpdateFinancialIntegration,
} from "@mio/ui";
import { isError, PublicIntegration, FinancialIntegration, APIError } from "@mio/helpers";

import { CreateIntegrationForm } from "./createIntegrationForm";
import { UpdateIntegrationForm } from "./updateIntegrationForm";

type FormTarget = "create" | undefined | PublicIntegration;

type Props = {
  formTarget: FormTarget;
  onClose: () => void;
  isLoading: boolean;
  serverError: APIError | null;
  onCreate: (data: unknown) => void;
  onUpdate: (data: unknown) => void;
};

const FormUI: FC<Props> = ({ formTarget, onClose, isLoading, serverError, onCreate, onUpdate }) => {
  const { t } = useTranslation();

  return (
    <Dialog
      open={!!formTarget}
      onClose={(_event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
          <Typography mr={3}>
            {formTarget === "create" && t("financial.integration.form.create-title")}
            {isObject(formTarget) &&
              t("financial.integration.form.edit-title", { name: formTarget.name })}
          </Typography>
          <IconButton onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent>
        {formTarget === "create" && (
          <CreateIntegrationForm
            onSubmit={onCreate}
            loading={isLoading}
            serverError={serverError}
          />
        )}
        {isObject(formTarget) && (
          <UpdateIntegrationForm
            data={formTarget}
            onSubmit={onUpdate}
            loading={isLoading}
            serverError={serverError}
          />
        )}
      </DialogContent>
    </Dialog>
  );
};

export const useIntegrationForm = () => {
  const organizationId = useOrganizationId();
  const [formTarget, setFormTarget] = useState<FormTarget>();

  const showForm = (item: FormTarget) => {
    setFormTarget(item);
  };

  const createIntegration = useCreateFinancialIntegration();
  const updateIntegration = useUpdateFinancialIntegration();

  const handleCreate = (data: unknown) => {
    const parsed = FinancialIntegration.toCreateDto(data);

    if (!isError(parsed)) {
      createIntegration.mutate(parsed, {
        onSuccess: () => {
          setFormTarget(undefined);
        },
      });
    }
  };

  const handleUpdate = (data: unknown) => {
    const parsed = FinancialIntegration.toUpdateDto(data);

    if (!isError(parsed) && organizationId) {
      updateIntegration.mutate(
        { organizationId, updatedFinancialIntegration: parsed },
        {
          onSuccess: () => {
            setFormTarget(undefined);
          },
        },
      );
    }
  };

  return {
    FormUI: (
      <FormUI
        onCreate={handleCreate}
        onUpdate={handleUpdate}
        formTarget={formTarget}
        onClose={() => setFormTarget(undefined)}
        isLoading={updateIntegration.isLoading || createIntegration.isLoading}
        serverError={updateIntegration.error || createIntegration.error}
      />
    ),
    showForm,
  };
};
