import { useState } from "react";
import { useTranslation } from "react-i18next";

import {
  Confirmation,
  Alert,
  Typography,
  ToggleSnackbar,
  useOrganizationId,
  useDeleteFinancialIntegration,
} from "@mio/ui";
import { FinancialIntegrationId } from "@mio/helpers";

export const useDeleteItem = () => {
  const { t } = useTranslation();
  const organizationId = useOrganizationId();
  const [deleteTarget, setDeleteTarget] = useState<FinancialIntegrationId | undefined>();

  const deleteIntegration = useDeleteFinancialIntegration();

  const DeleteUI = () => (
    <>
      <Confirmation
        open={!!deleteTarget}
        title={<Typography>{t("financial.integration.delete.title")}</Typography>}
        onConfirm={() => {
          if (deleteTarget && organizationId) {
            deleteIntegration.mutate(
              {
                financialIntegrationId: deleteTarget,
                organizationId,
              },
              {
                onSettled: () => {
                  setDeleteTarget(undefined);
                },
              },
            );
          }
        }}
        onClose={() => setDeleteTarget(undefined)}
      />

      <ToggleSnackbar open={deleteIntegration.isError}>
        <Alert severity="error">{t("financial.integration.delete.error")}</Alert>
      </ToggleSnackbar>
    </>
  );

  return { DeleteUI, setDeleteTarget };
};
