import { FC } from "react";
import { useTranslation } from "react-i18next";

import {
  Box,
  Button,
  Typography,
  Stack,
  CircularProgress,
  Alert,
  useFindFinancialIntegrations,
  useOrganization,
} from "@mio/ui";

import { IntegrationItem } from "./financialIntegrationItem";
import { useDeleteItem } from "./useDeleteItem";
import { useIntegrationForm } from "./useIntegrationForm";
import { SidebarLayout } from "../layouts";

export const FinancialIntegrationsView: FC = () => {
  const { t } = useTranslation();
  const organization = useOrganization();

  const { DeleteUI, setDeleteTarget } = useDeleteItem();
  const { FormUI, showForm } = useIntegrationForm();

  const queryList = useFindFinancialIntegrations(organization.id);

  return (
    <SidebarLayout>
      <Box>
        <Box component="header" mb={3} sx={{ display: "flex", justifyContent: "flex-end" }}>
          <Box>
            <Button
              variant="contained"
              size="large"
              color="secondary"
              onClick={() => showForm("create")}
            >
              {t("financial.integration.view.add-button")}
            </Button>
          </Box>
        </Box>

        {queryList.status === "loading" && (
          <Box p={2}>
            <CircularProgress />
          </Box>
        )}

        {queryList.status === "error" && (
          <Box p={2}>
            <Alert severity="error">
              <Typography variant="body1">{t("financial.integration.view.load-error")}</Typography>
            </Alert>
          </Box>
        )}

        {queryList.status === "success" && (
          <Stack spacing={4}>
            {queryList.data.map((integration) => (
              <IntegrationItem
                key={integration.id}
                data={integration}
                onEdit={() => showForm(integration)}
                onDelete={() => setDeleteTarget(integration.id)}
              />
            ))}
          </Stack>
        )}

        {FormUI}
        <DeleteUI />
      </Box>
    </SidebarLayout>
  );
};
