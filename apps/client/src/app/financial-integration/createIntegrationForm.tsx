import { FC, useEffect } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";

import {
  visuallyHidden,
  Box,
  TextField,
  FormLabel,
  Stack,
  Alert,
  Select,
  MenuItem,
  SecretField,
  FormControl,
  FormHelperText,
  useOrganizationId,
  LoadingButton,
  useFormResolver,
} from "@mio/ui";
import {
  APIError,
  CreateFinancialIntegrationDto,
  ErrorMessages,
  FinancialIntegration,
  FinancialIntegrationType,
  Primitive,
} from "@mio/helpers";

type DataShape = Primitive<CreateFinancialIntegrationDto>;

type Props = {
  onSubmit: (data: unknown) => void;
  loading?: boolean;
  serverError?: APIError | null;
};

export const CreateIntegrationForm: FC<Props> = ({ onSubmit, loading, serverError }) => {
  const { t } = useTranslation();
  const organizationId = useOrganizationId();
  const formResolver = useFormResolver<DataShape>(FinancialIntegration.toCreateDto);

  const formMethods = useForm<DataShape>({
    defaultValues: {
      organizationId: organizationId,
      type: FinancialIntegrationType.GoCardless,
    },
    resolver: formResolver,
    mode: "onChange",
  });

  useEffect(() => {
    if (serverError) {
      if (serverError.message === ErrorMessages.EntityAlreadyExists) {
        formMethods.setError("type", { message: t("financial.integration.already-exists") });
      }
    }
  }, [serverError, formMethods, t]);

  const hasSubmitted = formMethods.formState.submitCount > 0;
  const integrationType = formMethods.watch("type");

  const { errors } = formMethods.formState;

  return (
    <form onSubmit={formMethods.handleSubmit(onSubmit)}>
      <Box component="legend" sx={visuallyHidden}>
        {t("financial.integration.create-title")}
      </Box>

      <Box mb={2}>
        <TextField
          fullWidth
          required
          label={<FormLabel required={false}>{t("financial.integration.name")}</FormLabel>}
          id="name"
          name="name"
          inputProps={{ ...formMethods.register("name") }}
          helperText={(hasSubmitted && errors.name?.message) || ""}
          error={hasSubmitted && !!errors.name?.message}
          aria-invalid={hasSubmitted && !!errors.name?.message}
        />
      </Box>

      <Box mb={2}>
        <FormControl error={hasSubmitted && !!errors.type?.message} fullWidth>
          <FormLabel>{t("financial.integration.type")}</FormLabel>
          <Select
            fullWidth
            required
            labelId="integration-type-label"
            id="integration-type"
            inputProps={{ ...formMethods.register("type") }}
            value={integrationType}
            aria-invalid={hasSubmitted && !!errors.type?.message}
          >
            <MenuItem value={FinancialIntegrationType.GoCardless}>
              {t("financial.integration.types.gocardless")}
            </MenuItem>
            <MenuItem value={FinancialIntegrationType.Stripe}>
              {t("financial.integration.types.stripe")}
            </MenuItem>
          </Select>
          {hasSubmitted && !!errors.type?.message && (
            <FormHelperText>{errors.type?.message}</FormHelperText>
          )}
        </FormControl>
      </Box>

      <Box mb={2}>
        <SecretField
          fullWidth
          label={<FormLabel>{t("financial.integration.api-key")}</FormLabel>}
          id="key"
          name="key"
          inputProps={{ ...formMethods.register("key") }}
          helperText={(hasSubmitted && errors.key?.message) || ""}
          error={hasSubmitted && !!errors.key?.message}
          aria-invalid={hasSubmitted && !!errors.key?.message}
        />
      </Box>

      <Stack mt={4} alignItems="center">
        {serverError && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {t("financial.integration.error")}
          </Alert>
        )}
        <LoadingButton
          sx={{ width: 1 / 2 }}
          type="submit"
          loading={loading}
          variant="contained"
          size="large"
          color="primary"
        >
          {t("financial.integration.submit")}
        </LoadingButton>
      </Stack>
    </form>
  );
};
