import { FC, useState, MouseEvent } from "react";
import { useTranslation } from "react-i18next";

import {
  Box,
  Typography,
  IconButton,
  MoreVertIcon,
  Menu,
  MenuItem,
  DeleteIcon,
  Card,
  EditIcon,
} from "@mio/ui";
import { PublicIntegration } from "@mio/helpers";

type Props = {
  data: PublicIntegration;
  onEdit: () => void;
  onDelete: () => void;
};

export const IntegrationItem: FC<Props> = ({ data, onEdit, onDelete }) => {
  const { t } = useTranslation();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleOpen = (event: MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <Card sx={{ padding: 2 }}>
      <Box>
        <Box
          component="header"
          mb={2}
          sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}
        >
          <Typography variant="h5" fontWeight={600}>
            {data.name}
          </Typography>

          <IconButton
            id="menu-button"
            aria-controls={open ? "integration-item-menu" : undefined}
            aria-haspopup="true"
            aria-label={t("financial.integration.item.menu-label")}
            aria-expanded={open ? "true" : undefined}
            onClick={handleOpen}
          >
            <MoreVertIcon />
          </IconButton>
        </Box>
      </Box>

      <Menu
        id="integration-item-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          "aria-labelledby": "menu-button",
        }}
      >
        <MenuItem
          onClick={() => {
            onEdit();
            handleClose();
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <EditIcon aria-label={t("financial.integration.item.edit")} sx={{ marginRight: 1 }} />
            <Typography>{t("financial.integration.item.edit")}</Typography>
          </Box>
        </MenuItem>

        <MenuItem
          onClick={() => {
            onDelete();
            handleClose();
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <DeleteIcon
              aria-label={t("financial.integration.item.delete")}
              sx={{ marginRight: 1 }}
            />
            <Typography>{t("financial.integration.item.delete")}</Typography>
          </Box>
        </MenuItem>
      </Menu>
    </Card>
  );
};
