import { FC } from "react";

import { AccessProps } from "./types";
import { useAccessControl } from "./useAccessControl";

type Props = AccessProps & { children: JSX.Element };

/** Hides the resource from the UI if permissions are insufficient */
export const CanSee: FC<Props> = ({ actions, children, teams }) => {
  const hasAccess = useAccessControl(actions, teams);

  return hasAccess ? children : null;
};
