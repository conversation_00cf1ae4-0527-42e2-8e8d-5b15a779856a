import { PermissionsModule, TeamId } from "@mio/helpers";
import { useOrganization, useRequiredPermissions } from "@mio/ui";

/** Determines if the User has sufficient permissions to perform an action  */
export const useAccessControl = (
  actions: PermissionsModule.Action.Actions[],
  teams: TeamId[] = [],
) => {
  const organization = useOrganization();
  const permissions = useRequiredPermissions(organization.id);

  return PermissionsModule.PermissionEntity.Permission.hasAccess(actions, permissions, teams);
};
