import { FC } from "react";
import { useTranslation } from "react-i18next";

import { Alert, CenteredLayout, Box } from "@mio/ui";
import { AccessProps } from "./types";
import { useAccessControl } from "./useAccessControl";

type Props = AccessProps & { children: JSX.Element };

/** Shows "Permission denied message" if permissions are insufficient */
export const CanAccess: FC<Props> = ({ actions, children, teams }) => {
  const { t } = useTranslation();
  const hasAccess = useAccessControl(actions, teams);

  if (hasAccess) {
    return children;
  }

  return (
    <CenteredLayout>
      <Box sx={{ p: 5 }}>
        <Alert severity="warning">{t("permissions.denied")}</Alert>
      </Box>
    </CenteredLayout>
  );
};
