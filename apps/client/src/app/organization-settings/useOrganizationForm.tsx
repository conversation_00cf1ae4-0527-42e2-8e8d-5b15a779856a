import { useForm } from "react-hook-form";

import { Organization, PublicOrganization } from "@mio/helpers";
import { DataShape } from "./types";
import { useFormResolver } from "@mio/ui";

export const useOrganizationForm = (organization: PublicOrganization) => {
  const formResolver = useFormResolver<DataShape>(Organization.parseUpdateDto);

  const formMethods = useForm<DataShape>({
    defaultValues: {
      name: organization.name,
      displayName: organization.displayName,
      slug: organization.slug,
      contactEmail: organization.contactEmail,
      senderEmail: organization.senderEmail,
      applications: organization.applications,
      privacyPolicy: organization.privacyPolicy,
    },
    resolver: formResolver,
    mode: "onChange",
  });

  const hasSubmitted = formMethods.formState.submitCount > 0;
  const { errors } = formMethods.formState;
  const openApplications = formMethods.watch("applications.open");
  const privacyPolicy = formMethods.watch("privacyPolicy") || "";

  return {
    formMethods,
    openApplications,
    privacyPolicy,
    errors: hasSubmitted ? errors : {},
  };
};
