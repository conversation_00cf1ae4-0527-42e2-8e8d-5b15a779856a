import { useState } from "react";
import { useTranslation } from "react-i18next";

import { Organization, isError, PublicOrganization } from "@mio/helpers";
import { Alert, Snackbar, usePublicOrganization, useCurrentProfile } from "@mio/ui";
import { DataShape } from "./types";

export const useUpdate = (organization: PublicOrganization) => {
  const { t } = useTranslation();
  const [showSnackbar, toggleSnackbar] = useState(false);
  const { updateOrganization } = usePublicOrganization();
  const profile = useCurrentProfile();

  const update = (data: DataShape) => {
    const parsed = Organization.parseUpdateDto(data);

    if (!isError(parsed)) {
      updateOrganization.mutate(
        {
          ...parsed,
          organizationId: organization.id,
        },
        {
          onSuccess: () => {
            toggleSnackbar(true);
            profile.refetch();
          },
        },
      );
    }
  };

  const ServerError = () => {
    if (updateOrganization.error) {
      return <Alert severity="error">{t("organization.settings.update.error")}</Alert>;
    }
    return null;
  };

  const UpdateSuccess = () => {
    return (
      <Snackbar open={showSnackbar} autoHideDuration={2000} onClose={() => toggleSnackbar(false)}>
        <Alert severity="success">{t("organization.settings.update.success")}</Alert>
      </Snackbar>
    );
  };

  return { update, isLoading: updateOrganization.isLoading, ServerError, UpdateSuccess };
};
