import { FC } from "react";
import { useTranslation } from "react-i18next";

import { Alert, useOrganization } from "@mio/ui";
import { SidebarLayout } from "../layouts";
import { OrganizationForm } from "./form";

export const OrganizationSettings: FC = () => {
  const { t } = useTranslation();
  const organization = useOrganization();

  if (organization) {
    return (
      <SidebarLayout>
        <OrganizationForm organization={organization} />
      </SidebarLayout>
    );
  }

  return <Alert severity="error">{t("organization.settings.not-found")}</Alert>;
};
