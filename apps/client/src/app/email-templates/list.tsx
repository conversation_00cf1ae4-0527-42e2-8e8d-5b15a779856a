import { FC, useState } from "react";
import { useTranslation } from "react-i18next";

import {
  <PERSON>,
  Stack,
  Typo<PERSON>,
  PageError,
  PageLoader,
  Button,
  AddIcon,
  useOrganization,
  useGetEmailTemplates,
  useDeleteEmailTemplate,
} from "@mio/ui";
import { EmailTemplateId } from "@mio/helpers";
import { SidebarLayout } from "../layouts";
import { TemplateItem } from "./templateItem";
import { useCreateEmailTemplateUI } from "./useCreateEmailTemplate";
import { useEditEmailTemplateUI } from "./useEditEmailTemplate";
import { useConfirmationModal } from "../shared/hooks/useConfirmation";

export const EmailTemplates: FC = () => {
  const { t } = useTranslation();
  const organization = useOrganization();
  const { createEmailTemplate, CreateUI } = useCreateEmailTemplateUI();
  const { editEmailTemplate, EditUI } = useEditEmailTemplateUI();
  const [deleteId, setDeleteId] = useState<EmailTemplateId | undefined>(undefined);

  const query = useGetEmailTemplates(organization.id);
  const deleteMutation = useDeleteEmailTemplate(organization.id);

  const { ConfirmationModalUI, show: showDeleteConfirmation } = useConfirmationModal({
    isError: deleteMutation.isError,
    isSuccess: deleteMutation.isSuccess,
    isLoading: deleteMutation.isLoading,
    title: t("email-templates.delete-confirm-title"),
    onConfirm: () => deleteId && deleteMutation.mutate(deleteId),
    onCancel: () => deleteMutation.reset(),
  });

  const deleteEmailTemplate = (id: EmailTemplateId) => {
    setDeleteId(id);
    showDeleteConfirmation(t("email-templates.delete-warning"));
  };

  if (query.isLoading) {
    return <PageLoader message={t("email-templates.loading")} />;
  }

  if (query.isError) {
    return <PageError message={t("email-templates.load-error")} />;
  }

  return (
    <SidebarLayout>
      <Box sx={{ width: "100%", bgcolor: "background.paper", padding: { sm: 2 } }}>
        <Stack
          component="header"
          flexDirection="row"
          justifyContent="space-between"
          mb={5}
          gap={2}
          flexWrap="wrap"
        >
          <Box>
            <Typography variant="h4" component="h1">
              {t("email-templates.title")}
            </Typography>
          </Box>

          <Box>
            <Button
              onClick={createEmailTemplate}
              variant="contained"
              size="large"
              color="secondary"
              startIcon={<AddIcon />}
            >
              {t("email-templates.add-template")}
            </Button>
          </Box>
        </Stack>
        <Stack gap={3}>
          {query.data.map((template) => (
            <TemplateItem
              template={template}
              onEdit={() => editEmailTemplate(template)}
              onDelete={() => deleteEmailTemplate(template.id)}
            />
          ))}
          {query.data.length === 0 && (
            <Typography color="grey">{t("email-templates.no-templates")}</Typography>
          )}
        </Stack>
      </Box>

      {CreateUI}
      {EditUI}
      <ConfirmationModalUI />
    </SidebarLayout>
  );
};
