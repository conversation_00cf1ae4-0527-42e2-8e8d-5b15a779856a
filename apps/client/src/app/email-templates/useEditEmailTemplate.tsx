import { FC, useState } from "react";
import { useTranslation } from "react-i18next";
import { EmailTemplate, isError } from "@mio/helpers";
import { Modal, useUpdateEmailTemplate, Typography } from "@mio/ui";
import { UpsertEmailTemplate } from "./upsert.form";

type Props = {
  template: EmailTemplate;
  onSuccess: () => void;
};

const EditEmailTemplate: FC<Props> = ({ template, onSuccess }) => {
  const editEmailTemplate = useUpdateEmailTemplate();
  const [error, setError] = useState<undefined | boolean>(undefined);

  const handleSubmit = (data: unknown) => {
    const validated = EmailTemplate.toEntity(data);
    if (isError(validated)) {
      setError(true);
    } else {
      editEmailTemplate.mutate(validated, {
        onSuccess,
      });
    }
  };

  return (
    <UpsertEmailTemplate
      template={template}
      onSubmit={handleSubmit}
      loading={editEmailTemplate.isLoading}
      error={!!editEmailTemplate.error || error}
    />
  );
};

export const useEditEmailTemplateUI = () => {
  const { t } = useTranslation();
  const [template, setTemplate] = useState<EmailTemplate | undefined>(undefined);

  return {
    editEmailTemplate: (template: EmailTemplate) => setTemplate(template),
    EditUI: (
      <Modal
        open={!!template}
        fullWidth
        onClose={() => setTemplate(undefined)}
        title={
          <Typography variant="h5" component="h2">
            {t("email-templates.edit-template")}
          </Typography>
        }
        content={
          template && (
            <EditEmailTemplate template={template} onSuccess={() => setTemplate(undefined)} />
          )
        }
      />
    ),
  };
};
