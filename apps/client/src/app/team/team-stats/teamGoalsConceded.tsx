import { FC, useMemo } from "react";
import { useTranslation } from "react-i18next";
import {
  Box,
  Divider,
  footballMatchStatsState,
  PageError,
  PageLoader,
  Paper,
  Stack,
  Typography,
  useOrganization,
  useRequiredTeamId,
} from "@mio/ui";
import GoalsTeamStats from "./goalsTeamStats";
import { ErrorMessages } from "@mio/helpers";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip } from "recharts";

const TeamGoalsConceded: FC = () => {
  const { t } = useTranslation();
  const organization = useOrganization();
  const teamId = useRequiredTeamId();

  const goalsStatsQuery = footballMatchStatsState.useGetMatchTeamAttemptsAgainstStats(
    organization.id,
    teamId,
  );

  const goalsPerMatch = useMemo(() => {
    if (!goalsStatsQuery.data?.matchGoals) return null;

    // Group goals by match-
    const matchGoals = goalsStatsQuery.data.matchGoals.reduce((acc, goal) => {
      const gameWeek = goal.gameWeek.toString();
      acc[gameWeek] = (acc[gameWeek] || 0) + goal.goalsConceded;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(matchGoals).map(([gameWeek, goals], index) => ({
      name: `${t("stats.game-week-abbreviation")}${gameWeek}`,
      goals,
    }));
  }, [goalsStatsQuery.data?.matchGoals]);

  if (goalsStatsQuery.status === "loading") {
    return <PageLoader message={t("stats.loading")} />;
  }

  if (goalsStatsQuery.status === "error") {
    return (
      <Box>
        <PageError
          message={
            goalsStatsQuery?.error?.message === ErrorMessages.PermissionDenied
              ? t("common.permission-denied")
              : t("stats.load-error")
          }
        />
      </Box>
    );
  }

  return (
    <Paper>
      {goalsPerMatch && (
        <Stack mb={4}>
          <Box>
            <Typography variant="h6" m={1}>
              {t("stats.goals-received-per-match")}
            </Typography>
            <BarChart width={600} height={300} data={goalsPerMatch}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="goals" fill="#8B2E2E " />
            </BarChart>
          </Box>
        </Stack>
      )}
      <Stack direction="row" spacing={2} mt={4} mb={4}>
        <Box width={1 / 2} justifyContent="center">
          <Typography variant="h6" m={1}>
            {t("stats.goals-received-per-half")}
          </Typography>
          <GoalsTeamStats data={goalsStatsQuery.data?.half} />
        </Box>

        <Box width={1 / 2}>
          <Typography variant="h6" m={1}>
            {t("stats.goals-received-times")}
          </Typography>
          <GoalsTeamStats data={goalsStatsQuery.data?.goalTimes || {}} />
        </Box>
      </Stack>
      <Divider />
      <Stack direction="row" spacing={2} mt={4} mb={4}>
        <Box width={1 / 3}>
          <Typography variant="h6" m={1}>
            {t("stats.goals-received-came-from")}
          </Typography>
          <GoalsTeamStats data={goalsStatsQuery.data?.situationType} />
        </Box>

        <Box width={1 / 3}>
          <Typography variant="h6" m={1}>
            {t("stats.goals-received-came-from")}
          </Typography>
          <GoalsTeamStats data={goalsStatsQuery.data?.situationOpenPlaySubType} />
        </Box>

        <Box width={1 / 3}>
          <Typography variant="h6" m={1}>
            {t("stats.goals-set-piece-received")}
          </Typography>
          <GoalsTeamStats data={goalsStatsQuery.data?.situationSetPieceSubType} />
        </Box>
      </Stack>
      <Divider />
      <Stack direction="row" spacing={2} mt={4} mb={4}>
        <Box width={1 / 2}>
          <Typography variant="h6" m={1}>
            {t("stats.goals-received-scored-with")}
          </Typography>
          <GoalsTeamStats data={goalsStatsQuery.data?.bodyPart} />
        </Box>

        <Box width={1 / 2}>
          <Typography variant="h6" m={1}>
            {t("stats.goals-received-by-zone")}
          </Typography>
          <GoalsTeamStats data={goalsStatsQuery.data?.zone} />
        </Box>
      </Stack>
    </Paper>
  );
};

export { TeamGoalsConceded };
