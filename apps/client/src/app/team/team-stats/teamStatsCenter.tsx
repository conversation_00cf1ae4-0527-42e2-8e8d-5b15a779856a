import { FC, SyntheticEvent, useState } from "react";
import { useTranslation } from "react-i18next";

import { TabContext, Box, TabList, Tab, TabPanel } from "@mio/ui";

import { SidebarLayout } from "../../layouts";
import { TeamGoalsScored } from "./teamGoalsScored";
import { TeamGoalsConceded } from "./teamGoalsConceded";

enum Panels {
  GoalsScored = "goals-scored",
  GoalsConceded = "goals-conceded",
}

export const TeamStatsCenter: FC = () => {
  const { t } = useTranslation();
  const [value, setValue] = useState<Panels>(Panels.GoalsScored);

  const handleChange = (_: SyntheticEvent, newValue: Panels) => {
    setValue(newValue);
  };

  return (
    <SidebarLayout>
      <TabContext value={value}>
        <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
          <TabList
            onChange={handleChange}
            aria-label={t("stats.tabs-label")}
            variant="fullWidth"
            centered
          >
            <Tab label={t("stats.goals-scored")} value={Panels.GoalsScored} />
            <Tab label={t("stats.goals-conceded")} value={Panels.GoalsConceded} />
          </TabList>
        </Box>
        <TabPanel value={Panels.GoalsScored}>
          <TeamGoalsScored />
        </TabPanel>
        <TabPanel value={Panels.GoalsConceded}>
          <TeamGoalsConceded />
        </TabPanel>
      </TabContext>
    </SidebarLayout>
  );
};
