import { FC } from "react";
import { useTranslation } from "react-i18next";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Tooltip, Legend, ResponsiveContainer } from "recharts";

// Define the colors for each segment
const COLORS = [
  "#2196f3", // Primary blue
  "#4caf50", // Green
  "#8884d8", // Purple
  "#ff9800", // Orange
  "#607d8b", // Blue grey
  "#9c27b0", // Purple
  "#00acc1", // Cyan
  "#43a047", // Dark green
  "#fb8c00", // Dark orange
  "#5c6bc0", // Indigo
];

const kebabToCapitalizedText = (kebab: string): string => {
  return kebab
    .replace(/-/g, " ") // Replace hyphens with spaces
    .split(" ") // Split the string by spaces
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize the first letter of each word
    .join(" "); // Join the words back into a single string
};

type Props = {
  data: Record<string, number>;
};

const GoalsTeamStats: FC<Props> = ({ data }) => {
  const { t } = useTranslation();

  // Prepare data for each category
  const createChartData = (categoryData: Record<string, number>) => {
    if (!categoryData) {
      return [];
    }

    const data = Object.entries(categoryData).map(([name, value]) => {
      const res = {
        name: t(`stats.goal-types.${name}`, {
          defaultValue: kebabToCapitalizedText(name),
        }),
        value,
      };
      return res;
    });

    return data;
  };

  return (
    <ResponsiveContainer width="100%" height={400}>
      <PieChart>
        <Tooltip />
        <Legend />
        <Pie
          data={createChartData(data)}
          dataKey="value"
          nameKey="name"
          cx="50%"
          cy="50%"
          outerRadius={80}
          fill="#8884d8"
          label
        >
          {createChartData(data).map((_, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Pie>
      </PieChart>
    </ResponsiveContainer>
  );
};

export default GoalsTeamStats;
