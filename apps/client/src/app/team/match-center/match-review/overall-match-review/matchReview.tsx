import { FC } from "react";
import { useTranslation } from "react-i18next";

import {
  useOrganization,
  footballMatchState,
  useRequiredTeamId,
  PageLoader,
  PageError,
} from "@mio/ui";
import { ErrorMessages, FootballMatchReview, isError, PopulatedPlayer, UUID } from "@mio/helpers";

import { SidebarLayout } from "../../../../layouts";
import { UpsertFootballMatchReview } from "./matchReviewUpsertForm";

type Props = {
  teamPlayers: PopulatedPlayer[];
};

export const MatchReview: FC<Props> = ({ teamPlayers }) => {
  const { t } = useTranslation();
  const organization = useOrganization();
  const teamId = useRequiredTeamId();
  const footballMatchPlanId = footballMatchState.useRequiredFootballMatchPlanId();

  const createMatchReview = footballMatchState.useCreateFootballMatchReview();
  const updateMatchReview = footballMatchState.useUpdateFootballMatchReview();

  const reviewQuery = footballMatchState.useGetFootballMatchReview(
    organization.id,
    teamId,
    footballMatchPlanId,
  );

  const matchPlanQuery = footballMatchState.useGetFootballMatchPlan(
    organization.id,
    teamId,
    footballMatchPlanId,
  );

  const handleSubmit = (data: unknown) => {
    if (data && typeof data === "object" && "id" in data && UUID.parse(data.id)) {
      const validated = FootballMatchReview.Entity.toUpdateDto(data);

      if (!isError(validated)) {
        updateMatchReview.mutate(validated);
      }

      return;
    }

    const validated = FootballMatchReview.Entity.toCreateDto(data);

    if (!isError(validated)) {
      createMatchReview.mutate(validated);
    }
  };

  if (reviewQuery.status === "loading" || matchPlanQuery.status === "loading") {
    return (
      <SidebarLayout>
        <PageLoader message={t("matches.review.loading")} />
      </SidebarLayout>
    );
  }

  if (reviewQuery.status === "error" || matchPlanQuery.status === "error") {
    return (
      <SidebarLayout>
        <PageError
          message={
            reviewQuery?.error?.message === ErrorMessages.PermissionDenied
              ? t("matches.review.permission-denied")
              : t("matches.review.load-error")
          }
        />
        <PageLoader message={t("matches.review.load-error")} />
      </SidebarLayout>
    );
  }

  if (reviewQuery.status === "success" && matchPlanQuery.status === "success") {
    return (
      <UpsertFootballMatchReview
        footballMatchReview={reviewQuery.data}
        footballMatchPlan={matchPlanQuery.data}
        onSubmit={handleSubmit}
        loading={createMatchReview.isLoading}
        serverError={createMatchReview.error}
        upsertReviewIsSuccess={createMatchReview.isSuccess || updateMatchReview.isSuccess}
        teamPlayers={teamPlayers}
      />
    );
  }

  return null;
};
