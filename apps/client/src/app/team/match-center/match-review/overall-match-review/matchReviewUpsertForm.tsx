import { FC, useEffect } from "react";
import { Controller, useForm } from "react-hook-form";

import {
  Box,
  Typography,
  TextField,
  Stack,
  LocalError,
  LoadingButton,
  useFormResolver,
  useRequiredTeamId,
  IconButton,
  useTheme,
  RemoveIcon,
  AddIcon,
  InputLabel,
  Select,
  MenuItem,
  footballMatchState,
  ToggleSnackbar,
  Alert,
  useOrganization,
} from "@mio/ui";
import {
  APIError,
  FootballMatchPlan,
  FootballMatchReview,
  PopulatedPlayer,
  Primitive,
} from "@mio/helpers";
import { BackButton } from "../../../../shared";
import { NumericFormat } from "react-number-format";
import { useTranslation } from "react-i18next";
type DataShape = Partial<Primitive<FootballMatchReview.CreateDto> & { id?: string }>;
type Props = {
  footballMatchReview?: FootballMatchReview.CompleteReview | null;
  onSubmit: (data: DataShape) => void;
  loading?: boolean;
  serverError?: APIError | null;
  upsertReviewIsSuccess: boolean;
  footballMatchPlan?: FootballMatchPlan.CompletePlan;
  teamPlayers: PopulatedPlayer[];
};
export const UpsertFootballMatchReview: FC<Props> = ({
  footballMatchReview,
  onSubmit,
  loading,
  serverError,
  upsertReviewIsSuccess,
  footballMatchPlan,
  teamPlayers,
}) => {
  const { t } = useTranslation();
  const theme = useTheme();

  const organization = useOrganization();
  const teamId = useRequiredTeamId();
  const footballMatchPlanId = footballMatchState.useRequiredFootballMatchPlanId();
  const formResolver = useFormResolver(FootballMatchReview.Entity.toUpsertDto);
  const { FootballMatchHost } = FootballMatchPlan;
  const { WeatherConditionsEnum, FootballMatchOutcomeEnum } = FootballMatchReview;
  const { register, handleSubmit, formState, setValue, watch, control } = useForm<DataShape>({
    defaultValues: {
      footballMatchPlanId: footballMatchPlanId,
      goalsConceded: footballMatchReview?.goalsConceded || 0,
      goalsScored: footballMatchReview?.goalsScored || 0,
      minutesDelayed: footballMatchReview?.minutesDelayed,
      outcome: footballMatchReview?.outcome || FootballMatchOutcomeEnum.Draw,
      pitchRating: footballMatchReview?.pitchRating,
      rating: footballMatchReview?.rating || 6.5,
      refereeRating: footballMatchReview?.refereeRating,
      weatherConditions: footballMatchReview?.weatherConditions,
      endDate: footballMatchReview?.endDate,
      arrivalAndWarmUp: footballMatchReview?.arrivalAndWarmUp,
      coachBehaviours: footballMatchReview?.coachBehaviours,
      learningOutcomes: footballMatchReview?.learningOutcomes,
      name: footballMatchReview?.name,
      playersEngagement: footballMatchReview?.playersEngagement,
      safetyConsiderations: footballMatchReview?.safetyConsiderations,
      startDate: footballMatchReview?.startDate,

      oppositionName: footballMatchReview?.oppositionName || footballMatchPlan?.oppositionName,
      host: footballMatchReview?.host || footballMatchPlan?.host || FootballMatchHost.Home,
      organizationId: organization.id,
      teamId: teamId,
      id: footballMatchReview?.id,
    },
    resolver: formResolver,
  });
  const goalsScored = watch("goalsScored");
  const goalsConceded = watch("goalsConceded");
  const host = watch("host");
  const rating = watch("rating");
  const outcome = watch("outcome");
  const weatherConditions = watch("weatherConditions");
  const { errors, submitCount } = formState;
  const hasSubmitted = submitCount > 0;
  useEffect(() => {
    if (goalsConceded === undefined || goalsScored === undefined) return;
    if (goalsScored === goalsConceded) {
      setValue("outcome", FootballMatchOutcomeEnum.Draw, {
        shouldValidate: true,
        shouldDirty: true,
        shouldTouch: true,
      });
    }
    if (goalsScored > goalsConceded) {
      setValue("outcome", FootballMatchOutcomeEnum.Win, {
        shouldValidate: true,
        shouldDirty: true,
        shouldTouch: true,
      });
    }
    if (goalsScored < goalsConceded) {
      setValue("outcome", FootballMatchOutcomeEnum.Loss, {
        shouldValidate: true,
        shouldDirty: true,
        shouldTouch: true,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [goalsScored, goalsConceded]);
  return (
    <Box
      width={1}
      component="form"
      onSubmit={handleSubmit(onSubmit)}
      sx={{ width: "100%", bgcolor: "background.paper" }}
    >
      <Box mt={1}>
        <TextField
          fullWidth
          label={t("matches.review.match.fields.opposition-name")}
          error={hasSubmitted && !!errors.oppositionName}
          helperText={hasSubmitted && errors.oppositionName?.message}
          aria-invalid={hasSubmitted && !!errors.oppositionName?.message}
          {...register("oppositionName")}
        />
      </Box>

      <Box mt={1}>
        <InputLabel aria-required>{t("matches.review.match.fields.host.label")}</InputLabel>
        <Select
          fullWidth
          aria-required
          labelId="hosted-by-label"
          id="host"
          label={t("matches.review.match.fields.host.label")}
          name="host"
          inputProps={{ ...register("host") }}
          value={host}
        >
          <MenuItem value={FootballMatchHost.Home}>
            {t("matches.review.match.fields.host.home")}
          </MenuItem>
          <MenuItem value={FootballMatchHost.Away}>
            {t("matches.review.match.fields.host.away")}
          </MenuItem>
          <MenuItem value={FootballMatchHost.Neutral}>
            {t("matches.review.match.fields.host.neutral")}
          </MenuItem>
        </Select>
      </Box>

      <Stack direction="row" mt={2}>
        <Box alignItems="center" width={4 / 6}>
          <Typography variant="h6" component="h2">
            {t("matches.review.match.fields.goals-scored")}
          </Typography>
        </Box>

        <Stack
          alignItems="center"
          direction="row"
          justifyContent="right"
          gap={1}
          mb={1}
          width={2 / 6}
        >
          <IconButton
            size="small"
            sx={{ borderRadius: "100%", backgroundColor: theme.palette.other.lightGrey }}
            onClick={() => {
              if (goalsScored === 0) return;

              setValue("goalsScored", (goalsScored || 0) - 1, {
                shouldValidate: true,
                shouldDirty: true,
                shouldTouch: true,
              });
            }}
          >
            <RemoveIcon />
          </IconButton>

          <Box width={1 / 5}>
            <Controller
              name={`goalsScored`}
              control={control}
              render={({ field }) => (
                <NumericFormat
                  {...field}
                  customInput={TextField}
                  size="small"
                  helperText={hasSubmitted && errors.goalsScored?.message}
                  error={hasSubmitted && !!errors.goalsScored?.message}
                  aria-invalid={hasSubmitted && !!errors.goalsScored?.message}
                  sx={{
                    textAlign: "center",
                    "& input": {
                      textAlign: "center",

                      paddingLeft: "2px",
                      paddingRight: "2px",
                    },
                  }}
                  onValueChange={(values) => {
                    setValue(`goalsScored`, values.floatValue, {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                  }}
                />
              )}
            />
          </Box>

          <IconButton
            size="small"
            sx={{ borderRadius: "100%", backgroundColor: theme.palette.other.lightGrey }}
            onClick={() => {
              if (goalsScored === 100) return;

              setValue("goalsScored", (goalsScored || 0) + 1, {
                shouldValidate: true,
                shouldDirty: true,
                shouldTouch: true,
              });
            }}
          >
            <AddIcon />
          </IconButton>
        </Stack>
      </Stack>

      <Stack direction="row" mt={2}>
        <Box alignItems="center" width={4 / 6}>
          <Typography variant="h6" component="h2">
            {t("matches.review.match.fields.goals-conceded")}
          </Typography>
        </Box>

        <Stack
          alignItems="center"
          direction="row"
          justifyContent="right"
          gap={1}
          mb={1}
          width={2 / 6}
        >
          <IconButton
            size="small"
            sx={{ borderRadius: "100%", backgroundColor: theme.palette.other.lightGrey }}
            onClick={() => {
              if (goalsScored === 0) return;

              setValue("goalsConceded", (goalsConceded || 0) - 1, {
                shouldValidate: true,
                shouldDirty: true,
                shouldTouch: true,
              });
            }}
          >
            <RemoveIcon />
          </IconButton>

          <Box width={1 / 5}>
            <Controller
              name={`goalsConceded`}
              control={control}
              render={({ field }) => (
                <NumericFormat
                  {...field}
                  customInput={TextField}
                  size="small"
                  helperText={hasSubmitted && errors.goalsConceded?.message}
                  error={hasSubmitted && !!errors.goalsConceded?.message}
                  aria-invalid={hasSubmitted && !!errors.goalsConceded?.message}
                  sx={{
                    textAlign: "center",
                    "& input": {
                      textAlign: "center",

                      paddingLeft: "2px",
                      paddingRight: "2px",
                    },
                  }}
                  onValueChange={(values) => {
                    setValue(`goalsConceded`, values.floatValue, {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                  }}
                />
              )}
            />
          </Box>

          <IconButton
            size="small"
            sx={{ borderRadius: "100%", backgroundColor: theme.palette.other.lightGrey }}
            onClick={() => {
              if (goalsConceded === 100) return;

              setValue("goalsConceded", (goalsConceded || 0) + 1, {
                shouldValidate: true,
                shouldDirty: true,
                shouldTouch: true,
              });
            }}
          >
            <AddIcon />
          </IconButton>
        </Stack>
      </Stack>

      <Box mt={1}>
        <InputLabel aria-required>{t("matches.review.match.fields.outcome.label")}</InputLabel>
        <Select
          fullWidth
          aria-required
          labelId="outcome-label"
          id="outcome"
          label="Outcome"
          name="outcome"
          inputProps={{ ...register("outcome") }}
          value={outcome}
        >
          <MenuItem value={FootballMatchOutcomeEnum.Win}>
            {t("matches.review.match.fields.outcome.win")}
          </MenuItem>
          <MenuItem value={FootballMatchOutcomeEnum.Loss}>
            {t("matches.review.match.fields.outcome.loss")}
          </MenuItem>
          <MenuItem value={FootballMatchOutcomeEnum.Draw}>
            {t("matches.review.match.fields.outcome.draw")}
          </MenuItem>
        </Select>
      </Box>

      {/* <Box mt={2}>
        <TextField
          fullWidth
          label="Minutes delayed"
          error={hasSubmitted && !!errors.minutesDelayed}
          helperText={hasSubmitted && errors.minutesDelayed?.message}
          aria-invalid={hasSubmitted && !!errors.minutesDelayed?.message}
          {...register("minutesDelayed")}
        />
      </Box> */}

      <Stack direction="row" mt={2}>
        <Box alignItems="center" width={4 / 6}>
          <Typography variant="h6" component="h2">
            {t("matches.review.match.fields.rating")}
          </Typography>
        </Box>

        <Stack
          alignItems="center"
          direction="row"
          justifyContent="right"
          gap={1}
          mb={1}
          width={2 / 6}
        >
          <IconButton
            size="small"
            sx={{ borderRadius: "100%", backgroundColor: theme.palette.other.lightGrey }}
            onClick={() => {
              if (rating === 0) return;

              setValue("rating", (rating || 0) - 0.5, {
                shouldValidate: true,
                shouldDirty: true,
                shouldTouch: true,
              });
            }}
          >
            <RemoveIcon />
          </IconButton>

          <Box width={1 / 5} minWidth="25px">
            <Controller
              name={`rating`}
              control={control}
              render={({ field }) => (
                <NumericFormat
                  {...field}
                  customInput={TextField}
                  size="small"
                  helperText={hasSubmitted && errors.rating?.message}
                  error={hasSubmitted && !!errors.rating?.message}
                  aria-invalid={hasSubmitted && !!errors.rating?.message}
                  fixedDecimalScale
                  decimalScale={1}
                  decimalSeparator="."
                  placeholder="6.5"
                  sx={{
                    textAlign: "center",
                    "& input": {
                      textAlign: "center",

                      paddingLeft: 0,
                      paddingRight: 0,
                    },
                  }}
                  onValueChange={(values) => {
                    setValue(`rating`, values.floatValue, {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                  }}
                />
              )}
            />
          </Box>

          <IconButton
            size="small"
            sx={{ borderRadius: "100%", backgroundColor: theme.palette.other.lightGrey }}
            onClick={() => {
              if (rating === 100) return;

              setValue("rating", (rating || 0) + 0.5, {
                shouldValidate: true,
                shouldDirty: true,
                shouldTouch: true,
              });
            }}
          >
            <AddIcon />
          </IconButton>
        </Stack>
      </Stack>

      {/* <Box mt={2}>
        <TextField
          fullWidth
          label="Pitch rating"
          error={hasSubmitted && !!errors.pitchRating}
          helperText={hasSubmitted && errors.pitchRating?.message}
          aria-invalid={hasSubmitted && !!errors.pitchRating?.message}
          {...register("pitchRating")}
        />
      </Box>
      <Box mt={2}>
        <TextField
          fullWidth
          label="Referee rating"
          error={hasSubmitted && !!errors.refereeRating}
          helperText={hasSubmitted && errors.refereeRating?.message}
          aria-invalid={hasSubmitted && !!errors.refereeRating?.message}
          {...register("refereeRating")}
        />
      </Box> */}

      <Box mt={1}>
        <InputLabel aria-required>{t("matches.review.match.fields.weather.label")}</InputLabel>
        <Select
          fullWidth
          aria-required
          labelId="weather-conditions-label"
          id="weatherConditions"
          label="Weather conditions"
          name="weatherConditions"
          inputProps={{ ...register("weatherConditions") }}
          value={weatherConditions}
        >
          <MenuItem value={WeatherConditionsEnum.Sunny}>
            {t("matches.review.match.fields.weather.sunny")}
          </MenuItem>
          <MenuItem value={WeatherConditionsEnum.Rain}>
            {t("matches.review.match.fields.weather.rain")}
          </MenuItem>
          <MenuItem value={WeatherConditionsEnum.Wet}>
            {t("matches.review.match.fields.weather.wet")}
          </MenuItem>
          <MenuItem value={WeatherConditionsEnum.Overcast}>
            {t("matches.review.match.fields.weather.overcast")}
          </MenuItem>
          <MenuItem value={WeatherConditionsEnum.Snow}>
            {t("matches.review.match.fields.weather.snow")}
          </MenuItem>
          <MenuItem value={WeatherConditionsEnum.Fog}>
            {t("matches.review.match.fields.weather.fog")}
          </MenuItem>
        </Select>
      </Box>

      <Box mt={5} mb={2} sx={{ display: "flex", justifyContent: "center" }}>
        <LoadingButton
          size="large"
          type="submit"
          loading={loading}
          variant="contained"
          color="secondary"
        >
          {t("matches.review.match.save")}
        </LoadingButton>
      </Box>

      {serverError && <LocalError message={t("matches.review.match.error")} />}

      <ToggleSnackbar open={upsertReviewIsSuccess}>
        <Alert severity="success">
          {`${
            footballMatchReview?.id
              ? t("matches.review.match.updated")
              : t("matches.review.match.created")
          }`}
        </Alert>
      </ToggleSnackbar>
    </Box>
  );
};
