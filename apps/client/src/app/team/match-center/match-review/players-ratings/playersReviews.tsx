import { FC } from "react";
import { useTranslation } from "react-i18next";

import {
  useOrganization,
  footballMatchState,
  useRequiredTeamId,
  PageLoader,
  PageError,
} from "@mio/ui";
import { ErrorMessages, FootballMatchPlayerReview, isError, PopulatedPlayer } from "@mio/helpers";

import { SidebarLayout } from "../../../../layouts";

import { PlayersRatingsForm } from "./playersRatingsForm";

type Props = {
  teamPlayers: PopulatedPlayer[];
};

export const PlayersReviews: FC<Props> = ({ teamPlayers }) => {
  const { t } = useTranslation();
  const organization = useOrganization();
  const teamId = useRequiredTeamId();
  const footballMatchPlanId = footballMatchState.useRequiredFootballMatchPlanId();

  const upsertMatchPlayerReviews = footballMatchState.useUpsertFootballMatchReviews(
    organization.id,
    teamId,
    footballMatchPlanId,
  );

  const playerRatingsQuery = footballMatchState.useGetFootballMatchPlayerReviews(
    organization.id,
    teamId,
    footballMatchPlanId,
  );

  const handlePlayerReviewsSubmit = (data: unknown) => {
    if (!data || typeof data !== "object" || !("reviews" in data) || !Array.isArray(data.reviews)) {
      return;
    }

    const validated = FootballMatchPlayerReview.Entity.toCreateManyDto(
      data.reviews.filter(
        ({ overallRating, overallPosition, overallHighlights, overallMinutesPlayed }) =>
          !!overallRating || !!overallPosition || !!overallHighlights || !!overallMinutesPlayed,
      ),
    );

    if (!isError(validated)) {
      upsertMatchPlayerReviews.mutate(validated);
    }
  };

  if (playerRatingsQuery.status === "loading") {
    return (
      <SidebarLayout>
        <PageLoader message={t("matches.review.loading")} />
      </SidebarLayout>
    );
  }

  if (playerRatingsQuery.status === "error") {
    return (
      <SidebarLayout>
        <PageError
          message={
            playerRatingsQuery?.error?.message === ErrorMessages.PermissionDenied
              ? t("matches.review.permission-denied")
              : t("matches.review.load-error")
          }
        />
        <PageLoader message={t("matches.review.load-error")} />
      </SidebarLayout>
    );
  }

  if (playerRatingsQuery.status === "success") {
    return (
      <PlayersRatingsForm
        onSubmit={handlePlayerReviewsSubmit}
        loading={upsertMatchPlayerReviews.isLoading}
        serverError={upsertMatchPlayerReviews.error}
        teamPlayers={teamPlayers}
        playersRatings={playerRatingsQuery.data}
        createReviewIsSuccess={upsertMatchPlayerReviews.isSuccess}
      />
    );
  }

  return null;
};
