import { FC } from "react";
import { Controller, useFormContext } from "react-hook-form";
import {
  Box,
  Stack,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  TextField,
  Button,
  AddIcon,
  IconButton,
  RemoveIcon,
  useTheme,
  Chip,
  Tooltip,
} from "@mio/ui";
import { Attendance, PlayerAttributes, FootballMatchPlayerReview } from "@mio/helpers";
import { NumericFormat } from "react-number-format";
import { DataShape } from "../types";
import { useTranslation } from "react-i18next";

// TODO: these should be defined elsewhere, like the model
const MINUTES_CONFIG = {
  MIN: 0,
  MAX: 90,
  DEFAULT: 0,
  STEP: 10,
} as const;

type Props = {
  index: number;
  onSubmit: () => void;
};

export const UpsertPlayerReviewForm: FC<Props> = ({ index, onSubmit }) => {
  const { formState, setValue, getValues, control } = useFormContext<DataShape>();
  const { dirtyFields, errors: formErrors, submitCount } = formState;
  const { t } = useTranslation();

  const hasSubmitted = submitCount > 0;
  const errors = !hasSubmitted ? {} : formErrors;
  const currentReview = getValues(`reviews.${index}`);

  const theme = useTheme();

  return (
    <Stack gap={3} mt={2}>
      <Controller
        name={`reviews.${index}.attendance`}
        control={control}
        render={(params) => (
          <FormControl>
            <InputLabel htmlFor="attendance-select">
              {t("matches.review.players.attendance")}
            </InputLabel>

            <Select
              fullWidth
              inputProps={{ ...params.field }}
              id="attendance-select"
              label={t("matches.review.players.attendance")}
            >
              {Object.values(Attendance).map((attribute) => (
                <MenuItem key={attribute} value={attribute}>
                  {t(`training.attendance-types.${attribute}`)}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        )}
      />

      <Controller
        name={`reviews.${index}.overallPosition`}
        control={control}
        render={(params) => (
          <FormControl>
            <InputLabel htmlFor="position-select">
              {t("matches.review.players.mainPosition")}
            </InputLabel>
            <Select
              fullWidth
              inputProps={{ ...params.field }}
              id="position-select"
              label={t("matches.review.players.mainPosition")}
            >
              {Object.values(FootballMatchPlayerReview.TacticalPlayerPositionsEnum).map(
                (position) => (
                  <MenuItem key={position} value={position}>
                    {t(`matches.review.players.tactical-positions.${position}`)}
                  </MenuItem>
                ),
              )}
            </Select>
          </FormControl>
        )}
      />

      <Stack alignItems="center" direction="row" justifyContent="space-between" gap={1} mb={1}>
        <InputLabel htmlFor="minutes-played">
          {t("matches.review.players.minutesPlayed")}
        </InputLabel>
        <Stack
          alignItems="center"
          direction="row"
          justifyContent="right"
          gap={1}
          mb={1}
          width={2 / 6}
        >
          <Tooltip title={t("matches.review.players.review.minutesPlayed.decrease")}>
            <IconButton
              aria-label={t("matches.review.players.review.minutesPlayed.decrease")}
              size="small"
              sx={{ borderRadius: "100%", backgroundColor: theme.palette.other.lightGrey }}
              onClick={() => {
                const value = currentReview?.overallMinutesPlayed;
                if (value !== undefined && value - MINUTES_CONFIG.STEP < MINUTES_CONFIG.MIN) return;

                setValue(
                  `reviews.${index}.overallMinutesPlayed`,
                  (value || MINUTES_CONFIG.DEFAULT) - MINUTES_CONFIG.STEP,
                  {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  },
                );
              }}
            >
              <RemoveIcon />
            </IconButton>
          </Tooltip>

          <Controller
            name={`reviews.${index}.overallMinutesPlayed`}
            control={control}
            render={({ field }) => (
              <Box width={160}>
                <NumericFormat
                  {...field}
                  id="minutes-played"
                  customInput={TextField}
                  size="small"
                  sx={{
                    textAlign: "center",
                    "& input": {
                      textAlign: "center",
                    },
                  }}
                  helperText={
                    hasSubmitted &&
                    dirtyFields.reviews?.[index] &&
                    errors.reviews?.[index]?.overallMinutesPlayed?.message
                  }
                  error={
                    hasSubmitted &&
                    dirtyFields.reviews?.[index] &&
                    !!errors.reviews?.[index]?.overallMinutesPlayed?.message
                  }
                  aria-invalid={
                    hasSubmitted &&
                    dirtyFields.reviews?.[index] &&
                    !!errors.reviews?.[index]?.overallMinutesPlayed?.message
                  }
                  onValueChange={(values) => {
                    setValue(`reviews.${index}.overallMinutesPlayed`, values.floatValue, {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                  }}
                />
              </Box>
            )}
          />

          <Tooltip title={t("matches.review.players.review.minutesPlayed.increase")}>
            <IconButton
              aria-label={t("matches.review.players.review.minutesPlayed.increase")}
              size="small"
              sx={{ borderRadius: "100%", backgroundColor: theme.palette.other.lightGrey }}
              onClick={() => {
                const value = currentReview?.overallMinutesPlayed;
                if (value !== undefined && value + MINUTES_CONFIG.STEP > MINUTES_CONFIG.MAX) return;

                setValue(
                  `reviews.${index}.overallMinutesPlayed`,
                  (value || MINUTES_CONFIG.DEFAULT) + MINUTES_CONFIG.STEP,
                  {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  },
                );
              }}
            >
              <AddIcon />
            </IconButton>
          </Tooltip>
        </Stack>
      </Stack>

      <Controller
        name={`reviews.${index}.overallHighlights`}
        control={control}
        render={(params) => (
          <FormControl>
            <InputLabel htmlFor="highlights-select">
              {t("matches.review.players.highlights")}
            </InputLabel>

            <Select
              fullWidth
              inputProps={{ ...params.field }}
              id="highlights-select"
              label={t("matches.review.players.highlights")}
              multiple
              renderValue={(selected: PlayerAttributes[]) => (
                <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
                  {selected.map((value) => (
                    <Chip
                      key={value}
                      label={t(`matches.review.players.player-attributes.${value}`)}
                    />
                  ))}
                </Box>
              )}
            >
              {Object.values(PlayerAttributes).map((attribute) => (
                <MenuItem key={attribute} value={attribute}>
                  {t(`matches.review.players.player-attributes.${attribute}`)}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        )}
      />

      <Box gap={2} sx={{ display: "flex", justifyContent: "center" }}>
        <Button variant="contained" onClick={onSubmit}>
          {t("common.save")}
        </Button>
      </Box>
    </Stack>
  );
};
