import { useState } from "react";
import { Modal, Typography } from "@mio/ui";

import { UpsertPlayerReviewForm } from "./upsertPlayerReviewForm";
import { useTranslation } from "react-i18next";
type Props = {
  playerName: string;
  onSubmit: () => void;
};

export const useEditPlayerReview = ({ playerName, onSubmit }: Props) => {
  const { t } = useTranslation();
  const [reviewIndex, setReviewIndex] = useState<number | undefined>();
  const handleClose = () => {
    setReviewIndex(undefined);
  };

  const handleSubmit = () => {
    onSubmit();
    handleClose();
  };

  return {
    editPlayerReview: setReviewIndex,
    EditPlayerReviewUI: (
      <Modal
        open={reviewIndex !== undefined}
        fullWidth
        onClose={handleClose}
        title={
          <Typography variant="h5" component="h2">
            {t("matches.review.players.review.title")} {playerName}
          </Typography>
        }
        content={
          reviewIndex !== undefined && (
            <UpsertPlayerReviewForm index={reviewIndex} onSubmit={handleSubmit} />
          )
        }
      />
    ),
  };
};
