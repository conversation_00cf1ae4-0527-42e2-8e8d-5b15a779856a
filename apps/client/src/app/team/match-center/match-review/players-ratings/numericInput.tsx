import React from "react";
import { Stack, Box, Typography, IconButton, TextField } from "@mui/material";
import { Control, Controller } from "react-hook-form";
import { NumericFormat } from "react-number-format";
import RemoveIcon from "@mui/icons-material/Remove";
import AddIcon from "@mui/icons-material/Add";

interface NumericInputProps {
  value: number;
  setValue: (name: string, value: unknown, options?: object) => void;
  control: Control;
  hasSubmitted: boolean;
  errors: any;
  buttonBackgroundColour: string;
  label: string;
  minValue: number;
  maxValue: number;
  step: number;
}

// TODO: MAKE THIS REUSABLE COMPONENT WORK OUT
const NumericInput: React.FC<NumericInputProps> = ({
  value,
  setValue,
  control,
  hasSubmitted,
  errors,
  buttonBackgroundColour,
  label,
  minValue,
  maxValue,
  step,
}) => {
  return (
    <Stack direction="row" mt={2}>
      <Box alignItems="center" width={4 / 6}>
        <Typography variant="h6" component="h2">
          {label}
        </Typography>
      </Box>

      <Stack
        alignItems="center"
        direction="row"
        justifyContent="right"
        gap={1}
        mb={1}
        width={2 / 6}
      >
        <IconButton
          size="small"
          sx={{ borderRadius: "100%", backgroundColor: buttonBackgroundColour }}
          onClick={() => {
            if (value <= minValue) return;

            setValue("value", (value || 0) - step, {
              shouldValidate: true,
              shouldDirty: true,
              shouldTouch: true,
            });
          }}
        >
          <RemoveIcon />
        </IconButton>

        <Box width={1 / 5}>
          <Controller
            name={`value`}
            control={control}
            render={({ field }) => (
              <NumericFormat
                {...field}
                customInput={TextField}
                size="small"
                helperText={hasSubmitted && errors.value?.message}
                error={hasSubmitted && !!errors.value?.message}
                aria-invalid={hasSubmitted && !!errors.value?.message}
                sx={{
                  textAlign: "center",
                  "& input": {
                    textAlign: "center",
                  },
                }}
                onValueChange={(values) => {
                  setValue(`value`, values.floatValue, {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                }}
              />
            )}
          />
        </Box>

        <IconButton
          size="small"
          sx={{ borderRadius: "100%", backgroundColor: buttonBackgroundColour }}
          onClick={() => {
            if (value >= maxValue) return;

            setValue("value", (value || 0) + step, {
              shouldValidate: true,
              shouldDirty: true,
              shouldTouch: true,
            });
          }}
        >
          <AddIcon />
        </IconButton>
      </Stack>
    </Stack>
  );
};

export default NumericInput;
