import { FC, SyntheticEvent, useState } from "react";
import { useTranslation } from "react-i18next";

import {
  useOrganization,
  playersState,
  useRequiredTeamId,
  PageLoader,
  PageError,
  TabContext,
  Box,
  TabList,
  Tab,
  TabPanel,
  Stack,
  Typography,
} from "@mio/ui";
import { ErrorMessages } from "@mio/helpers";

import { SidebarLayout } from "../../../layouts";
import { MatchReview } from "./overall-match-review/matchReview";
import { PlayersReviews } from "./players-ratings/playersReviews";
import { GoalsReview } from "./goals-review/goalsReview";
import { BackButton } from "../../../shared";
import { AttemptsForReview } from "./attempts/attemptsReview";

enum Panels {
  Match = "match",
  Goals = "goals",
  PlayersRatings = "players ratings",
  Attempts = "attempts",
}

export const MatchReviewCenter: FC = () => {
  const { t } = useTranslation();
  const organization = useOrganization();
  const teamId = useRequiredTeamId();

  const teamPlayers = playersState.useTeamPlayers(teamId, organization.id);

  const [value, setValue] = useState<Panels>(Panels.Match);

  const handleChange = (_: SyntheticEvent, newValue: Panels) => {
    setValue(newValue);
  };

  if (teamPlayers.query.status === "loading") {
    return (
      <SidebarLayout>
        <PageLoader message={t("matches.review.loading")} />
      </SidebarLayout>
    );
  }

  if (teamPlayers.query.status === "error") {
    return (
      <SidebarLayout>
        <PageError
          message={
            teamPlayers?.query?.error?.message === ErrorMessages.PermissionDenied
              ? t("matches.review.permission-denied")
              : t("matches.review.load-error")
          }
        />
        <PageLoader message={t("matches.review.load-error")} />
      </SidebarLayout>
    );
  }

  if (teamPlayers.query.status === "success") {
    return (
      <SidebarLayout>
        <Stack direction="row" alignItems="center" mb={5} gap={1} flexWrap="wrap">
          <Box>
            <BackButton path="../">{t("matches.review.match.back-to-match-center")}</BackButton>
          </Box>
          <Box component="legend" display="block" sx={{ flexGrow: 1, pr: 5 }}>
            <Typography variant="h5" component="h1" textAlign="center">
              {t("matches.review.match.title")}
            </Typography>
          </Box>
        </Stack>

        <TabContext value={value}>
          <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
            <TabList
              onChange={handleChange}
              aria-label="Match review tabs"
              variant="fullWidth"
              centered
            >
              <Tab label={t("matches.review.tabs.overview")} value={Panels.Match} />
              <Tab label={t("matches.review.tabs.goals")} value={Panels.Goals} />
              <Tab label={t("matches.review.tabs.attempts")} value={Panels.Attempts} />
              <Tab label={t("matches.review.tabs.players")} value={Panels.PlayersRatings} />
            </TabList>
          </Box>
          <TabPanel value={Panels.Match}>
            <MatchReview teamPlayers={teamPlayers.query.data} />
          </TabPanel>
          <TabPanel value={Panels.Goals}>
            <GoalsReview teamPlayers={teamPlayers.query.data} />
          </TabPanel>
          <TabPanel value={Panels.Attempts}>
            <AttemptsForReview />
          </TabPanel>
          <TabPanel value={Panels.PlayersRatings}>
            <PlayersReviews teamPlayers={teamPlayers.query.data} />
          </TabPanel>
        </TabContext>
      </SidebarLayout>
    );
  }

  return null;
};
