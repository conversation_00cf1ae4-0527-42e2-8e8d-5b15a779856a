import { FC } from "react";
import { useTranslation } from "react-i18next";

import {
  useOrganization,
  footballMatchState,
  useRequiredTeamId,
  PageLoader,
  PageError,
  Box,
  useTheme,
  Typography,
} from "@mio/ui";
import { ErrorMessages, FootballMatchAttemptAgainst, isError, PopulatedPlayer } from "@mio/helpers";

import { SidebarLayout } from "../../../../../layouts";
import { AttemptAgainstForm } from "./goalConcededForm";

type Props = {
  teamPlayers: PopulatedPlayer[];
};

export const GoalsConcededReview: FC<Props> = ({ teamPlayers }) => {
  const theme = useTheme();
  const { t } = useTranslation();
  const organization = useOrganization();
  const teamId = useRequiredTeamId();
  const footballMatchPlanId = footballMatchState.useRequiredFootballMatchPlanId();

  const updateAttemptAgainstReview = footballMatchState.useUpdateFootballMatchAttemptAgainst(
    organization.id,
    teamId,
  );

  const goalsConcededQuery = footballMatchState.useGetFootballMatchAttemptsAgainst(
    organization.id,
    teamId,
    footballMatchPlanId,
  );

  const handleSubmit = (data: unknown) => {
    const validated = FootballMatchAttemptAgainst.Entity.toUpdateDto(data);

    if (!isError(validated)) {
      updateAttemptAgainstReview.mutate(validated);
    }
  };

  if (goalsConcededQuery.status === "loading") {
    return (
      <SidebarLayout>
        <PageLoader message={t("matches.review.loading")} />
      </SidebarLayout>
    );
  }

  if (goalsConcededQuery.status === "error") {
    return (
      <SidebarLayout>
        <PageError
          message={
            goalsConcededQuery?.error?.message === ErrorMessages.PermissionDenied
              ? t("matches.review.permission-denied")
              : t("matches.review.load-error")
          }
        />
        <PageLoader message={t("matches.review.load-error")} />
      </SidebarLayout>
    );
  }

  if (goalsConcededQuery.status === "success") {
    return (
      <Box>
        <Box>
          {goalsConcededQuery.data && goalsConcededQuery.data.length > 0
            ? goalsConcededQuery.data.map((goal, index) => (
                <Box
                  sx={{
                    border: "1px solid",
                    borderRadius: "4px",
                    padding: 3,
                    margin: 1,
                    borderColor: theme.palette.secondary.main,
                  }}
                >
                  <Typography variant="h5" gutterBottom color={theme.palette.secondary.main}>
                    {t("match.goal-conceded")} #{index + 1}
                  </Typography>

                  <AttemptAgainstForm
                    index={index}
                    key={goal.id}
                    attemptAgainst={goal}
                    teamPlayers={teamPlayers}
                    onSubmit={handleSubmit}
                    upsertReviewIsSuccess={updateAttemptAgainstReview.isSuccess}
                  />
                </Box>
              ))
            : null}
        </Box>
        <Box></Box>
      </Box>
    );
  }

  return null;
};
