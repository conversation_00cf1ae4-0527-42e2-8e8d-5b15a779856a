import { FC } from "react";
import { useTranslation } from "react-i18next";

import {
  useOrganization,
  footballMatchState,
  useRequiredTeamId,
  PageLoader,
  PageError,
  Box,
  Button,
  AddIcon,
  useTheme,
  Typography,
} from "@mio/ui";
import {
  ErrorMessages,
  FootballMatchAttemptFor,
  isError,
  PopulatedPlayer,
  TeamId,
} from "@mio/helpers";

import { SidebarLayout } from "../../../../../layouts";
import { AttemptForForm } from "./goalScoredForm";

type Props = {
  teamPlayers: PopulatedPlayer[];
};

export const GoalsScoredReview: FC<Props> = ({ teamPlayers }) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const organization = useOrganization();
  const teamId = useRequiredTeamId();
  const footballMatchPlanId = footballMatchState.useRequiredFootballMatchPlanId();

  const updateAttemptForReview = footballMatchState.useUpdateFootballMatchAttemptFor(
    organization.id,
    teamId,
  );

  const goalsScoredQuery = footballMatchState.useGetFootballMatchAttemptsFor(
    organization.id,
    teamId,
    footballMatchPlanId,
  );

  const deleteAttemptForReview = footballMatchState.useDeleteFootballMatchAttemptFor(
    organization.id,
    teamId,
  );

  const handleSubmit = (data: unknown) => {
    const validated = FootballMatchAttemptFor.Entity.toUpdateDto(data);

    if (!isError(validated)) {
      updateAttemptForReview.mutate(validated);
    }
  };

  if (goalsScoredQuery.status === "loading") {
    return (
      <SidebarLayout>
        <PageLoader message={t("matches.review.loading")} />
      </SidebarLayout>
    );
  }

  if (goalsScoredQuery.status === "error") {
    return (
      <SidebarLayout>
        <PageError
          message={
            goalsScoredQuery?.error?.message === ErrorMessages.PermissionDenied
              ? t("matches.review.permission-denied")
              : t("matches.review.load-error")
          }
        />
        <PageLoader message={t("matches.review.load-error")} />
      </SidebarLayout>
    );
  }

  const handleDelete = (attempt: FootballMatchAttemptFor.AttemptFor) => {
    deleteAttemptForReview.mutate(attempt);
  };

  const handleAddNewAttempt = () => {
    const newAttempt = {
      footballMatchPlanId,
      organizationId: organization.id,
      teamId,
    };

    handleSubmit(newAttempt);
  };

  if (goalsScoredQuery.status === "success") {
    return (
      <Box>
        <Box sx={{ display: "flex", justifyContent: "center", mt: 2 }}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleAddNewAttempt}
          >
            {t("match.add-attempt")}
          </Button>
        </Box>
        <Box>
          {goalsScoredQuery.data && goalsScoredQuery.data.length > 0
            ? goalsScoredQuery.data.map((goal, index) => (
                <Box
                  sx={{
                    border: "1px solid",
                    borderRadius: "4px",
                    padding: 3,
                    margin: 1,
                    borderColor: theme.palette.success.main,
                  }}
                >
                  <Typography variant="h5" gutterBottom color={theme.palette.success.main}>
                    {t("match.goal-scored")} #{index + 1}
                  </Typography>

                  <AttemptForForm
                    index={index}
                    key={goal.id}
                    attemptFor={goal}
                    teamPlayers={teamPlayers}
                    onSubmit={handleSubmit}
                    upsertReviewIsSuccess={updateAttemptForReview.isSuccess}
                  />
                </Box>
              ))
            : null}
        </Box>
      </Box>
    );
  }

  return null;
};
