import { FC } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";

import {
  Box,
  TextField,
  Stack,
  LocalError,
  LoadingButton,
  useFormResolver,
  useRequiredTeamId,
  InputLabel,
  Select,
  MenuItem,
  footballMatchState,
  ToggleSnackbar,
  Alert,
  useOrganization,
  Typography,
  useTheme,
  Grid,
  Paper,
  FormControl,
} from "@mio/ui";

import {
  APIError,
  FootballMatchAttemptAgainst,
  FootballMatchAttemptFor,
  PopulatedPlayer,
  Primitive,
} from "@mio/helpers";

type DataShape = Partial<Primitive<FootballMatchAttemptAgainst.CreateDto> & { id?: string }>;

type Props = {
  attemptAgainst?: FootballMatchAttemptAgainst.AttemptAgainst;
  onSubmit: (data: DataShape) => void;
  loading?: boolean;
  serverError?: APIError | null;
  upsertReviewIsSuccess: boolean;
  teamPlayers: PopulatedPlayer[];
  index: number;
};

export const AttemptAgainstForm: FC<Props> = ({
  attemptAgainst,
  onSubmit,
  loading,
  serverError,
  upsertReviewIsSuccess,
  teamPlayers,
  index,
}) => {
  const { t } = useTranslation();
  const theme = useTheme();

  const organization = useOrganization();
  const teamId = useRequiredTeamId();
  const footballMatchPlanId = footballMatchState.useRequiredFootballMatchPlanId();
  const formResolver = useFormResolver(FootballMatchAttemptAgainst.Entity.toUpsertDto);

  const {
    FootballMatchHalves,
    AttemptForBodyPart,
    GoalSituationOpenPlaySubType,
    GoalSituationSetPieceSubType,
    GoalSituationType,
  } = FootballMatchAttemptFor;

  const { AttemptAgainstType } = FootballMatchAttemptAgainst;

  const { register, handleSubmit, formState, watch } = useForm<DataShape>({
    defaultValues: {
      footballMatchPlanId: footballMatchPlanId,
      organizationId: organization.id,
      teamId: teamId,

      id: attemptAgainst?.id,

      addedTime: attemptAgainst?.addedTime,
      assist: attemptAgainst?.assist,
      assistBodyPart: attemptAgainst?.assistBodyPart,
      assistZone: attemptAgainst?.assistZone,
      bodyPart: attemptAgainst?.bodyPart,
      type: attemptAgainst?.type || AttemptAgainstType.goal,
      half: attemptAgainst?.half,
      keyPass: attemptAgainst?.keyPass,
      keyPassZone: attemptAgainst?.keyPassZone,
      minute: attemptAgainst?.minute,
      shooter: attemptAgainst?.shooter,
      errorLeadingDirectlyToTheGoal: attemptAgainst?.errorLeadingDirectlyToTheGoal,
      ownGoalScorer: attemptAgainst?.ownGoalScorer,
      assisterPosition: attemptAgainst?.assisterPosition,
      improvementHighlights: attemptAgainst?.improvementHighlights,
      keyPassPosition: attemptAgainst?.keyPassPosition,
      mistakes: attemptAgainst?.mistakes,
      scorerPosition: attemptAgainst?.scorerPosition,

      zone: attemptAgainst?.zone,
      keyContributions: attemptAgainst?.keyContributions,
      highlights: attemptAgainst?.highlights,
      situationType: attemptAgainst?.situationType,
      situationOpenPlaySubType: attemptAgainst?.situationOpenPlaySubType,
      situationSetPieceSubType: attemptAgainst?.situationSetPieceSubType,
    },
    resolver: formResolver,
  });

  const half = watch("half");
  const situationType = watch("situationType");
  const bodyPart = watch("bodyPart");
  const assistBodyPart = watch("assistBodyPart");
  const scorer = watch("shooter");
  const situationOpenPlaySubType = watch("situationOpenPlaySubType");
  const situationSetPieceSubType = watch("situationSetPieceSubType");
  const type = watch("type");
  const errorLeadingDirectlyToTheGoal = watch("errorLeadingDirectlyToTheGoal");

  const { errors, submitCount, isDirty } = formState;
  const hasSubmitted = submitCount > 0;

  return (
    <Box
      width={1}
      component="form"
      onSubmit={handleSubmit(onSubmit)}
      sx={{ width: "100%", bgcolor: "background.paper" }}
    >
      <Grid container spacing={2} p={2}>
        {(!attemptAgainst ||
          (attemptAgainst && attemptAgainst.type !== AttemptAgainstType.goal)) && (
          <Grid item xs={12} sm={6} md={4}>
            <FormControl fullWidth>
              <InputLabel
                id="attempt-type-label"
                sx={{
                  backgroundColor: "background.paper",
                  px: 1,
                }}
              >
                {t("match.attempt-type")}
              </InputLabel>
              <Select
                labelId="attempt-type-label"
                label={t("match.attempt-type")}
                fullWidth
                {...register(`type`)}
                value={type || AttemptAgainstType.shotOffGoal}
              >
                <MenuItem value={AttemptAgainstType.shotOffGoal}>
                  {t("match.attempt-type-options.shot-off-goal")}
                </MenuItem>
                <MenuItem value={AttemptAgainstType.shotOnGoal}>
                  {t("match.attempt-type-options.shot-on-goal")}
                </MenuItem>
                <MenuItem value={AttemptAgainstType.shotBlocked}>
                  {t("match.attempt-type-options.shot-blocked")}
                </MenuItem>
              </Select>
            </FormControl>
          </Grid>
        )}

        <Grid item xs={12}>
          {/* Group 1: Time Details */}
          <Grid container spacing={2} mb={3}>
            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label={t("match.minute")}
                error={hasSubmitted && !!errors?.minute?.message}
                helperText={hasSubmitted && errors.minute?.message}
                InputLabelProps={{ shrink: true }}
                {...register(`minute`)}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label={t("match.added-time")}
                error={hasSubmitted && !!errors.addedTime?.message}
                helperText={hasSubmitted && errors.addedTime?.message}
                InputLabelProps={{ shrink: true }}
                {...register(`addedTime`)}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <FormControl fullWidth>
                <InputLabel shrink>{t("match.half")}</InputLabel>
                <Select
                  inputProps={{ ...register(`half`) }}
                  value={half}
                  label={t("match.half")}
                  notched
                >
                  <MenuItem value={FootballMatchHalves.first}>{t("match.first-half")}</MenuItem>
                  <MenuItem value={FootballMatchHalves.second}>{t("match.second-half")}</MenuItem>
                  <MenuItem value={FootballMatchHalves.extraTimeFirst}>
                    {t("match.extra-time-first")}
                  </MenuItem>
                  <MenuItem value={FootballMatchHalves.extraTimeSecond}>
                    {t("match.extra-time-second")}
                  </MenuItem>
                  <MenuItem value={FootballMatchHalves.afterMatchPenalties}>
                    {t("match.penalties")}
                  </MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>

          {/* Group 2: Situation Details */}
          <Grid container spacing={2} mb={3}>
            <Grid item xs={12} sm={6} md={4}>
              <FormControl fullWidth>
                <InputLabel shrink>{t("match.situation")}</InputLabel>
                <Select
                  inputProps={{ ...register(`situationType`) }}
                  value={situationType}
                  label={t("match.situation")}
                  notched
                >
                  <MenuItem value={GoalSituationType.openPlay}>{t("match.open-play")}</MenuItem>
                  <MenuItem value={GoalSituationType.setPiece}>{t("match.set-piece")}</MenuItem>
                  <MenuItem value={GoalSituationType.errorLeadingToAGoal}>
                    {t("match.error-leading-to-goal")}
                  </MenuItem>
                  <MenuItem value={GoalSituationType.pressing}>{t("match.pressing")}</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            {situationType === GoalSituationType.openPlay && (
              <Grid item xs={12} sm={6} md={4}>
                <FormControl fullWidth>
                  <InputLabel shrink>{t("match.situation-detailed")}</InputLabel>
                  <Select
                    inputProps={{ ...register(`situationOpenPlaySubType`) }}
                    value={situationOpenPlaySubType}
                    label={t("match.situation-detailed")}
                    notched
                  >
                    <MenuItem value={GoalSituationOpenPlaySubType.combinationPlay}>
                      {t("match.open-play-sub-type.combination-play")}
                    </MenuItem>
                    <MenuItem value={GoalSituationOpenPlaySubType.counterAttack}>
                      {t("match.open-play-sub-type.counter-attack")}
                    </MenuItem>
                    <MenuItem value={GoalSituationOpenPlaySubType.cross}>
                      {t("match.open-play-sub-type.cross")}
                    </MenuItem>
                    <MenuItem value={GoalSituationOpenPlaySubType.dribble}>
                      {t("match.open-play-sub-type.dribble")}
                    </MenuItem>
                    <MenuItem value={GoalSituationOpenPlaySubType.longShot}>
                      {t("match.open-play-sub-type.long-shot")}
                    </MenuItem>
                    <MenuItem value={GoalSituationOpenPlaySubType.oneToOne}>
                      {t("match.open-play-sub-type.one-to-one")}
                    </MenuItem>
                    <MenuItem value={GoalSituationOpenPlaySubType.rebound}>
                      {t("match.open-play-sub-type.rebound")}
                    </MenuItem>
                    <MenuItem value={GoalSituationOpenPlaySubType.throughBall}>
                      {t("match.open-play-sub-type.through-ball")}
                    </MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            )}
            {situationType === GoalSituationType.setPiece && (
              <Grid item xs={12} sm={6} md={4}>
                <FormControl fullWidth>
                  <InputLabel shrink>{t("match.situation-detailed")}</InputLabel>
                  <Select
                    inputProps={{ ...register(`situationSetPieceSubType`) }}
                    value={situationSetPieceSubType}
                    label={t("match.situation-detailed")}
                    notched
                  >
                    <MenuItem value={GoalSituationSetPieceSubType.corner}>
                      {t("match.set-piece-sub-type.corner")}
                    </MenuItem>
                    <MenuItem value={GoalSituationSetPieceSubType.directFreeKick}>
                      {t("match.set-piece-sub-type.direct-free-kick")}
                    </MenuItem>
                    <MenuItem value={GoalSituationSetPieceSubType.goalKick}>
                      {t("match.set-piece-sub-type.goal-kick")}
                    </MenuItem>
                    <MenuItem value={GoalSituationSetPieceSubType.indirectFreeKick}>
                      {t("match.set-piece-sub-type.indirect-free-kick")}
                    </MenuItem>
                    <MenuItem value={GoalSituationSetPieceSubType.penalty}>
                      {t("match.set-piece-sub-type.penalty")}
                    </MenuItem>
                    <MenuItem value={GoalSituationSetPieceSubType.throwIn}>
                      {t("match.set-piece-sub-type.throw-in")}
                    </MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            )}
          </Grid>

          {/* Group 3: Goal Details */}
          <Grid container spacing={2} mb={3}>
            {type === AttemptAgainstType.goal && (
              <Grid item xs={12} sm={6} md={4}>
                <FormControl fullWidth>
                  <InputLabel shrink>Type</InputLabel>
                  <Select inputProps={{ ...register(`type`) }} value={type} label="Type" notched>
                    <MenuItem value={AttemptAgainstType.goal}>{t("match.type-normal")}</MenuItem>
                    <MenuItem value={AttemptAgainstType.ownGoal}>{t("match.type-own")}</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            )}
            <Grid item xs={12} sm={6} md={4}>
              {type === AttemptAgainstType.ownGoal ? (
                <FormControl fullWidth>
                  <InputLabel shrink>{t("match.goal-scorer")}</InputLabel>
                  <Select
                    inputProps={{ ...register(`shooter`) }}
                    value={scorer}
                    label={t("match.goal-scorer")}
                    notched
                  >
                    {teamPlayers.map((player) => (
                      <MenuItem key={player.profiles[0].id} value={player.profiles[0].id}>
                        {player.firstName} {player.lastName}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              ) : (
                <TextField
                  fullWidth
                  label="Scorer"
                  error={hasSubmitted && !!errors.shooter?.message}
                  helperText={hasSubmitted && errors.shooter?.message}
                  InputLabelProps={{ shrink: true }}
                  {...register(`shooter`)}
                />
              )}
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label="Zone"
                error={hasSubmitted && !!errors.zone?.message}
                helperText={hasSubmitted && errors.zone?.message}
                InputLabelProps={{ shrink: true }}
                {...register(`zone`)}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <FormControl fullWidth>
                <InputLabel shrink>{t("match.body-part")}</InputLabel>
                <Select
                  inputProps={{ ...register(`bodyPart`) }}
                  value={bodyPart}
                  label={t("match.body-part")}
                  notched
                >
                  <MenuItem value={AttemptForBodyPart.rightFoot}>
                    {t("match.body-parts.right-foot")}
                  </MenuItem>
                  <MenuItem value={AttemptForBodyPart.leftFoot}>
                    {t("match.body-parts.left-foot")}
                  </MenuItem>
                  <MenuItem value={AttemptForBodyPart.head}>{t("match.body-parts.head")}</MenuItem>
                  <MenuItem value={AttemptForBodyPart.chest}>
                    {t("match.body-parts.chest")}
                  </MenuItem>
                  <MenuItem value={AttemptForBodyPart.knee}>{t("match.body-parts.knee")}</MenuItem>
                  <MenuItem value={AttemptForBodyPart.shoulder}>
                    {t("match.body-parts.shoulder")}
                  </MenuItem>
                  <MenuItem value={AttemptForBodyPart.hip}>{t("match.body-parts.hip")}</MenuItem>
                  <MenuItem value={AttemptForBodyPart.back}>{t("match.body-parts.back")}</MenuItem>
                  <MenuItem value={AttemptForBodyPart.hand}>{t("match.body-parts.hand")}</MenuItem>
                  <MenuItem value={AttemptForBodyPart.other}>
                    {t("match.body-parts.other")}
                  </MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>

          {/* Group 4: Assist Details */}
          <Grid container spacing={2} mb={3}>
            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label={t("match.assist")}
                error={hasSubmitted && !!errors.assist?.message}
                helperText={hasSubmitted && errors.assist?.message}
                InputLabelProps={{ shrink: true }}
                {...register(`assist`)}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label={t("match.assist-zone")}
                error={hasSubmitted && !!errors.assistZone?.message}
                helperText={hasSubmitted && errors.assistZone?.message}
                InputLabelProps={{ shrink: true }}
                {...register(`assistZone`)}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <FormControl fullWidth>
                <InputLabel shrink>{t("match.assist-body-part")}</InputLabel>
                <Select
                  inputProps={{ ...register(`assistBodyPart`) }}
                  value={assistBodyPart}
                  label={t("match.assist-body-part")}
                  notched
                >
                  <MenuItem value={AttemptForBodyPart.rightFoot}>
                    {t("match.body-parts.right-foot")}
                  </MenuItem>
                  <MenuItem value={AttemptForBodyPart.leftFoot}>
                    {t("match.body-parts.left-foot")}
                  </MenuItem>
                  <MenuItem value={AttemptForBodyPart.head}>{t("match.body-parts.head")}</MenuItem>
                  <MenuItem value={AttemptForBodyPart.chest}>
                    {t("match.body-parts.chest")}
                  </MenuItem>
                  <MenuItem value={AttemptForBodyPart.knee}>{t("match.body-parts.knee")}</MenuItem>
                  <MenuItem value={AttemptForBodyPart.shoulder}>
                    {t("match.body-parts.shoulder")}
                  </MenuItem>
                  <MenuItem value={AttemptForBodyPart.hip}>{t("match.body-parts.hip")}</MenuItem>
                  <MenuItem value={AttemptForBodyPart.back}>{t("match.body-parts.back")}</MenuItem>
                  <MenuItem value={AttemptForBodyPart.hand}>{t("match.body-parts.hand")}</MenuItem>
                  <MenuItem value={AttemptForBodyPart.other}>
                    {t("match.body-parts.other")}
                  </MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>

          {/* Group 5: Key Pass Details */}
          <Grid container spacing={2} mb={3}>
            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label={t("match.key-pass")}
                error={hasSubmitted && !!errors.keyPass?.message}
                helperText={hasSubmitted && errors.keyPass?.message}
                InputLabelProps={{ shrink: true }}
                {...register(`keyPass`)}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label={t("match.key-pass-zone")}
                error={hasSubmitted && !!errors.keyPassZone?.message}
                helperText={hasSubmitted && errors.keyPassZone?.message}
                InputLabelProps={{ shrink: true }}
                {...register(`keyPassZone`)}
              />
            </Grid>
          </Grid>

          {/* Error leading to goal */}
          <Grid container spacing={2} mb={3}>
            <Grid item xs={12} sm={6} md={4}>
              <FormControl fullWidth>
                <InputLabel shrink>{t("match.error-leading-to-goal")}</InputLabel>
                <Select
                  inputProps={{ ...register(`errorLeadingDirectlyToTheGoal`) }}
                  value={errorLeadingDirectlyToTheGoal}
                  label={t("match.error-leading-to-goal")}
                  notched
                >
                  {teamPlayers.map((player) => (
                    <MenuItem key={player.profiles[0].id} value={player.profiles[0].id}>
                      {player.firstName} {player.lastName}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>

          {/* Submit Button and Messages */}
          <Grid container justifyContent="center" spacing={2}>
            <Grid item>
              <LoadingButton
                size="large"
                type="submit"
                loading={loading}
                variant="contained"
                color="secondary"
                disabled={!isDirty}
              >
                {t("common.save")}
              </LoadingButton>
            </Grid>
          </Grid>
          {serverError && <LocalError message={t("common.error")} />}
          <ToggleSnackbar open={upsertReviewIsSuccess}>
            <Alert severity="success">{t("match.review-updated")}</Alert>
          </ToggleSnackbar>
        </Grid>
      </Grid>
    </Box>
  );
};
