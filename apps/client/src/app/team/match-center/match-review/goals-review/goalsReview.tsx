import { FC } from "react";
import { useTranslation } from "react-i18next";

import { Box, Typography } from "@mio/ui";
import { PopulatedPlayer } from "@mio/helpers";
import { GoalsScoredReview } from "./scored-goals/goalsScoredReview";
import { GoalsConcededReview } from "./conceded-goals/goalsConcededReview";

type Props = {
  teamPlayers: PopulatedPlayer[];
};

export const GoalsReview: FC<Props> = ({ teamPlayers }) => {
  const { t } = useTranslation();

  return (
    <Box>
      <Box>
        <Typography variant="h4">{t("matches.review.goals.scored.title")}</Typography>
        <GoalsScoredReview teamPlayers={teamPlayers} />
      </Box>

      <Box>
        <Typography variant="h4">{t("matches.review.goals.conceded.title")}</Typography>
        <GoalsConcededReview teamPlayers={teamPlayers} />
      </Box>
    </Box>
  );
};
