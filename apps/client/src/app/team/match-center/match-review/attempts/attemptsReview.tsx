import { FC } from "react";
import { useTranslation } from "react-i18next";

import {
  useOrganization,
  footballMatchState,
  useRequiredTeamId,
  PageLoader,
  PageError,
  Box,
  Button,
  AddIcon,
  useTheme,
  Typography,
  useConfirmation,
} from "@mio/ui";
import { ErrorMessages, FootballMatchAttemptAgainst, FootballMatchAttemptFor } from "@mio/helpers";

import { SidebarLayout } from "../../../../layouts";
import { AttemptCard } from "./attemptCard";
import { NoGoalsAttemptForFormModal, useNoGoalsAttemptForForm } from "./attemptForModal";
import { useTeamPlayers } from "@mio/ui/lib/state/usePlayers";
import {
  NoGoalsAttemptAgainstFormModal,
  useNoGoalsAttemptAgainstForm,
} from "./attemptAgainstModal";
import { AttemptCardAgainst } from "./attemptCardAgainst";

export const AttemptsForReview: FC = () => {
  const theme = useTheme();
  const { t } = useTranslation();
  const organization = useOrganization();
  const teamId = useRequiredTeamId();
  const footballMatchPlanId = footballMatchState.useRequiredFootballMatchPlanId();

  const teamPlayers = useTeamPlayers(teamId, organization.id);

  const {
    attempt,
    setAttempt,
    isOpen,
    toggle: toggleNoGoalsAttemptForForm,
  } = useNoGoalsAttemptForForm();

  const {
    attempt: attemptAgainst,
    setAttempt: setAttemptAgainst,
    isOpen: isOpenAttemptAgainst,
    toggle: toggleNoGoalsAttemptAgainstForm,
  } = useNoGoalsAttemptAgainstForm();

  const attemptsFor = footballMatchState.useGetFootballMatchAttemptsForExtra(
    organization.id,
    teamId,
    footballMatchPlanId,
  );

  const attemptsAgainst = footballMatchState.useGetFootballMatchAttemptsAgainstExtra(
    organization.id,
    teamId,
    footballMatchPlanId,
  );

  const deleteAttemptForReview = footballMatchState.useDeleteFootballMatchAttemptFor(
    organization.id,
    teamId,
  );

  const deleteAttemptAgainstReview = footballMatchState.useDeleteFootballMatchAttemptAgainst(
    organization.id,
    teamId,
  );

  const { ConfirmationUI, open } = useConfirmation({
    title: t("match.delete-attempt-confirmation-title"),
    content: t("match.delete-attempt-confirmation"),
    onConfirm: () => {
      if (attempt) {
        deleteAttemptForReview.mutate(attempt);
      }
    },
  });

  const handleDelete = (attempt: FootballMatchAttemptFor.AttemptFor) => {
    setAttempt(attempt);
    open();
  };

  const { ConfirmationUI: ConfirmationUIAgainst, open: openAgainst } = useConfirmation({
    title: t("match.delete-attempt-confirmation-title"),
    content: t("match.delete-attempt-confirmation"),
    onConfirm: () => {
      console.log("attemptAgainst", attemptAgainst);
      if (attemptAgainst) {
        deleteAttemptAgainstReview.mutate(attemptAgainst);
      }
    },
  });

  const handleDeleteAgainst = (attempt: FootballMatchAttemptAgainst.AttemptAgainst) => {
    setAttemptAgainst(attempt);
    openAgainst();
  };

  const handleEdit = (attempt: FootballMatchAttemptFor.AttemptFor) => {
    setAttempt(attempt);
    toggleNoGoalsAttemptForForm();
  };

  const handleEditAgainst = (attempt: FootballMatchAttemptAgainst.AttemptAgainst) => {
    setAttemptAgainst(attempt);
    toggleNoGoalsAttemptAgainstForm();
  };

  const handleAddNewAttempt = () => {
    setAttempt(undefined);
    toggleNoGoalsAttemptForForm();
  };

  const handleAddNewAttemptAgainst = () => {
    setAttemptAgainst(undefined);
    toggleNoGoalsAttemptAgainstForm();
  };

  if (attemptsFor.status === "error" || attemptsAgainst.status === "error") {
    return (
      <SidebarLayout>
        <PageError
          message={
            attemptsFor?.error?.message === ErrorMessages.PermissionDenied
              ? t("matches.review.permission-denied")
              : t("matches.review.load-error")
          }
        />
        <PageLoader message={t("matches.review.load-error")} />
      </SidebarLayout>
    );
  }

  if (
    attemptsFor.status === "success" &&
    teamPlayers.query.status === "success" &&
    teamPlayers.query.data
  ) {
    return (
      <Box>
        <Box sx={{ display: "flex", justifyContent: "center", mt: 2, gap: 2 }}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleAddNewAttempt}
          >
            {t("match.add-attempt")}
          </Button>
          <Button
            variant="contained"
            color="secondary"
            startIcon={<AddIcon />}
            onClick={handleAddNewAttemptAgainst}
          >
            {t("match.add-attempt-against")}
          </Button>
        </Box>
        <Box>
          <Typography variant="h4" gutterBottom color={theme.palette.primary.main}>
            {t("match.attempts")}
          </Typography>

          {attemptsFor.data && attemptsFor.data.length > 0
            ? attemptsFor.data.map((goal, index) => (
                <AttemptCard
                  key={index}
                  index={index}
                  attempt={goal}
                  teamPlayers={teamPlayers.query.data || []}
                  onEdit={handleEdit}
                  onDelete={handleDelete}
                />
              ))
            : null}
        </Box>

        <Box mt={4}>
          <Typography variant="h4" gutterBottom color={theme.palette.secondary.main}>
            {t("match.attempts-against")}
          </Typography>

          {attemptsAgainst.data && attemptsAgainst.data.length > 0
            ? attemptsAgainst.data.map((goal, index) => (
                <AttemptCardAgainst
                  key={index}
                  index={index}
                  attempt={goal}
                  teamPlayers={teamPlayers.query.data || []}
                  onEdit={handleEditAgainst}
                  onDelete={handleDeleteAgainst}
                />
              ))
            : null}
        </Box>

        <NoGoalsAttemptForFormModal
          isOpen={isOpen}
          onClose={toggleNoGoalsAttemptForForm}
          attemptFor={attempt}
        />

        <NoGoalsAttemptAgainstFormModal
          isOpen={isOpenAttemptAgainst}
          onClose={toggleNoGoalsAttemptAgainstForm}
          attemptAgainst={attemptAgainst}
        />

        {ConfirmationUI}
        {ConfirmationUIAgainst}
      </Box>
    );
  }

  return null;
};
