import { FC } from "react";
import { useTranslation } from "react-i18next";
import {
  Divider,
  useTheme,
  Box,
  Typography,
  Grid,
  Card,
  IconButton,
  EditIcon,
  DeleteIcon,
} from "@mio/ui";
import {
  FootballMatchAttemptAgainst,
  FootballMatchAttemptFor,
  PopulatedPlayer,
} from "@mio/helpers";

export type Props = {
  attempt: FootballMatchAttemptAgainst.AttemptAgainst;
  index: number;
  teamPlayers: PopulatedPlayer[];
  onEdit?: (attempt: FootballMatchAttemptAgainst.AttemptAgainst) => void;
  onDelete?: (attempt: FootballMatchAttemptAgainst.AttemptAgainst) => void;
};

export const AttemptCardAgainst: FC<Props> = ({ attempt, index, onEdit, onDelete }) => {
  const { t } = useTranslation();
  const theme = useTheme();

  const renderDetailRow = (label: string, value: string | undefined | null) => (
    <Grid container spacing={1} sx={{ mb: 1 }}>
      <Grid item xs={4}>
        <Typography variant="body2" color="text.secondary">
          {label}:
        </Typography>
      </Grid>
      <Grid item xs={8}>
        <Typography variant="body2">{value || "-"}</Typography>
      </Grid>
    </Grid>
  );

  return (
    <Card>
      <Box
        sx={{
          border: "1px solid",
          borderRadius: "4px",
          padding: 3,
          margin: 1,
          borderColor: theme.palette.secondary.main,
          position: "relative",
        }}
      >
        <Box
          sx={{
            position: "absolute",
            top: 20,
            right: 20,
            display: "flex",
            gap: 1,
          }}
        >
          <IconButton size="small" onClick={() => onEdit?.(attempt)} color="primary">
            <EditIcon />
          </IconButton>
          <IconButton size="small" onClick={() => onDelete?.(attempt)} color="error">
            <DeleteIcon />
          </IconButton>
        </Box>

        <Typography variant="h5" gutterBottom color={theme.palette.secondary.main}>
          {`#${index + 1}`}
        </Typography>

        <Divider />

        {/* Time Details Group */}
        <Typography variant="subtitle2" color="text.primary" mt={2} mb={1}>
          {t("match.time-details")}
        </Typography>
        <Box mb={3}>
          {renderDetailRow(
            t("match.minute"),
            `${attempt.minute}'${attempt.addedTime ? "+" + attempt.addedTime : ""}`,
          )}
          {renderDetailRow(
            t("match.half"),
            attempt.half ? t(`match.halves.${attempt.half}` as any) : "-",
          )}
        </Box>

        <Divider />

        {/* Situation Group */}
        <Typography variant="subtitle2" color="text.primary" mt={2} mb={1}>
          {t("match.situation-details")}
        </Typography>
        <Box mb={3}>
          {renderDetailRow(
            t("match.situation"),
            attempt.situationType
              ? t(`match.situation-detailed-options.${attempt.situationType}` as any)
              : "-",
          )}
          {attempt.situationType === FootballMatchAttemptFor.GoalSituationType.openPlay &&
            renderDetailRow(
              t("match.situation-detailed"),
              attempt.situationOpenPlaySubType
                ? t(`match.open-play-sub-type.${attempt.situationOpenPlaySubType}` as any)
                : "-",
            )}
          {attempt.situationType === FootballMatchAttemptFor.GoalSituationType.setPiece &&
            renderDetailRow(
              t("match.situation-detailed"),
              attempt.situationSetPieceSubType
                ? t(`match.set-piece-sub-type.${attempt.situationSetPieceSubType}` as any)
                : "-",
            )}
        </Box>

        <Divider />

        {/* Attempt Details Group */}
        <Typography variant="subtitle2" color="text.primary" mt={2} mb={1}>
          {t("match.attempt-details")}
        </Typography>
        <Box mb={3}>
          {renderDetailRow(
            t("match.attempt-type"),
            attempt.type ? t(`match.attempt-type-options.${attempt.type}` as any) : "-",
          )}
          {renderDetailRow(
            t("match.body-part"),
            attempt.bodyPart ? t(`match.body-parts.${attempt.bodyPart}` as any) : "-",
          )}
          {attempt.shooter && renderDetailRow(t("match.shooter"), attempt.shooter)}
          {renderDetailRow(t("match.zone"), attempt.zone?.toString())}
        </Box>

        <Divider />

        {/* Assist Details Group */}
        {(attempt.assist || attempt.assistBodyPart || attempt.assistZone) && (
          <>
            <Typography variant="subtitle2" color="text.primary" mt={2} mb={1}>
              {t("match.assist-details")}
            </Typography>
            <Box mb={3}>
              {attempt.assist && renderDetailRow(t("match.assist"), attempt.assist)}
              {!!attempt.assistBodyPart &&
                renderDetailRow(
                  t("match.assist-body-part"),
                  t(`match.body-parts.${attempt.assistBodyPart}`),
                )}
              {attempt.assistZone &&
                renderDetailRow(t("match.assist-zone"), attempt.assistZone?.toString())}
            </Box>
          </>
        )}

        <Divider />

        {/* Key Pass Details Group */}
        {(attempt.keyPass || attempt.keyPassZone) && (
          <>
            <Typography variant="subtitle2" color="text.primary" mt={2} mb={1}>
              {t("match.key-pass-details")}
            </Typography>
            <Box mb={3}>
              {attempt.keyPass && renderDetailRow(t("match.key-pass"), attempt.keyPass)}
              {attempt.keyPassZone &&
                renderDetailRow(t("match.key-pass-zone"), attempt.keyPassZone?.toString())}
            </Box>
          </>
        )}
      </Box>
    </Card>
  );
};
