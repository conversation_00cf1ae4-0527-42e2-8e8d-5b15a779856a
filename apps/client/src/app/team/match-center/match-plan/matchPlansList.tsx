import { FC } from "react";
import { <PERSON> } from "react-router-dom";
import { useTranslation } from "react-i18next";

import {
  Box,
  Stack,
  Button,
  Typography,
  PageError,
  PageLoader,
  AddIcon,
  useOrganization,
  footballMatchState,
  useRequiredTeam,
} from "@mio/ui";
import { ErrorMessages } from "@mio/helpers";
import { SidebarLayout } from "../../../layouts";
import { MatchPlanItem } from "./matchPlanItem";

export const MatchPlanList: FC = () => {
  const { t } = useTranslation();
  const organization = useOrganization();
  const team = useRequiredTeam();

  const footballMatches = footballMatchState.useGetFootballMatchPlans(organization.id, team.id);

  if (footballMatches.status === "loading") {
    return (
      <SidebarLayout>
        <PageLoader message={t("matches.list.loading")} />
      </SidebarLayout>
    );
  }

  if (footballMatches.status === "error") {
    return (
      <SidebarLayout>
        <PageError
          message={
            footballMatches.error.message === ErrorMessages.PermissionDenied
              ? t("matches.list.permission-denied")
              : t("matches.list.load-error")
          }
        />
      </SidebarLayout>
    );
  }

  if (footballMatches.status === "success") {
    return (
      <SidebarLayout>
        <Box>
          <Stack
            component="header"
            flexDirection="row"
            justifyContent="space-between"
            mb={5}
            gap={2}
            flexWrap="wrap"
          >
            <Box>
              <Typography variant="h4" component="h1">
                {t("matches.list.title", { count: footballMatches.data.length })}
              </Typography>
            </Box>

            <Box>
              <Button
                component={Link}
                to="create"
                variant="contained"
                color="secondary"
                size="large"
                startIcon={<AddIcon />}
              >
                {t("matches.list.add-new")}
              </Button>
            </Box>
          </Stack>

          <Stack gap={4}>
            {footballMatches.data.map((item) => (
              <MatchPlanItem key={item.id} matchPlan={item} />
            ))}
          </Stack>
        </Box>
      </SidebarLayout>
    );
  }

  return null;
};
