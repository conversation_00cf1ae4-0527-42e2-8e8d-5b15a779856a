import { FC } from "react";
import { <PERSON> } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { IconButton, EditIcon, Stack, Tooltip, CommentIcon } from "@mio/ui";
import { FootballMatchPlan } from "@mio/helpers";
import { MatchPlanCard } from "./matchPlanCard";

type Props = {
  matchPlan: FootballMatchPlan.CompletePlan;
};

export const MatchPlanItem: FC<Props> = ({ matchPlan }) => {
  const { t } = useTranslation();

  return (
    <MatchPlanCard matchPlan={matchPlan}>
      <Stack direction="row" alignItems="center" sx={{ justifyContent: "right" }}>
        <Tooltip title={t("matches.actions.add-review")}>
          <IconButton
            aria-label={t("matches.actions.add-review")}
            component={Link}
            to={`review/${matchPlan.id}`}
            color="inherit"
          >
            <CommentIcon color="primary" />
          </IconButton>
        </Tooltip>

        <Tooltip title={t("matches.actions.edit-match")}>
          <IconButton
            aria-label={t("matches.actions.edit-match")}
            component={Link}
            to={`edit/${matchPlan.id}`}
            color="inherit"
          >
            <EditIcon color="primary" />
          </IconButton>
        </Tooltip>
      </Stack>
    </MatchPlanCard>
  );
};
