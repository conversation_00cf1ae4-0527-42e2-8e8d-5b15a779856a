import { FC } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";

import {
  Box,
  Typography,
  TextField,
  Stack,
  LocalError,
  LoadingButton,
  useOrganizationId,
  useFormResolver,
  DateTimePicker,
  useRequiredTeamId,
  InputLabel,
  Select,
  MenuItem,
  useRequiredTeam,
} from "@mio/ui";

import { APIError, CustomDate, ErrorMessages, FootballMatchPlan, Primitive } from "@mio/helpers";

import { BackButton } from "../../../shared";

type DataShape = Partial<Primitive<FootballMatchPlan.CreateDto> & { id?: string }>;

type Props = {
  footballMatchPlan?: FootballMatchPlan.CompletePlan;
  onSubmit: (data: DataShape) => void;
  loading?: boolean;
  serverError?: APIError | null;
};

export const UpsertFootballMatchPlan: FC<Props> = ({
  footballMatchPlan,
  onSubmit,
  loading,
  serverError,
}) => {
  const { t } = useTranslation();
  const organizationId = useOrganizationId();
  const teamId = useRequiredTeamId();
  const team = useRequiredTeam();
  const formResolver = useFormResolver(FootballMatchPlan.Entity.toUpsertDto);

  const { FootballMatchHost } = FootballMatchPlan;

  const { register, handleSubmit, setValue, watch, formState } = useForm<DataShape>({
    defaultValues: {
      arrivalAndWarmUp: footballMatchPlan?.arrivalAndWarmUp,
      coachBehaviours: footballMatchPlan?.coachBehaviours,
      competition:
        footballMatchPlan?.competition || FootballMatchPlan.FootballMatchCompetition.league,
      description: footballMatchPlan?.description,
      endDate: footballMatchPlan?.endDate,
      learningOutcomes: footballMatchPlan?.learningOutcomes,
      name: footballMatchPlan?.name,
      oppositionName: footballMatchPlan?.oppositionName,
      gameWeek: footballMatchPlan?.gameWeek,
      leaguePosition: footballMatchPlan?.leaguePosition,
      leaguePositionOpponent: footballMatchPlan?.leaguePositionOpponent,

      playersEngagement: footballMatchPlan?.playersEngagement,
      safetyConsiderations: footballMatchPlan?.safetyConsiderations,
      startDate: footballMatchPlan?.startDate,
      host: footballMatchPlan?.host || FootballMatchHost.Home,

      organizationId: organizationId,
      teamId: teamId,
      id: footballMatchPlan?.id,
    },
    resolver: formResolver,
  });

  const competition = watch("competition");
  const host = watch("host");
  const oppositionName = watch("oppositionName");

  const { errors, submitCount } = formState;
  const hasSubmitted = submitCount > 0;

  const title = footballMatchPlan
    ? t("matches.form.edit-title", { name: footballMatchPlan.name })
    : t("matches.form.create-title");

  return (
    <Box
      width={1}
      component="form"
      onSubmit={handleSubmit(onSubmit)}
      sx={{ width: "100%", bgcolor: "background.paper" }}
    >
      <Stack direction="row" alignItems="center" mb={5} gap={1} flexWrap="wrap">
        <Box>
          <BackButton path="../">{t("matches.form.back-to-match-center")}</BackButton>
        </Box>
        <Box component="legend" display="block" sx={{ flexGrow: 1, pr: 5 }}>
          <Typography variant="h5" component="h1" textAlign="center">
            {title}
          </Typography>
        </Box>
      </Stack>

      <Box>
        <TextField
          fullWidth
          aria-required
          id="name"
          label={t("matches.form.fields.name")}
          name="name"
          inputProps={{ ...register("name") }}
          helperText={(hasSubmitted && errors.name?.message) || ""}
          error={hasSubmitted && !!errors.name?.message}
          aria-invalid={hasSubmitted && !!errors.name?.message}
        />
      </Box>

      <Box mt={2}>
        <TextField
          fullWidth
          aria-required
          id="oppositionName"
          label={t("matches.form.fields.opposition-name")}
          name="oppositionName"
          inputProps={{ ...register("oppositionName") }}
          helperText={(hasSubmitted && errors.oppositionName?.message) || ""}
          error={hasSubmitted && !!errors.oppositionName?.message}
          aria-invalid={hasSubmitted && !!errors.oppositionName?.message}
        />
      </Box>

      <Stack direction="row" gap={4} mt={2}>
        <Select
          fullWidth
          aria-required
          labelId="hosted-by-label"
          id="host"
          label={t("matches.form.fields.host.label")}
          name="host"
          inputProps={{ ...register("host") }}
          value={host}
        >
          <MenuItem value={FootballMatchHost.Home}>{t("matches.form.fields.host.home")}</MenuItem>
          <MenuItem value={FootballMatchHost.Away}>{t("matches.form.fields.host.away")}</MenuItem>
          <MenuItem value={FootballMatchHost.Neutral}>
            {t("matches.form.fields.host.neutral")}
          </MenuItem>
        </Select>

        <TextField
          fullWidth
          aria-required
          id="gameWeek"
          label={t("matches.form.fields.game-week")}
          name="gameWeek"
          type="number"
          inputProps={{ ...register("gameWeek") }}
          helperText={(hasSubmitted && errors.gameWeek?.message) || ""}
          error={hasSubmitted && !!errors.gameWeek?.message}
          aria-invalid={hasSubmitted && !!errors.gameWeek?.message}
        />
      </Stack>

      <Stack direction="row" gap={4} mt={2}>
        <TextField
          fullWidth
          aria-required
          id="leaguePosition"
          label={team?.name + " " + t("matches.form.fields.league-position")}
          name="leaguePosition"
          type="number"
          inputProps={{ ...register("leaguePosition") }}
          helperText={(hasSubmitted && errors.leaguePosition?.message) || ""}
          error={hasSubmitted && !!errors.leaguePosition?.message}
          aria-invalid={hasSubmitted && !!errors.leaguePosition?.message}
        />

        <TextField
          fullWidth
          aria-required
          id="leaguePositionOpponent"
          label={
            t("matches.form.fields.league-position-opponent") +
            " " +
            (oppositionName ?? t("matches.form.fields.opponent"))
          }
          name="leaguePositionOpponent"
          type="number"
          inputProps={{ ...register("leaguePositionOpponent") }}
          helperText={(hasSubmitted && errors.leaguePositionOpponent?.message) || ""}
          error={hasSubmitted && !!errors.leaguePositionOpponent?.message}
          aria-invalid={hasSubmitted && !!errors.leaguePositionOpponent?.message}
        />
      </Stack>

      <Box mt={2}>
        <TextField
          fullWidth
          aria-required
          id="description"
          label={t("matches.form.fields.description")}
          name="description"
          inputProps={{ ...register("description") }}
          helperText={(hasSubmitted && errors.description?.message) || ""}
          error={hasSubmitted && !!errors.description?.message}
          aria-invalid={hasSubmitted && !!errors.description?.message}
        />
      </Box>

      <Box mt={2}>
        <DateTimePicker
          label={t("matches.form.fields.start-date")}
          inputFormat="dd/MM/y HH:mm"
          ampm={false}
          onChange={(newValue) => {
            setValue("startDate", CustomDate.validOrEmpty(newValue), {
              shouldValidate: true,
              shouldDirty: true,
              shouldTouch: true,
            });
          }}
          renderInput={(params) => (
            <TextField
              fullWidth
              aria-required
              {...params}
              helperText={(hasSubmitted && errors.startDate?.message) || ""}
              error={hasSubmitted && !!errors.startDate?.message}
              aria-invalid={hasSubmitted && !!errors.startDate?.message}
            />
          )}
          value={watch("startDate")}
        />
      </Box>

      <Box mt={1}>
        <InputLabel aria-required>{t("matches.form.fields.competition.label")}</InputLabel>
        <Select
          fullWidth
          aria-required
          labelId="competition-label"
          id="competition"
          label={t("matches.form.fields.competition.label")}
          name="competition"
          inputProps={{ ...register("competition") }}
          value={competition}
        >
          <MenuItem value={FootballMatchPlan.FootballMatchCompetition.league}>
            {t("matches.form.fields.competition.league")}
          </MenuItem>
          <MenuItem value={FootballMatchPlan.FootballMatchCompetition.cup}>
            {t("matches.form.fields.competition.cup")}
          </MenuItem>
          <MenuItem value={FootballMatchPlan.FootballMatchCompetition.friendly}>
            {t("matches.form.fields.competition.friendly")}
          </MenuItem>
        </Select>
      </Box>

      <Box mt={2}>
        <TextField
          fullWidth
          id="learningOutcomes"
          label={t("matches.form.fields.learning-outcomes")}
          name="learningOutcomes"
          multiline
          rows={3}
          inputProps={{ ...register("learningOutcomes") }}
          helperText={(hasSubmitted && errors.learningOutcomes?.message) || ""}
          error={hasSubmitted && !!errors.learningOutcomes?.message}
          aria-invalid={hasSubmitted && !!errors.learningOutcomes?.message}
        />
      </Box>

      <Box mt={2}>
        <TextField
          fullWidth
          id="playersEngagement"
          label={t("matches.form.fields.players-engagement")}
          name="playersEngagement"
          multiline
          rows={3}
          inputProps={{ ...register("playersEngagement") }}
          helperText={(hasSubmitted && errors.playersEngagement?.message) || ""}
          error={hasSubmitted && !!errors.playersEngagement?.message}
          aria-invalid={hasSubmitted && !!errors.playersEngagement?.message}
        />
      </Box>

      <Box mt={2}>
        <TextField
          fullWidth
          id="coachBehaviours"
          label={t("matches.form.fields.coach-behaviours")}
          name="coachBehaviours"
          multiline
          rows={3}
          inputProps={{ ...register("coachBehaviours") }}
          helperText={(hasSubmitted && errors.coachBehaviours?.message) || ""}
          error={hasSubmitted && !!errors.coachBehaviours?.message}
          aria-invalid={hasSubmitted && !!errors.coachBehaviours?.message}
        />
      </Box>

      <Box mt={2}>
        <TextField
          fullWidth
          id="safetyConsiderations"
          label={t("matches.form.fields.safety-considerations")}
          name="safetyConsiderations"
          multiline
          rows={3}
          inputProps={{ ...register("safetyConsiderations") }}
          helperText={(hasSubmitted && errors.safetyConsiderations?.message) || ""}
          error={hasSubmitted && !!errors.safetyConsiderations?.message}
          aria-invalid={hasSubmitted && !!errors.safetyConsiderations?.message}
        />
      </Box>

      <Box mt={2}>
        <TextField
          fullWidth
          id="arrivalAndWarmUp"
          label={t("matches.form.fields.arrival-and-warm-up")}
          name="arrivalAndWarmUp"
          multiline
          rows={3}
          inputProps={{ ...register("arrivalAndWarmUp") }}
          helperText={(hasSubmitted && errors.arrivalAndWarmUp?.message) || ""}
          error={hasSubmitted && !!errors.arrivalAndWarmUp?.message}
          aria-invalid={hasSubmitted && !!errors.arrivalAndWarmUp?.message}
        />
      </Box>

      <Box mt={5} mb={2} sx={{ display: "flex", justifyContent: "center" }}>
        <LoadingButton
          size="large"
          type="submit"
          loading={loading}
          variant="contained"
          color="secondary"
        >
          {t("matches.form.save")}
        </LoadingButton>
      </Box>

      {serverError &&
        (serverError.message === ErrorMessages.MatchAlreadyExistsInGameWeek ? (
          <LocalError message={t("matches.form.match-already-exists-in-game-week-error")} />
        ) : (
          <LocalError message={t("matches.form.error")} />
        ))}
    </Box>
  );
};
