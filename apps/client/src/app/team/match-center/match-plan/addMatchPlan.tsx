import { FC } from "react";
import { Navigate } from "react-router-dom";

import { useOrganization, footballMatchState, useRequiredTeam } from "@mio/ui";
import { FootballMatchPlan, isError } from "@mio/helpers";

import { SidebarLayout } from "../../../layouts";
import { UpsertFootballMatchPlan } from "./upsertForm";

export const AddMatchPlan: FC = () => {
  const organization = useOrganization();
  const team = useRequiredTeam();

  const createMatchPlan = footballMatchState.useCreateFootballMatchPlan(organization.id, team.id);

  const handleSubmit = (data: unknown) => {
    const validated = FootballMatchPlan.Entity.toCreateDto(data);

    if (!isError(validated)) {
      createMatchPlan.mutate(validated);
    }
  };

  if (createMatchPlan.isSuccess) {
    return <Navigate to={`/${organization.id}/teams/${team.id}/match-center`} />;
  }

  return (
    <SidebarLayout>
      <UpsertFootballMatchPlan
        onSubmit={handleSubmit}
        loading={createMatchPlan.isLoading}
        serverError={createMatchPlan.error}
      />
    </SidebarLayout>
  );
};
