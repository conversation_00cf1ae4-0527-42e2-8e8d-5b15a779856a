import { FC } from "react";
import { startCase } from "lodash/fp";
import { useTranslation } from "react-i18next";

import { Card, Typography, Stack, Box, Chip } from "@mio/ui";
import { CustomDate, FootballMatchPlan } from "@mio/helpers";

type Props = {
  matchPlan: FootballMatchPlan.CompletePlan;
  children: JSX.Element;
};

const gridColumns = "1.5fr 2fr 1fr 1fr 1fr";

export const MatchPlanCard: FC<Props> = ({ matchPlan, children }) => {
  const { t } = useTranslation();

  return (
    <Card sx={{ p: 2 }}>
      <Stack
        sx={{
          display: {
            md: "grid",
            xs: "flex",
          },
          flexWrap: "wrap",
          flexDirection: "row",
          gap: {
            xs: 2,
            sm: 4,
          },
          gridTemplateColumns: {
            md: gridColumns,
          },
        }}
      >
        <Typography component="h2" variant="h5" fontWeight="bold">
          {matchPlan.name}
        </Typography>

        <Box>
          <Chip label={t("matches.card.description")} />
          <Box>{startCase(matchPlan.description || "")}</Box>
        </Box>

        <Stack alignItems="center" gap={2}>
          <Chip label={t("matches.card.competition")} />
          <Box>{t(`matches.card.${matchPlan.competition}`)}</Box>
        </Stack>

        <Stack alignItems="center" gap={2}>
          <Chip label={t("matches.card.date")} />
          <Box>{CustomDate.toDisplayDate(matchPlan.startDate)}</Box>
        </Stack>

        <Box>{children}</Box>
      </Stack>
    </Card>
  );
};
