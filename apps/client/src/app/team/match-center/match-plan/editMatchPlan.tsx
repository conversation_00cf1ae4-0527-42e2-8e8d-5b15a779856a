import { FC } from "react";
import { Navigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { FootballMatchPlan, isError } from "@mio/helpers";
import {
  footballMatchState,
  PageError,
  PageLoader,
  useOrganization,
  useOrganizationId,
  useRequiredTeamId,
} from "@mio/ui";

import { SidebarLayout } from "../../../layouts";
import { UpsertFootballMatchPlan } from "./upsertForm";

export const EditMatchPlan: FC = () => {
  const { t } = useTranslation();
  const teamId = useRequiredTeamId();
  const organization = useOrganization();

  const footballMatchPlanId = footballMatchState.useRequiredFootballMatchPlanId();

  const footballMatchPlan = footballMatchState.useGetFootballMatchPlan(
    organization.id,
    teamId,
    footballMatchPlanId,
  );

  const updateMatchPlan = footballMatchState.useUpdateFootballMatchPlan(organization.id, teamId);

  const organizationId = useOrganizationId();

  const handleSubmit = (data: unknown) => {
    const validated = FootballMatchPlan.Entity.toUpdateDto(data);

    if (!isError(validated)) {
      updateMatchPlan.mutate(validated);
    }
  };

  if (footballMatchPlan.status === "loading") {
    return (
      <SidebarLayout>
        <PageLoader message={t("matches.edit.loading")} />
      </SidebarLayout>
    );
  }

  if (footballMatchPlan.status === "error") {
    return (
      <SidebarLayout>
        <PageError message={t("matches.edit.load-error")} />
      </SidebarLayout>
    );
  }

  if (updateMatchPlan.isSuccess) {
    return <Navigate to={`/${organizationId}/teams/${teamId}/match-center`} />;
  }

  if (footballMatchPlan.status === "success") {
    return (
      <SidebarLayout>
        <UpsertFootballMatchPlan
          onSubmit={handleSubmit}
          footballMatchPlan={footballMatchPlan.data}
          loading={updateMatchPlan.isLoading}
          serverError={updateMatchPlan.error}
        />
      </SidebarLayout>
    );
  }

  return <Navigate to={`/${organizationId}/teams-admin`} />;
};
