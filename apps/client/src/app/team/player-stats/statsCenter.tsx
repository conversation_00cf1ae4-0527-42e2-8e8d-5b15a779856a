import { FC, SyntheticEvent, useState } from "react";
import { useTranslation } from "react-i18next";

import { TabContext, Box, TabList, Tab, TabPanel } from "@mio/ui";

import { SidebarLayout } from "../../layouts";
import { PlayersMatchPerformance } from "./playerMatchPerformance";
import { GoalsStats } from "./goalsStats";
import { AssistsStats } from "./assistsStats";
import { KeyPassesStats } from "./keyPassStats";

enum Panels {
  PlayerPerformance = "player-performance",
  Goals = "goals",
  Assists = "assists",
  KeyPasses = "key-passes",
}

export const StatsCenter: FC = () => {
  const { t } = useTranslation();
  const [value, setValue] = useState<Panels>(Panels.PlayerPerformance);

  const handleChange = (_: SyntheticEvent, newValue: Panels) => {
    setValue(newValue);
  };

  return (
    <SidebarLayout>
      <TabContext value={value}>
        <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
          <TabList
            onChange={handleChange}
            aria-label={t("stats.tabs-label")}
            variant="fullWidth"
            centered
          >
            <Tab label={t("stats.player-performance")} value={Panels.PlayerPerformance} />
            <Tab label={t("stats.goals")} value={Panels.Goals} />
            <Tab label={t("stats.assists")} value={Panels.Assists} />
            <Tab label={t("stats.key-passes")} value={Panels.KeyPasses} />
          </TabList>
        </Box>
        <TabPanel value={Panels.PlayerPerformance}>
          <PlayersMatchPerformance />
        </TabPanel>
        <TabPanel value={Panels.Goals}>
          <GoalsStats />
        </TabPanel>
        <TabPanel value={Panels.Assists}>
          <AssistsStats />
        </TabPanel>
        <TabPanel value={Panels.KeyPasses}>
          <KeyPassesStats />
        </TabPanel>
      </TabContext>
    </SidebarLayout>
  );
};
