import { FC } from "react";
import { useTranslation } from "react-i18next";
import { DataGrid, GridColDef } from "@mui/x-data-grid";
import Paper from "@mui/material/Paper";

import {
  footballMatchState,
  PageError,
  PageLoader,
  useOrganization,
  useRequiredTeamId,
} from "@mio/ui";
import { ErrorMessages } from "@mio/helpers";

import { SidebarLayout } from "../../layouts";

export const PlayersMatchPerformance: FC = () => {
  const { t } = useTranslation();
  const organization = useOrganization();
  const teamId = useRequiredTeamId();

  const columns: GridColDef[] = [
    { field: "firstName", headerName: t("stats.first-name"), flex: 1 },
    { field: "lastName", headerName: t("stats.last-name"), flex: 1 },
    {
      field: "appearances",
      headerName: t("stats.appearances"),
      width: 200,
      align: "center",
      headerAlign: "center",
    },
    {
      field: "averageOverallRating",
      headerName: t("stats.average-rating"),
      type: "number",
      align: "center",
      headerAlign: "center",
      width: 300,
      renderCell: (params) => {
        const value = params.value;
        const max = 10; // Maximum rating
        const percentage = (value / max) * 100; // Calculate percentage for progress bar
        let fillColor;

        // Determine the fill color based on the rating
        if (value >= 1 && value <= 3.9) {
          fillColor = "#e75a18"; // Red
        } else if (value >= 4 && value <= 4.9) {
          fillColor = "#ea7a41"; // Light Red
        } else if (value >= 5 && value <= 5.9) {
          fillColor = "#ef9e71"; // Beige
        } else if (value >= 6 && value <= 6.9) {
          fillColor = "#bfbfbf"; // Grey
        } else if (value >= 7 && value <= 7.9) {
          fillColor = "#82d094"; // Light Green
        } else if (value >= 8 && value <= 8.9) {
          fillColor = "#5bc072"; // Green
        } else if (value >= 9 && value <= 10) {
          fillColor = "#37ae4a"; // Dark Green
        }

        return (
          <div style={{ display: "flex", alignItems: "center" }}>
            <div
              style={{
                width: "100%",
                height: "10px",
                backgroundColor: "#e0e0e0",
                borderRadius: "5px",
                overflow: "hidden",
                marginRight: "10px",
              }}
            >
              <div
                style={{
                  height: "100%",
                  width: `${percentage}%`,
                  backgroundColor: fillColor,
                  transition: "width 0.3s ease",
                }}
              />
            </div>
            <span>{value.toFixed(1)}</span>
          </div>
        );
      },
    },
  ];

  const averageRatingsQuery = footballMatchState.useGetAverageFootballMatchPlayerReviews(
    organization.id,
    teamId,
  );

  if (averageRatingsQuery.status === "loading") {
    return (
      <SidebarLayout>
        <PageLoader message={t("stats.loading")} />
      </SidebarLayout>
    );
  }

  if (averageRatingsQuery.status === "error") {
    return (
      <SidebarLayout>
        <PageError
          message={
            averageRatingsQuery?.error?.message === ErrorMessages.PermissionDenied
              ? t("common.permission-denied")
              : t("stats.load-error")
          }
        />
      </SidebarLayout>
    );
  }

  if (averageRatingsQuery.status === "success") {
    return (
      <Paper sx={{ width: "100%" }}>
        <DataGrid
          getRowId={(row) => row.playerTeamProfileId}
          rows={averageRatingsQuery.data}
          columns={columns}
          initialState={{
            sorting: {
              sortModel: [
                { field: "averageOverallRating", sort: "desc" },
                { field: "appearances", sort: "desc" },
              ],
            },
          }}
          sx={{ border: 10, borderColor: "white" }}
        />
      </Paper>
    );
  }

  return null;
};
