import { FC } from "react";
import { <PERSON> } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { Team } from "@mio/helpers";
import { Typography, KeyboardArrowRightIcon, Button } from "@mio/ui";
import { TeamCard } from "../shared";

type Props = {
  team: Team;
};

export const TeamItem: FC<Props> = ({ team }) => {
  const { t } = useTranslation();

  return (
    <TeamCard team={team}>
      <Button variant="text" component={Link} to={team.id}>
        <KeyboardArrowRightIcon sx={{ marginRight: 1 }} />
        <Typography>{t("team.open")}</Typography>
      </Button>
    </TeamCard>
  );
};
