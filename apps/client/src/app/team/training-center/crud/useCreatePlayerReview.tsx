import { useState } from "react";
import { useTranslation } from "react-i18next";

import {
  Lean<PERSON><PERSON>,
  Player,
  TrainingSessionPlayerReview,
  TrainingSessionReview,
  isError,
} from "@mio/helpers";
import { Modal, Typography, trainingSessionState } from "@mio/ui";

import { UpsertPlayerReviewForm } from "./playerReview.form";

export const useCreatePlayerReview = (parentReview: TrainingSessionReview.ExtendedReview) => {
  const { t } = useTranslation();
  const { addPlayerReview } = trainingSessionState.useGetExtendedTrainingSessionReview();
  const [player, setPlayer] = useState<LeanPlayer | undefined>();
  const createPlayerReview = trainingSessionState.useCreateTrainingSessionPlayerReview();

  const handleSubmit = (payload: unknown) => {
    const parsed = TrainingSessionPlayerReview.Entity.toCreateDto(payload);

    if (!isError(parsed)) {
      createPlayerReview.mutate(parsed, {
        onSuccess: (createdReview) => {
          addPlayerReview(parentReview, createdReview);
          setPlayer(undefined);
        },
      });
    }
  };

  return {
    createPlayerReview: (player: LeanPlayer) => {
      setPlayer(player);
    },
    CreatePlayerReviewUI: (
      <Modal
        open={!!player}
        fullWidth
        onClose={() => setPlayer(undefined)}
        title={
          <Typography variant="h5" component="h2">
            {t("training.review-player", { name: player ? Player.getFullName(player) : "" })}
          </Typography>
        }
        content={
          player?.id && (
            <UpsertPlayerReviewForm
              onSubmit={handleSubmit}
              parentReview={parentReview}
              playerId={player.id}
              loading={createPlayerReview.isLoading}
              serverError={createPlayerReview.error}
            />
          )
        }
      />
    ),
  };
};
