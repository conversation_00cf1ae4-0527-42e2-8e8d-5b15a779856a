import { useState } from "react";
import { useTranslation } from "react-i18next";

import {
  Lean<PERSON><PERSON>,
  Player,
  TrainingSessionPlayerReview,
  TrainingSessionReview,
  isError,
} from "@mio/helpers";
import { Modal, Typography, trainingSessionState } from "@mio/ui";

import { UpsertPlayerReviewForm } from "./playerReview.form";

type Params = {
  player: LeanPlayer;
  review: TrainingSessionPlayerReview.PlayerReview;
};

export const useEditPlayerReview = (parentReview: TrainingSessionReview.ExtendedReview) => {
  const { t } = useTranslation();
  const { editPlayerReview } = trainingSessionState.useGetExtendedTrainingSessionReview();
  const [updateParams, setUpdateParams] = useState<Params | undefined>();
  const updatePlayerReview = trainingSessionState.useUpdateTrainingSessionPlayerReview();

  const handleSubmit = (payload: unknown) => {
    const parsed = TrainingSessionPlayerReview.Entity.toUpdateDto(payload);

    if (!isError(parsed)) {
      updatePlayerReview.mutate(parsed, {
        onSuccess: (updatedReview) => {
          editPlayerReview(parentReview, updatedReview);
          setUpdateParams(undefined);
        },
      });
    }
  };

  return {
    editPlayerReview: (params: Params) => {
      setUpdateParams(params);
    },
    EditPlayerReviewUI: (
      <Modal
        open={!!updateParams}
        fullWidth
        onClose={() => setUpdateParams(undefined)}
        title={
          <Typography variant="h5" component="h2">
            {t("training.review-player", {
              name: updateParams ? Player.getFullName(updateParams.player) : "",
            })}
          </Typography>
        }
        content={
          updateParams && (
            <UpsertPlayerReviewForm
              onSubmit={handleSubmit}
              parentReview={parentReview}
              review={updateParams.review}
              playerId={updateParams.player.id}
              loading={updatePlayerReview.isLoading}
              serverError={updatePlayerReview.error}
            />
          )
        }
      />
    ),
  };
};
