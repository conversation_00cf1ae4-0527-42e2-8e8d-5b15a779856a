import { FC } from "react";
import { useTranslation } from "react-i18next";

import {
  Box,
  TextField,
  Stack,
  LocalError,
  FormLabel,
  Select,
  MenuItem,
  LoadingButton,
  FormControl,
  FormHelperText,
} from "@mio/ui";
import {
  TrainingSessionReview,
  PlayerPerformanceBasicRatings,
  APIError,
  TeamEvent,
} from "@mio/helpers";

import { ReviewDataShape } from "./types";
import { useTrainingSessionReviewForm } from "./useForm";

type Props = {
  eventId: TeamEvent.EventId;
  review?: TrainingSessionReview.ExtendedReview;
  onSubmit: (data: ReviewDataShape) => void;
  loading?: boolean;
  serverError?: APIError | null;
};

export const UpsertReviewForm: FC<Props> = ({
  review,
  onSubmit,
  loading,
  serverError,
  eventId,
}) => {
  const { t } = useTranslation();
  const { handleSubmit, register, formState } = useTrainingSessionReviewForm(eventId, review);
  const { errors } = formState;

  return (
    <Stack gap={3} mt={2} component="form" onSubmit={handleSubmit(onSubmit)}>
      <FormControl>
        <TextField
          fullWidth
          aria-required
          label={t("training.learning-outcomes")}
          name="learningOutcomes"
          multiline
          minRows={2}
          inputProps={{ ...register("learningOutcomes") }}
          helperText={errors.learningOutcomes?.message || ""}
          error={!!errors.learningOutcomes?.message}
          aria-invalid={!!errors.learningOutcomes?.message}
        />
      </FormControl>

      <FormControl>
        <TextField
          fullWidth
          aria-required
          label={t("training.players-engagement")}
          name="playersEngagement"
          multiline
          minRows={3}
          inputProps={{ ...register("playersEngagement") }}
          helperText={errors.playersEngagement?.message || ""}
          error={!!errors.playersEngagement?.message}
          aria-invalid={!!errors.playersEngagement?.message}
        />
      </FormControl>

      <FormControl>
        <TextField
          fullWidth
          aria-required
          label={t("training.coach-behaviours")}
          name="coachBehaviours"
          multiline
          minRows={3}
          inputProps={{ ...register("coachBehaviours") }}
          helperText={errors.coachBehaviours?.message || ""}
          error={!!errors.coachBehaviours?.message}
          aria-invalid={!!errors.coachBehaviours?.message}
        />
      </FormControl>

      <FormControl>
        <TextField
          fullWidth
          aria-required
          label={t("training.safety-considerations")}
          name="safetyConsiderations"
          multiline
          minRows={3}
          inputProps={{ ...register("safetyConsiderations") }}
          helperText={errors.safetyConsiderations?.message || ""}
          error={!!errors.safetyConsiderations?.message}
          aria-invalid={!!errors.safetyConsiderations?.message}
        />
      </FormControl>

      <FormControl>
        <TextField
          fullWidth
          aria-required
          label={t("training.arrival-and-warm-up")}
          name="arrivalAndWarmUp"
          multiline
          minRows={3}
          inputProps={{ ...register("arrivalAndWarmUp") }}
          helperText={errors.arrivalAndWarmUp?.message || ""}
          error={!!errors.arrivalAndWarmUp?.message}
          aria-invalid={!!errors.arrivalAndWarmUp?.message}
        />
      </FormControl>

      <FormControl>
        <TextField
          fullWidth
          aria-required
          label={t("training.summary")}
          name="summary"
          multiline
          minRows={3}
          inputProps={{ ...register("summary") }}
          helperText={errors.summary?.message || ""}
          error={!!errors.summary?.message}
          aria-invalid={!!errors.summary?.message}
        />
      </FormControl>

      <FormControl>
        <TextField
          fullWidth
          aria-required
          label={t("training.details")}
          name="details"
          multiline
          minRows={3}
          inputProps={{ ...register("details") }}
          helperText={errors.details?.message || ""}
          error={!!errors.details?.message}
          aria-invalid={!!errors.details?.message}
        />
      </FormControl>

      <FormControl>
        <FormLabel>{t("training.rating")}</FormLabel>
        <Select<PlayerPerformanceBasicRatings>
          fullWidth
          aria-required
          label={t("training.rating")}
          name="rating"
          inputProps={{ ...register("rating") }}
          error={!!errors.rating?.message}
          aria-invalid={!!errors.rating?.message}
          defaultValue={PlayerPerformanceBasicRatings.Good}
        >
          <MenuItem value={PlayerPerformanceBasicRatings.Abysmal}>
            {t("training.ratings.abysmal")}
          </MenuItem>
          <MenuItem value={PlayerPerformanceBasicRatings.Bad}>{t("training.ratings.bad")}</MenuItem>
          <MenuItem value={PlayerPerformanceBasicRatings.Good}>
            {t("training.ratings.good")}
          </MenuItem>
          <MenuItem value={PlayerPerformanceBasicRatings.Outstanding}>
            {t("training.ratings.outstanding")}
          </MenuItem>
          <MenuItem value={PlayerPerformanceBasicRatings.Neutral}>
            {t("training.ratings.neutral")}
          </MenuItem>
        </Select>
        {errors.rating?.message && <FormHelperText>{errors.rating?.message}</FormHelperText>}
      </FormControl>

      <FormControl>
        <FormLabel>{t("training.expectations-achieved-rating")}</FormLabel>
        <Select<number>
          fullWidth
          aria-required
          label={t("training.expectations-achieved-rating")}
          name="expectationsAchievedRating"
          inputProps={{ ...register("expectationsAchievedRating") }}
          error={!!errors.expectationsAchievedRating?.message}
          aria-invalid={!!errors.expectationsAchievedRating?.message}
          defaultValue={8}
        >
          {Array.from(Array(10).keys()).map((i) => (
            <MenuItem key={i} value={i + 1}>
              {i + 1}
            </MenuItem>
          ))}
        </Select>
        {errors.expectationsAchievedRating?.message && (
          <FormHelperText>{errors.expectationsAchievedRating?.message}</FormHelperText>
        )}
      </FormControl>

      <Box gap={2} sx={{ display: "flex", justifyContent: "center" }}>
        <LoadingButton type="submit" variant="contained" loading={loading}>
          {t("common.save")}
        </LoadingButton>
      </Box>

      {serverError && <LocalError message={t("common.error")} />}
    </Stack>
  );
};
