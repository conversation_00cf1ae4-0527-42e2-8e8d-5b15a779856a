import { useState } from "react";
import { useTranslation } from "react-i18next";

import { isError, TeamEvent, TrainingSessionReview } from "@mio/helpers";
import { Modal, ToggleSnackbar, Typography, trainingSessionState, Alert } from "@mio/ui";

import { UpsertReviewForm } from "./review.form";

export const useCreateReview = (onSuccess: (review: TrainingSessionReview.Review) => void) => {
  const { t } = useTranslation();
  const [activeEvent, setActiveEvent] = useState<TeamEvent.EventId | undefined>(undefined);

  const createReview = trainingSessionState.useCreateTrainingSessionReview();

  const handleSubmit = (data: unknown) => {
    const validated = TrainingSessionReview.Entity.toCreateDto(data);

    if (!isError(validated)) {
      createReview.mutate(validated, {
        onSuccess: (response) => {
          onSuccess(response);
          setActiveEvent(undefined);
        },
      });
    }
  };

  return {
    createReview: (eventId: TeamEvent.EventId) => setActiveEvent(eventId),
    CreateReviewUI: (
      <>
        <Modal
          open={!!activeEvent}
          fullWidth
          onClose={() => setActiveEvent(undefined)}
          title={
            <Typography variant="h5" component="h2">
              {t("training.add-new-review")}
            </Typography>
          }
          content={
            activeEvent ? (
              <UpsertReviewForm
                onSubmit={handleSubmit}
                loading={createReview.isLoading}
                serverError={createReview.error}
                eventId={activeEvent}
              />
            ) : undefined
          }
        />

        <ToggleSnackbar open={createReview.isSuccess}>
          <Alert severity="success">{t("training.review-created")}</Alert>
        </ToggleSnackbar>
      </>
    ),
  };
};
