import { useForm } from "react-hook-form";

import { TrainingSessionPlan, TrainingSessionReview, UUID, TeamEvent } from "@mio/helpers";
import { useRequiredTeam, useFormResolver, useOrganization } from "@mio/ui";

import { ReviewDataShape } from "./types";

export const useTrainingSessionReviewForm = (
  eventId: TeamEvent.EventId,
  review: TrainingSessionReview.Review | undefined,
) => {
  const organization = useOrganization();
  const team = useRequiredTeam();
  const formResolver = useFormResolver<ReviewDataShape>(TrainingSessionReview.Entity.toUpsertDto);

  const formMethods = useForm<ReviewDataShape>({
    defaultValues: {
      ...review,
      organizationId: organization.id,
      teamId: team.id,
      /* TODO: proper plan id*/
      trainingSessionPlanId: UUID.generate() as TrainingSessionPlan.PlanId,
      teamEventId: review?.teamEventId || eventId,
    },
    resolver: formResolver,
  });

  return formMethods;
};
