import { FC } from "react";
import { useTranslation } from "react-i18next";

import { Player, TrainingSessionPlayerReview } from "@mio/helpers";
import { Stack, Box, Typography, SpeedIcon, StadiumIcon } from "@mio/ui";

import {
  SortingOption,
  SortingOrder,
  formatPercent,
  getPlayerTotals,
  percentToRatingFormatted,
} from "./insights-helpers";

type Props = {
  reviews: TrainingSessionPlayerReview.ExtendedReview[];
};

const BreakdownChart: FC<Props> = ({ reviews }) => {
  const { t } = useTranslation();
  const playerTotals = getPlayerTotals(reviews, SortingOption.Performance, SortingOrder.Descending);

  return (
    <Stack gap={5}>
      {playerTotals.map((record) => {
        return (
          <Stack key={record.player.id} gap={1}>
            <Stack direction="row" gap={1} flexWrap="wrap" justifyContent="space-between">
              <Typography fontWeight="bold">{Player.getFullName(record.player)}</Typography>
              <Typography>{t("training.review-count", { count: record.reviews })}</Typography>
            </Stack>

            <Stack direction="row" gap={2} alignItems="flex-end">
              <SpeedIcon fontSize="large" color="primary" />
              <Stack gap={1} flexGrow={1}>
                <Stack direction="row" gap={2} justifyContent="space-between">
                  <Typography sx={{ fontSize: "small" }}>{t("training.performance")}</Typography>
                  <Typography fontWeight="bold">
                    {percentToRatingFormatted(record.performance)}
                  </Typography>
                </Stack>

                <Box sx={{ flexGrow: 2, backgroundColor: "lightgray", borderRadius: 5 }}>
                  <Box
                    sx={{
                      backgroundColor: "primary.main",
                      width: `${record.performance.toFixed(0)}%`,
                      p: 1,
                      borderRadius: 5,
                    }}
                  ></Box>
                </Box>
              </Stack>
            </Stack>

            <Stack direction="row" gap={2} alignItems="flex-end">
              <StadiumIcon fontSize="large" color="secondary" />
              <Stack gap={1} flexGrow={1}>
                <Stack direction="row" gap={2} justifyContent="space-between">
                  <Typography sx={{ fontSize: "small" }}>{t("training.attendance")}</Typography>
                  <Typography fontWeight="bold">{formatPercent(record.attendance)}</Typography>
                </Stack>

                <Box sx={{ flexGrow: 2, backgroundColor: "lightgray", borderRadius: 5 }}>
                  <Box
                    sx={{
                      backgroundColor: "secondary.main",
                      width: `${record.attendance.toFixed(0)}%`,
                      p: 1,
                      borderRadius: 5,
                    }}
                  ></Box>
                </Box>
              </Stack>
            </Stack>
          </Stack>
        );
      })}
    </Stack>
  );
};

export default BreakdownChart;
