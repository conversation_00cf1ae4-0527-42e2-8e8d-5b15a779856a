import { FC, useState } from "react";
import { useTranslation } from "react-i18next";

import { TrainingSessionPlayerReview } from "@mio/helpers";
import {
  trainingSessionState,
  PageLoader,
  PageError,
  Stack,
  Card,
  Typography,
  RateReviewIcon,
  DirectionsRunIcon,
  Alert,
  SpeedIcon,
  StadiumIcon,
  Box,
  Divider,
  ToggleButtonGroup,
  ToggleButton,
  BarChartIcon,
  TableRowsIcon,
} from "@mio/ui";

import {
  formatPercent,
  getTotalAttendance,
  getTotalNumberOfPlayers,
  getTotalPerformance,
  percentToRatingFormatted,
} from "./insights-helpers";
import ReviewBreakdown from "./reviewBreakdown";
import BreakdownChart from "./breakdownChart";

type Props = {
  params: TrainingSessionPlayerReview.QueryDto;
};

enum BreakdownView {
  Chart,
  Table,
}

const ReviewInsights: FC<Props> = ({ params }) => {
  const { t } = useTranslation();
  const [breakdownView, setBreakdownView] = useState<BreakdownView>(BreakdownView.Chart);
  const reviews = trainingSessionState.useGetTrainingSessionPlayerReviews(params);

  if (reviews.isLoading) {
    return <PageLoader />;
  }

  if (reviews.error) {
    return <PageError />;
  }

  if (reviews.data.length === 0) {
    return <Alert severity="warning">{t("training.no-reviews-for-period")}</Alert>;
  }

  const totalPlayers = getTotalNumberOfPlayers(reviews.data);
  const totalPerformance = getTotalPerformance(reviews.data.map((review) => review.rating));
  const totalAttendance = getTotalAttendance(reviews.data.map((review) => review.attendance));

  return (
    <Stack gap={4}>
      <Stack direction="row" alignItems="center" flexWrap="wrap" gap={4}>
        <Card sx={{ p: 2 }}>
          <Typography fontWeight="bold">{t("training.reviews")}</Typography>
          <Stack direction="row" alignItems="center" gap={2}>
            <RateReviewIcon fontSize="large" color="primary" />
            <Typography component="h2" variant="h3" color="primary" fontWeight="bold">
              {reviews.data.length}
            </Typography>
          </Stack>
        </Card>

        <Card sx={{ p: 2 }}>
          <Typography fontWeight="bold">{t("training.players")}</Typography>
          <Stack direction="row" alignItems="center" gap={2}>
            <DirectionsRunIcon fontSize="large" color="primary" />
            <Typography component="h2" variant="h3" color="primary" fontWeight="bold">
              {totalPlayers}
            </Typography>
          </Stack>
        </Card>

        <Card sx={{ p: 2 }}>
          <Typography fontWeight="bold">{t("training.average-rating")}</Typography>
          <Stack direction="row" alignItems="center" gap={2}>
            <SpeedIcon fontSize="large" color="primary" />
            <Typography component="h2" variant="h3" color="primary" fontWeight="bold">
              {percentToRatingFormatted(totalPerformance)}
            </Typography>
          </Stack>
        </Card>

        <Card sx={{ p: 2 }}>
          <Typography fontWeight="bold">{t("training.average-attendance")}</Typography>
          <Stack direction="row" alignItems="center" gap={2}>
            <StadiumIcon fontSize="large" color="primary" />
            <Typography component="h2" variant="h3" color="primary" fontWeight="bold">
              {formatPercent(totalAttendance)}
            </Typography>
          </Stack>
        </Card>
      </Stack>

      <Stack gap={1}>
        <Typography>{t("training.insights.goals-first-half", { percent: "70" })}</Typography>
        <Typography>
          {t("training.insights.defensive-improvement", { weeks: "4", percent: "35" })}
        </Typography>
        <Typography>
          {t("training.insights.player-combination", {
            percent: "63",
            player1: "Peter Crouch",
            player2: "Jermaine Pennant",
          })}
        </Typography>
      </Stack>

      <Divider flexItem />

      <ToggleButtonGroup
        value={breakdownView}
        exclusive
        onChange={(_event, newValue) => {
          setBreakdownView(newValue);
        }}
      >
        <ToggleButton value={BreakdownView.Chart}>
          <BarChartIcon sx={{ mr: 1 }} />
          {t("training.chart-view")}
        </ToggleButton>
        <ToggleButton value={BreakdownView.Table}>
          <TableRowsIcon sx={{ mr: 1 }} />
          {t("training.table-view")}
        </ToggleButton>
      </ToggleButtonGroup>

      {breakdownView === BreakdownView.Chart && (
        <Box mt={2}>
          <BreakdownChart reviews={reviews.data} />
        </Box>
      )}

      {breakdownView === BreakdownView.Table && (
        <Box mt={2}>
          <ReviewBreakdown reviews={reviews.data} />
        </Box>
      )}
    </Stack>
  );
};

export default ReviewInsights;
