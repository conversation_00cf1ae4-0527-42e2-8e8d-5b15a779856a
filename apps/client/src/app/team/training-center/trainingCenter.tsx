import { Typography } from "@mui/material";
import { FC } from "react";
import { useTranslation } from "react-i18next";

import { SidebarLayout } from "../../layouts";
import { TrainingSessionList } from "./list";

export const TrainingCenter: FC = () => {
  const { t } = useTranslation();

  return (
    <SidebarLayout>
      <Typography variant="h4" component="h1" sx={{ mb: 4 }}>
        {t("training.training-center")}
      </Typography>
      <TrainingSessionList />
    </SidebarLayout>
  );
};
