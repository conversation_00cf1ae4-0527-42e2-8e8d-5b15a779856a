import { FC, useState, SyntheticEvent } from "react";
import { useTranslation } from "react-i18next";

import {
  trainingSessionState,
  PageLoader,
  PageError,
  Stack,
  Typography,
  TabContext,
  Box,
  TabList,
  Tab,
  TabPanel,
  Divider,
  Chip,
  RateReviewIcon,
} from "@mio/ui";
import { CustomDate, TeamEvent, TrainingSessionReview, isError } from "@mio/helpers";

import { SidebarLayout } from "../../layouts";
import { BackButton } from "../../shared";
import { PlayerReviewList } from "./playerReviewList";
import { UpsertReviewForm } from "./crud/review.form";

enum Panels {
  Details = "Details",
  PlayerReviews = "Player Reviews",
}

export const TrainingReviewDetails: FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<Panels>(Panels.Details);
  const { query } = trainingSessionState.useGetExtendedTrainingSessionReview();
  const updateReview = trainingSessionState.useUpdateTrainingSessionReview();

  const handleChange = (_: SyntheticEvent, newValue: Panels) => {
    setActiveTab(newValue);
  };

  const handleSubmit = (data: unknown) => {
    const parsed = TrainingSessionReview.Entity.toUpdateDto(data);

    if (!isError(parsed)) {
      updateReview.mutate(parsed);
    }
  };

  return (
    <SidebarLayout>
      {query.isLoading && <PageLoader />}

      {query.isError && <PageError />}

      {query.isSuccess && (
        <Stack gap={4}>
          <Stack
            component="header"
            direction="row"
            gap={2}
            alignItems="center"
            flexWrap="wrap"
            p={2}
          >
            <Stack
              direction="row"
              alignItems="center"
              justifyContent="space-between"
              flexWrap="wrap"
              width="100%"
            >
              <Box>
                <BackButton path="../">{t("training.back-to-training-center")}</BackButton>
              </Box>

              <Typography>
                <Chip
                  label={`${TeamEvent.Entity.getName(
                    query.data.event,
                  )} - ${CustomDate.toDisplayDate(query.data.event.startDateTime)}`}
                  color="secondary"
                />
              </Typography>
            </Stack>

            <Stack direction="row" alignItems="center" gap={2}>
              <RateReviewIcon fontSize="large" color="secondary" />
              <Typography variant="h4" component="h1" sx={{ width: "100%" }}>
                {t("training.session-review")}
              </Typography>
            </Stack>

            <Divider flexItem sx={{ width: "100%" }} />
          </Stack>

          <TabContext value={activeTab}>
            <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
              <TabList
                onChange={handleChange}
                aria-label={t("training.review-tabs-label")}
                variant="fullWidth"
                centered
              >
                <Tab label={t("training.details")} value={Panels.Details} />
                <Tab label={t("training.player-reviews")} value={Panels.PlayerReviews} />
              </TabList>
            </Box>
            <TabPanel value={Panels.Details}>
              <UpsertReviewForm
                onSubmit={handleSubmit}
                eventId={query.data.event.id}
                review={query.data}
                loading={updateReview.isLoading}
                serverError={updateReview.error}
              />
            </TabPanel>
            <TabPanel value={Panels.PlayerReviews}>
              <PlayerReviewList review={query.data} />
            </TabPanel>
          </TabContext>
        </Stack>
      )}
    </SidebarLayout>
  );
};
