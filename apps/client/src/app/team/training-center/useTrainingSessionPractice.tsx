import { FC, useState } from "react";
import { filter } from "lodash/fp";
import { useTranslation } from "react-i18next";

import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  useTheme,
  useMediaQuery,
  useOrganization,
  useUploadCoachImage,
  useProvidedCurrentUser,
  ImageUpload,
} from "@mio/ui";
import { Assets, Email, EmailTemplate, OrganizationId, isError } from "@mio/helpers";

type Props = {
  onClose: () => void;
  showModal: boolean;
};

const TrainingSessionPracticeModal: FC<Props> = ({ onClose, showModal }) => {
  const { t } = useTranslation();

  const theme = useTheme();
  const organization = useOrganization();
  const coach = useProvidedCurrentUser();
  const isOnSmallScreen = useMediaQuery(theme.breakpoints.down("md"));

  const uploadImage = useUploadCoachImage();

  const handleUpload = (file: Assets.Image.ParsedFile) => {
    console.log("useTrainingSessionPracticeModal", file.type);
    uploadImage.mutate(
      {
        dto: { file },
        coachId: coach.id,
        organizationId: organization.id,
      },
      {
        onSuccess: (data) => {
          console.log("data returned", data);
        },
      },
    );

    onClose();
  };

  const handleSubmit = (source: unknown) => {
    // const parsed = Assets.Image.Entity.
    // if (!isError(parsed)) {
    //   uploadImage.mutate(parsed);
    // } else {
    //   // TODO: we can save the errors into a variable and display them if we wish so
    // }
  };

  return (
    <Dialog open={showModal} fullWidth={!isOnSmallScreen} fullScreen={isOnSmallScreen}>
      <DialogTitle id="confirmation-dialog-title" sx={{ mb: 2 }}>
        {t("email.send.title")}
      </DialogTitle>

      <DialogContent>
        <ImageUpload
          onUpload={handleUpload}
          isLoading={uploadImage.isLoading}
          error={uploadImage.error || undefined}
          success={uploadImage.isSuccess}
        />
      </DialogContent>
      <DialogActions></DialogActions>
    </Dialog>
  );
};

export const usePracticeUpload = () => {
  const [showModal, setShowModal] = useState<boolean>(false);

  return {
    PracticeUploadUI: () =>
      showModal ? (
        <TrainingSessionPracticeModal onClose={() => setShowModal(false)} showModal={showModal} />
      ) : null,
    show: () => setShowModal(true),
  };
};
