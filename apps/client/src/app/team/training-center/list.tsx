import { useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { pick } from "lodash/fp";

import {
  <PERSON><PERSON>rror,
  <PERSON>L<PERSON>der,
  <PERSON>ack,
  Card,
  Button,
  Alert,
  useGetTrainingEvents,
  InsightsIcon,
  ListIcon,
  Box,
  AddIcon,
} from "@mio/ui";

import { groupEventsByDate } from "../../shared";
import Filters, { FilterValues, filterToQuery } from "./filters";
import TrainingSession from "./review-item";
import { useCreateReview } from "./crud/useCreateReview";
import ReviewInsights from "./insights/insights";
import { Link } from "react-router-dom";

enum Views {
  List = "list",
  Insights = "insights",
}

export const TrainingSessionList = () => {
  const { t } = useTranslation();
  const { CreateReviewUI, createReview } = useCreateReview(() => query.refetch());
  const { query, setQuery, searchQuery } = useGetTrainingEvents(filterToQuery(FilterValues.Past30));
  const [activeView, setActiveView] = useState(Views.List);

  const eventsByDate = useMemo(() => {
    if (query.data) {
      return groupEventsByDate(query.data);
    }
    return [] as const;
  }, [query.data]);

  return (
    <Stack>
      <Stack
        mb={4}
        component="header"
        direction="row"
        gap={4}
        flexWrap="wrap"
        alignItems="center"
        justifyContent="space-between"
      >
        <Box width={3.5 / 5}>
          <Filters onChange={setQuery} initialValue={FilterValues.Past30} />
        </Box>

        {activeView !== Views.Insights && (
          <Button
            variant="outlined"
            startIcon={<InsightsIcon />}
            onClick={() => setActiveView(Views.Insights)}
          >
            {t("training.show-insights")}
          </Button>
        )}
        {activeView !== Views.List && (
          <Button
            variant="outlined"
            startIcon={<ListIcon />}
            onClick={() => setActiveView(Views.List)}
          >
            {t("training.show-reviews")}
          </Button>
        )}

        <Button variant="contained" component={Link} to="create" startIcon={<AddIcon />}>
          {t("training.create-new")}
        </Button>
      </Stack>

      {query.isLoading && <PageLoader message={t("training.loading-events")} />}

      {query.isError && <PageError message={t("training.failed-to-load-events")} />}

      {eventsByDate.length === 0 && <Alert severity="info">{t("training.no-trainings-yet")}</Alert>}

      {activeView === Views.List && (
        <Stack mt={1} gap={4}>
          {eventsByDate.map((item) => (
            <Card key={item.date} sx={{ p: 2 }}>
              <Stack gap={2}>
                {item.events.map((event) => (
                  <TrainingSession
                    key={event.id}
                    event={event}
                    date={item.date}
                    onCreateReview={() => createReview(event.id)}
                  />
                ))}
              </Stack>
            </Card>
          ))}
        </Stack>
      )}

      {eventsByDate.length && activeView === Views.Insights && (
        <Box mt={5}>
          <ReviewInsights params={pick(["startDate", "endDate"], searchQuery)} />
        </Box>
      )}

      {CreateReviewUI}
    </Stack>
  );
};
