import { FC } from "react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { CustomDate, TeamEvent } from "@mio/helpers";
import { Box, Typography, Stack, Button, AddIcon, EditIcon } from "@mio/ui";

type Props = {
  event: TeamEvent.ExtendedTeamEvent;
  date: string;
  onCreateReview: () => void;
};

const TrainingSession: FC<Props> = ({ event, date, onCreateReview }) => {
  const { t } = useTranslation();

  return (
    <Box sx={gridStyle}>
      <Typography>{event.recurringEvent?.name}</Typography>
      <Stack>
        <Typography>{CustomDate.toDisplayDate(date)} </Typography>
        <Typography>
          {CustomDate.toDisplayTime(event.startDateTime)} -{" "}
          {CustomDate.toDisplayTime(event.endDateTime)}
        </Typography>
      </Stack>

      {!TeamEvent.Entity.isPast(event) && (
        <Stack direction="row-reverse">
          <Button variant="contained" color="secondary">
            {t("training.plan")}
          </Button>
        </Stack>
      )}

      {TeamEvent.Entity.isPast(event) && !event.trainingSessionReview && (
        <Stack direction="row-reverse">
          <Button
            variant="contained"
            color="secondary"
            onClick={onCreateReview}
            startIcon={<AddIcon />}
          >
            {t("training.add-review")}
          </Button>
        </Stack>
      )}

      {TeamEvent.Entity.isPast(event) && event.trainingSessionReview && (
        <Stack direction="row-reverse">
          <Button
            variant="contained"
            color="secondary"
            startIcon={<EditIcon />}
            component={Link}
            to={`reviews/${event.trainingSessionReview.id}`}
          >
            {t("training.review-details")}
          </Button>
        </Stack>
      )}
    </Box>
  );
};

export default TrainingSession;

const gridColumns = "1.5fr 1.5fr 1fr";

const gridStyle = {
  display: {
    md: "grid",
  },
  gap: {
    sm: 4,
  },
  gridTemplateColumns: {
    md: gridColumns,
  },
  alignItems: "center",
};
