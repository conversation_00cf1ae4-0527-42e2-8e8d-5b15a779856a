import { FC, useState } from "react";
import { useTranslation } from "react-i18next";

import { Stack, ToggleButtonGroup, ToggleButton, FilterIcon } from "@mio/ui";
import { CustomDate, PositiveInteger, TeamEvent } from "@mio/helpers";

export enum FilterValues {
  Next30,
  AllNext,
  Past30,
  AllPast,
}

type Query = Pick<TeamEvent.QueryDto, "startDate" | "endDate">;

type Props = {
  onChange: (data: Query) => void;
  initialValue: FilterValues;
};

const Filters: FC<Props> = ({ onChange, initialValue }) => {
  const { t } = useTranslation();
  const [value, setValue] = useState<FilterValues>(initialValue);

  const handleChange = (newValue: FilterValues) => {
    setValue(newValue);
    onChange(filterToQuery(newValue));
  };

  return (
    <Stack direction="row" alignItems="center" gap={2}>
      <FilterIcon fontSize="large" color="primary" />
      <ToggleButtonGroup
        exclusive
        value={value}
        onChange={(_event, newValue) => {
          handleChange(newValue);
        }}
      >
        <ToggleButton value={FilterValues.Next30}>
          {t("training.filters.next-30-days")}
        </ToggleButton>
        <ToggleButton value={FilterValues.AllNext}>{t("training.filters.all-next")}</ToggleButton>
        <ToggleButton value={FilterValues.Past30}>
          {t("training.filters.past-30-days")}
        </ToggleButton>
        <ToggleButton value={FilterValues.AllPast}>{t("training.filters.all-past")}</ToggleButton>
      </ToggleButtonGroup>
    </Stack>
  );
};

export default Filters;

export const filterToQuery = (value: FilterValues) => {
  switch (value) {
    case FilterValues.Next30:
      return {
        startDate: CustomDate.now(),
        endDate: CustomDate.addDays(30 as PositiveInteger),
      };

    case FilterValues.AllNext:
      return {
        startDate: CustomDate.now(),
      };

    case FilterValues.Past30:
      return {
        startDate: CustomDate.subDays(30 as PositiveInteger),
        endDate: CustomDate.now(),
      };

    case FilterValues.AllPast:
      return {
        endDate: CustomDate.now(),
      };
  }
};
