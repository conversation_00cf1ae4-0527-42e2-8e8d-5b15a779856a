import { FC } from "react";
import { Navigate } from "react-router-dom";

import { useOrganization, useRequiredTeam, trainingSessionState } from "@mio/ui";
import { TrainingSessionPlan, isError } from "@mio/helpers";

import { SidebarLayout } from "../../layouts";
import { UpsertTrainingSessionPlan } from "./upsertTrainingSessionPlanForm";

export const AddTrainingSessionPlan: FC = () => {
  const organization = useOrganization();
  const team = useRequiredTeam();

  const createTrainingSession = trainingSessionState.useCreateTrainingSessionPlan();

  const handleSubmit = (data: unknown) => {
    const validated = TrainingSessionPlan.Entity.toCreateDto(data);

    if (!isError(validated)) {
      createTrainingSession.mutate(validated);
    }
  };

  if (createTrainingSession.isSuccess) {
    return <Navigate to={`/${organization.id}/teams/${team.id}/training-center`} />;
  }

  return (
    <SidebarLayout>
      <UpsertTrainingSessionPlan
        onSubmit={handleSubmit}
        loading={createTrainingSession.isLoading}
        serverError={createTrainingSession.error}
      />
    </SidebarLayout>
  );
};
