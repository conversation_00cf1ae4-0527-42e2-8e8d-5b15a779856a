import { FC } from "react";
import { useTranslation } from "react-i18next";

import { <PERSON><PERSON>rror, PageLoader, useGetTeams, useOrganization } from "@mio/ui";

type Props = {
  children: JSX.Element;
};

export const WithLoadedTeams: FC<Props> = ({ children }) => {
  const { t } = useTranslation();
  const organization = useOrganization();
  const teams = useGetTeams(organization.id);

  if (teams.isLoading) {
    return <PageLoader message={t("team.loading-teams")} />;
  }

  if (teams.isError) {
    return <PageError message={t("team.failed-to-load-teams")} />;
  }

  return children;
};
