import { FC } from "react";
import { useTranslation } from "react-i18next";

import {
  Box,
  Stack,
  Typography,
  PageError,
  PageLoader,
  useOrganization,
  useGetTeams,
} from "@mio/ui";
import { SidebarLayout } from "../layouts";
import { TeamItem } from "./team";

export const TeamsList: FC = () => {
  const { t } = useTranslation();
  const organization = useOrganization();
  const teams = useGetTeams(organization.id);

  if (teams.status === "loading") {
    return <PageLoader message={t("team.loading-teams")} />;
  }

  if (teams.status === "error") {
    return <PageError message={t("team.failed-to-load-teams")} />;
  }

  if (teams.status === "success") {
    return (
      <SidebarLayout>
        <Box sx={{ width: "100%", bgcolor: "background.paper", padding: 2 }}>
          <Typography variant="h2" component="h1" mb={3}>
            {t("team.teams-list")}
          </Typography>
          <Stack aria-label={t("team.teams-list-navigation")} component="nav" gap={3}>
            {teams.data.map((team) => (
              <TeamItem team={team} key={team.id} />
            ))}
          </Stack>
        </Box>
      </SidebarLayout>
    );
  }

  return null;
};
