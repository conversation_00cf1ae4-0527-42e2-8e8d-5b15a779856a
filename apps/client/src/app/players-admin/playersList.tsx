import { FC, useState } from "react";
import { useTranslation } from "react-i18next";

import {
  <PERSON>,
  Alert,
  Typo<PERSON>,
  PageError,
  PageLoader,
  Stack,
  Checkbox,
  Button,
  LocalLoader,
  playersState,
  useOrganization,
} from "@mio/ui";
import { PopulatedPlayer } from "@mio/helpers";

import { PlayerFilters } from "./filters";
import { usePlayerDetails, usePlayerDocuments } from "../shared";

import { SidebarLayout } from "../layouts";
import { PlayerCard } from "./playerCard";
import { usePlayerEmailDialog } from "./usePlayerEmailDialog";

export const AdminPlayersList: FC = () => {
  const { t } = useTranslation();
  const organization = useOrganization();
  const [selectedPlayers, setSelectedPlayers] = useState<PopulatedPlayer[]>([]);
  const { SendEmailUI } = usePlayerEmailDialog(selectedPlayers);
  const searchPlayers = playersState.useSearchPlayers(organization.id);
  const { PlayerDetailsUI, show } = usePlayerDetails({
    removeFromTeam: searchPlayers.removeFromTeam,
  });
  const { show: showDocuments, DocumentsUI } = usePlayerDocuments();

  const allPlayers =
    searchPlayers.query?.data?.pages.reduce((players, page) => {
      return [...players, ...page.data];
    }, [] as PopulatedPlayer[]) || [];

  const toggleAllPlayers = (checked: boolean) => setSelectedPlayers(checked ? allPlayers : []);

  const handlePlayerSelection = (targetPlayer: PopulatedPlayer) => {
    const exists = selectedPlayers.find((player) => player.id === targetPlayer.id);

    if (exists) {
      setSelectedPlayers(selectedPlayers.filter((player) => player.id !== targetPlayer.id));
    } else {
      setSelectedPlayers([...selectedPlayers, targetPlayer]);
    }
  };

  const isLoading = searchPlayers.query.isLoading && searchPlayers.query.fetchStatus !== "idle";

  return (
    <SidebarLayout>
      <PlayerFilters
        isLoading={isLoading}
        onSubmit={(data) => {
          searchPlayers.search({ orgId: data.organizationId, query: data.query });
        }}
      />

      {isLoading && <PageLoader message={t("players.admin.list.loading")} />}

      {searchPlayers.query.isError && <PageError message={t("players.admin.list.load-error")} />}

      {searchPlayers.query.isSuccess && searchPlayers.query.data.pages.length === 0 && (
        <Box mt={2}>
          <Alert severity="info">{t("players.admin.list.no-players")}</Alert>
        </Box>
      )}

      {searchPlayers.query.isSuccess && (
        <>
          <Stack direction="row" alignItems="center" gap={3} mt={3} mb={3} flexWrap="wrap">
            <Stack direction="row" alignItems="center">
              <Checkbox onChange={(e) => toggleAllPlayers(e.target.checked)} />
              <Typography>
                {t("players.admin.list.showing-count", {
                  count: allPlayers.length,
                  total: searchPlayers.query.data.pages[0].total,
                })}
              </Typography>
            </Stack>

            {SendEmailUI}
          </Stack>

          <Stack gap={2}>
            {allPlayers.map((player) => (
              <PlayerCard
                key={player.id}
                player={player}
                showDetails={() => show(player)}
                showDocuments={() => showDocuments(player)}
                assignTeam={searchPlayers.assignToTeam}
                onSelect={() => handlePlayerSelection(player)}
                isSelected={!!selectedPlayers.find((elem) => player.id === elem.id)}
              />
            ))}

            {searchPlayers.query.isFetchingNextPage && (
              <LocalLoader message={t("players.admin.list.loading-more")} />
            )}

            {searchPlayers.query.hasNextPage && (
              <Box>
                <Button variant="text" onClick={() => searchPlayers.query.fetchNextPage()}>
                  {t("players.admin.list.load-more")}
                </Button>
              </Box>
            )}
          </Stack>

          <PlayerDetailsUI />
          {DocumentsUI}
        </>
      )}
    </SidebarLayout>
  );
};
