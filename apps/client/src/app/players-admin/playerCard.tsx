import { FC } from "react";
import { capitalize } from "lodash/fp";
import { useTranslation } from "react-i18next";

import {
  Box,
  Card,
  Stack,
  Typography,
  Chip,
  Button,
  Checkbox,
  playersState,
  Divider,
  useOrganization,
} from "@mio/ui";
import { CustomDate, Player, PopulatedPlayer } from "@mio/helpers";
import { AssignToTeam } from "../shared/components/assignToTeam";

type Props = {
  player: PopulatedPlayer;
  showDetails: () => void;
  showDocuments: () => void;
  assignTeam: playersState.AssignToTeamHandler;
  readonly?: boolean;
  onSelect: () => void;
  isSelected: boolean;
};
const gridColumns = "1.5fr 0.5fr 1fr 1fr";
export const PlayerCard: FC<Props> = ({
  player,
  showDetails,
  showDocuments,
  readonly,
  onSelect,
  isSelected,
  assignTeam,
}) => {
  const { t } = useTranslation();
  const organization = useOrganization();

  const totalDocuments = [
    player.documents?.photo_id,
    ...(player.documents?.image_document_ids ?? []),
  ].filter((value) => !!value).length;
  return (
    <Card sx={{ p: 2 }}>
      <Stack gap={3}>
        <Box
          sx={{
            display: {
              md: "grid",
              xs: "flex",
            },
            flexWrap: "wrap",
            gap: {
              xs: 2,
              sm: 4,
            },
            gridTemplateColumns: {
              md: gridColumns,
            },
            alignItems: "center",
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <Checkbox onChange={onSelect} checked={isSelected} />
            <Typography>
              {player.firstName} {player.lastName}
            </Typography>
          </Box>

          <Stack gap={1}>
            <Typography>Gender</Typography>
            <Box>
              <Chip label={capitalize(Player.getGender(player))} />
            </Box>
          </Stack>
          <Box>
            <Typography>
              {t("players.admin.card.born", { date: CustomDate.toDisplayDate(player.dob) })}
            </Typography>
            <Typography>
              {t("players.admin.card.years-old", { age: CustomDate.toDisplayAge(player.dob) })}
            </Typography>
          </Box>

          <Box flexGrow={1}>
            {Player.isOrganizationApplicant(player) && !readonly && (
              <AssignToTeam player={player} assignTeam={assignTeam} />
            )}
          </Box>
        </Box>
        <Stack direction="row" gap={2}>
          <Button variant="text" onClick={showDetails}>
            {t("players.admin.card.details")}
          </Button>
          {totalDocuments > 0 ? (
            <>
              <Divider orientation="vertical" flexItem />
              <Button variant="text" onClick={showDocuments}>
                {t("players.admin.card.documents-count", { count: totalDocuments })}
              </Button>
            </>
          ) : null}
        </Stack>
      </Stack>
    </Card>
  );
};
