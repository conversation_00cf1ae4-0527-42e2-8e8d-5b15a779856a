import { FC } from "react";
import { useForm } from "react-hook-form";
import { omitBy, isObject, startCase } from "lodash/fp";
import { useTranslation } from "react-i18next";

import {
  Box,
  Stack,
  DatePicker,
  TextField,
  InputLabel,
  Select,
  MenuItem,
  FormLabel,
  SearchIcon,
  InputAdornment,
  IconButton,
  ClearIcon,
  LoadingButton,
  FormControl,
  playersState,
  useGetAdminTeams,
  useOrganization,
  useFormResolver,
} from "@mio/ui";
import { CustomDate, isError, Player, PlayerSorting, PlayerTeamStatus } from "@mio/helpers";
import { DataShape } from "./types";

type Props = {
  isLoading: boolean;
  onSubmit: (dto: playersState.PlayerQueryParams) => void;
};

const sortingDisplayNames = {
  [PlayerSorting.CreationDate]: "players.admin.filters.sort-by.options.creation-date",
  [PlayerSorting.Names]: "players.admin.filters.sort-by.options.names",
  [PlayerSorting.Age]: "players.admin.filters.sort-by.options.age",
} as const;

export const PlayerFilters: FC<Props> = ({ onSubmit, isLoading }) => {
  const { t } = useTranslation();
  const organization = useOrganization();

  const formResolver = useFormResolver<DataShape>(Player.parseQueryDto);

  const { register, handleSubmit, setValue, watch, formState } = useForm<DataShape>({
    defaultValues: {
      teamId: "",
      playerStatus: "",
      sortBy: PlayerSorting.CreationDate,
    },
    mode: "onChange",
    resolver: formResolver,
  });

  const teams = useGetAdminTeams(organization.id);

  const query = watch();

  const { errors: formErrors, submitCount } = formState;
  const hasSubmitted = submitCount > 0;
  const errors = hasSubmitted ? {} : formErrors;

  const handleFormSubmit = (data: unknown) => {
    const withoutEmptyFields = omitBy((value) => value === "", isObject(data) ? data : {});

    const parsed = Player.parseQueryDto(withoutEmptyFields);

    if (!isError(parsed)) {
      onSubmit({ organizationId: organization.id, query: parsed });
    }
  };

  return (
    <Box
      width={1}
      mt={1}
      component="form"
      onSubmit={handleSubmit(handleFormSubmit)}
      sx={{ width: "100%", bgcolor: "background.paper" }}
    >
      <Stack direction="row" alignItems="center" flexWrap="wrap" gap={2}>
        <Box sx={{ width: { xs: 1, md: 1 / 5 } }}>
          <DatePicker
            label={<FormLabel>{t("players.admin.filters.born-after")}</FormLabel>}
            renderInput={(params) => (
              <TextField
                fullWidth
                inputProps={{ ...register("dobAfter") }}
                {...params}
                helperText={errors.dobAfter?.message || ""}
                error={!!errors.dobAfter?.message}
                aria-invalid={!!errors.dobAfter?.message}
                value={CustomDate.toDisplayDate(query.dobAfter)}
                InputProps={{
                  endAdornment: (
                    <IconButton
                      aria-label={t("players.admin.filters.clear-date-after")}
                      onClick={(event) => {
                        event.stopPropagation();
                        setValue("dobAfter", undefined, {
                          shouldDirty: true,
                          shouldTouch: true,
                          shouldValidate: true,
                        });
                      }}
                    >
                      <ClearIcon />
                    </IconButton>
                  ),
                }}
              />
            )}
            value={query.dobAfter}
            openTo="year"
            onChange={(newValue) => {
              setValue("dobAfter", CustomDate.pastOrEmpty(newValue), {
                shouldDirty: true,
                shouldTouch: true,
                shouldValidate: true,
              });
            }}
          />
        </Box>

        <Box sx={{ width: { xs: 1, md: 1 / 5 } }}>
          <DatePicker
            label={<FormLabel>{t("players.admin.filters.born-before")}</FormLabel>}
            renderInput={(params) => (
              <TextField
                fullWidth
                inputProps={{ ...register("dobBefore") }}
                {...params}
                helperText={errors.dobBefore?.message || ""}
                error={!!errors.dobBefore?.message}
                aria-invalid={!!errors.dobBefore?.message}
                value={CustomDate.toDisplayDate(query.dobBefore)}
                InputProps={{
                  endAdornment: (
                    <IconButton
                      aria-label={t("players.admin.filters.clear-date-before")}
                      onClick={(event) => {
                        event.stopPropagation();
                        setValue("dobBefore", undefined, {
                          shouldDirty: true,
                          shouldTouch: true,
                          shouldValidate: true,
                        });
                      }}
                    >
                      <ClearIcon />
                    </IconButton>
                  ),
                }}
              />
            )}
            value={query.dobBefore}
            openTo="year"
            onChange={(newValue) => {
              setValue("dobBefore", CustomDate.pastOrEmpty(newValue), {
                shouldDirty: true,
                shouldTouch: true,
                shouldValidate: true,
              });
            }}
          />
        </Box>

        <FormControl sx={{ width: { xs: 1, md: 1 / 5 } }}>
          <InputLabel>{t("players.admin.filters.status.label")}</InputLabel>
          <Select
            fullWidth
            labelId="player-status-label"
            id="playerStatus"
            label={t("players.admin.filters.status.label")}
            name="playerStatus"
            inputProps={{ ...register("playerStatus") }}
            value={query.playerStatus}
          >
            <MenuItem value="" key="none">
              {t("players.admin.filters.status.none")}
            </MenuItem>
            {Object.values(PlayerTeamStatus).map((status) => (
              <MenuItem value={status} key={status}>
                {t(`players.status.${status}`)}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <FormControl sx={{ width: { xs: 1, md: 1 / 5 } }}>
          {teams.status === "success" && (
            <>
              <InputLabel>{t("players.admin.filters.team.label")}</InputLabel>
              <Select
                fullWidth
                labelId="team-label"
                id="team"
                label={t("players.admin.filters.team.label")}
                name="team"
                inputProps={{ ...register("teamId") }}
                value={query.teamId}
              >
                <MenuItem value="" key="none">
                  {t("players.admin.filters.team.none")}
                </MenuItem>
                {teams.data.map((team) => (
                  <MenuItem value={team.id} key={team.id}>
                    {startCase(team.name)}
                  </MenuItem>
                ))}
              </Select>
            </>
          )}
        </FormControl>

        <Box sx={{ width: { xs: 1, md: 1 / 5 } }}>
          <TextField
            fullWidth
            label={<FormLabel>{t("players.admin.filters.name")}</FormLabel>}
            name="searchName"
            inputProps={{ ...register("searchName") }}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    aria-label={t("players.admin.filters.clear-name")}
                    onClick={() => setValue("searchName", "")}
                    edge="end"
                  >
                    <ClearIcon />
                  </IconButton>
                </InputAdornment>
              ),
            }}
            helperText={errors.searchName?.message || ""}
            error={!!errors.searchName?.message}
            aria-invalid={!!errors.searchName?.message}
          />
        </Box>

        <FormControl sx={{ width: { xs: 1, md: 1 / 5 } }}>
          <InputLabel id="sortBy-label">{t("players.admin.filters.sort-by.label")}</InputLabel>
          <Select
            fullWidth
            labelId="sortBy-label"
            label={t("players.admin.filters.sort-by.label")}
            name="sortBy"
            inputProps={{ ...register("sortBy") }}
            value={query.sortBy}
          >
            {Object.values(PlayerSorting).map((sortingOption) => (
              <MenuItem value={sortingOption} key={sortingOption}>
                {t(sortingDisplayNames[sortingOption])}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <Stack justifyContent="center" alignItems="center">
          <LoadingButton
            type="submit"
            loading={isLoading}
            startIcon={<SearchIcon />}
            variant="contained"
            size="large"
            color="secondary"
          >
            {t("players.admin.filters.search")}
          </LoadingButton>
        </Stack>
      </Stack>
    </Box>
  );
};
