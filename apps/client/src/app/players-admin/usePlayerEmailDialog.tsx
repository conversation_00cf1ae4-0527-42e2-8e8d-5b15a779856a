import { useState } from "react";
import { useTranslation } from "react-i18next";

import { PopulatedPlayer } from "@mio/helpers";
import { Stack, SendIcon, SplitButton } from "@mio/ui";

import { useEmailModal } from "../shared/hooks/useSendEmail";

enum EmailTargets {
  players = "players",
  guardians = "guardians",
}

const options: Array<{
  label: "players.admin.email.targets.players" | "players.admin.email.targets.guardians";
  value: EmailTargets;
}> = [
  {
    label: "players.admin.email.targets.players",
    value: EmailTargets.players,
  },
  {
    label: "players.admin.email.targets.guardians",
    value: EmailTargets.guardians,
  },
];

const getTargetTranslationKey = (
  target: EmailTargets,
): "players.admin.email.targets.players" | "players.admin.email.targets.guardians" =>
  target === EmailTargets.players
    ? "players.admin.email.targets.players"
    : "players.admin.email.targets.guardians";

export const usePlayerEmailDialog = (selectedPlayers: PopulatedPlayer[]) => {
  const { t } = useTranslation();
  const { SendEmailUI, show: showEmailModal } = useEmailModal();
  const [target, setTarget] = useState(EmailTargets.players);

  const sendEmail = () => {
    const targets = selectedPlayers.map((player) =>
      target === EmailTargets.players ? player.email : player.guardian?.email,
    );

    showEmailModal(targets);
  };

  return {
    SendEmailUI: selectedPlayers.length ? (
      <>
        <SplitButton
          accessibleLabel={t("players.admin.email.send-to")}
          value={target}
          onChange={(newTarget) => setTarget(newTarget)}
          onActivate={sendEmail}
          options={options.map((opt) => ({ ...opt, label: t(opt.label) }))}
          displayValue={
            <Stack direction="row" alignItems="center" gap={1}>
              <SendIcon />
              {t("players.admin.email.send-to")} {t(getTargetTranslationKey(target))}
            </Stack>
          }
        />

        <SendEmailUI />
      </>
    ) : null,
  };
};
