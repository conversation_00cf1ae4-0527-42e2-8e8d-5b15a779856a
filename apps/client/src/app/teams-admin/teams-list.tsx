import { FC } from "react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";

import {
  Box,
  Stack,
  Button,
  Typography,
  PageError,
  PageLoader,
  AddIcon,
  useGetAdminTeams,
  useOrganization,
} from "@mio/ui";
import { ErrorMessages } from "@mio/helpers";
import { SidebarLayout } from "../layouts";
import { TeamItem } from "./team";

export const AdminTeamsList: FC = () => {
  const { t } = useTranslation();
  const organization = useOrganization();
  const teams = useGetAdminTeams(organization.id);

  if (teams.status === "loading") {
    return (
      <SidebarLayout>
        <PageLoader message={t("team.loading-teams")} />
      </SidebarLayout>
    );
  }

  if (teams.status === "error") {
    return (
      <SidebarLayout>
        <PageError
          message={
            teams.error.message === ErrorMessages.PermissionDenied
              ? t("common.permission-denied")
              : t("team.failed-to-load-teams")
          }
        />
      </SidebarLayout>
    );
  }

  if (teams.status === "success") {
    return (
      <SidebarLayout>
        <Box>
          <Stack
            component="header"
            flexDirection="row"
            justifyContent="space-between"
            mb={5}
            gap={2}
            flexWrap="wrap"
          >
            <Box>
              <Typography variant="h4" component="h1">
                {t("team.teams-count", { count: teams.data.length })}
              </Typography>
            </Box>

            <Box>
              <Button
                component={Link}
                to="create"
                variant="contained"
                color="secondary"
                size="large"
                startIcon={<AddIcon />}
              >
                {t("team.add-team")}
              </Button>
            </Box>
          </Stack>

          <Stack gap={4}>
            {teams.data.map((item) => (
              <TeamItem key={item.id} team={item} />
            ))}
          </Stack>
        </Box>
      </SidebarLayout>
    );
  }

  return null;
};
