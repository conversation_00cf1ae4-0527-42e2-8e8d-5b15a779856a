import { FC } from "react";
import { Navigate } from "react-router-dom";

import { useOrganization, useCreateTeam } from "@mio/ui";
import { Team, isError } from "@mio/helpers";
import { UpsertTeamForm } from "./upsertForm";
import { SidebarLayout } from "../layouts";

export const AddTeam: FC = () => {
  const createTeam = useCreateTeam();
  const organization = useOrganization();

  const handleSubmit = (data: unknown) => {
    const validated = Team.toCreateDto(data);

    if (!isError(validated)) {
      createTeam.mutate(validated);
    }
  };

  if (createTeam.isSuccess) {
    return <Navigate to={`/${organization.id}/teams-admin`} />;
  }

  return (
    <SidebarLayout>
      <UpsertTeamForm
        onSubmit={handleSubmit}
        loading={createTeam.isLoading}
        serverError={createTeam.error}
      />
    </SidebarLayout>
  );
};
