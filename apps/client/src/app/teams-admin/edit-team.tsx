import { FC } from "react";
import { Navigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { isError, Team } from "@mio/helpers";
import {
  PageError,
  PageLoader,
  useGetTeam,
  useOrganization,
  useOrganizationId,
  useRequiredTeamId,
  useUpdateTeam,
} from "@mio/ui";

import { UpsertTeamForm } from "./upsertForm";
import { SidebarLayout } from "../layouts";

export const EditTeam: FC = () => {
  const { t } = useTranslation();
  const teamId = useRequiredTeamId();
  const organization = useOrganization();
  const teamQuery = useGetTeam(organization.id, teamId);

  const updateTeam = useUpdateTeam();

  const organizationId = useOrganizationId();

  const handleSubmit = (data: unknown) => {
    const validated = Team.toUpdateDto(data);

    if (!isError(validated)) {
      updateTeam.mutate(validated);
    }
  };

  if (teamQuery.status === "loading") {
    return (
      <SidebarLayout>
        <PageLoader message={t("team.loading-team")} />
      </SidebarLayout>
    );
  }

  if (teamQuery.status === "error") {
    return (
      <SidebarLayout>
        <PageError message={t("team.failed-to-load-team")} />
      </SidebarLayout>
    );
  }

  if (updateTeam.isSuccess) {
    return <Navigate to={`/${organizationId}/teams-admin`} />;
  }

  if (teamQuery.status === "success") {
    return (
      <SidebarLayout>
        <UpsertTeamForm
          onSubmit={handleSubmit}
          team={teamQuery.data}
          loading={updateTeam.isLoading}
          serverError={updateTeam.error}
        />
      </SidebarLayout>
    );
  }

  return <Navigate to={`/${organizationId}/teams-admin`} />;
};
