import { FC } from "react";
import { Controller, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { NumericFormat } from "react-number-format";

import {
  Box,
  Typography,
  DatePicker,
  TextField,
  MenuItem,
  InputLabel,
  Select,
  Stack,
  LocalError,
  LoadingButton,
  useOrganizationId,
  useFormResolver,
  Checkbox,
  FormControlLabel,
} from "@mio/ui";

import {
  APIError,
  CreateTeamDto,
  CustomDate,
  CustomNumber,
  FeeInterval,
  FinancialIntegrationItemStatus,
  Primitive,
  StripeEntities,
  Team,
  TeamGenders,
} from "@mio/helpers";

import { BackButton } from "../shared";

type DataShape = Partial<Primitive<CreateTeamDto> & { id?: string }>;

type Props = {
  team?: Team;
  onSubmit: (data: DataShape) => void;
  loading?: boolean;
  serverError?: APIError | null;
};

export const UpsertTeamForm: FC<Props> = ({ team, onSubmit, loading, serverError }) => {
  const { t } = useTranslation();
  const organizationId = useOrganizationId();
  const formResolver = useFormResolver(Team.toUpsertDto);

  const { register, handleSubmit, setValue, watch, formState, control } = useForm<DataShape>({
    defaultValues: {
      id: team?.id,
      name: team?.name,
      ageDescription: team?.ageDescription,
      playersBornAfter:
        team?.playersBornAfter || CustomDate.toPastYear(CustomNumber.castToPositiveInteger(7)),
      playersBornBefore: team?.playersBornBefore || CustomDate.now(),
      gender: team?.gender || TeamGenders.M,
      slogan: team?.slogan || undefined,
      fee: team?.fee || undefined,
      currency: team?.currency || undefined,
      feeInterval: team?.feeInterval || undefined,
      financialIntegrationStatus: team?.financialIntegrationStatus || undefined,
      organizationId,
    },
    resolver: formResolver,
  });

  const playersBornAfter = watch("playersBornAfter");
  const playersBornBefore = watch("playersBornBefore");
  const gender = watch("gender");
  const financialIntegrationStatus = watch("financialIntegrationStatus");
  const feeInterval = watch("feeInterval");
  const fee = watch("fee");
  const currency = watch("currency");

  const { errors, submitCount } = formState;
  const hasSubmitted = submitCount > 0;
  const title = team ? t("team.edit-team-title", { name: team.name }) : t("team.add-new-team");

  return (
    <Box
      width={1}
      component="form"
      onSubmit={handleSubmit(onSubmit)}
      sx={{ width: "100%", bgcolor: "background.paper" }}
    >
      <Stack direction="row" alignItems="center" mb={5} gap={1} flexWrap="wrap">
        <Box>
          <BackButton path="../">{t("team.back-to-teams")}</BackButton>
        </Box>
        <Box component="legend" display="block" sx={{ flexGrow: 1, pr: 5 }}>
          <Typography variant="h5" component="h1" textAlign="center">
            {title}
          </Typography>
        </Box>
      </Stack>

      <Box>
        <TextField
          fullWidth
          aria-required
          id="name"
          label={t("team.name")}
          name="name"
          inputProps={{ ...register("name") }}
          helperText={(hasSubmitted && errors.name?.message) || ""}
          error={hasSubmitted && !!errors.name?.message}
          aria-invalid={hasSubmitted && !!errors.name?.message}
        />
      </Box>

      <Box mt={2}>
        <TextField
          fullWidth
          aria-required
          id="ageDescription"
          name="ageDescription"
          label={t("team.age-description")}
          placeholder={t("team.age-description-placeholder")}
          inputProps={{ ...register("ageDescription") }}
          helperText={(hasSubmitted && errors.ageDescription?.message) || ""}
          error={hasSubmitted && !!errors.ageDescription?.message}
          aria-invalid={hasSubmitted && !!errors.ageDescription?.message}
        />
      </Box>

      <Box mt={2}>
        <InputLabel aria-required>{t("team.gender")}</InputLabel>
        <Select
          fullWidth
          aria-required
          labelId="gender-label"
          id="gender"
          label={t("team.gender")}
          name="gender"
          inputProps={{ ...register("gender") }}
          value={gender}
        >
          <MenuItem value={TeamGenders.M}>{t("team.genders.male")}</MenuItem>
          <MenuItem value={TeamGenders.F}>{t("team.genders.female")}</MenuItem>
          <MenuItem value={TeamGenders.Mixed}>{t("team.genders.mixed")}</MenuItem>
        </Select>
      </Box>

      <Box mt={2}>
        <DatePicker
          label={t("team.players-born-after")}
          onChange={(newValue) => {
            setValue("playersBornAfter", CustomDate.pastOrEmpty(newValue), {
              shouldValidate: true,
              shouldDirty: true,
              shouldTouch: true,
            });
          }}
          renderInput={(params) => (
            <TextField
              fullWidth
              aria-required
              {...params}
              helperText={(hasSubmitted && errors.playersBornAfter?.message) || ""}
              error={hasSubmitted && !!errors.playersBornAfter?.message}
              aria-invalid={hasSubmitted && !!errors.playersBornAfter?.message}
            />
          )}
          value={playersBornAfter}
        />
      </Box>

      <Box mt={2}>
        <DatePicker
          label={t("team.players-born-before")}
          onChange={(newValue) => {
            setValue("playersBornBefore", CustomDate.validOrEmpty(newValue), {
              shouldValidate: true,
              shouldDirty: true,
              shouldTouch: true,
            });
          }}
          renderInput={(params) => (
            <TextField
              fullWidth
              {...params}
              helperText={(hasSubmitted && errors.playersBornBefore?.message) || ""}
              error={hasSubmitted && !!errors.playersBornBefore?.message}
              aria-invalid={hasSubmitted && !!errors.playersBornBefore?.message}
            />
          )}
          value={playersBornBefore}
        />
      </Box>

      <Box mt={2}>
        <TextField
          fullWidth
          id="slogan"
          name="slogan"
          label={t("team.slogan")}
          inputProps={{ ...register("slogan") }}
          multiline
          minRows={3}
          helperText={(hasSubmitted && errors.slogan?.message) || ""}
          error={hasSubmitted && !!errors.slogan?.message}
          aria-invalid={hasSubmitted && !!errors.slogan?.message}
        />
      </Box>

      <FormControlLabel
        control={
          <Controller
            name="financialIntegrationStatus"
            control={control}
            render={({ field: props }) => (
              <Checkbox
                {...props}
                checked={props.value === FinancialIntegrationItemStatus.Active}
                onChange={(e) => {
                  setValue(
                    "financialIntegrationStatus",
                    e.target.checked
                      ? FinancialIntegrationItemStatus.Active
                      : FinancialIntegrationItemStatus.Inactive,
                    {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    },
                  );

                  if (e.target.checked && !feeInterval) {
                    setValue("feeInterval", FeeInterval.Monthly, {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });

                    setValue("fee", StripeEntities.DefaultMonthlyFee, {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                  }

                  if (!e.target.checked) {
                    setValue("feeInterval", undefined, {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });

                    setValue("fee", undefined, {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                  }
                }}
              />
            )}
          />
        }
        label={t("team.sync-with-financial-system")}
      />

      {financialIntegrationStatus === FinancialIntegrationItemStatus.Active && (
        <Box mt={2}>
          <Stack direction="row" spacing={2} alignItems="flex-start">
            <Box width={160}>
              <InputLabel aria-required>{t("team.fee")}</InputLabel>
              <NumericFormat
                fullWidth
                size="small"
                id="fee"
                name="fee"
                customInput={TextField}
                fixedDecimalScale
                value={fee}
                onValueChange={(values) => {
                  setValue("fee", values.floatValue, {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                }}
                decimalScale={2}
                decimalSeparator="."
                placeholder="0.00"
                thousandSeparator=","
                helperText={(hasSubmitted && errors.fee?.message) || ""}
                error={hasSubmitted && !!errors.fee?.message}
                aria-invalid={hasSubmitted && !!errors.fee?.message}
              />
            </Box>
            <Box width={140}>
              <InputLabel aria-required>{t("team.currency")}</InputLabel>
              <Select
                fullWidth
                size="small"
                id="currency"
                name="currency"
                inputProps={{ ...register("currency") }}
                value={currency}
              >
                <MenuItem value={StripeEntities.CurrencyLowerCase.GBP}>
                  {StripeEntities.CurrencySymbol.GBP} GBP
                </MenuItem>
                <MenuItem value={StripeEntities.CurrencyLowerCase.EUR}>
                  {StripeEntities.CurrencySymbol.EUR} EUR
                </MenuItem>
              </Select>
            </Box>
          </Stack>
        </Box>
      )}

      {financialIntegrationStatus === FinancialIntegrationItemStatus.Active && (
        <Box mt={2}>
          <InputLabel aria-required id="feeInterval-label">
            {t("team.fee-interval")}
          </InputLabel>
          <Select
            fullWidth
            aria-required
            labelId="feeInterval-label"
            id="feeInterval"
            label={t("team.fee-interval")}
            name="feeInterval"
            inputProps={{ ...register("feeInterval") }}
            value={feeInterval}
          >
            <MenuItem value={FeeInterval.Monthly}>{t("team.fee-intervals.monthly")}</MenuItem>
            <MenuItem value={FeeInterval.Yearly}>{t("team.fee-intervals.yearly")}</MenuItem>
          </Select>
        </Box>
      )}

      <Box mt={5} mb={2} sx={{ display: "flex", justifyContent: "center" }}>
        <LoadingButton
          size="large"
          type="submit"
          loading={loading}
          variant="contained"
          color="secondary"
        >
          {t("common.save")}
        </LoadingButton>
      </Box>

      {serverError && <LocalError message={t("common.something-went-wrong")} />}
    </Box>
  );
};
