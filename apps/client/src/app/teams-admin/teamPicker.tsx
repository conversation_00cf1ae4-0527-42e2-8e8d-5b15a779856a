import { FC, useState, useEffect, ChangeEvent } from "react";
import { useTranslation } from "react-i18next";
import { isEqual } from "lodash/fp";

import { TeamId } from "@mio/helpers";
import {
  FormControl,
  LocalError,
  LocalLoader,
  Checkbox,
  FormControlLabel,
  FormGroup,
  useGetAdminTeams,
  useOrganization,
} from "@mio/ui";

type Props = {
  selectedTeams: TeamId[];
  onChange: (newValue: TeamId[]) => void;
};

export const TeamPicker: FC<Props> = ({ selectedTeams = [], onChange }) => {
  const { t } = useTranslation();
  const [value, setValue] = useState(selectedTeams);
  const organization = useOrganization();
  const teams = useGetAdminTeams(organization.id);

  useEffect(() => {
    if (!isEqual(selectedTeams, value)) {
      setValue(selectedTeams);
    }
  }, [selectedTeams, value]);

  if (teams.isLoading) {
    return <LocalLoader message={t("team.loading-teams")} />;
  }

  if (teams.isError) {
    return <LocalError message={t("team.failed-to-load-teams")} />;
  }

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const targetTeam = event.target.value as TeamId;
    const checked = event.target.checked;

    const currentValue = new Set(value);

    if (checked) {
      currentValue.add(targetTeam);
    } else {
      currentValue.delete(targetTeam);
    }

    const newValue = Array.from(currentValue);
    setValue(newValue);
    onChange(newValue);
  };

  return (
    <FormControl component="fieldset" variant="standard">
      <FormGroup>
        {teams.data.map((team) => (
          <FormControlLabel
            key={team.id}
            control={
              <Checkbox
                checked={value.includes(team.id)}
                onChange={handleChange}
                name={team.name}
                value={team.id}
              />
            }
            label={team.name}
          />
        ))}
      </FormGroup>
    </FormControl>
  );
};
