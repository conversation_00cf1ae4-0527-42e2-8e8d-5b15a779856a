import { FC } from "react";
import { <PERSON> } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { IconButton, EditIcon, DeleteIcon, Stack, Tooltip, useDeleteTeam } from "@mio/ui";
import { Team } from "@mio/helpers";
import { TeamCard } from "../shared";
import { useConfirmationModal } from "../shared/hooks/useConfirmation";

type Props = {
  team: Team;
};

export const TeamItem: FC<Props> = ({ team }) => {
  const { t } = useTranslation();
  const deleteTeam = useDeleteTeam();

  const handleDelete = () => {
    deleteTeam.mutate(team.id);
  };

  const { ConfirmationModalUI, show: showDeleteConfirmation } = useConfirmationModal({
    isError: deleteTeam.isError,
    isSuccess: deleteTeam.isSuccess,
    isLoading: deleteTeam.isLoading,
    title: t("team.delete-team-title", { name: team.name }),
    onConfirm: handleDelete,
  });

  return (
    <TeamCard team={team}>
      <Stack direction="row" alignItems="center">
        <Tooltip title={t("team.edit-team")}>
          <IconButton
            aria-label={t("team.edit-team")}
            component={Link}
            to={`edit/${team.id}`}
            color="inherit"
          >
            <EditIcon color="primary" />
          </IconButton>
        </Tooltip>

        <Tooltip title={t("team.delete-team")}>
          <IconButton
            sx={{ marginTop: "-3px" }}
            aria-label={t("team.delete-team")}
            color="primary"
            onClick={() => showDeleteConfirmation(t("team.delete-team-confirmation"))}
          >
            <DeleteIcon />
          </IconButton>
        </Tooltip>

        <ConfirmationModalUI />
      </Stack>
    </TeamCard>
  );
};
