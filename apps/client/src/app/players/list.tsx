import { FC, useState } from "react";
import { useTranslation } from "react-i18next";

import {
  <PERSON><PERSON>rror,
  PageLoader,
  Stack,
  Box,
  Typography,
  Alert,
  playersState,
  useOrganization,
  useRequiredTeam,
} from "@mio/ui";
import { SidebarLayout } from "../layouts";
import { Player } from "@mio/helpers";
import { PlayerCard } from "./playerCard";
import { usePlayerDetails, usePlayerDocuments } from "../shared";
import { PlayerFilters } from "./filters";

export const PlayersList: FC = () => {
  const { t } = useTranslation();
  const organization = useOrganization();
  const team = useRequiredTeam();

  const {
    query: teamPlayersQuery,
    removeFromTeam,
    changeStatus,
    search,
  } = playersState.useTeamPlayers(team.id, organization.id);

  const { PlayerDetailsUI, show } = usePlayerDetails({ removeFromTeam });
  const { DocumentsUI, show: showDocuments } = usePlayerDocuments();

  const [selectedPlayers, setSelectedPlayers] = useState<Player[]>([]);

  const handlePlayerSelection = (player: Player) => {
    const exists = selectedPlayers.find((playah) => playah.id === player.id);
    if (exists) {
      setSelectedPlayers(selectedPlayers.filter((playah) => playah.id !== player.id));
      return;
    }

    setSelectedPlayers([...selectedPlayers, player]);
  };

  return (
    <SidebarLayout>
      <Box mb={2}>
        <Typography component="h1" variant="h5">
          {team.name}
        </Typography>
      </Box>
      <PlayerFilters
        isLoading={teamPlayersQuery.isLoading}
        onSubmit={(data) => {
          search({ orgId: data.organizationId, query: data.query });
        }}
      />

      {teamPlayersQuery.isLoading && <PageLoader message={t("players.list.loading")} />}

      {teamPlayersQuery.isError && <PageError message={t("players.list.load-error")} />}

      {teamPlayersQuery.isSuccess && !teamPlayersQuery.data.length && (
        <Box mt={2}>
          <Alert severity="warning">{t("players.list.no-players", { name: team.name })}</Alert>
        </Box>
      )}

      {teamPlayersQuery.isSuccess && !!teamPlayersQuery.data.length && (
        <Box mt={3}>
          <Stack gap={2}>
            <Box component="header" mb={3}>
              <Stack direction="row" alignItems="center">
                {/* <Checkbox onChange={(e) => toggleAllPlayers(e.target.checked)} /> */}
                <Typography>
                  {t("players.list.showing-count", { count: teamPlayersQuery.data.length })}{" "}
                  {t("players.list.filters-note")}
                </Typography>
              </Stack>
            </Box>
            {teamPlayersQuery.data.map((player) => (
              <Box key={player.id}>
                <PlayerCard
                  key={player.id}
                  player={player}
                  team={team}
                  showDetails={() => show(player)}
                  showDocuments={() => showDocuments(player)}
                  onSelect={handlePlayerSelection}
                  selectedPlayers={selectedPlayers}
                  refetch={teamPlayersQuery.refetch}
                  changeStatus={changeStatus}
                />
              </Box>
            ))}
          </Stack>

          <PlayerDetailsUI />

          {DocumentsUI}
        </Box>
      )}
    </SidebarLayout>
  );
};
