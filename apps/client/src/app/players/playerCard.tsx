import { FC } from "react";
import { useTranslation } from "react-i18next";

import { <PERSON>, Card, Stack, Typography, Button, Chip, playersState, Divider } from "@mio/ui";
import { AssignedPlayerTeamProfile, CustomDate, Player, PopulatedPlayer, Team } from "@mio/helpers";
import { ChangeStatus } from "./changeStatus";
import { PlayerAgeAlert } from "../shared/components/playerAgeAlert";

type Props = {
  player: PopulatedPlayer;
  team: Team;
  showDetails: () => void;
  showDocuments: () => void;
  changeStatus: playersState.ChangeStatusHandler;
  onSelect: (player: Player) => void;
  selectedPlayers: Player[];
  refetch: () => void;
};

const gridColumns = "1.5fr 1fr 1fr";

const toFullDobLabel = (player: Player, t: (key: string, options?: any) => string) =>
  `${t("players.card.born", { date: CustomDate.toDisplayDate(player.dob) })} ${t(
    "players.card.years-old",
    { age: CustomDate.toDisplayAge(player.dob) },
  )}`;

export const PlayerCard: FC<Props> = ({
  player,
  team,
  showDetails,
  showDocuments,
  changeStatus,
  onSelect,
  selectedPlayers,
}) => {
  const { t } = useTranslation();
  const totalDocuments = [
    player.documents?.photo_id,
    ...(player.documents?.image_document_ids ?? []),
  ].filter((value) => !!value).length;

  return (
    <Card sx={{ p: 2 }}>
      <Stack gap={3}>
        <Box
          sx={{
            display: {
              md: "grid",
              xs: "flex",
            },
            flexWrap: "wrap",
            gap: {
              xs: 2,
              sm: 4,
            },
            gridTemplateColumns: {
              md: gridColumns,
            },
            alignItems: "center",
          }}
        >
          <Stack>
            {/* <Checkbox
              onChange={(e) => onSelect(player)}
              checked={selectedPlayers.some((playah) => playah.id === player.id)}
            /> */}

            <Typography>
              {player.firstName} {player.lastName}
            </Typography>
            <Stack flexDirection="row" alignItems="center">
              <Typography variant="body2" color="gray">
                {toFullDobLabel(player, t as (key: string, options?: any) => string)}
              </Typography>
              <PlayerAgeAlert player={player} team={team} condensed />
            </Stack>
          </Stack>

          <Box>
            <Chip label={t(`players.status.${player.profiles[0].status}`)} />
          </Box>

          <Box ml="auto">
            <ChangeStatus
              changeStatus={changeStatus}
              player={player}
              playerProfile={player.profiles[0] as AssignedPlayerTeamProfile}
            />
          </Box>
        </Box>

        <Stack direction="row" gap={2}>
          <Button variant="text" onClick={showDetails}>
            {t("players.card.details")}
          </Button>

          {totalDocuments > 0 ? (
            <>
              <Divider orientation="vertical" flexItem />
              <Button variant="text" onClick={showDocuments}>
                {t("players.card.documents-count", { count: totalDocuments })}
              </Button>
            </>
          ) : null}
        </Stack>
      </Stack>
    </Card>
  );
};
