{"name": "client-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/client-e2e/src", "projectType": "application", "tags": [], "implicitDependencies": ["client"], "targets": {"type-check": {"executor": "nx:run-commands", "options": {"command": "npx tsc -b ./apps/client-e2e"}}, "e2e": {"executor": "@nx/cypress:cypress", "options": {"cypressConfig": "apps/client-e2e/cypress.json", "devServerTarget": "client:serve"}, "configurations": {"production": {"devServerTarget": "client:serve:production"}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}}