{"name": "api", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api/src", "projectType": "application", "tags": [], "targets": {"type-check": {"executor": "nx:run-commands", "options": {"command": "npx tsc --noEmit -p ./apps/api/tsconfig.app.json"}}, "build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/api", "main": "apps/api/src/main.ts", "tsConfig": "apps/api/tsconfig.app.json", "assets": ["apps/api/src/assets"], "target": "node", "compiler": "swc", "webpackConfig": "apps/api/webpack.config.js"}, "configurations": {"production": {"optimization": true, "sourceMap": true, "fileReplacements": [{"replace": "apps/api/src/environments/environment.ts", "with": "apps/api/src/environments/environment.prod.ts"}], "generatePackageJson": true}}}, "serve": {"executor": "@nx/js:node", "options": {"buildTarget": "api:build", "inspect": true, "port": 9229}}, "serve-prod": {"executor": "@nx/js:node", "options": {"buildTarget": "api:build:production", "inspect": true, "port": 9229}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/apps/api"], "options": {"jestConfig": "apps/api/jest.config.ts"}}, "test:watch": {"executor": "@nx/jest:jest", "options": {"jestConfig": "apps/api/jest.config.ts", "watch": true}}, "docker": {"executor": "nx:run-commands", "options": {"commands": [{"command": "nx run api:build --prod", "description": "Build for production", "forwardAllArgs": false}, {"command": "docker build -f ./apps/api/Dockerfile --build-arg RELEASE={args.release} --build-arg NODE_VERSION=$(cat .nvmrc | tr -cd [:digit:].) --build-arg APP_DIR=apps/api --build-arg TSLIB_VER=$(jq -r '.devDependencies.tslib' package.json) .", "description": "Create docker image by passing some build args. `args.release` is coming from the CI and it's a reference to the commit being deployed. NODE_VERSION is read from nvm.rc. APP_DIR is the relative path to the source and built code, needed in the Dockerfile to copy files. TSLIB is read from package.json.", "forwardAllArgs": false}], "parallel": false}}}}