const { composePlugins, withNx } = require("@nx/webpack");

// Nx plugins for webpack.
module.exports = composePlugins(withNx(), (config) => {
  // Update the config to work better with SWC
  if (config.module && config.module.rules) {
    // Find and modify the TypeScript rule to use swc-loader instead
    const tsRule = config.module.rules.find(
      (rule) => rule.test && rule.test.toString().includes("tsx?$"),
    );

    if (tsRule && tsRule.use) {
      // If it's an array of loaders
      if (Array.isArray(tsRule.use)) {
        tsRule.use = tsRule.use.map((loader) => {
          if (typeof loader === "string" && loader.includes("ts-loader")) {
            return "swc-loader";
          }
          return loader;
        });
      }
      // If it's a single loader as string
      else if (typeof tsRule.use === "string" && tsRule.use.includes("ts-loader")) {
        tsRule.use = "swc-loader";
      }
      // If it's a loader with options
      else if (
        typeof tsRule.use === "object" &&
        tsRule.use.loader &&
        tsRule.use.loader.includes("ts-loader")
      ) {
        tsRule.use.loader = "swc-loader";
      }
    }
  }

  return config;
});
