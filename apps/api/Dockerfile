ARG NODE_VERSION

FROM node:${NODE_VERSION}-alpine

# APP_DIR is needed in order to copy files from the source and build directories.
ARG APP_DIR
# TSLIB_VER is needed to install the `tslib` library with the exact version specified in the
# package.json file.
ARG TSLIB_VER
# RELEASE is the SHA of the commit used to build the image.
ARG RELEASE
ARG PORT=3000

# Make RELEASE env variable available to the app runtime so that it can be used for logging and
# other purposes.
ENV RELEASE $RELEASE
# Make PORT env variable available to the app runtime so that it can be used to start the server.
ENV PORT $PORT

# We use APP_DIR to also set the container working directory. Doesn't have to be the same but it
# spares us from creating another ARG.
WORKDIR /${APP_DIR}

# Copy package.json and install dependencies - this is done before copying the project files to
# better leverage Docker cache.
COPY ./package-lock.json .
COPY ./dist/${APP_DIR}/package.json .

RUN npm ci --only=production

# `tslib` needs to be added manually because Nx doesn't add it to package.json generated when
# building the project.
# TSLIB_VER is passed as an argument to the Dockerfile so that the actual version can be read from
# package.json before calling `docker build`. This way we can be sure that the Docker image will
# have the exact same version installed as the project running locally.
#
# More details here: https://github.com/nrwl/nx/issues/2625
RUN npm i tslib@${TSLIB_VER}

# Copy the actual project files. Note that the project is built by Nx before docker build the image
# we only need to copy whatever is already inside the `dist/${APP_DIR}` folder.
COPY ./dist/${APP_DIR} .

EXPOSE $APP_PORT
ENTRYPOINT ["node", "main"]
