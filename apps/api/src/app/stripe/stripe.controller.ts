import { Controller, Get, Param, UseGuards } from "@nestjs/common";

import { apiUrls, OrganizationId, PermissionsModule, UrlParams } from "@mio/helpers";

import { PermissionsGuard, RequiresPermissions } from "../permissions";
import { IDParamPipe } from "../shared";

import { JwtAuthGuard } from "../auth/jwt-auth.guard";
import { StripeService } from "./stripe.service";
import { StripePaymentService } from "./payment/payment.service";

@Controller()
export class StripeController {
  constructor(
    private stripeService: StripeService,
    private stripePaymentService: StripePaymentService,
  ) {}

  @Get(apiUrls.stripeSubscriptions)
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  //TODO: Add the appropriate new permission; handle errors
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageOrganization])
  async findSubscriptions(@Param(UrlParams.OrganizationId, IDParamPipe) orgId: OrganizationId) {
    return this.stripeService.findSubscriptions(orgId);
  }
}
