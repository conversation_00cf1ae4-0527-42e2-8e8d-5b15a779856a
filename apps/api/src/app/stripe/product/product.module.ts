import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { EncryptionModule } from "../../encryption/encryption.module";
import { FinancialIntegrationModule } from "../../financial-integration/financial-integration.module";
import { PermissionsModule } from "../../permissions";
import { StriperProductService as StripeProductService } from "./product.service";
import { ProductListenerService } from "./product-listener.service";
import { TeamModule } from "../../team/team.module";

@Module({
  imports: [EncryptionModule, FinancialIntegrationModule, PermissionsModule, TeamModule],
  providers: [StripeProductService, ProductListenerService],
  controllers: [],
  exports: [],
})
export class StripeProductModule {}
