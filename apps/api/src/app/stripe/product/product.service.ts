import { Injectable } from "@nestjs/common";
import { isNil } from "lodash/fp";
import Stripe from "stripe";

import {
  DomainError,
  ErrorMessages,
  FinancialIntegrationType,
  isError,
  OrganizationId,
  UnexpectedError,
  StripeEntities,
  TeamId,
  FinancialIntegrationItemStatus,
} from "@mio/helpers";

import { FinancialIntegrationService } from "../../financial-integration/financial-integration.service";
import { EncryptionService } from "../../encryption/encryption.service";
import { TeamService } from "../../team/team.service";

@Injectable()
export class StriperProductService {
  constructor(
    private teamService: TeamService,
    private financialIntegrationService: FinancialIntegrationService,
    private encryptionService: EncryptionService,
  ) {}

  extractStripePriceId(product: Stripe.Product) {
    if (typeof product.default_price === "string") {
      return product.default_price;
    }

    if (product.default_price && typeof product.default_price === "object") {
      return product.default_price.id;
    }

    return undefined;
  }

  async findProducts(organizationId: OrganizationId) {
    const financialIntegration = await this.financialIntegrationService.getByOrganizationIdAndType(
      organizationId,
      FinancialIntegrationType.Stripe,
    );

    if (isError(financialIntegration)) {
      financialIntegration.addContext({
        service: StriperProductService.name,
        method: this.findProducts.name,
        operation: this.financialIntegrationService.getByOrganizationIdAndType.name,
        organizationId,
      });

      return financialIntegration;
    }

    if (isNil(financialIntegration)) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    const STRIPE_ACCESS_TOKEN = await this.encryptionService.decrypt(
      financialIntegration.keyEncrypted,
      financialIntegration.iv,
    );

    if (isError(STRIPE_ACCESS_TOKEN)) {
      STRIPE_ACCESS_TOKEN.addContext({
        service: StriperProductService.name,
        method: this.findProducts.name,
        operation: this.encryptionService.decrypt.name,
        organizationId,
      });

      return STRIPE_ACCESS_TOKEN;
    }

    const stripe = new Stripe(STRIPE_ACCESS_TOKEN, {
      apiVersion: "2022-11-15",
    });

    try {
      const productsResult = await stripe.products.list({});

      if (productsResult && productsResult.data) {
        // return productsResult.data;
        const mbProducts = StripeEntities.Product.Entity.toEntities(productsResult.data);

        if (isError(mbProducts)) {
          mbProducts.addContext({
            service: StriperProductService.name,
            method: this.findProducts.name,
            operation: StripeEntities.Product.Entity.toEntities,
            organizationId,
          });

          return mbProducts;
        }

        return mbProducts;
      }

      return [];
    } catch (err) {
      return new UnexpectedError(err);
    }
  }

  async createProduct({
    organizationId,
    teamId,
    currency,
    price,
  }: {
    organizationId: OrganizationId;
    teamId: TeamId;
    currency: StripeEntities.CurrencyLowerCase;
    price: number;
  }) {
    const team = await this.teamService.findById(teamId);

    if (isError(team)) {
      team.addContext({
        service: StriperProductService.name,
        method: this.createProduct.name,
        operation: this.teamService.findById.name,
        organizationId,
        teamId,
      });

      return team;
    }

    if (isNil(team)) {
      return new DomainError(ErrorMessages.EntityNotFound, { message: "Team not found" });
    }

    const product = StripeEntities.Product.Entity.createDto(
      team,
      currency,
      (price * 100).toString(),
    );

    if (isError(product)) {
      product.addContext({
        service: StriperProductService.name,
        method: this.createProduct.name,
        operation: StripeEntities.Product.Entity.createDto.name,
        organizationId,
        team,
        currency,
        price,
      });

      return product;
    }

    const financialIntegration = await this.financialIntegrationService.getByOrganizationIdAndType(
      organizationId,
      FinancialIntegrationType.Stripe,
    );

    if (isError(financialIntegration)) {
      financialIntegration.addContext({
        service: StriperProductService.name,
        method: this.createProduct.name,
        operation: this.financialIntegrationService.getByOrganizationIdAndType.name,
        organizationId,
      });

      return financialIntegration;
    }

    if (isNil(financialIntegration)) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    const STRIPE_ACCESS_TOKEN = await this.encryptionService.decrypt(
      financialIntegration.keyEncrypted,
      financialIntegration.iv,
    );

    if (isError(STRIPE_ACCESS_TOKEN)) {
      STRIPE_ACCESS_TOKEN.addContext({
        service: StriperProductService.name,
        method: this.createProduct.name,
        operation: this.encryptionService.decrypt.name,
        organizationId,
      });

      return STRIPE_ACCESS_TOKEN;
    }

    const stripe = new Stripe(STRIPE_ACCESS_TOKEN, {
      apiVersion: "2022-11-15",
    });

    try {
      const productResult = await stripe.products.create(product);

      if (!productResult || !productResult.id) {
        return new UnexpectedError("Stripe product not created");
      }

      const updateTeamIntegrated = await this.teamService.updateTeam(team.id, {
        ...team,
        stripeProductId: productResult.id,
        stripePriceId: this.extractStripePriceId(productResult),
      });

      if (isError(updateTeamIntegrated)) {
        updateTeamIntegrated.addContext({
          service: StriperProductService.name,
          method: this.createProduct.name,
          operation: this.teamService.updateTeam.name,
          organizationId,
          team,
          productStripeId: productResult.id,
        });

        return updateTeamIntegrated;
      }

      return productResult;
    } catch (err) {
      return new UnexpectedError(err);
    }
  }

  async updateProduct({
    organizationId,
    teamId,
    currency,
    price,
  }: {
    organizationId: OrganizationId;
    teamId: TeamId;
    currency: StripeEntities.CurrencyLowerCase;
    price: number;
  }) {
    const team = await this.teamService.findById(teamId);

    if (isError(team)) {
      team.addContext({
        service: StriperProductService.name,
        method: this.createProduct.name,
        operation: this.teamService.findById.name,
        organizationId,
        teamId,
      });

      return team;
    }

    if (isNil(team)) {
      return new DomainError(ErrorMessages.EntityNotFound, { message: "Team not found" });
    }

    if (
      team.financialIntegrationStatus === FinancialIntegrationItemStatus.Active &&
      !team.stripeProductId &&
      !team.stripePriceId
    ) {
      return this.createProduct({ organizationId, teamId, currency, price });
    }

    const financialIntegration = await this.financialIntegrationService.getByOrganizationIdAndType(
      organizationId,
      FinancialIntegrationType.Stripe,
    );

    if (isError(financialIntegration)) {
      financialIntegration.addContext({
        service: StriperProductService.name,
        method: this.createProduct.name,
        operation: this.financialIntegrationService.getByOrganizationIdAndType.name,
        organizationId,
      });

      return financialIntegration;
    }

    if (isNil(financialIntegration)) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    const STRIPE_ACCESS_TOKEN = await this.encryptionService.decrypt(
      financialIntegration.keyEncrypted,
      financialIntegration.iv,
    );

    if (isError(STRIPE_ACCESS_TOKEN)) {
      STRIPE_ACCESS_TOKEN.addContext({
        service: StriperProductService.name,
        method: this.createProduct.name,
        operation: this.encryptionService.decrypt.name,
        organizationId,
      });

      return STRIPE_ACCESS_TOKEN;
    }

    const stripe = new Stripe(STRIPE_ACCESS_TOKEN, {
      apiVersion: "2022-11-15",
    });

    try {
      const integratedTeam = "stripeProductId" in team ? team : undefined;
      if (!integratedTeam || !integratedTeam.stripeProductId) {
        return new DomainError(ErrorMessages.EntityNotFound, {
          message: "Team not integrated with Stripe",
        });
      }

      const createNewPrice = await stripe.prices.create({
        product: integratedTeam.stripeProductId,
        currency,
        unit_amount_decimal: (price * 100).toString(),
        ...(team.feeInterval && {
          recurring: {
            interval: team.feeInterval,
            interval_count: team.feeInterval && 1,
          },
        }),
      });

      if (!createNewPrice || !createNewPrice.id) {
        return new UnexpectedError("Stripe price not created");
      }

      const productResult = await stripe.products.update(integratedTeam.stripeProductId, {
        default_price: createNewPrice.id,
      });

      if (!productResult || !productResult.id) {
        const mbProduct = StripeEntities.Product.Entity.toEntity(productResult);

        if (isError(mbProduct)) {
          return new UnexpectedError("Stripe product not updated");
        }
      }

      return productResult;
    } catch (err) {
      return new UnexpectedError(err);
    }
  }
}
