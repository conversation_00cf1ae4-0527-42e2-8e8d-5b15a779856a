import { FinancialIntegrationService } from "./../../financial-integration/financial-integration.service";
import { Injectable } from "@nestjs/common";
import { OnEvent } from "@nestjs/event-emitter";
import { StriperProductService as StripeProductService } from "./product.service";
import { Events } from "../../shared";
import { DomainError, ErrorMessages, StripeEntities } from "@mio/helpers";

@Injectable()
export class ProductListenerService {
  constructor(
    private productService: StripeProductService,
    private financialIntegrationService: FinancialIntegrationService,
  ) {}

  @OnEvent(Events.Type.TeamNew)
  async createProduct(payload: Events.TeamUpsert) {
    const isStripe = await this.financialIntegrationService.isStripe(payload.organizationId);
    if (!isStripe) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        message: "No Financial Integration of this type found",
      });
    }

    return this.productService.createProduct({
      teamId: payload.teamId,
      organizationId: payload.organizationId,
      price: payload.fee,
      currency: StripeEntities.CurrencyLowerCase.GBP,
    });
  }

  @OnEvent(Events.Type.TeamUpdate)
  async updateProduct(payload: Events.TeamUpsert) {
    const isStripe = await this.financialIntegrationService.isStripe(payload.organizationId);
    if (!isStripe) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        message: "No Financial Integration of this type found",
      });
    }

    return this.productService.updateProduct({
      teamId: payload.teamId,
      organizationId: payload.organizationId,
      price: payload.fee,
      currency: StripeEntities.CurrencyLowerCase.GBP,
    });
  }
}
