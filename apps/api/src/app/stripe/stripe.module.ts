import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { EncryptionModule } from "../encryption/encryption.module";
import { FinancialIntegrationModule } from "../financial-integration/financial-integration.module";
import { PermissionsModule } from "../permissions";

import { <PERSON>eController } from "./stripe.controller";
import { StripeService } from "./stripe.service";
import { StripeProductModule } from "./product/product.module";
import { StripePaymentService } from "./payment/payment.service";
import { TeamModule } from "../team/team.module";

@Module({
  imports: [
    EncryptionModule,
    FinancialIntegrationModule,
    PermissionsModule,
    StripeProductModule,
    TeamModule,
  ],
  providers: [StripeService, StripePaymentService],
  controllers: [StripeController],
  exports: [],
})
export class StripeModule {}
