import { Modu<PERSON> } from "@nestjs/common";
import { EncryptionModule } from "../../encryption/encryption.module";
import { FinancialIntegrationModule } from "../../financial-integration/financial-integration.module";
import { PermissionsModule } from "../../permissions";
import { StripePaymentService } from "./payment.service";

import { TeamModule } from "../../team/team.module";

@Module({
  imports: [EncryptionModule, FinancialIntegrationModule, PermissionsModule, TeamModule],
  providers: [StripePaymentService],
  controllers: [],
  exports: [StripePaymentService],
})
export class StripePaymentModule {}
