import { Injectable } from "@nestjs/common";
import { isNil } from "lodash/fp";
import Stripe from "stripe";

import {
  DomainError,
  ErrorMessages,
  FinancialIntegrationType,
  isError,
  UnexpectedError,
  PaymentRequest,
} from "@mio/helpers";

import { FinancialIntegrationService } from "../../financial-integration/financial-integration.service";
import { EncryptionService } from "../../encryption/encryption.service";

@Injectable()
export class StripePaymentService {
  constructor(
    private financialIntegrationService: FinancialIntegrationService,
    private encryptionService: EncryptionService,
  ) {}

  async createCheckoutSession(paymentRequest: PaymentRequest.Entity) {
    const financialIntegration = await this.financialIntegrationService.getByOrganizationIdAndType(
      paymentRequest.organizationId,
      FinancialIntegrationType.Stripe,
    );

    if (isError(financialIntegration)) {
      financialIntegration.addContext({
        service: StripePaymentService.name,
        method: this.createCheckoutSession.name,
        operation: this.financialIntegrationService.getByOrganizationIdAndType.name,
        organizationId: paymentRequest.organizationId,
      });

      return financialIntegration;
    }

    if (isNil(financialIntegration)) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    const STRIPE_ACCESS_TOKEN = await this.encryptionService.decrypt(
      financialIntegration.keyEncrypted,
      financialIntegration.iv,
    );

    if (isError(STRIPE_ACCESS_TOKEN)) {
      STRIPE_ACCESS_TOKEN.addContext({
        service: StripePaymentService.name,
        method: this.createCheckoutSession.name,
        operation: this.encryptionService.decrypt.name,
        organizationId: paymentRequest.organizationId,
      });

      return STRIPE_ACCESS_TOKEN;
    }

    const stripe = new Stripe(STRIPE_ACCESS_TOKEN, {
      apiVersion: "2022-11-15",
    });

    try {
      const checkoutSession = await stripe.checkout.sessions.create({
        success_url: "http://localhost:4002/players/003841ee-bcf7-494c-9cfc-637e9254032e/dashboard",
        cancel_url: "http://localhost:4002/players/003841ee-bcf7-494c-9cfc-637e9254032e/profile",
        line_items: [
          {
            price: paymentRequest.stripePriceId,
            quantity: 1,
          },
        ],
        mode: "subscription",
      });

      if (!checkoutSession || !checkoutSession.id) {
        return new UnexpectedError("Failed to create stripe checkout session");
      }

      return checkoutSession;
    } catch (err) {
      return new UnexpectedError(err);
    }
  }

  async updatePaymentRequestStatus(initiatedPayment: PaymentRequest.Entity) {
    if (!initiatedPayment.stripeCheckoutSessionId) {
      return new UnexpectedError("No stripe checkout session id found");
    }

    const financialIntegration = await this.financialIntegrationService.getByOrganizationIdAndType(
      initiatedPayment.organizationId,
      FinancialIntegrationType.Stripe,
    );

    if (isError(financialIntegration)) {
      financialIntegration.addContext({
        service: StripePaymentService.name,
        method: this.createCheckoutSession.name,
        operation: this.financialIntegrationService.getByOrganizationIdAndType.name,
        organizationId: initiatedPayment.organizationId,
      });

      return financialIntegration;
    }

    if (isNil(financialIntegration)) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    const STRIPE_ACCESS_TOKEN = await this.encryptionService.decrypt(
      financialIntegration.keyEncrypted,
      financialIntegration.iv,
    );

    if (isError(STRIPE_ACCESS_TOKEN)) {
      STRIPE_ACCESS_TOKEN.addContext({
        service: StripePaymentService.name,
        method: this.createCheckoutSession.name,
        operation: this.encryptionService.decrypt.name,
        organizationId: initiatedPayment.organizationId,
      });

      return STRIPE_ACCESS_TOKEN;
    }

    const stripe = new Stripe(STRIPE_ACCESS_TOKEN, {
      apiVersion: "2022-11-15",
    });

    try {
      const sessionResult = await stripe.checkout.sessions.retrieve(
        initiatedPayment.stripeCheckoutSessionId,
      );

      if (!sessionResult || !sessionResult.id) {
        return new UnexpectedError("Failed to create stripe checkout session");
      }

      return sessionResult;
    } catch (err) {
      return new UnexpectedError(err);
    }
  }
}
