import { Injectable } from "@nestjs/common";
import { isNil } from "lodash/fp";
import Stripe from "stripe";

import {
  DomainError,
  ErrorMessages,
  FinancialIntegrationType,
  isError,
  OrganizationId,
  UnexpectedError,
  GCSubscription,
  StripeEntities,
} from "@mio/helpers";

import { FinancialIntegrationService } from "../financial-integration/financial-integration.service";
import { EncryptionService } from "../encryption/encryption.service";

@Injectable()
export class StripeService {
  constructor(
    private financialIntegrationService: FinancialIntegrationService,
    private encryptionService: EncryptionService,
  ) {}

  async findSubscriptions(organizationId: OrganizationId) {
    const financialIntegration = await this.financialIntegrationService.getByOrganizationIdAndType(
      organizationId,
      FinancialIntegrationType.Stripe,
    );

    if (isError(financialIntegration)) {
      financialIntegration.addContext({
        service: StripeService.name,
        method: this.findSubscriptions.name,
        operation: this.financialIntegrationService.getByOrganizationIdAndType.name,
        organizationId,
      });

      return financialIntegration;
    }

    if (isNil(financialIntegration)) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    const STRIPE_ACCESS_TOKEN = await this.encryptionService.decrypt(
      financialIntegration.keyEncrypted,
      financialIntegration.iv,
    );

    if (isError(STRIPE_ACCESS_TOKEN)) {
      STRIPE_ACCESS_TOKEN.addContext({
        service: StripeService.name,
        method: this.findSubscriptions.name,
        operation: this.encryptionService.decrypt.name,
        organizationId,
      });

      return STRIPE_ACCESS_TOKEN;
    }

    const stripe = new Stripe(STRIPE_ACCESS_TOKEN, {
      apiVersion: "2022-11-15",
    });

    try {
      const subscriptionsResult = await stripe.subscriptions.list({});

      if (subscriptionsResult && subscriptionsResult.data) {
        // return subscriptionsResult.data;
        const mbSubscriptions = StripeEntities.Subscription.Entity.toEntities(
          subscriptionsResult.data,
        );

        if (isError(mbSubscriptions)) {
          mbSubscriptions.addContext({
            service: StripeService.name,
            method: this.findSubscriptions.name,
            operation: GCSubscription.toEntities,
            organizationId,
          });

          return mbSubscriptions;
        }

        return mbSubscriptions;
      }

      return [];
    } catch (err) {
      return new UnexpectedError(err);
    }
  }
}
