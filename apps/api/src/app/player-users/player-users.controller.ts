import { Controller, Get, UseGuards } from "@nestjs/common";

import { apiUrls, PlayerUser } from "@mio/helpers";

import { JwtAuthGuard } from "../auth/jwt-auth.guard";
import { WithPlayerUser } from "./with-player-user";
import { PlayerUserGuard } from "./player-user.guard";

@Controller()
export class PlayerUsersController {
  @Get(apiUrls.currentPlayerUser)
  @UseGuards(JwtAuthGuard, PlayerUserGuard)
  async getCurrentPlayerUser(@WithPlayerUser() player: PlayerUser.PlayerUser) {
    return player;
  }
}
