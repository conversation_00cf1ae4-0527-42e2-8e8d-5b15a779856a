import { Injectable } from "@nestjs/common";
import { isEmpty, isNil } from "lodash/fp";

import {
  Email,
  isError,
  PlayerUser,
  UnexpectedError,
  ParsingError,
  CustomError,
  DomainError,
  ErrorMessages,
  LoginCodeDto,
} from "@mio/helpers";

import { PlayerUsersRepository } from "./player-users-repository";
import { PlayerService } from "../player/player.service";

@Injectable()
export class PlayerUsersService {
  constructor(private repo: PlayerUsersRepository, private playerService: PlayerService) {}

  async signIn(
    user: PlayerUser.UnverifiedPlayerUser,
    dto: LoginCodeDto,
  ): Promise<PlayerUser.VerifiedPlayerUser | CustomError> {
    const verifiedUser = PlayerUser.Entity.signIn(user, dto.code);

    if (isError(verifiedUser)) {
      verifiedUser.addContext({
        service: PlayerUsersService.name,
        method: this.signIn.name,
        operation: PlayerUser.Entity.signIn.name,
        user,
        dto,
      });

      return verifiedUser;
    }

    const dbResult = await this.repo.update(verifiedUser);

    if (isError(dbResult)) {
      dbResult.addContext({
        service: PlayerUsersService.name,
        method: this.signIn.name,
        operation: this.repo.update.name,
        verifiedUser,
      });

      return dbResult;
    }

    return verifiedUser;
  }

  async verifyUser(dto: LoginCodeDto): Promise<PlayerUser.PlayerUser | CustomError> {
    const users = await this.repo.findByCode(dto);

    if (isError(users)) {
      users.addContext({
        service: PlayerUsersService.name,
        method: this.verifyUser.name,
        operation: this.repo.findByCode.name,
        dto,
      });

      return users;
    }

    if (isEmpty(users)) {
      return new DomainError(ErrorMessages.InvalidCredentials, {
        service: PlayerUsersService.name,
        method: this.verifyUser.name,
        message: "User not found",
      });
    }

    if (users.length > 1) {
      return new DomainError(ErrorMessages.Conflict, {
        service: PlayerUsersService.name,
        method: this.verifyUser.name,
        message: "Rare exception: found multiple players with the same login code",
        dto,
      });
    }

    return users[0];
  }

  async addLoginCode(
    dto: PlayerUser.SignInDto,
  ): Promise<PlayerUser.UnverifiedPlayerUser | CustomError> {
    const playerOrGuardianExists = await this.playerService.playerOrGuardianExists(dto.email);

    if (isError(playerOrGuardianExists)) {
      playerOrGuardianExists.addContext({
        service: PlayerUsersService.name,
        method: this.addLoginCode.name,
        operation: this.playerService.playerOrGuardianExists.name,
        dto,
      });

      return playerOrGuardianExists;
    }

    if (!playerOrGuardianExists) {
      return new DomainError(ErrorMessages.PermissionDenied, {
        service: PlayerUsersService.name,
        method: this.addLoginCode.name,
        message: "No associated player or guardian.",
        dto,
      });
    }

    const user = await this.findOrCreateUser(dto);

    if (isError(user)) {
      user.addContext({
        service: PlayerUsersService.name,
        method: this.addLoginCode.name,
        operation: this.findOrCreateUser.name,
        dto,
      });

      return user;
    }

    const code = PlayerUser.Entity.generateLoginCode();

    const updatedUser = PlayerUser.Entity.addLoginCode(user, code);

    const updateResult = await this.repo.update(updatedUser);

    if (isError(updateResult)) {
      updateResult.addContext({
        service: PlayerUsersService.name,
        method: this.addLoginCode.name,
        operation: this.repo.update.name,
        updatedUser,
      });

      return updateResult;
    }

    return updatedUser;
  }

  private async findByEmail(
    email: Email,
  ): Promise<PlayerUser.PlayerUser | null | ParsingError | UnexpectedError> {
    const user = await this.repo.getByEmail(email);

    if (isError(user)) {
      user.addContext({
        service: PlayerUsersService.name,
        method: this.findByEmail.name,
        operation: this.repo.getByEmail.name,
        email,
      });
    }

    return user;
  }

  async findById(
    id: PlayerUser.PlayerUserId,
  ): Promise<PlayerUser.PlayerUser | null | ParsingError | UnexpectedError> {
    const result = await this.repo.getUserById(id);

    if (isError(result)) {
      result.addContext({
        service: PlayerUsersService.name,
        method: this.findById.name,
        operation: this.repo.getUserById.name,
        id,
      });
    }

    return result;
  }

  private async findOrCreateUser(
    dto: PlayerUser.SignInDto,
  ): Promise<PlayerUser.PlayerUser | CustomError> {
    const existingUser = await this.findByEmail(dto.email);

    if (isError(existingUser)) {
      existingUser.addContext({
        service: PlayerUsersService.name,
        method: this.findOrCreateUser.name,
        operation: this.findByEmail.name,
        dto,
      });

      return existingUser;
    }

    if (isNil(existingUser)) {
      const code = PlayerUser.Entity.generateLoginCode();
      const newUser = PlayerUser.Entity.createUser(dto.email, code);

      const insertResult = await this.repo.create(newUser);

      if (isError(insertResult)) {
        insertResult.addContext({
          service: PlayerUsersService.name,
          method: this.findOrCreateUser.name,
          operation: this.repo.create.name,
          dto,
        });

        return insertResult;
      }

      return newUser;
    }

    return existingUser;
  }
}
