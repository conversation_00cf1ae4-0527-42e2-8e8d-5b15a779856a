import { createParamDecorator, ExecutionContext } from "@nestjs/common";

import { ErrorMessages, isError, PlayerUser, UnexpectedError } from "@mio/helpers";

import { userKey } from "./player-user.guard";
import { AuthenticatedRequest } from "../auth/types";
import { toInternalServerError } from "../shared";

export const WithPlayerUserId = createParamDecorator((_data: unknown, ctx: ExecutionContext) => {
  const request: AuthenticatedRequest = ctx.switchToHttp().getRequest();

  if (request.user) {
    const parsed = PlayerUser.Entity.parseId(request.user.sub);

    if (isError(parsed)) {
      throw toInternalServerError(
        new UnexpectedError(ErrorMessages.InvalidFields, {
          service: WithPlayerUserId.name,
          message: "Invalid user id",
          id: request.user.sub,
        }),
      );
    }

    return parsed;
  } else {
    throw toInternalServerError(
      new UnexpectedError(ErrorMessages.EntityNotFound, {
        service: WithPlayerUserId.name,
        message: "User id not found. Forgot to use PlayerUserGuard?",
      }),
    );
  }
});

export const WithPlayerUser = createParamDecorator((_data: unknown, ctx: ExecutionContext) => {
  const request: AuthenticatedRequest = ctx.switchToHttp().getRequest();

  if (request[userKey]) {
    const parsed = PlayerUser.Entity.toEntity(request[userKey]);

    if (isError(parsed)) {
      throw toInternalServerError(
        new UnexpectedError(ErrorMessages.InvalidUser, {
          service: WithPlayerUser.name,
          message: "Invalid User. Forgot to use PlayerUserGuard?",
          user: request[userKey],
        }),
      );
    }

    return parsed;
  } else {
    throw toInternalServerError(
      new UnexpectedError(ErrorMessages.EntityNotFound, {
        service: WithPlayerUser.name,
        message: "User not found. Forgot to use PlayerUserGuard?",
      }),
    );
  }
});
