import { <PERSON>du<PERSON> } from "@nestjs/common";

import { DatabaseModule } from "../database";
import { PlayerModule } from "../player/player.module";
import { PlayerUsersRepository } from "./player-users-repository";
import { PlayerUsersController } from "./player-users.controller";
import { PlayerUsersService } from "./player-users.service";
import { PlayerManageGuard } from "./player-manage.guard";

@Module({
  imports: [DatabaseModule, PlayerModule],
  providers: [PlayerUsersRepository, PlayerUsersService, PlayerManageGuard],
  exports: [PlayerUsersService, PlayerManageGuard, PlayerModule],
  controllers: [PlayerUsersController],
})
export class PlayerUsersModule {}
