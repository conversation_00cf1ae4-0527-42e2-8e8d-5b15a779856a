/*
Whenever we do mutations for some Player, we need to make sure that the PlayerUser currently making the request
is either that same player or a Guardian of that player. This is what this guard does. 
*/

import { Injectable, CanActivate, ExecutionContext } from "@nestjs/common";

import {
  PlayerUser,
  ErrorMessages,
  isError,
  Unexpected<PERSON>rror,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>rlPara<PERSON>,
  Player,
  DomainError,
} from "@mio/helpers";

import { AuthenticatedRequest } from "../auth/types";
import { toForbiddenError, toInternalServerError } from "../shared";
import { PlayerService } from "../player/player.service";

export const userKey = "fullPlayerUser";

@Injectable()
export class PlayerManageGuard implements CanActivate {
  constructor(private playerService: PlayerService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request: AuthenticatedRequest = context.switchToHttp().getRequest();

    const params = (request.params || {}) as UnknownRecord;

    /* the player we want to manage */
    const playerId = params[UrlParams.PlayerId];

    const parsedPlayerId = Player.toPlayerId(playerId);

    if (isError(parsedPlayerId)) {
      parsedPlayerId.addContext({
        service: PlayerManageGuard.name,
        message: "Invalid PlayerId",
        playerId,
      });

      throw toInternalServerError(parsedPlayerId);
    }

    if (request[userKey]) {
      /* who we are */
      const parsedUser = PlayerUser.Entity.toEntity(request[userKey]);

      if (isError(parsedUser)) {
        parsedUser.addContext({
          service: PlayerManageGuard.name,
          message: "Invalid User. Forgot to use PlayerUserGuard?",
          user: request[userKey],
        });

        throw toInternalServerError(parsedUser);
      }

      /* players we can manage */
      const relatedPlayers = await this.playerService.findRelatedPlayers(
        parsedUser.authentication.email,
      );

      if (isError(relatedPlayers)) {
        relatedPlayers.addContext({
          service: PlayerManageGuard.name,
          operation: this.playerService.findRelatedPlayers.name,
          email: parsedUser.authentication.email,
        });

        throw toInternalServerError(relatedPlayers);
      }

      /* is the playerId among those we can manage? */
      if (relatedPlayers.some((player) => player.id === parsedPlayerId)) {
        return true;
      }

      throw toForbiddenError(
        new DomainError(ErrorMessages.PermissionDenied, {
          service: PlayerManageGuard.name,
        }),
      );
    } else {
      throw toInternalServerError(
        new UnexpectedError(ErrorMessages.EntityNotFound, {
          service: PlayerManageGuard.name,
          message: "User not found. Forgot to use PlayerUserGuard?",
        }),
      );
    }
  }
}
