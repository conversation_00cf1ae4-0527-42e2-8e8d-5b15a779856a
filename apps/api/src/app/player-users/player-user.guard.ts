import { Injectable, CanActivate, ExecutionContext } from "@nestjs/common";
import { isNil } from "lodash/fp";

import { PlayerUser, DomainError, ErrorMessages, isError } from "@mio/helpers";

import { PlayerUsersService } from "./player-users.service";
import { AuthenticatedRequest } from "../auth/types";
import { toInternalServerError } from "../shared";

export const userKey = "fullPlayerUser";

@Injectable()
export class PlayerUserGuard implements CanActivate {
  constructor(private playerUserService: PlayerUsersService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request: AuthenticatedRequest = context.switchToHttp().getRequest();

    const validatedId = PlayerUser.Entity.parseId(request.user.sub);

    if (isError(validatedId)) {
      throw toInternalServerError(
        new DomainError(ErrorMessages.InvalidUser, {
          service: PlayerUserGuard.name,
          message: "Invalid user id",
          error: validatedId,
          userId: request.user.sub,
        }),
      );
    }

    const user = await this.playerUserService.findById(validatedId);

    if (isError(user)) {
      user.addContext({
        service: PlayerUserGuard.name,
        operation: this.playerUserService.findById.name,
        error: user,
        validatedId,
        userId: request.user.sub,
      });
      throw toInternalServerError(user);
    }

    if (isNil(user)) {
      throw toInternalServerError(
        new DomainError(ErrorMessages.EntityNotFound, {
          service: PlayerUserGuard.name,
          message: "User not found",
          userId: request.user.sub,
        }),
      );
    }

    request[userKey] = user;

    return true;
  }
}
