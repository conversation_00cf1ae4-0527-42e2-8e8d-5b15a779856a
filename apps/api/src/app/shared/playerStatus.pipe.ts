import { CustomEnum, ErrorMessages, ErrorTypes, PlayerTeamStatus } from "@mio/helpers";
import { PipeTransform, Injectable, ArgumentMetadata } from "@nestjs/common";
import { toBadRequest } from "./error-response";

@Injectable()
export class PlayerStatusQueryPipe implements PipeTransform {
  transform(value: unknown, metadata: ArgumentMetadata) {
    if (metadata.type === "query") {
      const result = CustomEnum.parser(PlayerTeamStatus).safeParse(value);

      if (result.success) {
        return result.data;
      }

      throw toBadRequest({
        message: ErrorMessages.InvalidParam,
        type: ErrorTypes.ParsingError,
      });
    }

    return value;
  }
}
