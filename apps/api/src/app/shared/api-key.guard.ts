import { Injectable, CanActivate, ExecutionContext } from "@nestjs/common";

import { API_KEY_HEADER, ErrorMessages, ErrorTypes } from "@mio/helpers";
import { AppConfigService } from "../config";
import { toUnauthorizedException } from "./error-response";

const forbid = () => {
  throw toUnauthorizedException({
    message: ErrorMessages.InvalidCredentials,
    type: ErrorTypes.DomainError,
  });
};

@Injectable()
export class ApiKeyGuard implements CanActivate {
  constructor(private appConfigService: AppConfigService) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();

    const apiKey = request.header(API_KEY_HEADER);

    if (this.appConfigService.getApiKey() === apiKey) {
      return true;
    }

    return forbid();
  }
}
