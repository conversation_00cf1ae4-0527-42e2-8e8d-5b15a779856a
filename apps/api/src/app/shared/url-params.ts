import { PipeTransform, Injectable, ArgumentMetadata } from "@nestjs/common";
import { isNil } from "lodash/fp";

import { ErrorMessages, ErrorTypes, UUID, isError } from "@mio/helpers";

import { toBadRequest } from "./error-response";

@Injectable()
export class IDParamPipe implements PipeTransform {
  transform(value: unknown, metadata: ArgumentMetadata) {
    if (metadata.type === "param") {
      const result = UUID.parser().safeParse(value);

      if (result.success) {
        return result.data;
      }

      throw toBadRequest({
        message: ErrorMessages.InvalidParam,
        type: ErrorTypes.ParsingError,
      });
    }

    return value;
  }
}

@Injectable()
export class IDQueryPipe implements PipeTransform {
  transform(value: unknown, metadata: ArgumentMetadata) {
    if (metadata.type === "query") {
      if (isNil(value)) {
        return value;
      }

      const result = UUID.parse(value);

      if (isError(result)) {
        throw toBadRequest({
          message: ErrorMessages.InvalidQueryParam,
          type: ErrorTypes.ParsingError,
          errors: { query: metadata.data },
        });
      }

      return result;
    }

    return value;
  }
}
