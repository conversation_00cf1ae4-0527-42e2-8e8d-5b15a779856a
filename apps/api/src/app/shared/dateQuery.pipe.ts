import { PipeTransform, Injectable, ArgumentMetadata } from "@nestjs/common";

import { CustomDate, ErrorMessages, ErrorTypes, isError } from "@mio/helpers";

import { toBadRequest } from "./error-response";

@Injectable()
export class DateQueryPipe implements PipeTransform {
  transform(value: unknown, metadata: ArgumentMetadata) {
    if (metadata.type === "query") {
      const result = CustomDate.toValidDate(value);

      if (isError(result)) {
        throw toBadRequest({
          message: ErrorMessages.InvalidQueryParam,
          type: ErrorTypes.ParsingError,
          errors: { query: metadata.data },
        });
      }

      return result;
    }

    return value;
  }
}
