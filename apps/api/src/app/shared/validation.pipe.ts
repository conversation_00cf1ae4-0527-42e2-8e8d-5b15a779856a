import { PipeTransform, Injectable, ArgumentMetadata } from "@nestjs/common";
import { z } from "zod";

import { ParsingError, isParsingError } from "@mio/helpers";

import { toBadRequest } from "./error-response";

@Injectable()
export class ZodValidationPipe implements PipeTransform {
  constructor(private schema: z.Schema<unknown>) {}

  transform(value: unknown, metadata: ArgumentMetadata) {
    if (metadata.type === "body") {
      const result = this.schema.safeParse(value);

      if (!result.success) {
        throw toBadRequest(
          new ParsingError(result.error, {
            service: ZodValidationPipe.name,
          }),
        );
      }

      return result.data;
    }
    return value;
  }
}

type ParsingFunction<T> = (source: unknown) => T | ParsingError;

@Injectable()
export class ParamValidationPipe implements PipeTransform {
  constructor(private parser: ParsingFunction<unknown>) {}

  transform(value: unknown, metadata: ArgumentMetadata) {
    if (metadata.type === "custom") {
      const result = this.parser(value);

      if (isParsingError(result)) {
        result.addContext({
          service: ParamValidationPipe.name,
        });

        throw toBadRequest(result);
      }

      return result;
    }
    return value;
  }
}
