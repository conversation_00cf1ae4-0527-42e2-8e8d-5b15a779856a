import {
  BadRequestException,
  ForbiddenException,
  InternalServerErrorException,
  NotFoundException,
  UnauthorizedException,
} from "@nestjs/common";

import { APIError, ErrorMessages, ErrorTypes } from "@mio/helpers";

export const toUnauthorizedException = (
  error: APIError = { type: ErrorTypes.DomainError, message: ErrorMessages.InvalidCredentials },
) => new UnauthorizedException(error);

export const toBadRequest = (error: APIError) => new BadRequestException(error);

export const toInternalServerError = (error: APIError) => new InternalServerErrorException(error);

export const toForbiddenError = (error: APIError) => new ForbiddenException(error);

export const toNotFoundError = (error: APIError) => new NotFoundException(error);
