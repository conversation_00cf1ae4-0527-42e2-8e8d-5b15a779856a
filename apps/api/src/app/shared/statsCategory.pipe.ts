import { CustomEnum, ErrorMessages, ErrorTypes, StatsCategory } from "@mio/helpers";
import { PipeTransform, Injectable, ArgumentMetadata } from "@nestjs/common";
import { toBadRequest } from "./error-response";

@Injectable()
export class StatsCategoryQueryPipe implements PipeTransform {
  transform(value: unknown, metadata: ArgumentMetadata) {
    if (metadata.type === "query" && typeof value === "object" && value && "category" in value) {
      const result = CustomEnum.parser(StatsCategory).safeParse(value.category);

      if (result.success) {
        return result.data;
      }

      throw toBadRequest({
        message: ErrorMessages.InvalidParam,
        type: ErrorTypes.ParsingError,
      });
    }

    return value;
  }
}
