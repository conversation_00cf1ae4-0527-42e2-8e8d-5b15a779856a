import { PipeTransform, Injectable, ArgumentMetadata } from "@nestjs/common";
import { isObject } from "lodash/fp";

import { isError, Player } from "@mio/helpers";

import { toBadRequest } from "../shared/error-response";

@Injectable()
export class PlayerQueryParamsPipe implements PipeTransform {
  transform(value: unknown, metadata: ArgumentMetadata) {
    if (metadata.type === "query") {
      if (!isObject(value)) {
        return value;
      }

      const result = Player.parseQueryDto(value);

      if (isError(result)) {
        throw toBadRequest(result);
      }

      return result;
    }

    return value;
  }
}
