import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
  UsePipes,
} from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";

import {
  apiUrls,
  AssignTeamDto,
  ChangeStatusDto,
  CreatePlayerDto,
  DomainError,
  Email,
  ErrorTypes,
  GetPlayerDto,
  isError,
  OrganizationId,
  PermissionsModule,
  Player,
  PlayerId,
  PlayerQueryDto,
  PlayerTeamProfile,
  PlayerUser,
  PopulatedPlayer,
  Profile,
  TeamId,
  UnexpectedError,
  UrlParams,
  PaginatedPopulatedPlayers,
  Assets,
} from "@mio/helpers";

import {
  ApiKeyGuard,
  Events,
  IDParamPipe,
  toBadRequest,
  toInternalServerError,
  ZodValidationPipe,
} from "../shared";
import { PlayerService } from "./player.service";
import { PlayerQueryParamsPipe } from "./query-params.pipe";
import { JwtAuthGuard } from "../auth/jwt-auth.guard";
import { ProfileGuard } from "../profile/profile.guard";
import { WithProfile } from "../profile/with-profile";
import { PermissionsGuard, RequiresPermissions } from "../permissions";
import { PlayerUserGuard } from "../player-users/player-user.guard";
import { WithPlayerUser } from "../player-users/with-player-user";
import { PlayerManageGuard } from "../player-users/player-manage.guard";
import { PlayerOwnsImageGuard } from "../assets/image-owner.guard";

@Controller()
export class PlayerController {
  constructor(private playerService: PlayerService, private eventEmitter: EventEmitter2) {}

  @Get(apiUrls.player_documents.document_images)
  @UseGuards(JwtAuthGuard, PlayerUserGuard, PlayerManageGuard)
  async getPlayerDocumentImages(
    @Param(UrlParams.PlayerId, IDParamPipe) playerId: PlayerId,
  ): Promise<Assets.Image.ImageAsset[]> {
    const result = await this.playerService.getPlayerImageDocuments(playerId);

    if (isError(result)) {
      result.addContext({
        service: PlayerController.name,
        method: this.getPlayerDocumentImages.name,
        operation: this.playerService.getPlayerImageDocuments.name,
        playerId,
      });

      switch (result.type) {
        case ErrorTypes.DomainError:
          throw toBadRequest(result);
        case ErrorTypes.ParsingError:
        case ErrorTypes.UnexpectedError:
          throw toInternalServerError(result);
        default:
          throw toInternalServerError(new UnexpectedError(result));
      }
    }

    return result;
  }

  @Patch(apiUrls.player_documents.submit)
  @UseGuards(JwtAuthGuard, PlayerUserGuard, PlayerManageGuard)
  async submitPlayerDocuments(@Param(UrlParams.PlayerId, IDParamPipe) playerId: PlayerId) {
    const result = await this.playerService.markDocumentsAsSubmitted(playerId);

    if (isError(result)) {
      result.addContext({
        service: PlayerController.name,
        method: this.submitPlayerDocuments.name,
        operation: this.playerService.markDocumentsAsSubmitted.name,
        playerId,
      });

      switch (result.type) {
        case ErrorTypes.DomainError:
          throw toBadRequest(result);
        case ErrorTypes.UnexpectedError:
        case ErrorTypes.ParsingError:
          throw toInternalServerError(result);
        default:
          throw toInternalServerError(new UnexpectedError(result));
      }
    }

    return result;
  }

  @Patch(apiUrls.player_documents.photo)
  @UseGuards(JwtAuthGuard, PlayerUserGuard, PlayerManageGuard, PlayerOwnsImageGuard)
  async addPlayerPhoto(
    @Param(UrlParams.PlayerId, IDParamPipe) playerId: PlayerId,
    @Param(UrlParams.PlayerImageId, IDParamPipe) photoId: Assets.Image.ImageId,
  ): Promise<Player> {
    const result = await this.playerService.addPhoto(playerId, photoId);

    if (isError(result)) {
      result.addContext({
        service: PlayerController.name,
        method: this.addPlayerPhoto.name,
        operation: this.playerService.addPhoto.name,
        playerId,
      });

      switch (result.type) {
        case ErrorTypes.DomainError:
          throw toBadRequest(result);
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Delete(apiUrls.player_documents.photo)
  @UseGuards(JwtAuthGuard, PlayerUserGuard, PlayerManageGuard)
  async removePlayerPhoto(
    @Param(UrlParams.PlayerId, IDParamPipe) playerId: PlayerId,
  ): Promise<Player> {
    const result = await this.playerService.removePhoto(playerId);

    if (isError(result)) {
      result.addContext({
        service: PlayerController.name,
        method: this.removePlayerPhoto.name,
        operation: this.playerService.removePhoto.name,
        playerId,
      });

      switch (result.type) {
        case ErrorTypes.DomainError:
          throw toBadRequest(result);
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Patch(apiUrls.player_documents.document_image)
  @UseGuards(JwtAuthGuard, PlayerUserGuard, PlayerManageGuard, PlayerOwnsImageGuard)
  async addPlayerImageDocument(
    @Param(UrlParams.PlayerId, IDParamPipe) playerId: PlayerId,
    @Param(UrlParams.PlayerImageId, IDParamPipe) imageId: Assets.Image.ImageId,
  ): Promise<Player> {
    const result = await this.playerService.addImageDocument(playerId, imageId);

    if (isError(result)) {
      result.addContext({
        service: PlayerController.name,
        method: this.addPlayerImageDocument.name,
        operation: this.playerService.addImageDocument.name,
        playerId,
      });

      switch (result.type) {
        case ErrorTypes.DomainError:
          throw toBadRequest(result);
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Delete(apiUrls.player_documents.document_image)
  @UseGuards(JwtAuthGuard, PlayerUserGuard, PlayerManageGuard)
  async removePlayerImageDocument(
    @Param(UrlParams.PlayerId, IDParamPipe) playerId: PlayerId,
    @Param(UrlParams.PlayerImageId, IDParamPipe) imageId: Assets.Image.ImageId,
  ): Promise<Player> {
    const result = await this.playerService.removeImageDocument(playerId, imageId);

    if (isError(result)) {
      result.addContext({
        service: PlayerController.name,
        method: this.removePlayerImageDocument.name,
        operation: this.playerService.removeImageDocument.name,
        playerId,
      });

      switch (result.type) {
        case ErrorTypes.DomainError:
          throw toBadRequest(result);
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Post(apiUrls.createApplicant)
  @UsePipes(new ZodValidationPipe(Player.toCreateDto))
  async createApplicant(@Body() dto: CreatePlayerDto): Promise<Player | never> {
    const result = await this.playerService.createApplicant(dto);

    if (isError(result)) {
      switch (result.type) {
        case ErrorTypes.DomainError:
          throw toBadRequest(result);
        default:
          throw toInternalServerError(result);
      }
    }

    const eventData: Events.NewApplication = {
      emails: [dto.email, dto.guardian?.email].filter(Boolean) as Email[],
    };
    this.eventEmitter.emit(Events.Type.NewApplication, eventData);

    return result;
  }

  @Post(apiUrls.applyToOrganization)
  @UseGuards(JwtAuthGuard, PlayerUserGuard, PlayerManageGuard)
  async applyToOrganization(
    @Param(UrlParams.OrganizationId, IDParamPipe) organizationId: OrganizationId,
    @Param(UrlParams.PlayerId, IDParamPipe) playerId: PlayerId,
  ): Promise<void | never> {
    const result = await this.playerService.applyToOrganization({
      playerId,
      organizationId,
    });

    if (isError(result)) {
      switch (result.type) {
        case ErrorTypes.DomainError:
          throw toBadRequest(result);
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Post(apiUrls.demoPlayer)
  @UseGuards(ApiKeyGuard)
  async demoCreateApplicant(
    @Body() { organizationId }: { organizationId: OrganizationId },
  ): Promise<void | never> {
    const result = await this.playerService.demoCreateApplicant(organizationId);

    if (isError(result)) {
      switch (result.type) {
        case ErrorTypes.DomainError:
          throw toBadRequest(result);
        default:
          throw toInternalServerError(result);
      }
    }

    return undefined;
  }

  @Patch(apiUrls.player)
  @UseGuards(JwtAuthGuard, PlayerUserGuard, PlayerManageGuard)
  @UsePipes(new ZodValidationPipe(Player.fullParser))
  async update(
    @WithPlayerUser() playerUser: PlayerUser.PlayerUser,
    @Body() dto: Player,
  ): Promise<Player | UnexpectedError | DomainError> {
    const result = await this.playerService.updatePlayer(dto, playerUser);

    if (isError(result)) {
      switch (result.type) {
        case ErrorTypes.DomainError:
          throw toBadRequest(result);
        case ErrorTypes.UnexpectedError:
          throw toInternalServerError(result);
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Post(apiUrls.assignNewTeam)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManagePlayers])
  @UseGuards(JwtAuthGuard, ProfileGuard, PermissionsGuard)
  @UsePipes(new ZodValidationPipe(PlayerTeamProfile.assignTeamDtoParser))
  async assignNewTeam(
    @Body() dto: AssignTeamDto,
    @WithProfile() profile: Profile,
  ): Promise<PopulatedPlayer | never> {
    const result = await this.playerService.assignNewTeam(dto, profile.id);

    if (isError(result)) {
      switch (result.type) {
        case ErrorTypes.DomainError:
          throw toBadRequest(result);
        case ErrorTypes.ParsingError:
          throw toInternalServerError(result);
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Post(apiUrls.changeTeamStatus)
  @RequiresPermissions([
    PermissionsModule.Action.Actions.ManageReviews,
    PermissionsModule.Action.Actions.ManageEvents,
  ])
  @UseGuards(JwtAuthGuard, ProfileGuard, PermissionsGuard)
  @UsePipes(new ZodValidationPipe(PlayerTeamProfile.changeStatusDtoParser))
  async changeStatus(
    @Body() dto: ChangeStatusDto,
    @WithProfile() profile: Profile,
  ): Promise<PopulatedPlayer | never> {
    const result = await this.playerService.changeStatus(dto, profile.id);

    if (isError(result)) {
      switch (result.type) {
        case ErrorTypes.DomainError:
          throw toBadRequest(result);
        case ErrorTypes.ParsingError:
          throw toInternalServerError(result);
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Delete(apiUrls.removeFromOrganization)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManagePlayers])
  @UseGuards(JwtAuthGuard, ProfileGuard, PermissionsGuard)
  async removeFromOrganization(
    @Param(UrlParams.OrganizationId, IDParamPipe) organizationId: OrganizationId,
    @Param(UrlParams.PlayerId, IDParamPipe) playerId: PlayerId,
  ) {
    const result = await this.playerService.removeFromOrganization(organizationId, playerId);

    if (isError(result)) {
      switch (result.type) {
        case ErrorTypes.DomainError:
          throw toBadRequest(result);
        case ErrorTypes.ParsingError:
          throw toInternalServerError(result);
        default:
          throw toInternalServerError(result);
      }
    }

    return playerId;
  }

  @Post(apiUrls.findPlayer)
  @UsePipes(new ZodValidationPipe(Player.getPlayerDtoParser))
  async playerExists(@Body() dto: GetPlayerDto) {
    const result = await this.playerService.findExistingPlayer(dto);

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return !!result;
  }

  @Get(apiUrls.isPlayerInOrganization)
  @UseGuards(JwtAuthGuard)
  async isWithinOrganization(
    @Param(UrlParams.OrganizationId, IDParamPipe) orgId: OrganizationId,
    @Param(UrlParams.PlayerId, IDParamPipe) playerId: PlayerId,
  ): Promise<boolean> {
    const result = await this.playerService.isWithinOrganization(playerId, orgId);

    if (isError(result)) {
      result.addContext({
        service: PlayerController.name,
        method: this.isWithinOrganization.name,
        operation: this.playerService.isWithinOrganization.name,
        orgId,
        playerId,
      });

      switch (result.type) {
        case ErrorTypes.DomainError:
          throw toBadRequest(result);
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Delete(apiUrls.removeFromTeam)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManagePlayers])
  @UseGuards(JwtAuthGuard, ProfileGuard, PermissionsGuard)
  async removeFromTeam(
    @Param(UrlParams.TeamId) teamId: TeamId,
    @Param(UrlParams.OrganizationId) organizationId: OrganizationId,
    @Param(UrlParams.PlayerId) playerId: PlayerId,
    @WithProfile() profile: Profile,
  ): Promise<PopulatedPlayer | never> {
    const result = await this.playerService.softRemoveFromTeam(
      {
        organizationId: organizationId,
        teamId: teamId,
        playerId,
        status: "removed-team",
      },
      profile.id,
    );

    if (isError(result)) {
      switch (result.type) {
        case ErrorTypes.DomainError:
          throw toBadRequest(result);
        case ErrorTypes.ParsingError:
          throw toInternalServerError(result);
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Get(apiUrls.searchOrganizationPlayers)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManagePlayers])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async searchPlayers(
    @Param(UrlParams.OrganizationId, IDParamPipe) orgId: OrganizationId,
    @Query("", PlayerQueryParamsPipe) query: PlayerQueryDto,
  ): Promise<PaginatedPopulatedPlayers> {
    const result = await this.playerService.searchPlayers({
      orgId,
      query,
    });

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return result;
  }

  @Get(apiUrls.getTeamPlayers)
  @RequiresPermissions([
    PermissionsModule.Action.Actions.ManageReviews,
    PermissionsModule.Action.Actions.ManageEvents,
  ])
  @UseGuards(JwtAuthGuard)
  async getTeamPlayers(
    @Param(UrlParams.TeamId, IDParamPipe) teamId: TeamId,
    @Param(UrlParams.OrganizationId, IDParamPipe) orgId: OrganizationId,
    @Query("", PlayerQueryParamsPipe) query: PlayerQueryDto,
  ): Promise<PopulatedPlayer[]> {
    const result = await this.playerService.searchTeamPlayers(teamId, { orgId, query });

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return result;
  }

  @Get(apiUrls.currentPlayers)
  @UseGuards(JwtAuthGuard, PlayerUserGuard)
  async getCurrentPlayers(@WithPlayerUser() user: PlayerUser.PlayerUser) {
    const result = await this.playerService.findRelatedPlayers(user.authentication.email);

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return result;
  }
}
