import { Inject, Injectable } from "@nestjs/common";

import { Primitive, UnexpectedError, PlayerTeamProfile } from "@mio/helpers";

import { DATABASE_CONNECTION, DBClient, DBCollection } from "../database";

type PlayerTeamProfileDocument = Primitive<PlayerTeamProfile>;

@Injectable()
export class PlayerTeamProfileDeletedRepository {
  private collection: DBCollection<PlayerTeamProfileDocument>;

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection("player-team-profiles-deleted");
  }

  async createPlayersTeamProfilesDeleted(
    playerTeamProfiles: PlayerTeamProfile[],
  ): Promise<UnexpectedError | void> {
    return this.collection
      .insertMany(playerTeamProfiles)
      .then(() => undefined)
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }
}
