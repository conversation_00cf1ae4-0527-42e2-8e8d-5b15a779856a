import { Modu<PERSON> } from "@nestjs/common";

import { DatabaseModule } from "../database";
import { PlayerTeamProfileDeletedRepository } from "./player-team-profile-deleted.repository";
import { PlayerTeamProfileRepository } from "./player-team-profile.repository";
import { PlayerTeamProfileService } from "./player-team-profile.service";

@Module({
  imports: [DatabaseModule],
  providers: [
    PlayerTeamProfileRepository,
    PlayerTeamProfileService,
    PlayerTeamProfileDeletedRepository,
  ],
  exports: [PlayerTeamProfileService],
  controllers: [],
})
export class PlayerTeamProfileModule {}
