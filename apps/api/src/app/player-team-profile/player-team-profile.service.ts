import { Injectable } from "@nestjs/common";

import {
  ParsingError,
  OrganizationId,
  PlayerTeamProfile,
  PlayerTeamStatus,
  TeamId,
  UnexpectedError,
  PlayerId,
  CreateApplicantDto,
  isError,
  DomainError,
  ErrorMessages,
  ProfileId,
  CustomDate,
  AssignedPlayerTeamProfile,
  PlayerTeamProfileId,
  PlayerTeamStatusWithTeam,
  StatsCategory,
  StatIndividualRole,
} from "@mio/helpers";

import { PlayerTeamProfileRepository } from "./player-team-profile.repository";
import { PlayerTeamProfileDeletedRepository } from "./player-team-profile-deleted.repository";

@Injectable()
export class PlayerTeamProfileService {
  constructor(
    private repo: PlayerTeamProfileRepository,
    private deleteRepo: PlayerTeamProfileDeletedRepository,
  ) {}

  //#region Write

  async assignOrganization(dto: CreateApplicantDto) {
    const existing = await this.repo.findByOrganizationAndPlayer(dto.organizationId, dto.playerId);

    if (isError(existing)) {
      return existing;
    }

    if (existing.length > 0) {
      return new DomainError(ErrorMessages.EntityAlreadyExists);
    }

    const profile = PlayerTeamProfile.createApplicant(dto);

    return this.repo.createPlayerTeamProfile(profile);
  }

  async assignNewTeam(data: {
    playerId: PlayerId;
    modifierId: ProfileId;
    teamId: TeamId;
    organizationId: OrganizationId;
  }): Promise<UnexpectedError | ParsingError | DomainError | AssignedPlayerTeamProfile> {
    const profiles = await this.findByPlayerIds(data.organizationId, [data.playerId]);

    if (isError(profiles)) {
      profiles.addContext({
        service: PlayerTeamProfileService.name,
        method: this.assignNewTeam.name,
        operation: this.findByPlayerIds.name,
        playerId: data.playerId,
        coachUserId: data.modifierId,
      });

      return profiles;
    }

    if (profiles.length === 0) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    if (profiles.length > 1) {
      return new DomainError(ErrorMessages.InvalidPlayer);
    }

    const previouslyRemovedPlayer = profiles.find(
      (profile) =>
        "teamId" in profile &&
        profile.teamId === data.teamId &&
        profile.status === PlayerTeamStatus.RemovedTeam,
    );

    if (previouslyRemovedPlayer) {
      return this.changeStatus({
        playerId: data.playerId,
        modifierId: data.modifierId,
        teamId: data.teamId,
        organizationId: data.organizationId,
        status: PlayerTeamStatus.ApplicantTeam,
      });
    }

    if (!PlayerTeamProfile.isOrganizationApplicant(profiles[0])) {
      return new DomainError(ErrorMessages.InvalidPlayerTeamProfile);
    }

    const updatedProfile = PlayerTeamProfile.assignNewTeam({
      profile: profiles[0],
      status: PlayerTeamStatus.ApplicantTeam,
      modifier: data.modifierId,
      teamId: data.teamId,
      now: CustomDate.now(),
    });

    const updateResult = await this.repo.replace(updatedProfile);

    if (isError(updateResult)) {
      return updateResult;
    }

    return updatedProfile;
  }

  async updatePersonalInfo(data: Omit<PlayerTeamProfile, "id" | "organizationId" | "status">) {
    const playerTeamProfiles = await this.findByPlayerId(data.playerId);

    if (isError(playerTeamProfiles)) {
      playerTeamProfiles.addContext({
        service: PlayerTeamProfileService.name,
        method: this.updatePersonalInfo.name,
        operation: this.findByPlayerIds.name,
        playerId: data.playerId,
      });

      return playerTeamProfiles;
    }

    if (playerTeamProfiles.length === 0) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    for (const profile of playerTeamProfiles) {
      const updatedProfile = PlayerTeamProfile.updatePersonalInfo(profile, data);

      if (isError(updatedProfile)) {
        updatedProfile.addContext({
          service: PlayerTeamProfileService.name,
          method: this.updatePersonalInfo.name,
          operation: this.repo.replace.name,
          playerId: data.playerId,
        });

        return updatedProfile;
      }

      const updateResult = await this.repo.updatePersonalInfo(updatedProfile);

      if (isError(updateResult)) {
        updateResult.addContext({
          service: PlayerTeamProfileService.name,
          method: this.updatePersonalInfo.name,
          operation: this.repo.replace.name,
          playerId: data.playerId,
        });

        return updateResult;
      }

      return updatedProfile;
    }

    return undefined;
  }

  async updateToRemovedFromOrganization(data: {
    playerId: PlayerId;
    organizationId: OrganizationId;
  }) {
    return this.repo.updateToRemovedFromOrganization(data.playerId, data.organizationId);
  }

  async changeStatus(data: {
    playerId: PlayerId;
    modifierId: ProfileId;
    teamId: TeamId;
    organizationId: OrganizationId;
    status: PlayerTeamStatusWithTeam;
  }): Promise<UnexpectedError | ParsingError | DomainError | AssignedPlayerTeamProfile> {
    const profiles = await this.findAssignedByPlayerIds(data.organizationId, [data.playerId]);

    if (isError(profiles)) {
      return profiles;
    }

    if (profiles.length === 0) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    if (profiles.length > 1) {
      return new DomainError(ErrorMessages.InvalidPlayer);
    }

    if (PlayerTeamProfile.isOrganizationApplicant(profiles[0])) {
      return new DomainError(ErrorMessages.InvalidPlayerTeamProfile);
    }

    const updatedProfile = PlayerTeamProfile.changeStatus({
      profile: profiles[0],
      modifier: data.modifierId,
      teamId: data.teamId,
      now: CustomDate.now(),
      status: data.status,
    });

    const updateResult = await this.repo.replace(updatedProfile);

    if (isError(updateResult)) {
      return updateResult;
    }

    return updatedProfile;
  }

  async removeFromTeam(data: {
    playersTeamProfilesIds: PlayerTeamProfileId[];
    modifierId: ProfileId;
  }): Promise<UnexpectedError | ParsingError | DomainError | void> {
    const playersProfiles = await this.findByPlayerTeamProfileIds(data.playersTeamProfilesIds);

    if (isError(playersProfiles)) {
      return playersProfiles;
    }

    if (playersProfiles.length === 0) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    const updatedProfileDto = PlayerTeamProfile.deletePlayersTeamProfiles({
      profiles: playersProfiles,
      modifier: data.modifierId,
      now: CustomDate.now(),
    });

    await this.deleteRepo.createPlayersTeamProfilesDeleted(updatedProfileDto);

    const allProfilesAcrossTeams = await this.findAssignedByPlayerIds(
      playersProfiles[0].organizationId,
      playersProfiles.map((playah) => playah.playerId),
    );

    if (isError(allProfilesAcrossTeams)) {
      return allProfilesAcrossTeams;
    }

    if (allProfilesAcrossTeams.length === 0) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    if (allProfilesAcrossTeams.some((prof) => PlayerTeamProfile.isOrganizationApplicant(prof))) {
      return new DomainError(ErrorMessages.InvalidPlayerTeamProfile);
    }

    // if there are other profiles with other teams, we can safely remove the whole record
    const profilesToBeDeleted = playersProfiles.filter(
      (playah) =>
        allProfilesAcrossTeams.filter((match) => match.playerId === playah.playerId).length > 1,
    );
    if (profilesToBeDeleted.length > 0) {
      return this.repo.delete(profilesToBeDeleted.map((playah) => playah.id));
    }

    // if there are no other profiles, we must revert the record to Applicant-organization so NOT to delete the player as a whole
    const applicants = playersProfiles.filter(
      (playah) =>
        allProfilesAcrossTeams.filter((match) => match.playerId === playah.playerId).length === 1,
    );

    if (applicants) {
      const playersNoTeams = PlayerTeamProfile.removeTeam({
        dto: applicants,
        modifier: data.modifierId,
      });

      const result = playersNoTeams.forEach(async (playerNoTeam) => {
        const updateResult = await this.repo.replace(playerNoTeam);

        if (isError(updateResult)) {
          return updateResult;
        }

        return updateResult;
      });

      return result;
    }
  }

  async deletePlayerTeamProfile(
    playerTeamProfile: PlayerTeamProfile[],
  ): Promise<void | UnexpectedError | ParsingError> {
    return this.deleteRepo.createPlayersTeamProfilesDeleted(playerTeamProfile);
  }

  //#endregion

  //#region Read

  async searchProfiles(
    organizationId: OrganizationId,
    teamId?: TeamId,
    playerStatus?: PlayerTeamStatus,
  ) {
    return this.repo.searchProfiles(organizationId, teamId, playerStatus);
  }

  async findByTeamId(
    teamId: TeamId,
  ): Promise<PlayerTeamProfile[] | UnexpectedError | ParsingError> {
    return this.repo.getByTeamId(teamId);
  }

  async findByPlayerIds(
    organizationId: OrganizationId,
    playerIds: PlayerId[],
  ): Promise<PlayerTeamProfile[] | UnexpectedError | ParsingError> {
    return this.repo.findByOrganizationAndPlayers(organizationId, playerIds);
  }

  async findByPlayerId(
    playerId: PlayerId,
  ): Promise<PlayerTeamProfile[] | UnexpectedError | ParsingError> {
    return this.repo.findByPlayerId(playerId);
  }

  async findAssignedByPlayerIds(
    organizationId: OrganizationId,
    playerIds: PlayerId[],
  ): Promise<AssignedPlayerTeamProfile[] | UnexpectedError | ParsingError> {
    return this.repo.findAssignedByPlayerIds(playerIds, organizationId);
  }

  async findByPlayerTeamProfileIds(
    playerTeamProfileIds: PlayerTeamProfileId[],
  ): Promise<AssignedPlayerTeamProfile[] | UnexpectedError | ParsingError> {
    return this.repo.findAssignedByPlayerTeamProfileIds(playerTeamProfileIds);
  }

  extractCategoryStats(category: StatsCategory) {
    switch (category) {
      case StatsCategory.Goals:
        return StatIndividualRole.Scorer;
      case StatsCategory.Assists:
        return StatIndividualRole.Assist;
      case StatsCategory.KeyPasses:
        return StatIndividualRole.KeyPass;
    }
  }

  getMatchStatsByCategory(teamId: TeamId, statCategory: StatsCategory) {
    const role = this.extractCategoryStats(statCategory);

    return this.getPlayersStatByCategory(teamId, role);
  }

  async getPlayersStatByCategory(teamId: TeamId, role: StatIndividualRole) {
    const result = await this.repo.getPlayersStatsByCategory(teamId, role);

    if (isError(result)) {
      result.addContext({
        service: PlayerTeamProfileService.name,
        method: this.getPlayersStatByCategory.name,
        operation: this.repo.getPlayersStatsByCategory.name,
        teamId,
      });
    }

    return result;
  }

  //#endregion
}
