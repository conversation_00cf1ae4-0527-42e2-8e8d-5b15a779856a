import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  UseGuards,
  UsePipes,
} from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { isNil } from "lodash/fp";

import {
  apiUrls,
  CreateTeamDto,
  DomainError,
  ErrorMessages,
  ErrorTypes,
  isError,
  OrganizationId,
  PermissionsModule,
  PositiveNumber,
  Profile,
  StripeEntities,
  Team,
  TeamId,
  UpdateTeamDto,
  UrlParams,
} from "@mio/helpers";

import {
  toInternalServerError,
  ZodValidationPipe,
  IDParamPipe,
  toNotFoundError,
  ApiKeyGuard,
  Events,
} from "../shared";
import { TeamService } from "./team.service";
import { JwtAuthGuard } from "../auth/jwt-auth.guard";
import { WithProfile } from "../profile/with-profile";
import { ProfileGuard } from "../profile/profile.guard";
import {
  OrPermissionsGuard,
  PermissionsGuard,
  RequiresPermissions,
  VisibleTeamsGuard,
  WithVisibleTeams,
} from "../permissions";

@Controller()
export class TeamController {
  constructor(private teamService: TeamService, private eventEmitter: EventEmitter2) {}

  @Post(apiUrls.createTeam)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @UsePipes(new ZodValidationPipe(Team.createDto))
  async createTeam(@Body() dto: CreateTeamDto) {
    const result = await this.teamService.createTeam(dto);

    if (isError(result)) {
      switch (result.type) {
        case ErrorTypes.UnexpectedError: {
          throw toInternalServerError(result);
        }
        default:
          throw toInternalServerError(result);
      }
    }

    if (Team.isIntegratedWithFinancialSystem(result)) {
      const eventData: Events.TeamUpsert = {
        teamId: result.id,
        organizationId: dto.organizationId,
        fee: dto.fee ?? (StripeEntities.DefaultMonthlyFee as PositiveNumber),
      };

      this.eventEmitter.emit(Events.Type.TeamNew, eventData);
    }

    return result;
  }

  @Post(apiUrls.demoTeam)
  @UseGuards(ApiKeyGuard)
  async demoCreateTeam(@Body() dto: CreateTeamDto) {
    return this.teamService.createTeam(dto).then((result) => {
      if (isError(result)) {
        switch (result.type) {
          case ErrorTypes.UnexpectedError: {
            throw toInternalServerError(result);
          }
          default:
            throw toInternalServerError(result);
        }
      }
      return result;
    });
  }

  @Get(apiUrls.getTeam)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async getTeam(@Param(UrlParams.TeamId, IDParamPipe) teamId: TeamId) {
    return this.teamService.findById(teamId).then((result) => {
      if (isError(result)) {
        switch (result.type) {
          case ErrorTypes.UnexpectedError: {
            throw toInternalServerError(result);
          }
          case ErrorTypes.ParsingError: {
            throw toInternalServerError(result);
          }
          default:
            throw toInternalServerError(result);
        }
      }

      if (isNil(result)) {
        throw toNotFoundError(new DomainError(ErrorMessages.EntityNotFound));
      }

      return result;
    });
  }

  @Delete(apiUrls.deleteTeam)
  @UseGuards(JwtAuthGuard, ProfileGuard)
  @UsePipes()
  async deleteTeam(
    @Param(UrlParams.TeamId, IDParamPipe) teamId: TeamId,
    @WithProfile() profile: Profile,
  ) {
    return this.teamService.deleteTeam(teamId, profile.id).then((result) => {
      if (isError(result)) {
        switch (result.type) {
          case ErrorTypes.UnexpectedError: {
            throw toInternalServerError(result);
          }
          case ErrorTypes.ParsingError: {
            throw toInternalServerError(result);
          }
          default:
            throw toInternalServerError(result);
        }
      }

      return result;
    });
  }

  @Get(apiUrls.getTeams)
  @UseGuards(JwtAuthGuard, VisibleTeamsGuard)
  async getTeams(
    @Param(UrlParams.OrganizationId, IDParamPipe) organizationId: OrganizationId,
    @WithVisibleTeams() allowedTeams?: TeamId[],
  ) {
    return this.teamService.findByOrganizationId(organizationId, allowedTeams).then((result) => {
      if (isError(result)) {
        switch (result.type) {
          case ErrorTypes.UnexpectedError: {
            throw toInternalServerError(result);
          }
          case ErrorTypes.ParsingError: {
            throw toInternalServerError(result);
          }
          default:
            throw toInternalServerError(result);
        }
      }
      return result;
    });
  }

  @Get(apiUrls.getAdminTeams)
  @RequiresPermissions([
    PermissionsModule.Action.Actions.ManageTeams,
    PermissionsModule.Action.Actions.ManageUsers,
    PermissionsModule.Action.Actions.ManagePlayers,
  ])
  @UseGuards(JwtAuthGuard, OrPermissionsGuard)
  async getAdminTeams(
    @Param(UrlParams.OrganizationId, IDParamPipe) organizationId: OrganizationId,
  ) {
    return this.teamService.findByOrganizationId(organizationId).then((result) => {
      if (isError(result)) {
        switch (result.type) {
          case ErrorTypes.UnexpectedError: {
            throw toInternalServerError(result);
          }
          case ErrorTypes.ParsingError: {
            throw toInternalServerError(result);
          }
          default:
            throw toInternalServerError(result);
        }
      }
      return result;
    });
  }

  @Patch(apiUrls.updateTeam)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @UsePipes(new ZodValidationPipe(Team.updateDto))
  async updateTeam(
    @Body() dto: UpdateTeamDto,
    @Param(UrlParams.TeamId, IDParamPipe) teamId: TeamId,
  ) {
    return this.teamService.updateTeam(teamId, dto).then((result) => {
      if (isError(result)) {
        switch (result.type) {
          case ErrorTypes.DomainError: {
            if (result.message === ErrorMessages.EntityNotFound) {
              throw toNotFoundError(result);
            }
            throw toInternalServerError(result);
          }
          case ErrorTypes.UnexpectedError: {
            throw toInternalServerError(result);
          }
          case ErrorTypes.ParsingError: {
            throw toInternalServerError(result);
          }
          default:
            throw toInternalServerError(result);
        }
      }

      if (Team.isIntegratedWithFinancialSystem(result)) {
        const eventData: Events.TeamUpsert = {
          teamId: result.id,
          organizationId: dto.organizationId,
          fee: dto.fee ?? (StripeEntities.DefaultMonthlyFee as PositiveNumber),
        };

        this.eventEmitter.emit(Events.Type.TeamUpdate, eventData);
      }

      return result;
    });
  }
}
