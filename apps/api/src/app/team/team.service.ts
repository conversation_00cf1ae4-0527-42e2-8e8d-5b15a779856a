import { Injectable } from "@nestjs/common";
import { isNil } from "lodash/fp";

import {
  CreateTeamDto,
  Team,
  TeamId,
  isError,
  OrganizationId,
  UpdateTeamDto,
  DomainError,
  ErrorMessages,
  UnexpectedError,
  ParsingError,
  ProfileId,
} from "@mio/helpers";
import { TeamRepository } from "./team.repository";
import { TeamDeletedRepository } from "./team-deleted.repository";
import { PlayerTeamProfileService } from "../player-team-profile";

@Injectable()
export class TeamService {
  constructor(
    private playerTeamProfileService: PlayerTeamProfileService,
    private teamRepo: TeamRepository,
    private deleteTeamRepo: TeamDeletedRepository,
  ) {}

  async findById(id: TeamId) {
    const team = await this.teamRepo.getById(id);

    if (isError(team)) {
      team.addContext({
        service: TeamService.name,
        method: this.findById.name,
        operation: this.teamRepo.getById.name,
        id,
      });

      return team;
    }

    return team;
  }

  async findByOrganizationId(
    id: OrganizationId,
    includeTeams?: TeamId[],
  ): Promise<Team[] | UnexpectedError | ParsingError> {
    const teams = await this.teamRepo.getByOrganizationId(id);

    if (isError(teams)) {
      teams.addContext({
        service: TeamService.name,
        method: this.findByOrganizationId.name,
        operation: this.teamRepo.getByOrganizationId.name,
        includeTeams,
        organizationId: id,
      });

      return teams;
    }

    return includeTeams ? teams.filter((team) => includeTeams.includes(team.id)) : teams;
  }

  createTeam(dto: CreateTeamDto) {
    const team = Team.create(dto);

    return this.teamRepo.createTeam(team).then((res) => {
      if (isError(res)) {
        res.addContext({
          service: TeamService.name,
          method: this.createTeam.name,
          operation: this.teamRepo.createTeam.name,
          teamName: dto?.name,
          organizationId: dto?.organizationId,
        });

        return res;
      }

      return team;
    });
  }

  demoCreateTeam(dto?: CreateTeamDto) {
    const team = Team.toDemoInstance(dto);

    return this.teamRepo.createTeam(team).then((res) => {
      if (isError(res)) {
        res.addContext({
          service: TeamService.name,
          method: this.demoCreateTeam.name,
          operation: this.teamRepo.createTeam.name,
          teamName: dto?.name,
          organizationId: dto?.organizationId,
        });

        return res;
      }

      return team;
    });
  }

  async deleteTeam(teamId: TeamId, modifier: ProfileId) {
    const team = await this.findById(teamId);

    if (isError(team)) {
      team.addContext({
        service: TeamService.name,
        method: this.deleteTeam.name,
        operation: this.findById.name,
        teamId,
        modifier,
      });

      return team;
    }

    if (!team) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    const profiles = await this.playerTeamProfileService.findByTeamId(teamId);

    if (isError(profiles)) {
      profiles.addContext({
        service: TeamService.name,
        method: this.deleteTeam.name,
        operation: this.playerTeamProfileService.findByTeamId.name,
        teamId,
        modifier,
      });

      return profiles;
    }

    if (profiles.length > 0) {
      await this.playerTeamProfileService.removeFromTeam({
        playersTeamProfilesIds: profiles.map((x) => x.id),
        modifierId: modifier,
      });
    }

    const teamDeletedDto = Team.createDeleted(
      team,
      profiles.map((x) => x.playerId),
      modifier,
    );

    this.deleteTeamRepo.createTeamDeleted(teamDeletedDto);

    return this.teamRepo.deleteTeam(teamId);
  }

  async updateTeam(teamId: TeamId, dto: UpdateTeamDto) {
    const team = await this.teamRepo.getById(teamId);

    if (isError(team)) {
      team.addContext({
        service: TeamService.name,
        method: this.updateTeam.name,
        operation: this.teamRepo.getById.name,
        teamId,
      });

      return team;
    }

    if (isNil(team)) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    const updatedTeam = Team.update(dto, team);

    const updateResult = await this.teamRepo.update(updatedTeam);

    if (isError(updateResult)) {
      return new UnexpectedError(updateResult);
    }

    return updatedTeam;
  }
}
