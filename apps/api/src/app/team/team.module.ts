import { <PERSON>du<PERSON> } from "@nestjs/common";

import { AppConfigModule } from "../config";
import { DatabaseModule } from "../database";
import { PermissionsModule } from "../permissions";
import { PlayerTeamProfileModule } from "../player-team-profile";
import { ProfileModule } from "../profile/profile.module";
import { TeamDeletedRepository } from "./team-deleted.repository";
import { TeamExistsGuard } from "./team-exists.guard";
import { TeamController } from "./team.controller";
import { TeamRepository } from "./team.repository";
import { TeamService } from "./team.service";

@Module({
  imports: [
    DatabaseModule,
    AppConfigModule,
    PermissionsModule,
    PlayerTeamProfileModule,
    ProfileModule,
  ],
  providers: [TeamRepository, TeamService, TeamExistsGuard, TeamDeletedRepository],
  controllers: [TeamController],
  exports: [TeamExistsGuard, TeamService],
})
export class TeamModule {}
