import { Test, TestingModule } from "@nestjs/testing";
import { InternalServerErrorException } from "@nestjs/common";

import {
  breakTest,
  Team,
  TeamId,
  ParsingError,
  UnexpectedError,
  UUID,
  CustomDate,
  PositiveInteger,
} from "@mio/helpers";
import { TeamController } from "./team.controller";
import { mockDep, passingGuard } from "../../test";
import { TeamService } from "./team.service";
import { ApiKeyGuard } from "../shared";
import { OrPermissionsGuard, PermissionsGuard, VisibleTeamsGuard } from "../permissions";
import { ProfileGuard } from "../profile/profile.guard";
import { EventEmitter2 } from "@nestjs/event-emitter";

describe("TeamController", () => {
  let controller: TeamController;

  describe(TeamController.prototype.getTeam.name, () => {
    const validTeam = Team.parse({
      name: "<PERSON><PERSON><PERSON>",
      id: UUID.generate(),
      organizationId: UUID.generate(),
      playersBornAfter: CustomDate.subDays(3000 as PositiveInteger),
      playersBornBefore: CustomDate.subDays(2500 as PositiveInteger),
      ageDescription: "Under 15s",
      gender: "male",
    });

    it(`returns an Team`, async () => {
      const module: TestingModule = await Test.createTestingModule({
        controllers: [TeamController],
        providers: [
          mockDep(TeamService, {
            findById: async () => validTeam,
          }),
          mockDep(EventEmitter2, new EventEmitter2()),
        ],
      })
        .overrideGuard(ApiKeyGuard)
        .useValue(passingGuard)
        .overrideGuard(PermissionsGuard)
        .useValue(passingGuard)
        .overrideGuard(OrPermissionsGuard)
        .useValue(passingGuard)
        .overrideGuard(VisibleTeamsGuard)
        .useValue(passingGuard)
        .overrideGuard(ProfileGuard)
        .useValue(passingGuard)
        .compile();

      controller = module.get<TeamController>(TeamController);

      const result = await controller.getTeam(validTeam.id);

      expect(result).toEqual(validTeam);
    });

    it(`throws InternalServerError`, async () => {
      const module: TestingModule = await Test.createTestingModule({
        controllers: [TeamController],
        providers: [
          mockDep(TeamService, {
            findById: async () => new UnexpectedError(),
          }),
          mockDep(EventEmitter2, new EventEmitter2()),
        ],
      })
        .overrideGuard(ApiKeyGuard)
        .useValue(passingGuard)
        .overrideGuard(PermissionsGuard)
        .useValue(passingGuard)
        .overrideGuard(OrPermissionsGuard)
        .useValue(passingGuard)
        .overrideGuard(VisibleTeamsGuard)
        .useValue(passingGuard)
        .overrideGuard(ProfileGuard)
        .useValue(passingGuard)
        .compile();

      controller = module.get<TeamController>(TeamController);

      try {
        await controller.getTeam(validTeam.id);
        breakTest();
      } catch (err) {
        expect(err).toBeInstanceOf(InternalServerErrorException);
      }
    });

    /* this is a ParsingError on the DB read level thus returns a 500 instead of
    400. 400 will be returned on the incoming DTO parsing level which is not
    testable in a unit test */
    it(`throws an InternalServerError when a ParsingError has occured`, async () => {
      const module: TestingModule = await Test.createTestingModule({
        controllers: [TeamController],
        providers: [
          mockDep(TeamService, {
            findById: async () => new ParsingError({}),
          }),
          mockDep(EventEmitter2, new EventEmitter2()),
        ],
      })
        .overrideGuard(ApiKeyGuard)
        .useValue(passingGuard)
        .overrideGuard(PermissionsGuard)
        .useValue(passingGuard)
        .overrideGuard(OrPermissionsGuard)
        .useValue(passingGuard)
        .overrideGuard(VisibleTeamsGuard)
        .useValue(passingGuard)
        .overrideGuard(ProfileGuard)
        .useValue(passingGuard)
        .compile();

      controller = module.get<TeamController>(TeamController);

      try {
        await controller.getTeam(validTeam.id);
        breakTest();
      } catch (err) {
        expect(err).toBeInstanceOf(InternalServerErrorException);
      }
    });
  });

  describe(TeamController.prototype.createTeam.name, () => {
    const dto = Team.createDto.parse({
      name: "Krakra",
      organizationId: UUID.generate(),
      playersBornAfter: CustomDate.subDays(3000 as PositiveInteger),
      playersBornBefore: CustomDate.subDays(2500 as PositiveInteger),
      ageDescription: "Under 15s",
      gender: "male",
    });
    const teamId = UUID.generate<TeamId>();
    const createdTeam = {
      ...dto,
      id: teamId,
    };

    it(`creates and returns an Team`, async () => {
      const module: TestingModule = await Test.createTestingModule({
        controllers: [TeamController],
        providers: [
          mockDep(TeamService, {
            createTeam: async () => createdTeam,
          }),
          mockDep(EventEmitter2, new EventEmitter2()),
        ],
      })
        .overrideGuard(ApiKeyGuard)
        .useValue(passingGuard)
        .overrideGuard(PermissionsGuard)
        .useValue(passingGuard)
        .overrideGuard(OrPermissionsGuard)
        .useValue(passingGuard)
        .overrideGuard(VisibleTeamsGuard)
        .useValue(passingGuard)
        .overrideGuard(ProfileGuard)
        .useValue(passingGuard)
        .compile();

      controller = module.get<TeamController>(TeamController);

      const result = await controller.createTeam(dto);

      expect(result).toEqual(createdTeam);
    });

    it(`throws an InternalServerError`, async () => {
      const module: TestingModule = await Test.createTestingModule({
        controllers: [TeamController],
        providers: [
          mockDep(TeamService, {
            createTeam: async () => new UnexpectedError(),
          }),
          mockDep(EventEmitter2, new EventEmitter2()),
        ],
      })
        .overrideGuard(ApiKeyGuard)
        .useValue(passingGuard)
        .overrideGuard(PermissionsGuard)
        .useValue(passingGuard)
        .overrideGuard(OrPermissionsGuard)
        .useValue(passingGuard)
        .overrideGuard(VisibleTeamsGuard)
        .useValue(passingGuard)
        .overrideGuard(ProfileGuard)
        .useValue(passingGuard)
        .compile();

      controller = module.get<TeamController>(TeamController);

      try {
        await controller.createTeam(dto);
      } catch (err) {
        expect(err).toBeInstanceOf(InternalServerErrorException);
      }
    });
  });
});
