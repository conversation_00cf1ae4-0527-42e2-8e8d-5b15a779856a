import { Injectable, CanActivate, ExecutionContext } from "@nestjs/common";
import { isNull } from "lodash/fp";

import { DomainError, ErrorMessages, isError, UnexpectedError } from "@mio/helpers";
import { toForbiddenError, toInternalServerError } from "../shared";
import { TeamService } from "./team.service";

@Injectable()
export class TeamExistsGuard implements CanActivate {
  constructor(private orgService: TeamService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const teamId = request.body.team;

    return this.orgService.findById(teamId).then((result) => {
      if (isError(result)) {
        throw toInternalServerError(new UnexpectedError(result));
      }
      if (isNull(result)) {
        throw toForbiddenError(new DomainError(ErrorMessages.TeamNotFound));
      }

      return true;
    });
  }
}
