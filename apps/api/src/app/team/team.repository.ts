import { Inject, Injectable } from "@nestjs/common";

import {
  isError,
  Team,
  TeamId,
  ParsingError,
  Primitive,
  UnexpectedError,
  OrganizationId,
  DomainError,
  ErrorMessages,
} from "@mio/helpers";

import { DATABASE_CONNECTION, DBClient, DBCollection } from "../database";

type TeamDocument = Primitive<Team>;

export const teamDbCollection = "teams";

@Injectable()
export class TeamRepository {
  private collection: DBCollection<TeamDocument>;

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection(teamDbCollection);
  }

  async getById(id: TeamId): Promise<UnexpectedError | ParsingError | Team | null> {
    return this.collection
      .findOne({ id })
      .then((mbTeam) => {
        if (mbTeam) {
          const asEntity = Team.toEntity(mbTeam);
          if (isError(asEntity)) {
            asEntity.addContext({
              service: TeamRepository.name,
              method: this.getById.name,
              operation: this.collection.findOne.name,
              id,
            });
          }

          return asEntity;
        }

        return null;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  getByOrganizationId(id: OrganizationId): Promise<UnexpectedError | ParsingError | Team[]> {
    return this.collection
      .find({ organizationId: id })
      .sort({ name: 1 })
      .toArray()
      .then((mbTeams) => {
        if (mbTeams) {
          const asEntities = Team.toEntities(mbTeams);
          if (isError(asEntities)) {
            // TODO: log
          }

          return asEntities;
        }
        return [];
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async createTeam(team: Team): Promise<UnexpectedError | void> {
    return this.collection
      .insertOne({ ...team })
      .then(() => undefined)
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async deleteTeam(teamId: TeamId) {
    return this.collection
      .deleteOne({ id: teamId })
      .then(() => undefined)
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async update(team: Team) {
    const result = await this.collection.updateOne({ id: team.id }, { $set: team }).catch((err) => {
      // TODO: log
      return new UnexpectedError(err);
    });

    if (isError(result)) {
      return result;
    }

    if (result.matchedCount < 1) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    if (result.matchedCount > 1) {
      return new DomainError(ErrorMessages.MultipleEntitiesFound);
    }

    return undefined;
  }
}
