import { Inject, Injectable } from "@nestjs/common";

import { Primitive, UnexpectedError, TeamDeleted } from "@mio/helpers";

import { DATABASE_CONNECTION, DBClient, DBCollection } from "../database";

type TeamDocument = Primitive<TeamDeleted>;

@Injectable()
export class TeamDeletedRepository {
  private collection: DBCollection<TeamDocument>;

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection("teams-deleted");
  }

  async createTeamDeleted(team: TeamDeleted): Promise<UnexpectedError | void> {
    return this.collection
      .insertOne({ ...team })
      .then(() => undefined)
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }
}
