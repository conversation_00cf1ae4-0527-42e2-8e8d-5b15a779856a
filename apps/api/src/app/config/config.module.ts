import { Module } from "@nestjs/common";
import { ConfigModule, ConfigModuleOptions } from "@nestjs/config";

import { AppConfigService } from "./app-config.service";
import { validate } from "./validation";

const configOptions: ConfigModuleOptions = {
  isGlobal: true,
  validate,
  cache: true,
};

@Module({
  imports: [ConfigModule.forRoot(configOptions)],
  providers: [AppConfigService],
  exports: [AppConfigService],
})
export class AppConfigModule {}
