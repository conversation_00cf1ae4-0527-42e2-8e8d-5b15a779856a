import { Modu<PERSON> } from "@nestjs/common";

import { DatabaseModule } from "../database";
import { ProfileRepository } from "./profile.repository";
import { ProfileService } from "./profile.service";
import { ProfilesController } from "./profiles.controller";

@Module({
  imports: [DatabaseModule],
  providers: [ProfileRepository, ProfileService],
  exports: [ProfileService, ProfileRepository],
  controllers: [ProfilesController],
})
export class ProfileModule {}
