import { createParamDecorator, ExecutionContext } from "@nestjs/common";

import { ErrorMessages, ErrorTypes } from "@mio/helpers";

import { AuthenticatedRequest } from "../auth/types";
import { toInternalServerError } from "../shared";

export const WithProfile = createParamDecorator((_data: unknown, ctx: ExecutionContext) => {
  const request: AuthenticatedRequest = ctx.switchToHttp().getRequest();

  if (request.profile) {
    return request.profile;
  } else {
    // TODO: log. We forgot to use ProfileGuard
    throw toInternalServerError({
      type: ErrorTypes.UnexpectedError,
      message: ErrorMessages.EntityNotFound,
    });
  }
});
