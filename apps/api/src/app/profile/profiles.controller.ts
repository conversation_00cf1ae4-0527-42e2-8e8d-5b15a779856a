import { Controller, Get, Param, UseGuards } from "@nestjs/common";
import { isNull } from "lodash/fp";

import {
  apiUrls,
  DomainError,
  ErrorMessages,
  isError,
  CoachUserId,
  UrlParams,
  OrganizationId,
  TeamId,
} from "@mio/helpers";

import { JwtAuthGuard } from "../auth/jwt-auth.guard";
import { IDParamPipe, toInternalServerError, toNotFoundError } from "../shared";
import { ProfileService } from "./profile.service";
import { WithCoachUserId } from "../coach-users/with-user";

@Controller()
export class ProfilesController {
  constructor(private profileService: ProfileService) {}

  @Get(apiUrls.currentProfile)
  @UseGuards(JwtAuthGuard)
  async getCurrentProfile(@WithCoachUserId() user: CoachUserId) {
    const result = await this.profileService.getPublicByUserId(user);

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    if (isNull(result)) {
      throw toNotFoundError(new DomainError(ErrorMessages.EntityNotFound));
    }

    return result;
  }

  @Get(apiUrls.teamCoaches)
  @UseGuards(JwtAuthGuard)
  async getTeamCoaches(
    @Param(UrlParams.OrganizationId, IDParamPipe) orgId: OrganizationId,
    @Param(UrlParams.TeamId, IDParamPipe) teamId: TeamId,
  ) {
    const result = await this.profileService.getTeamCoaches(teamId, orgId);

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return result;
  }
}
