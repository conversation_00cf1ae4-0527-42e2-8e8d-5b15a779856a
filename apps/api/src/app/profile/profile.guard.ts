import { Injectable, CanActivate, ExecutionContext } from "@nestjs/common";
import { isNull } from "lodash/fp";

import { DomainError, ErrorMessages, ErrorTypes, isError, CoachUserId, UUID } from "@mio/helpers";

import { AuthenticatedRequest } from "../auth/types";
import { ProfileService } from "./profile.service";
import { toInternalServerError } from "../shared";

@Injectable()
export class ProfileGuard implements CanActivate {
  constructor(private profileService: ProfileService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request: AuthenticatedRequest = context.switchToHttp().getRequest();

    const validatedId = UUID.parse(request.user.sub);

    if (isError(validatedId)) {
      // TODO: log
      throw toInternalServerError(new DomainError(ErrorMessages.InvalidParam));
    }

    return this.profileService.getByUserId(validatedId as CoachUserId).then((mbProfile) => {
      if (isError(mbProfile)) {
        // TODO: log
        throw toInternalServerError(mbProfile);
      }

      if (isNull(mbProfile)) {
        // TODO: log
        throw toInternalServerError({
          message: ErrorMessages.EntityNotFound,
          type: ErrorTypes.UnexpectedError,
        });
      }

      request.profile = mbProfile;
      return true;
    });
  }
}
