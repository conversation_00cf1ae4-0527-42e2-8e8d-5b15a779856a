import { Injectable } from "@nestjs/common";

import {
  isError,
  ParsingError,
  Profile,
  ProfileDto,
  PublicProfile,
  UnexpectedError,
  CoachUserId,
  DomainError,
  ErrorMessages,
  TeamId,
  OrganizationId,
} from "@mio/helpers";

import { ProfileRepository } from "./profile.repository";

@Injectable()
export class ProfileService {
  constructor(private repo: ProfileRepository) {}

  async createNewProfile(
    dto: ProfileDto,
    user: CoachUserId,
  ): Promise<Profile | UnexpectedError | DomainError | ParsingError> {
    const existingProfile = await this.getByUserId(user);

    if (isError(existingProfile)) {
      existingProfile.addContext({
        service: ProfileService.name,
        method: this.createNewProfile.name,
        operation: this.getByUserId.name,
        params: { dto, user },
      });

      return existingProfile;
    }

    if (existingProfile) {
      return new DomainError(ErrorMessages.EntityAlreadyExists, {
        service: ProfileService.name,
        method: this.createNewProfile.name,
        operation: this.getByUserId.name,
        params: { dto, user },
        message: "Profile already exists",
      });
    }

    const profile = Profile.create(dto, user);

    const result = await this.repo.create(profile);

    if (isError(result)) {
      result.addContext({
        service: ProfileService.name,
        method: this.createNewProfile.name,
        operation: this.repo.create.name,
        params: { profile, dto, user },
      });

      return result;
    }

    return profile;
  }

  async getByUserId(user: CoachUserId): Promise<Profile | null | UnexpectedError> {
    const result = await this.repo.getByUserId(user);

    if (isError(result)) {
      return new UnexpectedError(result, {
        service: ProfileService.name,
        method: this.getByUserId.name,
        operation: this.repo.getByUserId.name,
        user,
      });
    }

    return result;
  }

  async getPublicByUserId(user: CoachUserId): Promise<PublicProfile | null | UnexpectedError> {
    const result = await this.repo.getPublicProfileByUserId(user);

    if (isError(result)) {
      return new UnexpectedError(result, {
        service: ProfileService.name,
        method: this.getPublicByUserId.name,
        operation: this.repo.getPublicProfileByUserId.name,
        user,
      });
    }

    return result;
  }

  async getTeamCoaches(teamId: TeamId, orgId: OrganizationId) {
    return this.repo.getTeamCoaches(teamId, orgId);
  }
}
