import { Inject, Injectable } from "@nestjs/common";
import { ClientSession, MongoClient } from "mongodb";

import { DATABASE_CLIENT, DATABASE_CONNECTION, DBClient } from "./index";
import { CustomError, UnexpectedError, isError } from "@mio/helpers";

@Injectable()
export class BaseRepository {
  protected db: DBClient;
  protected mongoClient: MongoClient;

  constructor(
    @Inject(DATABASE_CONNECTION) db: DBClient,
    @Inject(DATABASE_CLIENT) mongoClient: MongoClient,
  ) {
    this.db = db;
    this.mongoClient = mongoClient;
  }

  protected getDb(): DBClient {
    return this.db;
  }

  protected getMongoClient(): MongoClient {
    return this.mongoClient;
  }

  public startSession(): ClientSession {
    return this.mongoClient.startSession();
  }

  /**
   * Execute operations in a transaction
   * @param operationFn Function that contains the operations to execute in the transaction
   * @returns Result of the operation function
   */
  public async executeTransaction<T>(
    operationFn: (session: ClientSession) => Promise<T>,
  ): Promise<T | CustomError> {
    const session = this.startSession();
    let result: T;

    try {
      session.startTransaction();
      result = await operationFn(session);
      await session.commitTransaction();

      return result;
    } catch (error) {
      try {
        await session.abortTransaction();
      } catch (abortError) {
        // Log the abort error but don't throw it
        console.error("Failed to abort transaction:", abortError);

        return isError(abortError)
          ? abortError.addContext({
              service: BaseRepository.name,
              method: this.executeTransaction.name,
              operation: operationFn.name,
              message: "Failed to abort transaction",
            })
          : new UnexpectedError(abortError, {
              service: BaseRepository.name,
              method: this.executeTransaction.name,
              operation: operationFn.name,
              message: "Failed to abort transaction",
            });
      }

      return isError(error)
        ? error.addContext({
            service: BaseRepository.name,
            method: this.executeTransaction.name,
            operation: operationFn.name,
            message: "Transaction failed",
          })
        : new UnexpectedError(error, {
            service: BaseRepository.name,
            method: this.executeTransaction.name,
            operation: operationFn.name,
            message: "Transaction failed",
          });
    } finally {
      try {
        await session.endSession();
      } catch (endError) {
        // Log but don't throw - we don't want to mask the original error
        console.error("Failed to end session:", endError);
      }
    }
  }
}
