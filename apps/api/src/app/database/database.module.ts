import { Mo<PERSON><PERSON> } from "@nestjs/common";
import { Db, MongoClient } from "mongodb";

import { AppConfigModule, AppConfigService } from "../config";
import { DATABASE_CONNECTION, DATABASE_CLIENT } from "./constants";

@Module({
  imports: [AppConfigModule],
  providers: [
    {
      provide: DATABASE_CLIENT,
      inject: [AppConfigService],
      useFactory: async (configService: AppConfigService): Promise<MongoClient> => {
        return await MongoClient.connect(configService.getDbHost());
      },
    },
    {
      provide: DATABASE_CONNECTION,
      inject: [AppConfigService],
      useFactory: async (configService: AppConfigService): Promise<Db> => {
        const client = await MongoClient.connect(configService.getDbHost()).then((client) =>
          client.db(undefined, { ignoreUndefined: true }),
        );
        return client;
      },
    },
  ],
  exports: [DATABASE_CONNECTION, DATABASE_CLIENT],
})
export class DatabaseModule {}
