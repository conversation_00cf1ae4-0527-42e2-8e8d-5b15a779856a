import { Injectable } from "@nestjs/common";
import { isNil } from "lodash/fp";
import { Environments } from "gocardless-nodejs/constants";

import {
  DomainError,
  ErrorMessages,
  FinancialIntegrationType,
  isError,
  OrganizationId,
  UnexpectedError,
  GCSubscription,
  ValidDate,
  CustomDate,
  GCPayment,
  GCSubscriptionStatusEnum,
} from "@mio/helpers";

import { FinancialIntegrationService } from "../financial-integration/financial-integration.service";
import { EncryptionService } from "../encryption/encryption.service";

import { environment } from "../../environments/environment";

// eslint-disable-next-line @typescript-eslint/no-var-requires
const gocardless = require("gocardless-nodejs");

@Injectable()
export class GoCardlessService {
  constructor(
    private financialIntegrationService: FinancialIntegrationService,
    private encryptionService: EncryptionService,
  ) {}

  async findSubscriptions(organizationId: OrganizationId) {
    const financialIntegration = await this.financialIntegrationService.getByOrganizationIdAndType(
      organizationId,
      FinancialIntegrationType.GoCardless,
    );

    if (isError(financialIntegration)) {
      financialIntegration.addContext({
        service: GoCardlessService.name,
        method: this.findSubscriptions.name,
        operation: this.financialIntegrationService.getByOrganizationIdAndType.name,
        organizationId,
      });

      return financialIntegration;
    }

    if (isNil(financialIntegration)) {
      return new DomainError(ErrorMessages.TeamNotFound);
    }

    const GoCardless_ACCESS_TOKEN = await this.encryptionService.decrypt(
      financialIntegration.keyEncrypted,
      financialIntegration.iv,
    );

    if (isError(GoCardless_ACCESS_TOKEN)) {
      GoCardless_ACCESS_TOKEN.addContext({
        service: GoCardlessService.name,
        method: this.findSubscriptions.name,
        operation: this.encryptionService.decrypt.name,
        organizationId,
      });

      return GoCardless_ACCESS_TOKEN;
    }

    const client = gocardless(
      GoCardless_ACCESS_TOKEN,
      environment.production === true ? Environments.Live : Environments.Sandbox,
    );

    try {
      const subscriptionsResult = await client.subscriptions.list({
        status: GCSubscriptionStatusEnum.active,
      });

      if (subscriptionsResult && subscriptionsResult.subscriptions) {
        const mbSubscriptions = GCSubscription.toEntities(subscriptionsResult.subscriptions);

        if (isError(mbSubscriptions)) {
          mbSubscriptions.addContext({
            service: GoCardlessService.name,
            method: this.findSubscriptions.name,
            operation: GCSubscription.toEntities,
            organizationId,
          });

          return mbSubscriptions;
        }

        return mbSubscriptions;
      }

      return [];
    } catch (err) {
      return new UnexpectedError(err);
    }
  }

  async findPayments(organizationId: OrganizationId, dateStart: ValidDate, dateEnd: ValidDate) {
    const financialIntegration = await this.financialIntegrationService.getByOrganizationIdAndType(
      organizationId,
      FinancialIntegrationType.GoCardless,
    );

    if (isError(financialIntegration)) {
      financialIntegration.addContext({
        service: GoCardlessService.name,
        method: this.findSubscriptions.name,
        operation: this.financialIntegrationService.getByOrganizationIdAndType.name,
        organizationId,
      });

      return financialIntegration;
    }

    if (isNil(financialIntegration)) {
      return new DomainError(ErrorMessages.TeamNotFound);
    }

    const GoCardless_ACCESS_TOKEN = await this.encryptionService.decrypt(
      financialIntegration.keyEncrypted,
      financialIntegration.iv,
    );

    if (isError(GoCardless_ACCESS_TOKEN)) {
      GoCardless_ACCESS_TOKEN.addContext({
        service: GoCardlessService.name,
        method: this.findSubscriptions.name,
        operation: this.encryptionService.decrypt.name,
        organizationId,
      });

      return GoCardless_ACCESS_TOKEN;
    }

    const client = gocardless(
      GoCardless_ACCESS_TOKEN,
      environment.production === true ? Environments.Live : Environments.Sandbox,
    );

    try {
      const paymentsResult = await client.payments.list({
        charge_date: {
          gte: CustomDate.toGoCardlessRequestDate(dateStart),
          lte: CustomDate.toGoCardlessRequestDate(dateEnd),
        },
        limit: 10000,
      });

      if (paymentsResult && paymentsResult.payments) {
        const payments = GCPayment.toEntities(paymentsResult.payments);

        if (isError(payments)) {
          payments.addContext({
            service: GoCardlessService.name,
            method: this.findPayments.name,
            operation: GCPayment.toEntities,
            organizationId,
            dateStart,
            dateEnd,
          });

          return payments;
        }

        return payments;
      }

      return [];
    } catch (err) {
      return new UnexpectedError(err);
    }
  }
}
