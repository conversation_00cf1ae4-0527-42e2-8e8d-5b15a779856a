import { Controller, Get, Param, UseGuards } from "@nestjs/common";

import {
  apiUrls,
  CustomDate,
  OrganizationId,
  PermissionsModule,
  PositiveInteger,
  UrlParams,
} from "@mio/helpers";

import { PermissionsGuard, RequiresPermissions } from "../permissions";
import { IDParamPipe } from "../shared";
import { GoCardlessService } from "./go-cardless.service";
import { JwtAuthGuard } from "../auth/jwt-auth.guard";

@Controller()
export class GoCardlessController {
  constructor(private coCardlessService: GoCardlessService) {}

  @Get(apiUrls.generalGoCardlessSubscriptions)
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  //TODO: Add the appropriate new permission; handle errors
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageOrganization])
  async findSubscriptions(@Param(UrlParams.OrganizationId, IDParamPipe) orgId: OrganizationId) {
    return this.coCardlessService.findSubscriptions(orgId);
  }

  @Get(apiUrls.generalGoCardlessPayments)
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  //TODO: Add the appropriate new permission; handle errors
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageOrganization])
  async findPayments(@Param(UrlParams.OrganizationId, IDParamPipe) orgId: OrganizationId) {
    return this.coCardlessService.findPayments(
      orgId,
      CustomDate.subDays(30 as PositiveInteger),
      CustomDate.now(),
    );
  }
}
