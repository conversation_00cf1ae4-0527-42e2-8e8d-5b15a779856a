import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { EncryptionModule } from "../encryption/encryption.module";
import { FinancialIntegrationModule } from "../financial-integration/financial-integration.module";
import { PermissionsModule } from "../permissions";

import { GoCardlessController } from "./go-cardless.controller";
import { GoCardlessService } from "./go-cardless.service";

@Module({
  imports: [EncryptionModule, FinancialIntegrationModule, PermissionsModule],
  providers: [GoCardlessService],
  controllers: [GoCardlessController],
  exports: [],
})
export class GoCardlessModule {}
