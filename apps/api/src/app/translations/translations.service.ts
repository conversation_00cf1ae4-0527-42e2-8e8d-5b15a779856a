import { Injectable } from "@nestjs/common";
import { translations as enTranslations } from "./en";
import { translations as esTranslations } from "./es";
import { EmailTranslations, TranslationKey } from "./types";

export type Locale = "en" | "es";
type InterpolationValues = Record<string, string>;

@Injectable()
export class TranslationsService {
  private translations: Record<Locale, EmailTranslations> = {
    en: enTranslations,
    es: esTranslations,
  };

  translate(
    key: TranslationKey,
    locale: Locale = "en",
    interpolation?: InterpolationValues,
  ): string {
    const keys = key.split(".");
    let translation: any = this.translations[locale];

    for (const k of keys) {
      translation = translation[k];
    }

    if (typeof translation !== "string") {
      return key;
    }

    if (interpolation) {
      return Object.entries(interpolation).reduce(
        (text, [key, value]) => text.replace(`{{${key}}}`, value),
        translation,
      );
    }

    return translation;
  }
}
