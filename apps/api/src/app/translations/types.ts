export type EmailTranslations = {
  emails: {
    invite: {
      greeting: string;
      body: string;
      thanks: string;
      signature: string;
      subject: string;
    };
    newApplicant: {
      title: string;
      body: string;
      optOut: string;
      subject: string;
    };
    playerLogin: {
      title: string;
      clickHere: string;
      validityNote: string;
      codeInstructions: string;
      subject: string;
    };
    passwordReset: {
      title: string;
      instructions: string;
      validityNote: string;
      subject: string;
    };
    marketing: {
      title: string;
      messageLabel: string;
      subject: string;
    };
  };
};

export type TranslationKey =
  | "emails.invite.greeting"
  | "emails.invite.body"
  | "emails.invite.thanks"
  | "emails.invite.signature"
  | "emails.invite.subject"
  | "emails.newApplicant.title"
  | "emails.newApplicant.body"
  | "emails.newApplicant.optOut"
  | "emails.newApplicant.subject"
  | "emails.playerLogin.title"
  | "emails.playerLogin.clickHere"
  | "emails.playerLogin.validityNote"
  | "emails.playerLogin.codeInstructions"
  | "emails.playerLogin.subject"
  | "emails.passwordReset.title"
  | "emails.passwordReset.instructions"
  | "emails.passwordReset.validityNote"
  | "emails.passwordReset.subject"
  | "emails.marketing.title"
  | "emails.marketing.messageLabel"
  | "emails.marketing.subject";
