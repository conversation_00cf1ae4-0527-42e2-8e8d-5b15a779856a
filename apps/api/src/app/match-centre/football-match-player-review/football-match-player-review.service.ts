import { Injectable } from "@nestjs/common";

import {
  isError,
  FootballMatchPlayerReview,
  FootballMatchPlan,
  Profile,
  TeamId,
} from "@mio/helpers";

import { FootballMatchPlayerReviewRepository } from "./football-match-player-review.repository";

@Injectable()
export class FootballMatchPlayerReviewService {
  constructor(private repo: FootballMatchPlayerReviewRepository) {}

  async upsertMany(dtos: FootballMatchPlayerReview.CreateDto[], coach: Profile) {
    const reviewsExist = await this.find(dtos[0].footballMatchPlanId, coach);

    if (isError(reviewsExist)) {
      return reviewsExist;
    }

    const existingReviews = reviewsExist.filter((review) =>
      dtos.some(
        (dto) =>
          dto.playerTeamProfileId === review.playerTeamProfileId &&
          dto.footballMatchPlanId === review.footballMatchPlanId &&
          review.coachId === coach.id,
      ),
    );

    if (existingReviews.length > 0) {
      await this.repo.updateMany(
        existingReviews.map((er) => {
          const dtoReview = dtos.find((dto) => dto.playerTeamProfileId === er.playerTeamProfileId);
          return {
            ...er,
            overallRating: dtoReview?.overallRating || er.overallRating,
            overallHighlights: dtoReview?.overallHighlights || er.overallHighlights,
            overallPosition: dtoReview?.overallPosition || er.overallPosition,
            overallMinutesPlayed: dtoReview?.overallMinutesPlayed || er.overallMinutesPlayed,
            attendance: dtoReview?.attendance || er.attendance,
          };
        }),
        coach.id,
      );
    }

    const newReviews = dtos.filter(
      (dto) =>
        !existingReviews.some((review) => review.playerTeamProfileId === dto.playerTeamProfileId),
    );

    const footballMatchReviews = FootballMatchPlayerReview.Entity.createMany(newReviews, coach.id);

    if (newReviews.length > 0) {
      const result = await this.repo.createMany(footballMatchReviews, coach.id);

      if (isError(result)) {
        result.addContext({
          service: FootballMatchPlayerReviewService.name,
          method: this.upsertMany.name,
          operation: this.repo.createMany.name,
          dtos,
        });

        return result;
      }
    }

    return [...existingReviews, ...footballMatchReviews];
  }

  async find(footballMatchPlanId: FootballMatchPlan.Id, coach: Profile) {
    return this.repo.getByCoachId(footballMatchPlanId, coach.id);
  }

  async getAveragePlayersRatingByTeamId(teamId: TeamId) {
    return this.repo.getAveragePlayersRatingByTeamId(teamId);
  }
}
