import { Body, Controller, Get, Param, Post, UseGuards, UsePipes } from "@nestjs/common";

import {
  apiUrls,
  isError,
  PermissionsModule,
  FootballMatchPlayerReview,
  UrlParams,
  FootballMatchPlan,
  UnexpectedError,
  Profile,
  TeamId,
} from "@mio/helpers";

import { IDParamPipe, toInternalServerError, ZodValidationPipe } from "../../shared";
import { JwtAuthGuard } from "../../auth/jwt-auth.guard";
import { PermissionsGuard, RequiresPermissions } from "../../permissions";
import { FootballMatchPlayerReviewService } from "../football-match-player-review/football-match-player-review.service";
import { WithProfile } from "../../profile/with-profile";
import { ProfileGuard } from "../../profile/profile.guard";

@Controller()
export class FootballMatchPlayerReviewController {
  constructor(private footballMatchPlayerReviewService: FootballMatchPlayerReviewService) {}

  @Post(apiUrls.footballMatchPlayersReviews)
  @UseGuards(JwtAuthGuard, PermissionsGuard, ProfileGuard)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UsePipes(new ZodValidationPipe(FootballMatchPlayerReview.Entity.createManyDto))
  async upsertFootballMatchPlayerReviews(
    @Body() dtos: FootballMatchPlayerReview.CreateDto[],
    @WithProfile() profile: Profile,
  ): Promise<FootballMatchPlayerReview.CompletePlayerReview[] | never> {
    const result = await this.footballMatchPlayerReviewService.upsertMany(dtos, profile);

    if (isError(result)) {
      switch (result.type) {
        default:
          throw toInternalServerError(result);
      }
    }

    if (!result) {
      throw toInternalServerError(new UnexpectedError(result));
    }

    return result;
  }

  @Get(apiUrls.footballMatchPlayersReviews)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard, ProfileGuard)
  async findFootballMatchReviews(
    @Param(UrlParams.FootballMatchPlanId, IDParamPipe) footballMatchPlanId: FootballMatchPlan.Id,
    @WithProfile() profile: Profile,
  ) {
    const result = await this.footballMatchPlayerReviewService.find(footballMatchPlanId, profile);

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return result;
  }

  @Get(apiUrls.footballMatchPlayersAverageReviews)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard, ProfileGuard)
  async getAveragePlayerMatchRatings(@Param(UrlParams.TeamId, IDParamPipe) teamId: TeamId) {
    const result = await this.footballMatchPlayerReviewService.getAveragePlayersRatingByTeamId(
      teamId,
    );

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return result;
  }
}
