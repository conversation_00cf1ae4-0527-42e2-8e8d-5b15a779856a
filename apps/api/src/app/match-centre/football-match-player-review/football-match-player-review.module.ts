import { Module } from "@nestjs/common";

import { AppConfigModule } from "../../config";
import { DatabaseModule } from "../../database";
import { PermissionsModule } from "../../permissions";
import { FootballMatchPlayerReviewService } from "./football-match-player-review.service";
import { FootballMatchPlayerReviewRepository } from "./football-match-player-review.repository";
import { FootballMatchPlayerReviewController } from "./football-match-player-review.controller";
import { ProfileModule } from "../../profile/profile.module";

@Module({
  imports: [DatabaseModule, AppConfigModule, PermissionsModule, ProfileModule],
  providers: [FootballMatchPlayerReviewRepository, FootballMatchPlayerReviewService],
  controllers: [FootballMatchPlayerReviewController],
  exports: [FootballMatchPlayerReviewService],
})
export class FootballMatchPlayerReviewModule {}
