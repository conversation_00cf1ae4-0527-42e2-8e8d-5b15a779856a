import { Module } from "@nestjs/common";

import { AppConfigModule } from "../../config";
import { DatabaseModule } from "../../database";
import { PermissionsModule } from "../../permissions";
import { FootballMatchPlanService } from "./football-match-plan.service";
import { FootballMatchPlanRepository } from "./football-match-plan.repository";
import { FootballMatchPlanController } from "./football-match-plan.controller";

@Module({
  imports: [DatabaseModule, AppConfigModule, PermissionsModule],
  providers: [FootballMatchPlanRepository, FootballMatchPlanService],
  controllers: [FootballMatchPlanController],
  exports: [FootballMatchPlanService],
})
export class FootballMatchPlanModule {}
