import { Injectable, NotImplementedException } from "@nestjs/common";
import { isNil } from "lodash/fp";

import {
  DomainError,
  ErrorMessages,
  isError,
  FootballMatchPlan,
  TeamId,
  OrganizationId,
} from "@mio/helpers";

import { FootballMatchPlanRepository } from "./football-match-plan.repository";

@Injectable()
export class FootballMatchPlanService {
  constructor(private repo: FootballMatchPlanRepository) {}

  async create(dto: FootballMatchPlan.CreateDto) {
    const validation = await this.validateOnlyOneMatchPerGameWeek(
      dto.teamId,
      dto.organizationId,
      dto.gameWeek,
    );

    if (isError(validation)) {
      return validation;
    }

    const footballMatchPlan = FootballMatchPlan.Entity.create(dto);

    const result = await this.repo.create(footballMatchPlan);

    if (isError(result)) {
      result.addContext({
        service: FootballMatchPlanService.name,
        method: this.create.name,
        operation: this.repo.create.name,
        dto,
      });

      return result;
    }
    return footballMatchPlan;
  }

  async update(dto: FootballMatchPlan.UpdateDto) {
    const validation = await this.validateOnlyOneMatchPerGameWeek(
      dto.teamId,
      dto.organizationId,
      dto.gameWeek,
      dto.id,
    );

    if (isError(validation)) {
      return validation;
    }

    const footballMatchPlan = await this.getById(dto.id);

    if (isError(footballMatchPlan)) {
      return footballMatchPlan;
    }

    if (isNil(footballMatchPlan)) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: FootballMatchPlanService.name,
        method: this.update.name,
        operation: this.repo.getById.name,
        dto,
      });
    }

    const updatedFootballMatchPlan = FootballMatchPlan.Entity.update(dto, footballMatchPlan);

    const result = await this.repo.update(updatedFootballMatchPlan);

    if (isError(result)) {
      result.addContext({
        service: FootballMatchPlanService.name,
        method: this.update.name,
        operation: this.repo.update.name,
        dto,
      });

      return result;
    }

    return updatedFootballMatchPlan;
  }

  async delete() {
    throw new NotImplementedException();
  }

  async getById(id: FootballMatchPlan.Id) {
    const result = await this.repo.getById(id);

    if (isError(result)) {
      result.addContext({
        service: FootballMatchPlanService.name,
        method: this.getById.name,
        operation: this.repo.getById.name,
        id,
      });
    }

    return result;
  }

  async find(teamId: TeamId) {
    const result = await this.repo.getByTeamId(teamId);

    if (isError(result)) {
      result.addContext({
        service: FootballMatchPlanService.name,
        method: this.find.name,
        operation: this.repo.getByTeamId.name,
        teamId,
      });
    }

    return result;
  }

  async validateOnlyOneMatchPerGameWeek(
    teamId: TeamId,
    organizationId: OrganizationId,
    gameWeek: number,
    id?: FootballMatchPlan.Id,
  ) {
    const plan = await this.repo.getByGameWeek(teamId, organizationId, gameWeek, id);

    if (isError(plan)) {
      plan.addContext({
        service: FootballMatchPlanService.name,
        method: this.validateOnlyOneMatchPerGameWeek.name,
        operation: this.repo.getByGameWeek.name,
        gameWeek,
      });

      return plan;
    }

    if (plan && plan.id) {
      return new DomainError(ErrorMessages.MatchAlreadyExistsInGameWeek, {
        service: FootballMatchPlanService.name,
        method: this.validateOnlyOneMatchPerGameWeek.name,
        operation: this.repo.getByGameWeek.name,
        gameWeek,
      });
    }

    return null;
  }
}
