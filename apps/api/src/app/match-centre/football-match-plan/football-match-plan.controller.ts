import { Body, Controller, Get, Param, Patch, Post, UseGuards, UsePipes } from "@nestjs/common";
import { isNil } from "lodash/fp";

import {
  apiUrls,
  DomainError,
  ErrorMessages,
  ErrorTypes,
  isError,
  PermissionsModule,
  TeamId,
  FootballMatchPlan,
  UrlParams,
} from "@mio/helpers";

import {
  IDParamPipe,
  toInternalServerError,
  toNotFoundError,
  ZodValidationPipe,
} from "../../shared";
import { JwtAuthGuard } from "../../auth/jwt-auth.guard";
import { PermissionsGuard, RequiresPermissions } from "../../permissions";
import { FootballMatchPlanService } from "./football-match-plan.service";

@Controller()
export class FootballMatchPlanController {
  constructor(private footballMatchPlanService: FootballMatchPlanService) {}

  @Post(apiUrls.footballMatchPlans)
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UsePipes(new ZodValidationPipe(FootballMatchPlan.Entity.createDto))
  async createFootballMatchPlan(
    @Body() dto: FootballMatchPlan.CreateDto,
  ): Promise<FootballMatchPlan.CompletePlan | never> {
    const result = await this.footballMatchPlanService.create(dto);

    if (isError(result)) {
      switch (result.type) {
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Patch(apiUrls.footballMatchPlan)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @UsePipes(new ZodValidationPipe(FootballMatchPlan.Entity.updateDto))
  async updateFootballMatchPlan(@Body() dto: FootballMatchPlan.UpdateDto) {
    return this.footballMatchPlanService.update(dto).then((result) => {
      if (isError(result)) {
        switch (result.type) {
          case ErrorTypes.DomainError: {
            if (result.message === ErrorMessages.EntityNotFound) {
              throw toNotFoundError(result);
            }
            throw toInternalServerError(result);
          }
          case ErrorTypes.UnexpectedError: {
            throw toInternalServerError(result);
          }
          case ErrorTypes.ParsingError: {
            throw toInternalServerError(result);
          }
          default:
            throw toInternalServerError(result);
        }
      }
      return result;
    });
  }

  @Get(apiUrls.footballMatchPlans)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async findFootballMatchPlans(@Param(UrlParams.TeamId, IDParamPipe) teamId: TeamId) {
    const result = await this.footballMatchPlanService.find(teamId);

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return result;
  }

  @Get(apiUrls.footballMatchPlan)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async getFootballMatchPlan(
    @Param(UrlParams.FootballMatchPlanId, IDParamPipe)
    footballMatchPlanId: FootballMatchPlan.Id,
  ) {
    return this.footballMatchPlanService.getById(footballMatchPlanId).then((result) => {
      if (isError(result)) {
        throw toInternalServerError(result);
      }

      if (isNil(result)) {
        throw toNotFoundError(new DomainError(ErrorMessages.EntityNotFound));
      }

      return result;
    });
  }
}
