import { Module } from "@nestjs/common";

import { AppConfigModule } from "../../config";
import { DatabaseModule } from "../../database";
import { PermissionsModule } from "../../permissions";
import { FootballMatchReviewService } from "./football-match-review.service";
import { FootballMatchReviewRepository } from "./football-match-review.repository";
import { FootballMatchReviewController } from "./football-match-review.controller";
import { FootballMatchAttemptAgainstModule } from "../football-match-attempt-against/football-match-attempt-against.module";
import { FootballMatchAttemptForModule } from "../football-match-attempt-for/football-match-attempt-for.module";

@Module({
  imports: [
    DatabaseModule,
    AppConfigModule,
    PermissionsModule,
    FootballMatchAttemptAgainstModule,
    FootballMatchAttemptForModule,
  ],
  providers: [FootballMatchReviewRepository, FootballMatchReviewService],
  controllers: [FootballMatchReviewController],
  exports: [FootballMatchReviewService],
})
export class FootballMatchReviewModule {}
