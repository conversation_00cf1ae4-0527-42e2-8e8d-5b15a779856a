import { Inject, Injectable } from "@nestjs/common";

import {
  Primitive,
  UnexpectedError,
  TeamId,
  ParsingError,
  isError,
  DomainError,
  ErrorMessages,
  FootballMatchReview,
  FootballMatchPlan,
} from "@mio/helpers";

import { DATABASE_CONNECTION, DBClient, DBCollection } from "../../database";

type FootballMatchReviewDocument = Primitive<FootballMatchReview.CompleteReview>;

export const footballMatchReviewCollection = "football-match-reviews";

@Injectable()
export class FootballMatchReviewRepository {
  private collection: DBCollection<FootballMatchReviewDocument>;

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection(footballMatchReviewCollection);
  }

  //#region Commands

  async create(review: FootballMatchReview.CompleteReview): Promise<UnexpectedError | void> {
    return this.collection
      .insertOne({
        ...review,
      })
      .then(() => undefined)
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err, {
          service: FootballMatchReviewRepository.name,
          method: this.create.name,
          operation: this.collection.insertOne.name,
        });
      });
  }

  async update(review: FootballMatchReview.CompleteReview) {
    const result = await this.collection
      .replaceOne({ id: review.id }, review)
      .then((res) => {
        if (res.modifiedCount < 1) {
          return new DomainError(ErrorMessages.EntityNotFound, {
            service: FootballMatchReviewRepository.name,
            method: this.update.name,
            operation: this.collection.replaceOne.name,
            review: review,
          });
        }
        if (res.modifiedCount > 1) {
          return new DomainError(ErrorMessages.MultipleEntitiesFound, {
            service: FootballMatchReviewRepository.name,
            method: this.update.name,
            operation: this.collection.replaceOne.name,
            review: review,
          });
        }

        return undefined;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: FootballMatchReviewRepository.name,
          method: this.update.name,
          operation: this.collection.replaceOne.name,
          review: review,
        });
      });

    if (isError(result)) {
      result.addContext({
        service: FootballMatchReviewRepository.name,
        method: this.update.name,
        operation: this.collection.replaceOne,
        payload: result,
      });

      return result;
    }

    return undefined;
  }

  //#endregion

  //#region Queries

  async getById(
    id: FootballMatchPlan.Id,
  ): Promise<UnexpectedError | ParsingError | FootballMatchReview.CompleteReview | null> {
    return this.collection
      .findOne({ footballMatchPlanId: id })
      .then((review) => {
        if (review) {
          if (!review) {
            return null;
          }

          const parsed = FootballMatchReview.Entity.toEntity(review);

          if (isError(parsed)) {
            parsed.addContext({
              service: FootballMatchReviewRepository.name,
              method: this.getById.name,
              operation: FootballMatchReview.Entity.toEntity.name,
              reviewId: id,
            });

            return parsed;
          }

          return parsed;
        }

        return null;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err, {
          service: FootballMatchReviewRepository.name,
          method: this.getById.name,
          operation: this.collection.findOne.name,
        });
      });
  }

  getByTeamId(
    teamId: TeamId,
  ): Promise<UnexpectedError | ParsingError | FootballMatchReview.CompleteReview[]> {
    return this.collection
      .find({ teamId })
      .toArray()
      .then((reviews) => {
        if (reviews) {
          const parsed = FootballMatchReview.Entity.toEntities(reviews);

          if (isError(parsed)) {
            parsed.addContext({
              service: FootballMatchReviewRepository.name,
              method: this.getByTeamId.name,
              operation: FootballMatchReview.Entity.toEntities.name,
              teamId,
            });

            return parsed;
          }

          return parsed;
        }

        return [];
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err, {
          service: FootballMatchReviewRepository.name,
          method: this.getByTeamId.name,
          operation: this.collection.find.name,
        });
      });
  }

  //#endregion
}
