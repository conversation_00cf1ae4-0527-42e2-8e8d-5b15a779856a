import { Injectable, NotImplementedException } from "@nestjs/common";
import { isNil } from "lodash/fp";

import {
  DomainError,
  ErrorMessages,
  isError,
  FootballMatchReview,
  TeamId,
  FootballMatchPlan,
  FootballMatchAttemptFor,
  FootballMatchAttemptAgainst,
} from "@mio/helpers";

import { FootballMatchReviewRepository } from "./football-match-review.repository";
import { FootballMatchAttemptForService } from "../football-match-attempt-for/football-match-attempt-for.service";
import { FootballMatchAttemptAgainstService } from "../football-match-attempt-against/football-match-attempt-against.service";

@Injectable()
export class FootballMatchReviewService {
  constructor(
    private repo: FootballMatchReviewRepository,
    private goalsScoredService: FootballMatchAttemptForService,
    private goalsConcededService: FootballMatchAttemptAgainstService,
  ) {}

  async create(dto: FootballMatchReview.CreateDto) {
    const footballMatchReview = FootballMatchReview.Entity.create(dto);

    const result = await this.repo.create(footballMatchReview);

    if (isError(result)) {
      result.addContext({
        service: FootballMatchReviewService.name,
        method: this.create.name,
        operation: this.repo.create.name,
        dto,
      });

      return result;
    }

    await this.updateAttemptsAgainst(footballMatchReview);
    await this.updateAttemptsFor(footballMatchReview);

    return footballMatchReview;
  }

  async update(dto: FootballMatchReview.UpdateDto) {
    const footballMatchReview = await this.getById(dto.footballMatchPlanId);

    if (isError(footballMatchReview)) {
      return footballMatchReview;
    }

    if (isNil(footballMatchReview)) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: FootballMatchReviewService.name,
        method: this.update.name,
        operation: this.repo.getById.name,
        dto,
      });
    }

    const updatedFootballMatchReview = FootballMatchReview.Entity.update(dto, footballMatchReview);

    const result = await this.repo.update(updatedFootballMatchReview);

    if (isError(result)) {
      result.addContext({
        service: FootballMatchReviewService.name,
        method: this.update.name,
        operation: this.repo.update.name,
        dto,
      });

      return result;
    }

    await this.updateAttemptsAgainst(updatedFootballMatchReview);
    await this.updateAttemptsFor(updatedFootballMatchReview);

    return updatedFootballMatchReview;
  }

  async updateAttemptsFor(matchReview: FootballMatchReview.CompleteReview) {
    const goalsScoredDetails = await this.goalsScoredService.findGoals(
      matchReview.teamId,
      matchReview.footballMatchPlanId,
    );

    if (isError(goalsScoredDetails)) {
      return goalsScoredDetails;
    }

    if (goalsScoredDetails.length > matchReview.goalsScored) {
      const goalsToDelete = goalsScoredDetails.slice(matchReview.goalsScored);

      for (const goal of goalsToDelete) {
        const result = await this.goalsScoredService.delete(goal.id);

        if (isError(result)) {
          return result;
        }
      }
    }

    if (goalsScoredDetails.length < matchReview.goalsScored) {
      const goalsToAdd = matchReview.goalsScored - goalsScoredDetails.length;

      for (let i = 0; i < goalsToAdd; i++) {
        const goalDetails = FootballMatchAttemptFor.Entity.create({
          organizationId: matchReview.organizationId,
          teamId: matchReview.teamId,
          footballMatchPlanId: matchReview.footballMatchPlanId,
          footballMatchReviewId: matchReview.id,
          type: FootballMatchAttemptFor.AttemptForType.goal,
        });

        const result = await this.goalsScoredService.create(goalDetails);

        if (isError(result)) {
          return result;
        }
      }
    }
  }

  async updateAttemptsAgainst(matchReview: FootballMatchReview.CompleteReview) {
    const goalsConcededDetails = await this.goalsConcededService.findGoals(
      matchReview.teamId,
      matchReview.footballMatchPlanId,
    );

    if (isError(goalsConcededDetails)) {
      return goalsConcededDetails;
    }

    if (goalsConcededDetails.length > matchReview.goalsConceded) {
      const goalsToDelete = goalsConcededDetails.slice(matchReview.goalsConceded);

      for (const goal of goalsToDelete) {
        const result = await this.goalsConcededService.delete(goal.id);

        if (isError(result)) {
          return result;
        }
      }
    }

    if (goalsConcededDetails.length < matchReview.goalsConceded) {
      const goalsToAdd = matchReview.goalsConceded - goalsConcededDetails.length;

      for (let i = 0; i < goalsToAdd; i++) {
        const goalDetails = FootballMatchAttemptAgainst.Entity.create({
          organizationId: matchReview.organizationId,
          teamId: matchReview.teamId,
          footballMatchPlanId: matchReview.footballMatchPlanId,
          footballMatchReviewId: matchReview.id,
        });

        const result = await this.goalsConcededService.create(goalDetails);

        if (isError(result)) {
          return result;
        }
      }
    }
  }

  async delete() {
    throw new NotImplementedException();
  }

  async getById(id: FootballMatchPlan.Id) {
    const result = await this.repo.getById(id);

    if (isError(result)) {
      result.addContext({
        service: FootballMatchReviewService.name,
        method: this.getById.name,
        operation: this.repo.getById.name,
        id,
      });
    }

    return result;
  }

  async find(teamId: TeamId) {
    const result = await this.repo.getByTeamId(teamId);

    if (isError(result)) {
      result.addContext({
        service: FootballMatchReviewService.name,
        method: this.find.name,
        operation: this.repo.getByTeamId.name,
        teamId,
      });
    }

    return result;
  }
}
