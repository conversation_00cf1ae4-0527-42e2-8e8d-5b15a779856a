import { Body, Controller, Get, Param, Patch, Post, UseGuards, UsePipes } from "@nestjs/common";

import {
  apiUrls,
  ErrorMessages,
  ErrorTypes,
  isError,
  PermissionsModule,
  TeamId,
  FootballMatchReview,
  UrlParams,
  FootballMatchPlan,
} from "@mio/helpers";

import {
  IDParamPipe,
  toInternalServerError,
  toNotFoundError,
  ZodValidationPipe,
} from "../../shared";
import { JwtAuthGuard } from "../../auth/jwt-auth.guard";
import { PermissionsGuard, RequiresPermissions } from "../../permissions";
import { FootballMatchReviewService } from "./football-match-review.service";

@Controller()
export class FootballMatchReviewController {
  constructor(private footballMatchReviewService: FootballMatchReviewService) {}

  @Post(apiUrls.footballMatchReviews)
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UsePipes(new ZodValidationPipe(FootballMatchReview.Entity.createDto))
  async createFootballMatchReview(
    @Body() dto: FootballMatchReview.CreateDto,
  ): Promise<FootballMatchReview.CompleteReview | never> {
    const result = await this.footballMatchReviewService.create(dto);

    if (isError(result)) {
      switch (result.type) {
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Patch(apiUrls.footballMatchReview)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @UsePipes(new ZodValidationPipe(FootballMatchReview.Entity.updateDto))
  async updateFootballMatchReview(@Body() dto: FootballMatchReview.UpdateDto) {
    return this.footballMatchReviewService.update(dto).then((result) => {
      if (isError(result)) {
        switch (result.type) {
          case ErrorTypes.DomainError: {
            if (result.message === ErrorMessages.EntityNotFound) {
              throw toNotFoundError(result);
            }
            throw toInternalServerError(result);
          }
          case ErrorTypes.UnexpectedError: {
            throw toInternalServerError(result);
          }
          case ErrorTypes.ParsingError: {
            throw toInternalServerError(result);
          }
          default:
            throw toInternalServerError(result);
        }
      }
      return result;
    });
  }

  @Get(apiUrls.footballMatchReviews)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async findFootballMatchReviews(@Param(UrlParams.TeamId, IDParamPipe) teamId: TeamId) {
    const result = await this.footballMatchReviewService.find(teamId);

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return result;
  }

  @Get(apiUrls.footballMatchReview)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async getFootballMatchReview(
    @Param(UrlParams.FootballMatchPlanId, IDParamPipe)
    footballMatchPlanId: FootballMatchPlan.Id,
  ) {
    return this.footballMatchReviewService.getById(footballMatchPlanId).then((result) => {
      if (isError(result)) {
        throw toInternalServerError(result);
      }

      return result;
    });
  }
}
