import { Modu<PERSON> } from "@nestjs/common";

import { AppConfigModule } from "../../config";
import { DatabaseModule } from "../../database";
import { PermissionsModule } from "../../permissions";
import { FootballMatchAttemptAgainstService } from "./football-match-attempt-against.service";
import { FootballMatchAttemptAgainstRepository } from "./football-match-attempt-against.repository";
import { FootballMatchAttemptAgainstController } from "./football-match-attempt-against.controller";

@Module({
  imports: [DatabaseModule, AppConfigModule, PermissionsModule],
  providers: [FootballMatchAttemptAgainstRepository, FootballMatchAttemptAgainstService],
  controllers: [FootballMatchAttemptAgainstController],
  exports: [FootballMatchAttemptAgainstService],
})
export class FootballMatchAttemptAgainstModule {}
