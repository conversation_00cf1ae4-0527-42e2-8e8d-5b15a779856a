import { Injectable } from "@nestjs/common";
import { isNil } from "lodash/fp";

import {
  DomainError,
  ErrorMessages,
  isError,
  FootballMatchAttemptAgainst,
  TeamId,
  FootballMatchPlan,
  ParsingError,
  UnexpectedError,
  FootballMatchAttemptFor,
} from "@mio/helpers";

import { FootballMatchAttemptAgainstRepository } from "./football-match-attempt-against.repository";

@Injectable()
export class FootballMatchAttemptAgainstService {
  constructor(private repo: FootballMatchAttemptAgainstRepository) {}

  async create(dto: FootballMatchAttemptAgainst.CreateDto) {
    const footballMatchAttemptAgainst = FootballMatchAttemptAgainst.Entity.create(dto);

    const result = await this.repo.create(footballMatchAttemptAgainst);

    if (isError(result)) {
      result.addContext({
        service: FootballMatchAttemptAgainstService.name,
        method: this.create.name,
        operation: this.repo.create.name,
        dto,
      });

      return result;
    }
    return footballMatchAttemptAgainst;
  }

  async update(dto: FootballMatchAttemptAgainst.UpdateDto) {
    const footballMatchAttemptAgainst = await this.getById(dto.id);

    if (isError(footballMatchAttemptAgainst)) {
      return footballMatchAttemptAgainst;
    }

    if (isNil(footballMatchAttemptAgainst)) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: FootballMatchAttemptAgainstService.name,
        method: this.update.name,
        operation: this.repo.getById.name,
        dto,
      });
    }

    const updatedFootballMatchAttemptAgainst = FootballMatchAttemptAgainst.Entity.update(
      dto,
      footballMatchAttemptAgainst,
    );

    const result = await this.repo.update(updatedFootballMatchAttemptAgainst);

    if (isError(result)) {
      result.addContext({
        service: FootballMatchAttemptAgainstService.name,
        method: this.update.name,
        operation: this.repo.update.name,
        dto,
      });

      return result;
    }

    return updatedFootballMatchAttemptAgainst;
  }

  async delete(id: FootballMatchAttemptAgainst.Id) {
    const footballMatchAttemptAgainst = await this.getById(id);

    if (isError(footballMatchAttemptAgainst)) {
      footballMatchAttemptAgainst.addContext({
        service: FootballMatchAttemptAgainstService.name,
        method: this.delete.name,
        operation: this.repo.getById.name,
        id,
      });

      return footballMatchAttemptAgainst;
    }

    if (isNil(footballMatchAttemptAgainst)) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: FootballMatchAttemptAgainstService.name,
        method: this.delete.name,
        operation: this.repo.getById.name,
        id,
      });
    }

    if (footballMatchAttemptAgainst.type === FootballMatchAttemptAgainst.AttemptAgainstType.goal) {
      return new DomainError(ErrorMessages.EntityNotDeletable, {
        service: FootballMatchAttemptAgainstService.name,
        method: this.delete.name,
        operation: this.repo.delete.name,
        id,
      });
    }

    const result = this.repo.delete(id);

    if (isError(result)) {
      result.addContext({
        service: FootballMatchAttemptAgainstService.name,
        method: this.delete.name,
        operation: this.repo.delete.name,
        id,
      });
    }

    return footballMatchAttemptAgainst;
  }
  async getById(id: FootballMatchAttemptAgainst.Id) {
    const result = await this.repo.getById(id);

    if (isError(result)) {
      result.addContext({
        service: FootballMatchAttemptAgainstService.name,
        method: this.getById.name,
        operation: this.repo.getById.name,
        id,
      });
    }

    return result;
  }

  async findExtraAttempts(teamId: TeamId, footballMatchPlanId: FootballMatchPlan.Id) {
    return this.find(teamId, footballMatchPlanId, {
      $ne: FootballMatchAttemptFor.AttemptForType.goal,
    });
  }

  async findGoals(teamId: TeamId, footballMatchPlanId: FootballMatchPlan.Id) {
    return this.find(teamId, footballMatchPlanId, FootballMatchAttemptFor.AttemptForType.goal);
  }

  async find(
    teamId: TeamId,
    footballMatchPlanId: FootballMatchPlan.Id,
    type: FootballMatchAttemptFor.AttemptForType | { $ne: FootballMatchAttemptFor.AttemptForType },
  ) {
    const result = await this.repo.getByMatchPlanId(teamId, footballMatchPlanId, type);

    if (isError(result)) {
      result.addContext({
        service: FootballMatchAttemptAgainstService.name,
        method: this.find.name,
        operation: this.repo.getByMatchPlanId.name,
        teamId,
      });
    }

    return result;
  }

  async getAggregatedTeamGoals(
    teamId: TeamId,
  ): Promise<
    FootballMatchAttemptAgainst.AggregatedTeamGoals | ParsingError | UnexpectedError | never[]
  > {
    const result = await this.repo.getAggregatedGoalsStats(teamId);

    if (isError(result)) {
      result.addContext({
        service: FootballMatchAttemptAgainstService.name,
        method: this.getAggregatedTeamGoals.name,
        operation: this.repo.getAggregatedGoalsStats.name,
        teamId,
      });
    }

    return result;
  }
}
