import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  UseGuards,
  UsePipes,
} from "@nestjs/common";

import {
  apiUrls,
  ErrorMessages,
  ErrorTypes,
  isError,
  PermissionsModule,
  TeamId,
  FootballMatchAttemptAgainst,
  UrlParams,
  FootballMatchPlan,
} from "@mio/helpers";

import {
  IDParamPipe,
  toInternalServerError,
  toNotFoundError,
  ZodValidationPipe,
} from "../../shared";
import { JwtAuthGuard } from "../../auth/jwt-auth.guard";
import { PermissionsGuard, RequiresPermissions } from "../../permissions";
import { FootballMatchAttemptAgainstService } from "./football-match-attempt-against.service";

@Controller()
export class FootballMatchAttemptAgainstController {
  constructor(private footballMatchAttemptAgainstService: FootballMatchAttemptAgainstService) {}

  @Post(apiUrls.footballMatchAttemptsAgainst)
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UsePipes(new ZodValidationPipe(FootballMatchAttemptAgainst.Entity.createDto))
  async createFootballMatchAttemptAgainst(
    @Body() dto: FootballMatchAttemptAgainst.CreateDto,
  ): Promise<FootballMatchAttemptAgainst.AttemptAgainst | never> {
    const result = await this.footballMatchAttemptAgainstService.create(dto);

    if (isError(result)) {
      switch (result.type) {
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Patch(apiUrls.footballMatchAttemptAgainst)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @UsePipes(new ZodValidationPipe(FootballMatchAttemptAgainst.Entity.updateDto))
  async updateFootballMatchAttemptAgainst(@Body() dto: FootballMatchAttemptAgainst.UpdateDto) {
    return this.footballMatchAttemptAgainstService.update(dto).then((result) => {
      if (isError(result)) {
        switch (result.type) {
          case ErrorTypes.DomainError: {
            if (result.message === ErrorMessages.EntityNotFound) {
              throw toNotFoundError(result);
            }
            throw toInternalServerError(result);
          }
          case ErrorTypes.UnexpectedError: {
            throw toInternalServerError(result);
          }
          case ErrorTypes.ParsingError: {
            throw toInternalServerError(result);
          }
          default:
            throw toInternalServerError(result);
        }
      }
      return result;
    });
  }

  @Delete(apiUrls.footballMatchAttemptAgainst)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async deleteFootballMatchAttemptFor(
    @Param(UrlParams.FootballMatchAttemptAgainstId, IDParamPipe)
    footballMatchAttemptAgainstId: FootballMatchAttemptAgainst.Id,
  ) {
    return this.footballMatchAttemptAgainstService.delete(footballMatchAttemptAgainstId);
  }

  @Get(apiUrls.footballMatchAttemptsAgainst)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async findFootballMatchAttemptsAgainst(
    @Param(UrlParams.TeamId, IDParamPipe) teamId: TeamId,
    @Param(UrlParams.FootballMatchPlanId, IDParamPipe)
    footballMatchPlanId: FootballMatchPlan.Id,
  ) {
    const result = await this.footballMatchAttemptAgainstService.findGoals(
      teamId,
      footballMatchPlanId,
    );

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return result;
  }

  @Get(apiUrls.footballMatchAttemptsAgainstExtra)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async findFootballMatchAttemptsAgainstExtra(
    @Param(UrlParams.TeamId, IDParamPipe) teamId: TeamId,
    @Param(UrlParams.FootballMatchPlanId, IDParamPipe)
    footballMatchPlanId: FootballMatchPlan.Id,
  ) {
    const result = await this.footballMatchAttemptAgainstService.findExtraAttempts(
      teamId,
      footballMatchPlanId,
    );

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return result;
  }
}
