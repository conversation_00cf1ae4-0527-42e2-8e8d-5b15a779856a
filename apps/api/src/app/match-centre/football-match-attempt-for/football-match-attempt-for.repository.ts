import { Inject, Injectable } from "@nestjs/common";

import {
  Primitive,
  UnexpectedError,
  TeamId,
  ParsingError,
  isError,
  DomainError,
  ErrorMessages,
  FootballMatchAttemptFor,
  FootballMatchPlan,
} from "@mio/helpers";

import { DATABASE_CONNECTION, DBClient, DBCollection } from "../../database";

type FootballMatchAttemptForDocument = Primitive<FootballMatchAttemptFor.AttemptFor>;

export const footballMatchAttemptForCollection = "football-match-attempts-for";

@Injectable()
export class FootballMatchAttemptForRepository {
  private collection: DBCollection<FootballMatchAttemptForDocument>;

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection(footballMatchAttemptForCollection);
  }

  //#region Commands

  async create(plan: FootballMatchAttemptFor.AttemptFor): Promise<UnexpectedError | void> {
    return this.collection
      .insertOne({
        ...plan,
      })
      .then(() => undefined)
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err, {
          service: FootballMatchAttemptForRepository.name,
          method: this.create.name,
          operation: this.collection.insertOne.name,
        });
      });
  }

  async update(plan: FootballMatchAttemptFor.AttemptFor) {
    const result = await this.collection
      .replaceOne({ id: plan.id }, plan)
      .then((res) => {
        if (res.modifiedCount < 1) {
          return new DomainError(ErrorMessages.EntityNotFound, {
            service: FootballMatchAttemptForRepository.name,
            method: this.update.name,
            operation: this.collection.replaceOne.name,
            plan: plan,
          });
        }
        if (res.modifiedCount > 1) {
          return new DomainError(ErrorMessages.MultipleEntitiesFound, {
            service: FootballMatchAttemptForRepository.name,
            method: this.update.name,
            operation: this.collection.replaceOne.name,
            plan: plan,
          });
        }

        return undefined;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: FootballMatchAttemptForRepository.name,
          method: this.update.name,
          operation: this.collection.replaceOne.name,
          plan: plan,
        });
      });

    if (isError(result)) {
      result.addContext({
        service: FootballMatchAttemptForRepository.name,
        method: this.update.name,
        operation: this.collection.replaceOne,
        payload: result,
      });

      return result;
    }

    return undefined;
  }

  async delete(id: FootballMatchAttemptFor.Id) {
    return this.collection
      .deleteOne({ id })
      .then((res) => {
        if (res.deletedCount < 1) {
          return new DomainError(ErrorMessages.EntityNotFound, {
            service: FootballMatchAttemptForRepository.name,
            method: this.delete.name,
            operation: this.collection.deleteOne.name,
            planId: id,
          });
        }

        if (res.deletedCount > 1) {
          return new DomainError(ErrorMessages.MultipleEntitiesFound, {
            service: FootballMatchAttemptForRepository.name,
            method: this.delete.name,
            operation: this.collection.deleteOne.name,
            planId: id,
          });
        }

        return undefined;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: FootballMatchAttemptForRepository.name,
          method: this.delete.name,
          operation: this.collection.deleteOne.name,
          planId: id,
        });
      });
  }

  //#endregion

  //#region Queries

  async getById(
    id: FootballMatchAttemptFor.Id,
  ): Promise<UnexpectedError | ParsingError | FootballMatchAttemptFor.AttemptFor | null> {
    return this.collection
      .findOne({ id })
      .then((plan) => {
        if (plan) {
          const parsed = FootballMatchAttemptFor.Entity.toEntity(plan);
          if (isError(parsed)) {
            parsed.addContext({
              service: FootballMatchAttemptForRepository.name,
              method: this.getById.name,
              operation: FootballMatchAttemptFor.Entity.toEntity.name,
              planId: id,
            });

            return parsed;
          }

          return parsed;
        }
        return null;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err, {
          service: FootballMatchAttemptForRepository.name,
          method: this.getById.name,
          operation: this.collection.findOne.name,
        });
      });
  }

  getByMatchPlanId(
    teamId: TeamId,
    footballMatchPlanId: FootballMatchPlan.Id,
    type: FootballMatchAttemptFor.AttemptForType | { $ne: FootballMatchAttemptFor.AttemptForType },
  ): Promise<UnexpectedError | ParsingError | FootballMatchAttemptFor.AttemptFor[]> {
    return this.collection
      .find({ teamId, footballMatchPlanId, type })
      .toArray()
      .then((plans) => {
        if (plans) {
          const parsed = FootballMatchAttemptFor.Entity.toEntities(plans);

          if (isError(parsed)) {
            parsed.addContext({
              service: FootballMatchAttemptForRepository.name,
              method: this.getByMatchPlanId.name,
              operation: FootballMatchAttemptFor.Entity.toEntities.name,
              teamId,
            });

            return parsed;
          }

          return parsed;
        }

        return [];
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err, {
          service: FootballMatchAttemptForRepository.name,
          method: this.getByMatchPlanId.name,
          operation: this.collection.find.name,
        });
      });
  }

  async getAggregatedGoalsStats(teamId: TeamId) {
    return this.collection
      .aggregate([
        {
          $match: { teamId, type: FootballMatchAttemptFor.AttemptForType.goal },
        },
        {
          $lookup: {
            from: "football-match-plans",
            localField: "footballMatchPlanId",
            foreignField: "id",
            pipeline: [
              {
                $project: {
                  _id: 0,
                  gameWeek: 1,
                },
              },
            ],
            as: "gameWeek",
          },
        },
        {
          $unwind: "$gameWeek",
        },
        {
          $set: {
            gameWeek: "$gameWeek.gameWeek",
          },
        },
      ])
      .toArray()
      .then((stats) => {
        if (stats && stats.length > 0) {
          const aggregatedStats = FootballMatchAttemptFor.Entity.toScoredGoalsWithGameWeek(stats);

          if (isError(aggregatedStats)) {
            aggregatedStats.addContext({
              service: FootballMatchAttemptForRepository.name,
              method: this.getAggregatedGoalsStats.name,
              operation: FootballMatchAttemptFor.Entity.toScoredGoalsWithGameWeek.name,
              teamId,
            });

            return aggregatedStats;
          }

          const parsed = FootballMatchAttemptFor.Entity.aggregateTeamGoals(aggregatedStats);

          if (isError(parsed)) {
            parsed.addContext({
              service: FootballMatchAttemptForRepository.name,
              method: this.getByMatchPlanId.name,
              operation: FootballMatchAttemptFor.Entity.toEntities.name,
              teamId,
            });

            return parsed;
          }

          return parsed;
        }

        return [];
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: FootballMatchAttemptForRepository.name,
          method: this.getAggregatedGoalsStats.name,
          operation: this.collection.aggregate.name,
        });
      });
  }

  //#endregion
}
