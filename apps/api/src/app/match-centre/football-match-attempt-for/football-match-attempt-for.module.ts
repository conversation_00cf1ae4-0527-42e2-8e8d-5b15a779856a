import { Modu<PERSON> } from "@nestjs/common";

import { AppConfigModule } from "../../config";
import { DatabaseModule } from "../../database";
import { PermissionsModule } from "../../permissions";
import { FootballMatchAttemptForService } from "./football-match-attempt-for.service";
import { FootballMatchAttemptForRepository } from "./football-match-attempt-for.repository";
import { FootballMatchAttemptForController } from "./football-match-attempt-for.controller";

@Module({
  imports: [DatabaseModule, AppConfigModule, PermissionsModule],
  providers: [FootballMatchAttemptForRepository, FootballMatchAttemptForService],
  controllers: [FootballMatchAttemptForController],
  exports: [FootballMatchAttemptForService],
})
export class FootballMatchAttemptForModule {}
