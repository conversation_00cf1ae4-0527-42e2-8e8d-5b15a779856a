import { Injectable } from "@nestjs/common";
import { isNil } from "lodash/fp";

import {
  DomainError,
  ErrorMessages,
  isError,
  FootballMatchAttemptFor,
  TeamId,
  FootballMatchPlan,
  UnexpectedError,
  ParsingError,
} from "@mio/helpers";

import { FootballMatchAttemptForRepository } from "./football-match-attempt-for.repository";

@Injectable()
export class FootballMatchAttemptForService {
  constructor(private repo: FootballMatchAttemptForRepository) {}

  async create(dto: FootballMatchAttemptFor.CreateDto) {
    const footballMatchAttemptFor = FootballMatchAttemptFor.Entity.create(dto);

    const result = await this.repo.create(footballMatchAttemptFor);

    if (isError(result)) {
      result.addContext({
        service: FootballMatchAttemptForService.name,
        method: this.create.name,
        operation: this.repo.create.name,
        dto,
      });

      return result;
    }
    return footballMatchAttemptFor;
  }

  async update(dto: FootballMatchAttemptFor.UpdateDto) {
    const footballMatchAttemptFor = await this.getById(dto.id);

    if (isError(footballMatchAttemptFor)) {
      return footballMatchAttemptFor;
    }

    if (isNil(footballMatchAttemptFor)) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: FootballMatchAttemptForService.name,
        method: this.update.name,
        operation: this.repo.getById.name,
        dto,
      });
    }

    const updatedFootballMatchAttemptFor = FootballMatchAttemptFor.Entity.update(
      dto,
      footballMatchAttemptFor,
    );

    const result = await this.repo.update(updatedFootballMatchAttemptFor);

    if (isError(result)) {
      result.addContext({
        service: FootballMatchAttemptForService.name,
        method: this.update.name,
        operation: this.repo.update.name,
        dto,
      });

      return result;
    }

    return updatedFootballMatchAttemptFor;
  }

  async delete(id: FootballMatchAttemptFor.Id) {
    const footballMatchAttemptFor = await this.getById(id);

    if (isError(footballMatchAttemptFor)) {
      footballMatchAttemptFor.addContext({
        service: FootballMatchAttemptForService.name,
        method: this.delete.name,
        operation: this.repo.getById.name,
        id,
      });

      return footballMatchAttemptFor;
    }

    if (isNil(footballMatchAttemptFor)) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: FootballMatchAttemptForService.name,
        method: this.delete.name,
        operation: this.repo.getById.name,
        id,
      });
    }

    if (footballMatchAttemptFor.type === FootballMatchAttemptFor.AttemptForType.goal) {
      return new DomainError(ErrorMessages.EntityNotDeletable, {
        service: FootballMatchAttemptForService.name,
        method: this.delete.name,
        operation: this.repo.delete.name,
        id,
      });
    }

    const result = this.repo.delete(id);

    if (isError(result)) {
      result.addContext({
        service: FootballMatchAttemptForService.name,
        method: this.delete.name,
        operation: this.repo.delete.name,
        id,
      });
    }

    return footballMatchAttemptFor;
  }

  async getById(id: FootballMatchAttemptFor.Id) {
    const result = await this.repo.getById(id);

    if (isError(result)) {
      result.addContext({
        service: FootballMatchAttemptForService.name,
        method: this.getById.name,
        operation: this.repo.getById.name,
        id,
      });
    }

    return result;
  }

  async findGoals(teamId: TeamId, footballMatchPlanId: FootballMatchPlan.Id) {
    return this.find(teamId, footballMatchPlanId, FootballMatchAttemptFor.AttemptForType.goal);
  }

  async findExtraAttempts(teamId: TeamId, footballMatchPlanId: FootballMatchPlan.Id) {
    return this.find(teamId, footballMatchPlanId, {
      $ne: FootballMatchAttemptFor.AttemptForType.goal,
    });
  }

  private async find(
    teamId: TeamId,
    footballMatchPlanId: FootballMatchPlan.Id,
    type: FootballMatchAttemptFor.AttemptForType | { $ne: FootballMatchAttemptFor.AttemptForType },
  ) {
    const result = await this.repo.getByMatchPlanId(teamId, footballMatchPlanId, type);

    if (isError(result)) {
      result.addContext({
        service: FootballMatchAttemptForService.name,
        method: this.find.name,
        operation: this.repo.getByMatchPlanId.name,
        teamId,
      });
    }

    return result;
  }

  async getAggregatedTeamGoals(
    teamId: TeamId,
  ): Promise<
    FootballMatchAttemptFor.AggregatedTeamGoals | ParsingError | UnexpectedError | never[]
  > {
    const result = await this.repo.getAggregatedGoalsStats(teamId);

    if (isError(result)) {
      result.addContext({
        service: FootballMatchAttemptForService.name,
        method: this.getAggregatedTeamGoals.name,
        operation: this.repo.getAggregatedGoalsStats.name,
        teamId,
      });
    }

    return result;
  }
}
