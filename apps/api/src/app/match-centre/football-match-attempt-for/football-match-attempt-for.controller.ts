import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
  UsePipes,
} from "@nestjs/common";

import {
  apiUrls,
  ErrorMessages,
  ErrorTypes,
  isError,
  PermissionsModule,
  TeamId,
  FootballMatchAttemptFor,
  UrlParams,
  FootballMatchPlan,
  UrlQuery,
} from "@mio/helpers";

import {
  IDParamPipe,
  toInternalServerError,
  toNotFoundError,
  ZodValidationPipe,
} from "../../shared";
import { JwtAuthGuard } from "../../auth/jwt-auth.guard";
import { PermissionsGuard, RequiresPermissions } from "../../permissions";
import { FootballMatchAttemptForService } from "./football-match-attempt-for.service";

@Controller()
export class FootballMatchAttemptForController {
  constructor(private footballMatchAttemptForService: FootballMatchAttemptForService) {}

  @Post(apiUrls.footballMatchAttemptsFor)
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UsePipes(new ZodValidationPipe(FootballMatchAttemptFor.Entity.createDto))
  async createFootballMatchAttemptFor(
    @Body() dto: FootballMatchAttemptFor.CreateDto,
  ): Promise<FootballMatchAttemptFor.AttemptFor | never> {
    const result = await this.footballMatchAttemptForService.create(dto);

    if (isError(result)) {
      switch (result.type) {
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Patch(apiUrls.footballMatchAttemptFor)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @UsePipes(new ZodValidationPipe(FootballMatchAttemptFor.Entity.updateDto))
  async updateFootballMatchAttemptFor(@Body() dto: FootballMatchAttemptFor.UpdateDto) {
    return this.footballMatchAttemptForService.update(dto).then((result) => {
      if (isError(result)) {
        switch (result.type) {
          case ErrorTypes.DomainError: {
            if (result.message === ErrorMessages.EntityNotFound) {
              throw toNotFoundError(result);
            }
            throw toInternalServerError(result);
          }
          case ErrorTypes.UnexpectedError: {
            throw toInternalServerError(result);
          }
          case ErrorTypes.ParsingError: {
            throw toInternalServerError(result);
          }
          default:
            throw toInternalServerError(result);
        }
      }
      return result;
    });
  }

  @Get(apiUrls.footballMatchAttemptsFor)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async findFootballMatchAttemptsFor(
    @Param(UrlParams.TeamId, IDParamPipe) teamId: TeamId,
    @Param(UrlParams.FootballMatchPlanId, IDParamPipe)
    footballMatchPlanId: FootballMatchPlan.Id,
  ) {
    const result = await this.footballMatchAttemptForService.findGoals(teamId, footballMatchPlanId);

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return result;
  }

  @Get(apiUrls.footballMatchAttemptsForExtra)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async findFootballMatchAttemptsForExtra(
    @Param(UrlParams.TeamId, IDParamPipe) teamId: TeamId,
    @Param(UrlParams.FootballMatchPlanId, IDParamPipe)
    footballMatchPlanId: FootballMatchPlan.Id,
  ) {
    const result = await this.footballMatchAttemptForService.findExtraAttempts(
      teamId,
      footballMatchPlanId,
    );

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return result;
  }

  @Delete(apiUrls.footballMatchAttemptFor)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async deleteFootballMatchAttemptFor(
    @Param(UrlParams.FootballMatchAttemptForId, IDParamPipe)
    footballMatchAttemptForId: FootballMatchAttemptFor.Id,
  ) {
    return this.footballMatchAttemptForService.delete(footballMatchAttemptForId);
  }
}
