import { Injectable, CanActivate, ExecutionContext } from "@nestjs/common";

import {
  Assets,
  DomainError,
  ErrorMessages,
  PlayerUser,
  UnexpectedError,
  UnknownRecord,
  UrlParams,
  isError,
} from "@mio/helpers";

import { AuthenticatedRequest } from "../auth/types";
import { toBadRequest, toForbiddenError, toInternalServerError } from "../shared";
import { PlayerService } from "../player/player.service";
import { AssetsSharedService } from "./assets-shared.service";
import { userKey } from "../player-users/player-user.guard";

@Injectable()
export class PlayerOwnsImageGuard implements CanActivate {
  constructor(private playerService: PlayerService, private assetService: AssetsSharedService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request: AuthenticatedRequest = context.switchToHttp().getRequest();
    const params = (request.params || {}) as UnknownRecord;

    const imageId = params[UrlParams.PlayerImageId];

    if (request[userKey]) {
      const parsedImageId = Assets.Image.Entity.toImageId(imageId);

      if (isError(parsedImageId)) {
        parsedImageId.addContext({
          service: PlayerOwnsImageGuard.name,
          message: "Invalid ImageId",
          imageId,
        });

        throw toBadRequest(parsedImageId);
      }

      const image = await this.assetService.getImageById(parsedImageId);

      const parsedUser = PlayerUser.Entity.toEntity(request[userKey]);

      if (isError(parsedUser)) {
        parsedUser.addContext({
          service: PlayerOwnsImageGuard.name,
          message: "Invalid User. Forgot to use PlayerUserGuard?",
          user: request[userKey],
        });

        throw toBadRequest(parsedUser);
      }

      const relatedPlayers = await this.playerService.findRelatedPlayers(
        parsedUser.authentication.email,
      );

      if (isError(relatedPlayers)) {
        relatedPlayers.addContext({
          service: PlayerOwnsImageGuard.name,
          method: PlayerOwnsImageGuard.prototype.canActivate.name,
          operation: this.playerService.findRelatedPlayers.name,
          params: { email: parsedUser.authentication.email },
        });

        throw toBadRequest(relatedPlayers);
      }

      if (isError(image)) {
        image.addContext({
          service: PlayerOwnsImageGuard.name,
          method: PlayerOwnsImageGuard.prototype.canActivate.name,
          operation: this.assetService.getImageById.name,
          params: { imageId },
        });

        throw toBadRequest(image);
      }

      if (
        relatedPlayers.some(
          (player) =>
            image?.ownership &&
            "playerId" in image.ownership &&
            player.id === image?.ownership.playerId,
        )
      ) {
        return true;
      }

      throw toForbiddenError(
        new DomainError(ErrorMessages.PermissionDenied, {
          service: PlayerOwnsImageGuard.name,
        }),
      );
    } else {
      throw toInternalServerError(
        new UnexpectedError(ErrorMessages.EntityNotFound, {
          service: PlayerOwnsImageGuard.name,
          message: "User not found. Forgot to use PlayerUserGuard?",
        }),
      );
    }
  }
}
