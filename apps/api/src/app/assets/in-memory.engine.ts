import * as multer from "multer";
import { Request } from "express";
import concat from "concat-stream";

import { CustomFile } from "@mio/helpers";

export class MemoryStorageEngine implements multer.StorageEngine {
  _handleFile(
    _req: Request,
    file: Express.Multer.File,
    cb: (error: Error | null, file?: CustomFile.RawFile) => void,
  ): void {
    file.stream.pipe(
      concat({ encoding: "buffer" }, function (data) {
        const result = {
          arrayBuffer: data,
          size: data.length,
          name: file.originalname,
          stream: file.stream,
          type: file.mimetype,
        } satisfies CustomFile.RawFile;

        cb(null, result);
      }),
    );
  }

  _removeFile(
    _req: Request,
    _file: Express.Multer.File & { name: string },
    cb: (error: Error | null) => void,
  ): void {
    cb(null);
  }
}
