import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { MulterModule } from "@nestjs/platform-express";

import { DatabaseModule } from "../database";
import { AssetsController } from "./assets.controller";
import { MemoryStorageEngine } from "./in-memory.engine";
import { PlayerUsersModule } from "../player-users/player-users.module";
import { AssetsService } from "./assets.service";
import { ImageRepository } from "./image.repository";
import { FileService } from "./file.service";
import { AppConfigModule } from "../config";
import { AssetsSharedService } from "./assets-shared.service";

@Module({
  controllers: [AssetsController],
  imports: [
    DatabaseModule,
    AppConfigModule,
    MulterModule.register({
      storage: new MemoryStorageEngine(),
    }),
    PlayerUsersModule,
  ],
  providers: [AssetsService, AssetsSharedService, ImageRepository, FileService],
})
export class AssetsModule {}
