import { Injectable } from "@nestjs/common";

import { Assets, ParsingError, UnexpectedError, isError } from "@mio/helpers";

import { ImageRepository } from "./image.repository";

@Injectable()
export class AssetsSharedService {
  constructor(private imageRepo: ImageRepository) {}

  async getImageById(
    id: Assets.Image.ImageId,
  ): Promise<Assets.Image.ImageAsset | null | ParsingError | UnexpectedError> {
    const result = await this.imageRepo.getById(id);

    if (isError(result)) {
      result.addContext({
        service: AssetsSharedService.name,
        method: AssetsSharedService.prototype.getImageById.name,
        operation: this.imageRepo.getById.name,
        params: { id },
      });

      return result;
    }

    return result;
  }

  async getImagesByIds(
    ids: Assets.Image.ImageId[],
  ): Promise<ParsingError | UnexpectedError | Assets.Image.ImageAsset[]> {
    const results = await this.imageRepo.getByIds(ids);

    if (isError(results)) {
      results.addContext({
        service: AssetsSharedService.name,
        method: AssetsSharedService.prototype.getImagesByIds.name,
        operation: this.imageRepo.getByIds.name,
        params: { ids },
      });

      return results;
    }

    return results;
  }
}
