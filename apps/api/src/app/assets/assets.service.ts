import { Injectable } from "@nestjs/common";

import {
  Assets,
  CustomError,
  DomainError,
  ErrorMessages,
  ParsingError,
  PlayerId,
  UnexpectedError,
  isError,
} from "@mio/helpers";

import { ImageRepository } from "./image.repository";
import { FileService } from "./file.service";

@Injectable()
export class AssetsService {
  constructor(private imageRepo: ImageRepository, private fileService: FileService) {}

  async uploadPlayerImage(
    file: Assets.Image.ParsedFile,
    playerId: PlayerId,
    dto: Assets.Image.CreateDto,
  ) {
    const imageName = Assets.Image.Entity.generateName(file.type);

    const imageUrl = await this.fileService.uploadFile(file, imageName);

    if (isError(imageUrl)) {
      imageUrl.addContext({
        service: AssetsService.name,
        method: AssetsService.prototype.uploadPlayerImage.name,
        operation: this.fileService.uploadFile.name,
        params: { playerId, file: file.name },
      });

      return imageUrl;
    }

    const image = Assets.Image.Entity.create(dto, file, imageUrl, imageName, {
      type: Assets.Ownership.Type.Player,
      playerId,
    });

    const dbResult = await this.imageRepo.create(image);

    if (isError(dbResult)) {
      dbResult.addContext({
        service: AssetsService.name,
        method: AssetsService.prototype.uploadPlayerImage.name,
        operation: this.imageRepo.create.name,
        params: { playerId, file: file.name },
      });

      return dbResult;
    }

    return image;
  }

  async deleteImage(
    imageId: Assets.Image.ImageId,
  ): Promise<void | DomainError | UnexpectedError | ParsingError> {
    const image = await this.imageRepo.getById(imageId);

    if (isError(image)) {
      image.addContext({
        service: AssetsService.name,
        method: AssetsService.prototype.deleteImage.name,
        operation: this.imageRepo.getById.name,
        params: { imageId },
      });

      return image;
    }

    if (!image) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: AssetsService.name,
        method: AssetsService.prototype.deleteImage.name,
        params: { imageId },
      });
    }

    const fileSystemOperation = await this.fileService.deleteFile(image);

    if (isError(fileSystemOperation)) {
      fileSystemOperation.addContext({
        service: AssetsService.name,
        method: AssetsService.prototype.deleteImage.name,
        operation: this.fileService.deleteFile.name,
        params: { imageId },
      });

      return fileSystemOperation;
    }

    const dbOperation = await this.imageRepo.delete(imageId);

    if (isError(dbOperation)) {
      dbOperation.addContext({
        service: AssetsService.name,
        method: AssetsService.prototype.deleteImage.name,
        operation: this.imageRepo.delete.name,
        params: { imageId },
      });

      return dbOperation;
    }

    return fileSystemOperation;
  }

  async getImageById(
    id: Assets.Image.ImageId,
  ): Promise<Assets.Image.ImageAsset | null | CustomError> {
    const image = await this.imageRepo.getById(id);

    if (isError(image)) {
      image.addContext({
        service: AssetsService.name,
        method: AssetsService.prototype.getImageById.name,
        operation: this.imageRepo.getById.name,
        params: { id },
      });

      return image;
    }

    return image;
  }
}
