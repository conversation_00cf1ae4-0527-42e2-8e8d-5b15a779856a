/**
 * class FileExceptionFilter
 * normalizes Multer errors into our own domain errors
 */

import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  PayloadTooLargeException,
} from "@nestjs/common";
import { Response } from "express";

import { DomainError, ErrorMessages } from "@mio/helpers";

@Catch(HttpException)
export class FileExceptionFilter implements ExceptionFilter {
  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const status = exception.getStatus();

    if (exception instanceof PayloadTooLargeException) {
      return response.status(status).json(new DomainError(ErrorMessages.FileTooLarge));
    }

    return response.status(status).json(exception.getResponse());
  }
}
