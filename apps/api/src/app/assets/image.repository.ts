import { Inject, Injectable } from "@nestjs/common";

import {
  Primitive,
  UnexpectedError,
  isError,
  DomainError,
  ErrorMessages,
  Assets,
  ParsingError,
} from "@mio/helpers";

import { DATABASE_CONNECTION, DBClient, DBCollection } from "../database";

type ImageAssetDocument = Primitive<Assets.Image.ImageAsset>;

export const IMAGE_ASSETS_COLLECTION = "image-assets";

@Injectable()
export class ImageRepository {
  private collection: DBCollection<ImageAssetDocument>;

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection(IMAGE_ASSETS_COLLECTION);
  }

  async create(image: Assets.Image.ImageAsset): Promise<UnexpectedError | void> {
    return this.collection
      .insertOne({ ...image })
      .then(() => undefined)
      .catch((err) => {
        return new UnexpectedError(err, {
          service: ImageRepository.name,
          method: ImageRepository.prototype.create.name,
          operation: this.collection.insertOne.name,
          image,
        });
      });
  }

  async update(image: Assets.Image.ImageAsset): Promise<UnexpectedError | DomainError | void> {
    const result = await this.collection
      .updateOne({ id: image.id }, { $set: image })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err, {
          service: ImageRepository.name,
          method: ImageRepository.prototype.update.name,
          operation: this.collection.updateOne.name,
          image,
        });
      });

    if (isError(result)) {
      return result;
    }

    if (result.matchedCount < 1) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: ImageRepository.name,
        method: ImageRepository.prototype.update.name,
        operation: this.collection.updateOne.name,
        image,
      });
    }

    if (result.matchedCount > 1) {
      return new DomainError(ErrorMessages.MultipleEntitiesFound, {
        service: ImageRepository.name,
        method: ImageRepository.prototype.update.name,
        operation: this.collection.updateOne.name,
        image,
      });
    }

    return undefined;
  }

  async delete(imageId: Assets.Image.ImageId): Promise<UnexpectedError | void> {
    return this.collection
      .deleteOne({
        id: imageId,
      })
      .then(() => undefined)
      .catch((err) => {
        return new UnexpectedError(err, {
          service: ImageRepository.name,
          method: ImageRepository.prototype.delete.name,
          operation: this.collection.deleteOne.name,
          imageId,
        });
      });
  }

  async getByIds(
    ids: Assets.Image.ImageId[],
  ): Promise<ParsingError | UnexpectedError | Assets.Image.ImageAsset[]> {
    return this.collection
      .find({ id: { $in: ids } })
      .toArray()
      .then((results) => {
        const parsed = Assets.Image.Entity.toEntities(results);

        if (isError(parsed)) {
          parsed.addContext({
            service: ImageRepository.name,
            method: ImageRepository.prototype.getByIds.name,
            operation: this.collection.find.name,
            ids,
          });
        }

        return parsed;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: ImageRepository.name,
          method: ImageRepository.prototype.getByIds.name,
          operation: this.collection.find.name,
          ids,
        });
      });
  }

  async getById(
    id: Assets.Image.ImageId,
  ): Promise<UnexpectedError | ParsingError | Assets.Image.ImageAsset | null> {
    return this.collection
      .findOne({ id })
      .then((result) => {
        if (result) {
          const asEntity = Assets.Image.Entity.toEntity(result);
          if (isError(asEntity)) {
            asEntity.addContext({
              service: ImageRepository.name,
              method: ImageRepository.prototype.getById.name,
              operation: this.collection.findOne.name,
              id,
            });
          }

          return asEntity;
        }
        return null;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: ImageRepository.name,
          method: ImageRepository.prototype.getById.name,
          operation: this.collection.findOne.name,
          id,
        });
      });
  }
}
