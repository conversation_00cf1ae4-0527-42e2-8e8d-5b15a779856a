import fs from "fs/promises";
import path from "path";
import { Injectable } from "@nestjs/common";
import { BlobServiceClient, BlockBlobClient } from "@azure/storage-blob";

import {
  Assets,
  CustomUrl,
  DomainError,
  ParsingError,
  UnexpectedError,
  isError,
} from "@mio/helpers";

import { environment } from "../../environments/environment";

import { AppConfigService } from "../config";

@Injectable()
export class FileService {
  private readonly containerName = "file-uploads";

  constructor(private readonly configService: AppConfigService) {}

  async uploadFile(
    file: Assets.Image.ParsedFile,
    filename: string,
  ): Promise<CustomUrl.Type | UnexpectedError | ParsingError> {
    if (environment.useDiskFileStorage) {
      const result = await this._uploadFileToLocalDisk(file, filename);

      if (isError(result)) {
        result.addContext({
          service: FileService.name,
          method: FileService.prototype.uploadFile.name,
          operation: this._uploadFileToLocalDisk.name,

          filename,
        });
      }

      return result;
    }

    const result = await this._uploadFileToAzure(file, filename);

    if (isError(result)) {
      result.addContext({
        service: FileService.name,
        method: FileService.prototype.uploadFile.name,
        operation: this._uploadFileToAzure.name,

        filename,
      });
    }

    return result;
  }

  async deleteFile(file: Assets.Image.ImageAsset): Promise<void | DomainError | UnexpectedError> {
    if (environment.useDiskFileStorage) {
      const result = await this._deleteFileFromLocalDisk(file);

      if (isError(result)) {
        result.addContext({
          service: FileService.name,
          method: FileService.prototype.deleteFile.name,
          operation: this._deleteFileFromLocalDisk.name,
          file,
        });
      }

      return result;
    }

    const result = await this._deleteFileFromAzure(file.name);

    if (isError(result)) {
      result.addContext({
        service: FileService.name,
        method: FileService.prototype.deleteFile.name,
        operation: this._deleteFileFromAzure.name,
        file,
      });
    }

    return result;
  }

  private async _uploadFileToAzure(
    file: Assets.Image.ParsedFile,
    filename: string,
  ): Promise<CustomUrl.Type | ParsingError | UnexpectedError> {
    const blockBlobClient = await this.getAzureBlobClient(filename);

    if (isError(blockBlobClient)) {
      blockBlobClient.addContext({
        service: FileService.name,
        method: FileService.prototype._uploadFileToAzure.name,
        operation: this.getAzureBlobClient.name,
        filename,
      });

      return blockBlobClient;
    }

    const upload = await blockBlobClient.uploadData(file.arrayBuffer).catch(
      (err) =>
        new UnexpectedError(err, {
          service: FileService.name,
          method: FileService.prototype._uploadFileToAzure.name,
          operation: blockBlobClient.uploadData.name,
          filename,
        }),
    );

    if (isError(upload)) {
      return upload;
    }

    const fileUrl = CustomUrl.toUrl(blockBlobClient.url);

    if (isError(fileUrl)) {
      fileUrl.addContext({
        service: FileService.name,
        method: FileService.prototype._uploadFileToAzure.name,
        operation: CustomUrl.toUrl.name,
        url: blockBlobClient.url,
      });
    }

    return fileUrl;
  }

  private async _deleteFileFromAzure(file_name: string): Promise<void | UnexpectedError> {
    const blockBlobClient = await this.getAzureBlobClient(file_name);

    if (isError(blockBlobClient)) {
      blockBlobClient.addContext({
        service: FileService.name,
        method: FileService.prototype._deleteFileFromAzure.name,
        operation: this.getAzureBlobClient.name,
        file_name,
      });

      return blockBlobClient;
    }

    const result = await blockBlobClient.deleteIfExists().catch(
      (err) =>
        new UnexpectedError(err, {
          service: FileService.name,
          method: FileService.prototype._deleteFileFromAzure.name,
          operation: blockBlobClient.deleteIfExists.name,
          file_name,
        }),
    );

    if (isError(result)) {
      return result;
    }

    return undefined;
  }

  private async getAzureBlobClient(imageName: string): Promise<BlockBlobClient | UnexpectedError> {
    try {
      const connectionString = this.configService.getAzureBlobStorageConnectionString();
      const blobClientService = BlobServiceClient.fromConnectionString(connectionString);

      const containerClient = blobClientService.getContainerClient(this.containerName);
      const blockBlobClient = containerClient.getBlockBlobClient(imageName);

      return blockBlobClient;
    } catch (err) {
      return new UnexpectedError(err, {
        service: FileService.name,
        method: FileService.prototype.getAzureBlobClient.name,
        imageName,
      });
    }
  }

  private async _uploadFileToLocalDisk(
    file: Assets.Image.ParsedFile,
    filename: string,
  ): Promise<CustomUrl.Type | UnexpectedError | ParsingError> {
    const destination = environment.fileUploadFolder;

    const goToRoot = "../../../apps/api"; // the code is running in the context of dist/api and we want to go to the root of the api project instead

    const fullPath = path.join(__dirname, goToRoot, destination, filename);
    const folderPath = fullPath.split("/").slice(0, -1).join("/");

    const folder = await fs.mkdir(folderPath, { recursive: true }).catch(
      (err) =>
        new UnexpectedError(err, {
          service: FileService.name,
          method: FileService.prototype._uploadFileToLocalDisk.name,
          operation: fs.mkdir.name,
          folderPath,
        }),
    );

    if (isError(folder)) {
      return folder;
    }

    const result = await fs.writeFile(fullPath, file.arrayBuffer).catch(
      (err) =>
        new UnexpectedError(err, {
          service: FileService.name,
          method: FileService.prototype._uploadFileToLocalDisk.name,
          operation: fs.writeFile.name,
          path: fullPath,
        }),
    );

    if (isError(result)) {
      return result;
    }

    const virtualPath = `${environment.serverUrl}${destination}/${filename}`;
    const url = `${environment.serverUrl}/${fullPath}`.split("/").filter(Boolean).join("/");

    const parsedUrl = CustomUrl.toUrl(virtualPath);

    if (isError(parsedUrl)) {
      parsedUrl.addContext({
        service: FileService.name,
        method: FileService.prototype._uploadFileToLocalDisk.name,
        operation: CustomUrl.toUrl.name,
        url,
      });
    }

    return parsedUrl;
  }

  private async _deleteFileFromLocalDisk(
    file: Assets.Image.ImageAsset,
  ): Promise<void | DomainError | UnexpectedError> {
    const goToRoot = "../../../apps/api"; // the code is running in the context of dist/api and we want to go to the root of the api project instead
    const fullPath = path.join(__dirname, goToRoot, environment.fileUploadFolder, file.name);

    return fs.unlink(fullPath).catch(
      (err) =>
        new UnexpectedError(err, {
          service: FileService.name,
          method: FileService.prototype._deleteFileFromLocalDisk.name,
          operation: fs.unlink.name,
          file,
        }),
    );
  }
}
