import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  UploadedFile,
  UseFilters,
  UseGuards,
  UseInterceptors,
} from "@nestjs/common";

import {
  Assets,
  CoachUserId,
  DomainError,
  ErrorMessages,
  ErrorTypes,
  OrganizationId,
  PlayerId,
  UnexpectedError,
  UrlParams,
  apiUrls,
  isError,
} from "@mio/helpers";

import { FileExceptionFilter } from "./exception.filter";
import { JwtAuthGuard } from "../auth/jwt-auth.guard";
import { createImageInterceptor } from "./image.interceptor";
import {
  IDParamPipe,
  ParamValidationPipe,
  ZodValidationPipe,
  toBadRequest,
  toInternalServerError,
  toNotFoundError,
} from "../shared";
import { PlayerUserGuard } from "../player-users/player-user.guard";
import { AssetsService } from "./assets.service";
import { PlayerOwnsImageGuard } from "./image-owner.guard";
import { PlayerManageGuard } from "../player-users/player-manage.guard";
import { FileService } from "./file.service";

@UseFilters(FileExceptionFilter)
@Controller()
export class AssetsController {
  constructor(private assetsService: AssetsService, private fileService: FileService) {}

  @Post(apiUrls.assets.player_images)
  @UseGuards(JwtAuthGuard, PlayerUserGuard, PlayerManageGuard)
  @UseInterceptors(createImageInterceptor())
  async uploadPlayerImage(
    @UploadedFile(new ParamValidationPipe(Assets.Image.Entity.toFile))
    file: Assets.Image.ParsedFile,
    @Param(UrlParams.PlayerId, IDParamPipe) playerId: PlayerId,
    @Body(new ZodValidationPipe(Assets.Image.Entity.createDto)) dto: Assets.Image.CreateDto,
  ) {
    const result = await this.assetsService.uploadPlayerImage(file, playerId, dto);

    if (isError(result)) {
      result.addContext({
        service: AssetsController.name,
        method: AssetsController.prototype.uploadPlayerImage.name,
        operation: this.assetsService.uploadPlayerImage.name,
        params: { playerId: playerId, file: file.name },
      });

      throw toInternalServerError(result);
    }

    return result;
  }

  @Post(apiUrls.assets.coach_images)
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(createImageInterceptor())
  async uploadImageCloudOnly(
    @UploadedFile(new ParamValidationPipe(Assets.Image.Entity.toFile))
    file: Assets.Image.ParsedFile,
    // TODO: add coachId to the url; currently it's undefined
    @Param(UrlParams.CoachId, IDParamPipe) coachId: CoachUserId,
    @Param(UrlParams.OrganizationId, IDParamPipe) organizationId: OrganizationId,
    @Body(new ZodValidationPipe(Assets.Image.Entity.createDto)) dto: Assets.Image.CreateDto,
  ) {
    const result = await this.fileService.uploadFile(
      file,
      `${new Date().toUTCString()}-${coachId}-${organizationId}-${file.name}`,
    );

    if (isError(result)) {
      result.addContext({
        service: AssetsController.name,
        method: AssetsController.prototype.uploadImageCloudOnly.name,
        operation: this.fileService.uploadFile.name,
        params: { coachId, organizationId, file: file.name },
      });

      throw toInternalServerError(result);
    }

    return result;
  }

  @Get(apiUrls.assets.player_image)
  @UseGuards(JwtAuthGuard, PlayerUserGuard, PlayerOwnsImageGuard)
  async getImageById(
    @Param(UrlParams.PlayerImageId, IDParamPipe) imageId: Assets.Image.ImageId,
  ): Promise<Assets.Image.ImageAsset> {
    const result = await this.assetsService.getImageById(imageId);

    if (isError(result)) {
      result.addContext({
        service: AssetsController.name,
        method: AssetsController.prototype.getImageById.name,
        operation: this.assetsService.getImageById.name,
      });

      switch (result.type) {
        case ErrorTypes.DomainError:
          throw toBadRequest(result);
        case ErrorTypes.UnexpectedError:
        case ErrorTypes.ParsingError:
          throw toInternalServerError(result);
        default:
          throw toInternalServerError(new UnexpectedError(result));
      }
    }

    if (!result) {
      throw toNotFoundError(
        new DomainError(ErrorMessages.EntityNotFound, {
          service: AssetsController.name,
          method: AssetsController.prototype.getImageById.name,
          operation: this.assetsService.getImageById.name,
          params: { imageId },
        }),
      );
    }

    return result;
  }

  @Delete(apiUrls.assets.player_image)
  @UseGuards(JwtAuthGuard, PlayerUserGuard, PlayerOwnsImageGuard)
  async deletePlayerImage(
    @Param(UrlParams.PlayerImageId, IDParamPipe) imageId: Assets.Image.ImageId,
  ): Promise<void> {
    const result = await this.assetsService.deleteImage(imageId);

    if (isError(result)) {
      result.addContext({
        service: AssetsController.name,
        method: AssetsController.prototype.deletePlayerImage.name,
        operation: this.assetsService.deleteImage.name,
        params: { imageId },
      });

      if (result.type === ErrorTypes.DomainError) {
        throw toBadRequest(result);
      }

      throw toInternalServerError(result);
    }

    return result;
  }
}
