import { Injectable } from "@nestjs/common";
import {
  isError,
  CustomError,
  PaymentRequest,
  PlayerId,
  DomainError,
  ErrorMessages,
} from "@mio/helpers";

import { PaymentRequestsRepository } from "./payment-requests.repository";
import { isNil } from "lodash/fp";
import { StripePaymentService } from "../stripe/payment/payment.service";

@Injectable()
export class PaymentRequestsService {
  constructor(
    private repo: PaymentRequestsRepository,
    private stripePaymentService: StripePaymentService,
  ) {}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  extractStripeSubscriptionId(subscription: any) {
    if (isNil(subscription)) {
      return undefined;
    }

    if (typeof subscription === "string") {
      return subscription;
    }

    return subscription.id;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  extractStripeCustomerId(customer: any) {
    if (isNil(customer)) {
      return undefined;
    }

    if (typeof customer === "string") {
      return customer;
    }

    return customer.id;
  }

  async getPlayerPayments(playerId: PlayerId): Promise<PaymentRequest.Entity[] | CustomError> {
    const payments = await this.repo.findPlayersPayment(playerId);

    if (isError(payments)) {
      payments.addContext({
        service: PaymentRequestsService.name,
        method: this.getPlayerPayments.name,
        operation: this.repo.findPlayersPayment.name,
        playerTeamProfileId: playerId,
      });

      return payments;
    }

    const initiatedPayments = payments.filter(
      (payment) =>
        payment.status === PaymentRequest.PaymentStatus.Initiated &&
        payment.stripeCheckoutSessionId,
    );

    if (isError(initiatedPayments)) {
      initiatedPayments.addContext({
        service: PaymentRequestsService.name,
        method: this.getPlayerPayments.name,
        operation: this.repo.findPlayersPayment.name,
        playerTeamProfileId: playerId,
      });

      return initiatedPayments;
    }

    for (const payment of initiatedPayments) {
      const updateStatus = await this.stripePaymentService.updatePaymentRequestStatus(payment);

      if (isError(updateStatus)) {
        // we want to continue with the rest of the payments even if the update status fails
        continue;
      }

      if (updateStatus && updateStatus.status === "complete") {
        const completedPayment = {
          ...payment,
          status: PaymentRequest.PaymentStatus.Completed,
          stripeSubscriptionId: this.extractStripeSubscriptionId(updateStatus.subscription),
          stripeCustomerId: this.extractStripeCustomerId(updateStatus.customer),
        };

        const updatedPayment = await this.update(completedPayment);

        if (isError(updatedPayment)) {
          updatedPayment.addContext({
            service: PaymentRequestsService.name,
            method: this.getPlayerPayments.name,
            operation: this.update.name,
            paymentRequestId: payment.id,
          });

          return updatedPayment;
        }

        if (!updatedPayment) {
          const index = payments.findIndex((p) => p.id === payment.id);
          if (index !== -1) {
            payments[index] = completedPayment;
          }
        }
      }
    }

    return payments;
  }

  async getPlayerPaymentsByStatus(
    playerId: PlayerId,
    status: PaymentRequest.PaymentStatus,
  ): Promise<PaymentRequest.Entity[] | CustomError> {
    const payments = this.repo.findPlayersPaymentByStatus(playerId, status);

    if (isError(payments)) {
      payments.addContext({
        service: PaymentRequestsService.name,
        method: this.getPlayerPayments.name,
        operation: this.repo.findPlayersPayment.name,
        playerTeamProfileId: playerId,
      });

      return payments;
    }

    return payments;
  }

  async getById(paymentRequestId: PaymentRequest.Id) {
    const payment = this.repo.getById(paymentRequestId);

    if (isError(payment)) {
      payment.addContext({
        service: PaymentRequestsService.name,
        method: this.getPlayerPayments.name,
        operation: this.repo.findPlayersPayment.name,
        paymentRequestId,
      });

      return payment;
    }

    return payment;
  }

  async createPlayerPayment(playerId: PlayerId, createDto: PaymentRequest.CreateDto) {
    const paymentRequest = PaymentRequest.Entity.create({
      ...createDto,
      playerId,
    });

    const existingPayments = await this.getPlayerPayments(playerId);
    if (isError(existingPayments)) {
      existingPayments.addContext({
        service: PaymentRequestsService.name,
        method: this.createPlayerPayment.name,
        operation: this.getPlayerPayments.name,
        playerId,
      });
      return existingPayments;
    }

    if (PaymentRequest.Entity.duplicatePaymentExists(existingPayments, paymentRequest)) {
      return new DomainError(ErrorMessages.EntityAlreadyExists, {
        service: PaymentRequestsService.name,
        method: this.createPlayerPayment.name,
        operation: PaymentRequest.Entity.duplicatePaymentExists.name,
        playerId,
        payment: paymentRequest,
      });
    }

    const result = await this.repo.create(paymentRequest);

    if (isError(result)) {
      result.addContext({
        service: PaymentRequestsService.name,
        method: this.createPlayerPayment.name,
        operation: this.repo.create.name,
        playerId,
        payment: paymentRequest,
      });

      return result;
    }

    return paymentRequest;
  }

  async update(updateDto: PaymentRequest.UpdateDto) {
    if (isNil(updateDto.stripeCheckoutSessionId)) {
      return new DomainError(ErrorMessages.InvalidFields, {
        message: "Stripe checkout session id is required",
      });
    }

    // currently we only update a selection of stripe fields
    // reason: not sure if we need to update other fields when financial info is involved
    const result = await this.repo.update(updateDto);

    if (isError(result)) {
      result.addContext({
        service: PaymentRequestsService.name,
        method: this.createPlayerPayment.name,
        operation: this.repo.create.name,
        payment: updateDto,
      });

      return result;
    }

    return result;
  }

  async initiatePayment(paymentRequestId: PaymentRequest.Id) {
    const paymentRequest = await this.getById(paymentRequestId);

    if (isError(paymentRequest)) {
      paymentRequest.addContext({
        service: PaymentRequestsService.name,
        method: this.initiatePayment.name,
        operation: this.repo.getById.name,
        paymentRequestId,
      });

      return paymentRequest;
    }

    if (isNil(paymentRequest)) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        message: "Payment request not found",
      });
    }

    const checkoutSession = await this.stripePaymentService.createCheckoutSession(paymentRequest);

    if (isError(checkoutSession)) {
      return checkoutSession;
    }

    const updatePaymentRequest = await this.update({
      ...paymentRequest,
      stripeCheckoutSessionId: checkoutSession.id,
      status: PaymentRequest.PaymentStatus.Initiated,
    });

    if (isError(updatePaymentRequest)) {
      updatePaymentRequest.addContext({
        service: StripePaymentService.name,
        method: this.initiatePayment.name,
        operation: this.update.name,
        paymentRequestId: paymentRequest.id,
      });

      return updatePaymentRequest;
    }

    return checkoutSession;
  }
}
