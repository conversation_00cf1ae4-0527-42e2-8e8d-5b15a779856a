import { forwardRef, Module } from "@nestjs/common";

import { DatabaseModule } from "../database";
import { PaymentRequestsRepository } from "./payment-requests.repository";
import { PaymentRequestsService } from "./payment-requests.service";
import { PaymentRequestsController } from "./payment-requests.controller";
import { PlayerTeamProfileModule } from "../player-team-profile";
import { ProfileModule } from "../profile/profile.module";
import { PlayerUsersModule } from "../player-users/player-users.module";
import { AppConfigModule } from "../config";
import { PermissionsModule } from "../permissions";
import { StripeModule } from "../stripe/stripe.module";
import { StripePaymentModule } from "../stripe/payment/payment.module";

@Module({
  imports: [
    DatabaseModule,
    PlayerTeamProfileModule,
    ProfileModule,
    PermissionsModule,
    forwardRef(() => PlayerUsersModule),
    AppConfigModule,
    forwardRef(() => StripeModule),
    StripePaymentModule,
  ],
  providers: [PaymentRequestsRepository, PaymentRequestsService],
  controllers: [PaymentRequestsController],
  exports: [PaymentRequestsService],
})
export class PaymentRequestsModule {}
