import { Body, Controller, Get, Param, Post, UseGuards } from "@nestjs/common";

import { JwtAuthGuard } from "../auth/jwt-auth.guard";
import { PlayerUserGuard } from "../player-users/player-user.guard";
import { PlayerManageGuard } from "../player-users/player-manage.guard";
import { apiUrls, PlayerId, UrlParams, PaymentRequest, isError } from "@mio/helpers";
import { IDParamPipe, toInternalServerError } from "../shared";
import { PaymentRequestsService } from "./payment-requests.service";

@Controller()
export class PaymentRequestsController {
  constructor(private readonly paymentsService: PaymentRequestsService) {}

  @Get(apiUrls.payments.my_payments)
  @UseGuards(JwtAuthGuard, PlayerUserGuard, PlayerManageGuard)
  async findPlayerPaymentRequests(@Param(UrlParams.PlayerId, IDParamPipe) playerId: PlayerId) {
    console.log("getMyPayments", playerId);
    return this.paymentsService.getPlayerPayments(playerId);
  }

  @Post(apiUrls.payments.my_payments)
  @UseGuards(JwtAuthGuard, PlayerUserGuard, PlayerManageGuard)
  async createPlayerPaymentRequest(
    @Param(UrlParams.PlayerId, IDParamPipe) playerId: PlayerId,
    @Body() createDto: PaymentRequest.CreateDto,
  ) {
    return this.paymentsService.createPlayerPayment(playerId, createDto);
  }

  @Post(apiUrls.payments.create_checkout_session)
  @UseGuards(JwtAuthGuard, PlayerUserGuard)
  async initiatePayment(
    @Param(UrlParams.PaymentRequestId, IDParamPipe) paymentRequestId: PaymentRequest.Id,
  ) {
    const result = await this.paymentsService.initiatePayment(paymentRequestId);

    if (isError(result)) {
      switch (result.type) {
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }
}
