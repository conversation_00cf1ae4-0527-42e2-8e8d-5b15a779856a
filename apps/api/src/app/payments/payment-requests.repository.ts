import { Inject, Injectable } from "@nestjs/common";

import {
  Primitive,
  UnexpectedError,
  isError,
  PaymentRequest,
  PlayerId,
  GenericRecord,
  DomainError,
  ErrorMessages,
} from "@mio/helpers";

import { DATABASE_CONNECTION, DBClient, DBCollection } from "../database";

export const paymentRequestsCollection = "payment-requests";
const teamsCollection = "teams";
type PaymentDocument = Primitive<PaymentRequest.Entity>;

@Injectable()
export class PaymentRequestsRepository {
  private collection: DBCollection<PaymentDocument>;

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection(paymentRequestsCollection);
  }

  async findPlayersPayment(playerId: PlayerId) {
    return this.collection
      .aggregate([
        { $match: { playerId } },
        {
          $lookup: {
            from: teamsCollection,
            localField: "teamId",
            foreignField: "id",
            as: "team",
          },
        },
        {
          $unwind: {
            path: "$team",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $addFields: {
            teamName: "$team.name",
          },
        },
        {
          $project: {
            team: 0, // Remove the full team object
          },
        },
      ])
      .toArray()
      .then((result) => {
        if (result) {
          const parsed = PaymentRequest.Entity.toEntities(result as GenericRecord[]);

          if (isError(parsed)) {
            parsed.addContext({
              service: PaymentRequestsRepository.name,
              method: this.findPlayersPayment.name,
              operation: PaymentRequest.Entity.toEntities.name,
              data: result,
              playerTeamProfileId: playerId,
            });
          }

          return parsed;
        }

        return [];
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: PaymentRequestsRepository.name,
          method: this.findPlayersPayment.name,
          operation: PaymentRequest.Entity.toEntities.name,
          err,
          playerTeamProfileId: playerId,
        });
      });
  }

  async findPlayersPaymentByStatus(playerId: PlayerId, status: PaymentRequest.PaymentStatus) {
    return this.collection
      .aggregate([
        { $match: { playerId, status } },
        {
          $lookup: {
            from: teamsCollection,
            localField: "teamId",
            foreignField: "id",
            as: "team",
          },
        },
        {
          $unwind: {
            path: "$team",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $addFields: {
            teamName: "$team.name",
          },
        },
        {
          $project: {
            team: 0, // Remove the full team object
          },
        },
      ])
      .toArray()
      .then((result) => {
        if (result) {
          const parsed = PaymentRequest.Entity.toEntities(result as GenericRecord[]);

          if (isError(parsed)) {
            parsed.addContext({
              service: PaymentRequestsRepository.name,
              method: this.findPlayersPayment.name,
              operation: PaymentRequest.Entity.toEntities.name,
              data: result,
              playerTeamProfileId: playerId,
            });
          }

          return parsed;
        }

        return [];
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: PaymentRequestsRepository.name,
          method: this.findPlayersPayment.name,
          operation: PaymentRequest.Entity.toEntities.name,
          err,
          playerTeamProfileId: playerId,
        });
      });
  }

  async getById(paymentRequestId: PaymentRequest.Id) {
    return this.collection
      .findOne({ id: paymentRequestId })
      .then((result) => {
        if (result) {
          const parsed = PaymentRequest.Entity.toEntity(result);

          if (isError(parsed)) {
            parsed.addContext({
              service: PaymentRequestsRepository.name,
              method: this.findPlayersPayment.name,
              operation: PaymentRequest.Entity.toEntities.name,
              data: result,
              paymentRequestId,
            });
          }

          return parsed;
        }

        return undefined;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: PaymentRequestsRepository.name,
          method: this.findPlayersPayment.name,
          operation: PaymentRequest.Entity.toEntities.name,
          err,
          paymentRequestId,
        });
      });
  }

  async create(payment: PaymentRequest.Entity) {
    return this.collection
      .insertOne({
        ...payment,
      })
      .then(() => undefined)
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err, {
          service: PaymentRequestsRepository.name,
          method: this.create.name,
          operation: this.collection.insertOne.name,
        });
      });
  }

  async update(payment: PaymentRequest.Entity) {
    const result = await this.collection
      .updateOne(
        { id: payment.id },
        {
          $set: {
            stripeCheckoutSessionId: payment.stripeCheckoutSessionId,
            status: payment.status,
            stripeCustomerId: payment.stripeCustomerId,
            stripeSubscriptionId: payment.stripeSubscriptionId,
          },
        },
      )
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });

    if (isError(result)) {
      return result;
    }

    if (result.matchedCount < 1) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    if (result.matchedCount > 1) {
      return new DomainError(ErrorMessages.MultipleEntitiesFound);
    }

    return undefined;
  }
}
