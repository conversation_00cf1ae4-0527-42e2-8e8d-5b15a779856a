import { Inject, Injectable } from "@nestjs/common";

import {
  DomainError,
  Email,
  ErrorMessages,
  Invite,
  InviteId,
  isError,
  OrganizationId,
  ParsingError,
  Primitive,
  UnexpectedError,
} from "@mio/helpers";
import { DATABASE_CONNECTION, DBClient, DBCollection } from "../database";

type InviteDocument = Primitive<Invite>;

export const invitesCollection = "invites";

@Injectable()
export class InviteRepository {
  private collection: DBCollection<InviteDocument>;

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection(invitesCollection);
  }

  async getByEmaildAndOrganization(
    email: Email,
    organization: OrganizationId,
  ): Promise<ParsingError | UnexpectedError | Invite | null> {
    return this.collection
      .findOne({ email, organization })
      .then((mbInvite) => {
        if (mbInvite) {
          const asEntity = Invite.toEntity(mbInvite);
          if (isError(asEntity)) {
            // TODO: log
          }

          return asEntity;
        }
        return null;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async getByOrganization(
    organization: OrganizationId,
  ): Promise<ParsingError | UnexpectedError | Invite[]> {
    return this.collection
      .find({ organization })
      .toArray()
      .then((invites) => {
        const entities = Invite.toEntities(invites);
        if (isError(entities)) {
          // TODO: log
        }

        return entities;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async inviteExists(id: InviteId): Promise<UnexpectedError | boolean> {
    return this.collection
      .find({ id })
      .count()
      .then((result) => {
        return result > 0;
      })
      .catch((err) => new UnexpectedError(err));
  }

  async getById(id: InviteId): Promise<ParsingError | UnexpectedError | Invite | null> {
    return this.collection
      .findOne({ id })
      .then((mbInvite) => {
        if (mbInvite) {
          const asEntity = Invite.toEntity(mbInvite);
          if (isError(asEntity)) {
            // TODO: log
          }

          return asEntity;
        }
        return null;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async updateOne(invite: Invite) {
    return this.collection
      .updateOne({ id: invite.id }, { $set: invite })
      .then((res) => {
        if (res.matchedCount !== 1) {
          // TODO: log
          return new DomainError(ErrorMessages.RepositoryError);
        }
        return undefined;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async deleteOne(id: InviteId) {
    return this.collection
      .deleteOne({ id })
      .then((res) => {
        if (res.deletedCount < 1) {
          return new DomainError(ErrorMessages.EntityNotFound);
        }
        if (res.deletedCount > 1) {
          return new DomainError(ErrorMessages.MultipleEntitiesFound);
        }
        return undefined;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async create(invite: Invite): Promise<UnexpectedError | void> {
    return this.collection
      .insertOne({ ...invite })
      .then(() => undefined)
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }
}
