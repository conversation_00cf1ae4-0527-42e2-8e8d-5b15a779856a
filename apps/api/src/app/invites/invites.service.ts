import { Injectable } from "@nestjs/common";
import { isNil, isNull } from "lodash/fp";

import {
  CustomDate,
  DomainError,
  Email,
  ErrorMessages,
  ExpiredInvite,
  Invite,
  CreateInviteDto,
  InviteId,
  isError,
  OrganizationId,
  ParsingError,
  PendingInvite,
  UnexpectedError,
} from "@mio/helpers";

import { AppConfigService } from "../config";
import { InviteRepository } from "./invite.repository";
import { EmailService } from "../email/email.service";
import { SharedCoachUsersService } from "../coach-users/users.shared.service";
import { OrganizationSharedService } from "../organization/organization.shared.service";
import { TranslationsService } from "../translations/translations.service";

@Injectable()
export class InvitesService {
  constructor(
    private appConfig: AppConfigService,
    private emailService: EmailService,
    private repo: InviteRepository,
    private orgService: OrganizationSharedService,
    private coachUserService: SharedCoachUsersService,
    private translationsService: TranslationsService,
  ) {}

  private async createInvite(dto: CreateInviteDto) {
    const expiration = CustomDate.addDays(this.appConfig.getInviteDurationDays());

    const existingUser = await this.coachUserService.findByEmail(dto.email);

    if (isError(existingUser)) {
      existingUser.addContext({
        service: InvitesService.name,
        method: this.createInvite.name,
        operation: this.coachUserService.findByEmail.name,
        dto,
      });

      return existingUser;
    }

    const invite = Invite.create(dto, expiration, existingUser?.id);

    const organization = await this.orgService.findById(dto.organization);

    if (isError(organization)) {
      organization.addContext({
        service: InvitesService.name,
        method: this.createInvite.name,
        operation: this.orgService.findById.name,
        dto,
      });

      return organization;
    }

    if (isNil(organization)) {
      return new DomainError(ErrorMessages.OrganizationNotFound, {
        service: InvitesService.name,
        method: this.createInvite.name,
        operation: this.orgService.findById.name,
        dto,
      });
    }

    const result = await this.repo.create(invite);

    if (isError(result)) {
      return result;
    }

    const clientUrl = this.appConfig.getCoachPortalUrl();
    const url = existingUser
      ? `${clientUrl}/join/${organization.slug}/invite/${invite.id}`
      : `${clientUrl}/register/${invite.id}`;

    const locale = dto.locale || "en";
    const mailBody = `
      <p>
        ${this.translationsService.translate("emails.invite.greeting", locale)},
        <p>${this.translationsService.translate("emails.invite.body", locale, {
          organizationName: organization.displayName,
        })} <a href="${url}" target="_self">link</a></p>
        <p>${this.translationsService.translate("emails.invite.thanks", locale)},</p>
        <p>${this.translationsService.translate("emails.invite.signature", locale)}</p>
      </p>
    `;

    const emailSubject = this.translationsService.translate("emails.invite.subject", locale, {
      organizationName: organization.displayName,
    });

    const emailDto = Email.toEmailDto({
      to: [invite.email],
      body: mailBody,
      subject: emailSubject,
    });

    if (isError(emailDto)) {
      return emailDto;
    }

    await this.emailService.sendEmail(emailDto, organization.contactEmail);

    return invite;
  }

  private async extendInvite(existingInvite: PendingInvite | ExpiredInvite) {
    const expiration = CustomDate.addDays(this.appConfig.getInviteDurationDays());
    const invite = Invite.extendExpiration(existingInvite, expiration);

    return this.repo.updateOne(invite).then((result) => {
      return isError(result) ? result : invite;
    });
  }

  async createOrExtendInvite(dto: CreateInviteDto) {
    return this.getInviteByEmailAndOrganization(dto.email, dto.organization).then<
      ParsingError | DomainError | UnexpectedError | Invite
    >((result) => {
      if (isError(result)) {
        return result;
      }

      if (isNull(result)) {
        return this.createInvite(dto);
      }

      if (Invite.isRedeemed(result)) {
        return new DomainError(ErrorMessages.InviteAlreadyRedeemed);
      }

      return this.extendInvite(result);
    });
  }

  getInviteByEmailAndOrganization(email: Email, organization: OrganizationId) {
    return this.repo.getByEmaildAndOrganization(email, organization);
  }

  getInviteById(id: InviteId) {
    return this.repo.getById(id);
  }

  getByOrganization(orgId: OrganizationId) {
    return this.repo.getByOrganization(orgId);
  }

  deleteInvite(inviteId: InviteId) {
    return this.repo.deleteOne(inviteId);
  }
}
