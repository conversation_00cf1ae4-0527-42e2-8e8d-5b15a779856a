import { Body, Controller, Delete, Get, Param, Post, UseGuards, UsePipes } from "@nestjs/common";
import { isNull } from "lodash/fp";

import {
  apiUrls,
  DomainError,
  ErrorMessages,
  ErrorTypes,
  Invite,
  CreateInviteDto,
  InviteId,
  isError,
  OrganizationId,
  PermissionsModule,
  UnexpectedError,
  UrlParams,
} from "@mio/helpers";

import {
  ApiKeyGuard,
  IDParamPipe,
  toForbiddenError,
  toInternalServerError,
  toNotFoundError,
  ZodValidationPipe,
} from "../shared";

import { InvitesService } from "./invites.service";
import { OrganizationExistsGuard } from "../organization/organization-exists.guard";
import { JwtAuthGuard } from "../auth/jwt-auth.guard";
import { PermissionsGuard, RequiresPermissions } from "../permissions";

@Controller("")
export class InvitesController {
  constructor(private inviteService: InvitesService) {}

  @Get(apiUrls.getInvites)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageUsers])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async getUnredeemedInvites(
    @Param(UrlParams.OrganizationId, IDParamPipe) orgId: OrganizationId,
  ): Promise<Invite[]> {
    const invites = await this.inviteService.getByOrganization(orgId);

    if (isError(invites)) {
      switch (invites.type) {
        case ErrorTypes.UnexpectedError: {
          throw toInternalServerError(invites);
        }
        case ErrorTypes.ParsingError: {
          // parsing the Invite from the DB is not something we expect
          throw toInternalServerError(invites);
        }
        default:
          throw toInternalServerError(invites);
      }
    }

    return invites.filter((invite) => !Invite.isRedeemed(invite));
  }

  @Delete(apiUrls.inviteDetails)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageUsers])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async deleteInvite(@Param(UrlParams.InviteId, IDParamPipe) inviteId: InviteId): Promise<void> {
    const result = await this.inviteService.deleteInvite(inviteId);

    if (isError(result)) {
      switch (result.type) {
        case ErrorTypes.UnexpectedError: {
          throw toInternalServerError(result);
        }
        case ErrorTypes.DomainError: {
          // parsing the Invite from the DB is not something we expect
          throw toNotFoundError(result);
        }
        default:
          throw toInternalServerError(result);
      }
    }

    return undefined;
  }

  @Post(apiUrls.createSystemInvite)
  @UseGuards(ApiKeyGuard, OrganizationExistsGuard)
  @UsePipes(new ZodValidationPipe(Invite.toCreateDto))
  async createSystemInvite(@Body() dto: CreateInviteDto) {
    return this.inviteService.createOrExtendInvite(dto).then((result) => {
      if (isError(result)) {
        switch (result.type) {
          case ErrorTypes.UnexpectedError: {
            throw toInternalServerError(result);
          }
          case ErrorTypes.DomainError: {
            // invite is redeemed already
            throw toForbiddenError(result);
          }
          case ErrorTypes.ParsingError: {
            // parsing the Invite from the DB is not something we expect
            throw toInternalServerError(result);
          }
          default:
            throw toInternalServerError(result);
        }
      }

      return result;
    });
  }

  @Post(apiUrls.createInvite)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageUsers])
  @UseGuards(JwtAuthGuard, OrganizationExistsGuard, PermissionsGuard)
  @UsePipes(new ZodValidationPipe(Invite.toCreateDto))
  async createInvite(@Body() dto: CreateInviteDto) {
    return this.inviteService.createOrExtendInvite(dto).then((result) => {
      if (isError(result)) {
        switch (result.type) {
          case ErrorTypes.UnexpectedError: {
            throw toInternalServerError(result);
          }
          case ErrorTypes.DomainError: {
            // invite is redeemed already
            throw toForbiddenError(result);
          }
          case ErrorTypes.ParsingError: {
            // parsing the Invite from the DB is not something we expect
            throw toInternalServerError(result);
          }
          default:
            throw toInternalServerError(result);
        }
      }

      return result;
    });
  }

  @Get(apiUrls.getInvite)
  async getInvite(@Param(UrlParams.InviteId, IDParamPipe) inviteId: InviteId) {
    return this.inviteService.getInviteById(inviteId).then((result) => {
      if (isError(result)) {
        throw toInternalServerError(new UnexpectedError(result));
      }

      if (isNull(result)) {
        throw toNotFoundError(new DomainError(ErrorMessages.EntityNotFound));
      }

      return result;
    });
  }
}
