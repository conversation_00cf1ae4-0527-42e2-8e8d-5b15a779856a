import { Module } from "@nestjs/common";

import { DatabaseModule } from "../database";
import { AppConfigModule } from "../config";
import { InviteRepository } from "./invite.repository";
import { SharedInvitesService } from "./invites.shared.service";

@Module({
  imports: [DatabaseModule, AppConfigModule],
  providers: [InviteRepository, SharedInvitesService],
  controllers: [],
  exports: [SharedInvitesService],
})
export class SharedInvitesModule {}
