import { Injectable } from "@nestjs/common";

import {
  DomainError,
  ErrorMessages,
  Invite,
  InviteId,
  isError,
  ParsingError,
  UnexpectedError,
} from "@mio/helpers";

import { InviteRepository } from "./invite.repository";

@Injectable()
export class SharedInvitesService {
  constructor(private repo: InviteRepository) {}

  async getInviteById(
    id: InviteId,
  ): Promise<Invite | DomainError | ParsingError | UnexpectedError> {
    const invite = await this.repo.getById(id);

    if (isError(invite)) {
      invite.addContext({
        service: SharedInvitesService.name,
        method: this.getInviteById.name,
        operation: this.repo.getById.name,
        id,
      });

      return invite;
    }

    if (!invite) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: SharedInvitesService.name,
        method: this.getInviteById.name,
        operation: this.repo.getById.name,
        id,
      });
    }

    return invite;
  }

  async redeemInvite(inviteId: InviteId) {
    return this.repo.getById(inviteId).then((result) => {
      if (isError(result)) {
        return new UnexpectedError(result);
      }

      if (!result) {
        return new UnexpectedError(ErrorMessages.EntityNotFound);
      }

      if (Invite.isExpired(result)) {
        return new DomainError(ErrorMessages.InviteExpired);
      }

      if (Invite.isRedeemed(result)) {
        return new DomainError(ErrorMessages.InviteAlreadyRedeemed);
      }

      const asRedeemed = Invite.redeem(result);

      return this.repo.updateOne(asRedeemed).then((result) => {
        return isError(result) ? result : asRedeemed;
      });
    });
  }
}
