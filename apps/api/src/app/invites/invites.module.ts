import { Modu<PERSON> } from "@nestjs/common";

import { DatabaseModule } from "../database";
import { AppConfigModule } from "../config";
import { InviteRepository } from "./invite.repository";
import { InvitesService } from "./invites.service";
import { InvitesController } from "./invites.controller";
import { PermissionsModule } from "../permissions";
import { EmailModule } from "../email/email.module";
import { OrganizationSharedModule } from "../organization/organization.shared.module";
import { SharedCoachUsersModule } from "../coach-users/coach-users.shared.module";
import { TranslationsModule } from "../translations/translations.module";

@Module({
  imports: [
    DatabaseModule,
    AppConfigModule,
    OrganizationSharedModule,
    PermissionsModule,
    EmailModule,
    SharedCoachUsersModule,
    TranslationsModule,
  ],
  providers: [InviteRepository, InvitesService],
  controllers: [InvitesController],
  exports: [InvitesService, InviteRepository],
})
export class InvitesModule {}
