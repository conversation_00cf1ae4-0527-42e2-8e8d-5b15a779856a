import { Test, TestingModule } from "@nestjs/testing";
import { InternalServerErrorException } from "@nestjs/common";

import {
  breakTest,
  Email,
  Organization,
  OrganizationId,
  OrganizationSlug,
  ParsingError,
  StringOfLength,
  UnexpectedError,
  UUID,
} from "@mio/helpers";
import { OrganizationController } from "./organization.controller";
import { mockDep, passingGuard } from "../../test";
import { OrganizationService } from "./organization.service";
import { ApiKeyGuard } from "../shared";
import { PermissionsGuard } from "../permissions";
import { ProfileGuard } from "../profile/profile.guard";

describe("OrganizationController", () => {
  let controller: OrganizationController;

  describe(OrganizationController.prototype.getOrganization.name, () => {
    const validOrganization = Organization.parse({
      name: "Monster inc",
      displayName: "Monsters",
      slug: "monsters",
      id: UUID.generate(),
      members: [],
      contactEmail: "<EMAIL>",
      senderEmail: "<EMAIL>",
    });

    it(`returns an Organization`, async () => {
      const module: TestingModule = await Test.createTestingModule({
        controllers: [OrganizationController],
        providers: [
          mockDep(OrganizationService, {
            findById: async () => validOrganization,
          }),
        ],
      })
        .overrideGuard(ApiKeyGuard)
        .useValue(passingGuard)
        .overrideGuard(PermissionsGuard)
        .useValue(passingGuard)
        .overrideGuard(ProfileGuard)
        .useValue(passingGuard)
        .compile();

      controller = module.get<OrganizationController>(OrganizationController);

      const result = await controller.getOrganization(validOrganization.id);

      expect(result).toEqual(validOrganization);
    });

    it(`throws InternalServerError`, async () => {
      const module: TestingModule = await Test.createTestingModule({
        controllers: [OrganizationController],
        providers: [
          mockDep(OrganizationService, {
            findById: async () => new UnexpectedError(),
          }),
        ],
      })
        .overrideGuard(ApiKeyGuard)
        .useValue(passingGuard)
        .overrideGuard(PermissionsGuard)
        .useValue(passingGuard)
        .overrideGuard(ProfileGuard)
        .useValue(passingGuard)
        .compile();

      controller = module.get<OrganizationController>(OrganizationController);

      try {
        await controller.getOrganization(validOrganization.id);
        breakTest();
      } catch (err) {
        expect(err).toBeInstanceOf(InternalServerErrorException);
      }
    });

    /* this is a ParsingError on the DB read level thus returns a 500 instead of
    400. 400 will be returned on the incoming DTO parsing level which is not
    testable in a unit test */
    it(`throws an InternalServerError when a ParsingError has occured`, async () => {
      const module: TestingModule = await Test.createTestingModule({
        controllers: [OrganizationController],
        providers: [
          mockDep(OrganizationService, {
            findById: async () => new ParsingError({}),
          }),
        ],
      })
        .overrideGuard(ApiKeyGuard)
        .useValue(passingGuard)
        .overrideGuard(PermissionsGuard)
        .useValue(passingGuard)
        .overrideGuard(ProfileGuard)
        .useValue(passingGuard)
        .compile();

      controller = module.get<OrganizationController>(OrganizationController);

      try {
        await controller.getOrganization(validOrganization.id);
        breakTest();
      } catch (err) {
        expect(err).toBeInstanceOf(InternalServerErrorException);
      }
    });
  });

  describe(OrganizationController.prototype.createOrganization.name, () => {
    const dto = Organization.createDto.parse({
      name: "Monster inc",
      contactEmail: "<EMAIL>",
      senderEmail: "<EMAIL>",
    });

    const orgId = UUID.generate<OrganizationId>();

    const createdOrganization = {
      ...dto,
      id: orgId,
      displayName: "Monsters" as StringOfLength<2, 100>,
      slug: "monsters" as OrganizationSlug,
      members: [],
      contactEmail: "<EMAIL>" as Email,
      senderEmail: "<EMAIL>" as Email,
    };

    it(`creates and returns an Organization`, async () => {
      const module: TestingModule = await Test.createTestingModule({
        controllers: [OrganizationController],
        providers: [
          mockDep(OrganizationService, {
            createOrganization: async () => createdOrganization,
          }),
        ],
      })
        .overrideGuard(ApiKeyGuard)
        .useValue(passingGuard)
        .overrideGuard(PermissionsGuard)
        .useValue(passingGuard)
        .overrideGuard(ProfileGuard)
        .useValue(passingGuard)
        .compile();

      controller = module.get<OrganizationController>(OrganizationController);

      const result = await controller.createOrganization(dto);

      expect(result).toEqual(createdOrganization);
    });

    it(`throws an InternalServerError`, async () => {
      const module: TestingModule = await Test.createTestingModule({
        controllers: [OrganizationController],
        providers: [
          mockDep(OrganizationService, {
            createOrganization: async () => new UnexpectedError(),
          }),
        ],
      })
        .overrideGuard(ApiKeyGuard)
        .useValue(passingGuard)
        .overrideGuard(PermissionsGuard)
        .useValue(passingGuard)
        .overrideGuard(ProfileGuard)
        .useValue(passingGuard)
        .compile();

      controller = module.get<OrganizationController>(OrganizationController);

      try {
        await controller.createOrganization(dto);
      } catch (err) {
        expect(err).toBeInstanceOf(InternalServerErrorException);
      }
    });
  });
});
