import { Injectable } from "@nestjs/common";
import { isNil, isNull } from "lodash/fp";

import {
  CreateOrganizationDto,
  Organization,
  OrganizationId,
  isError,
  ProfileId,
  ErrorTypes,
  UnexpectedError,
  DomainError,
  ErrorMessages,
  OrganizationSlug,
  UpdateOrganizationDto,
  InviteId,
  PermissionsModule,
  InviteRegistrationDto,
  CoachUserId,
} from "@mio/helpers";

import { OrganizationRepository } from "./organization.repository";
import { SharedInvitesService } from "../invites/invites.shared.service";
import { ProfileService } from "../profile/profile.service";
import { PermissionsService } from "../permissions";

@Injectable()
export class OrganizationService {
  constructor(
    private organizationRepo: OrganizationRepository,
    private inviteService: SharedInvitesService,
    private profileService: ProfileService,
    private permissionService: PermissionsService,
  ) {}

  findById(id: OrganizationId) {
    return this.organizationRepo.getById(id);
  }

  findBySlug(slug: OrganizationSlug) {
    return this.organizationRepo.getBySlug(slug);
  }

  async addToFirstOrganization(dto: InviteRegistrationDto, userId: CoachUserId) {
    const invite = await this.inviteService.getInviteById(dto.invite);

    if (isError(invite)) {
      return invite;
    }

    if (invite.userId) {
      return new DomainError(ErrorMessages.InvalidAction);
    }

    const profile = await this.profileService.createNewProfile(
      { firstName: dto.firstName, lastName: dto.lastName },
      userId,
    );

    if (isError(profile)) {
      return profile;
    }

    const addMember = await this.addMember(invite.organization, profile.id);

    if (isError(addMember)) {
      return addMember;
    }

    const redeemInvite = await this.inviteService.redeemInvite(invite.id);

    if (isError(redeemInvite)) {
      return redeemInvite;
    }

    if (invite.permissions) {
      const permissionResult =
        invite.permissions === PermissionsModule.PermissionEntity.Type.Owner
          ? await this.permissionService.createOwner({
              profileId: profile.id,
              organizationId: invite.organization,
            })
          : await this.permissionService.createCustom({
              profileId: profile.id,
              organizationId: invite.organization,
              roleId: invite.permissions,
            });

      if (isError(permissionResult)) {
        permissionResult.addContext({
          service: OrganizationService.name,
          method: this.addToFirstOrganization.name,
          operation: "Create owner or custom permission",
          permission: invite.permissions,
          inviteDto: dto,
        });
      }
    }

    return undefined;
  }

  async joinOrganization(inviteId: InviteId) {
    const invite = await this.inviteService.getInviteById(inviteId);

    if (isError(invite)) {
      return invite;
    }

    if (!invite.userId) {
      return new DomainError(ErrorMessages.InvalidAction);
    }

    const profile = await this.profileService.getByUserId(invite.userId);

    if (isError(profile)) {
      return profile;
    }

    if (!profile) {
      return new DomainError(ErrorMessages.ProfileNotFound, {
        service: OrganizationService.name,
        method: this.joinOrganization.name,
        operation: this.profileService.getByUserId.name,
        inviteId,
        message: "Profile not found",
      });
    }

    const addMember = await this.addMember(invite.organization, profile.id);

    if (isError(addMember)) {
      return addMember;
    }

    const redeemInvite = await this.inviteService.redeemInvite(inviteId);

    if (isError(redeemInvite)) {
      return redeemInvite;
    }

    if (invite.permissions) {
      const permissionResult =
        invite.permissions === PermissionsModule.PermissionEntity.Type.Owner
          ? await this.permissionService.createOwner({
              profileId: profile.id,
              organizationId: invite.organization,
            })
          : await this.permissionService.createCustom({
              profileId: profile.id,
              organizationId: invite.organization,
              roleId: invite.permissions,
            });

      if (isError(permissionResult)) {
        permissionResult.addContext({
          service: OrganizationService.name,
          method: this.joinOrganization.name,
          operation: "Create owner or custom permission",
          permission: invite.permissions,
          inviteId,
        });
      }
    }

    return undefined;
  }

  async createOrganization(dto: CreateOrganizationDto) {
    const organization = Organization.create(dto);

    const res = await this.organizationRepo.createOrganization(organization);

    if (isError(res)) {
      res.addContext({
        service: OrganizationService.name,
        method: this.createOrganization.name,
        operation: this.organizationRepo.createOrganization.name,
        orgName: dto.name,
      });

      return res;
    }

    return organization;
  }

  async demoCreateOrganization(dto?: CreateOrganizationDto) {
    const organization = Organization.toDemoInstance(dto);

    const res = await this.organizationRepo.createOrganization(organization);

    if (isError(res)) {
      res.addContext({
        service: OrganizationService.name,
        method: this.demoCreateOrganization.name,
        operation: this.organizationRepo.createOrganization.name,
        orgName: dto?.name,
      });

      return res;
    }

    return organization;
  }

  async getAllByProfile(id: ProfileId) {
    const organizations = await this.organizationRepo.getAllByProfile(id);

    if (isError(organizations)) {
      /* wrap parsing errors for the outside world */
      return organizations.type === ErrorTypes.ParsingError
        ? new UnexpectedError(organizations)
        : organizations;
    }

    return organizations;
  }

  async addMember(orgId: OrganizationId, profileId: ProfileId) {
    const organization = await this.organizationRepo.getById(orgId);

    if (isError(organization)) {
      return organization;
    }

    if (isNull(organization)) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: OrganizationService.name,
        method: this.addMember.name,
        operation: this.organizationRepo.getById.name,
        orgId,
        message: "Organization not found",
      });
    }

    const updatedOrganization = Organization.addMember(organization, profileId);

    const updateResult = await this.organizationRepo.update(updatedOrganization);

    if (isError(updateResult)) {
      return new UnexpectedError(updateResult, {
        service: OrganizationService.name,
        method: this.addMember.name,
        operation: this.organizationRepo.update.name,
        orgId,
        message: "Failed to update organization",
      });
    }

    return updatedOrganization;
  }

  async updateOrganization(orgId: OrganizationId, dto: UpdateOrganizationDto) {
    const organization = await this.organizationRepo.getById(orgId);

    if (isError(organization)) {
      return organization;
    }

    if (isNil(organization)) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    const updatedOrganization = Organization.update(dto, organization);

    const updateResult = await this.organizationRepo.update(updatedOrganization);

    if (isError(updateResult)) {
      return new UnexpectedError(updateResult);
    }

    return updatedOrganization;
  }
}
