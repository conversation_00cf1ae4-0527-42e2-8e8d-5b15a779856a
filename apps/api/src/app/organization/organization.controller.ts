import { Body, Controller, Get, Param, Patch, Post, UseGuards, UsePipes } from "@nestjs/common";
import { isNil } from "lodash/fp";

import {
  apiUrls,
  CreateOrganizationDto,
  DomainError,
  ErrorMessages,
  ErrorTypes,
  InviteId,
  isError,
  Organization,
  OrganizationId,
  OrganizationSlug,
  PermissionsModule,
  PublicOrganization,
  UpdateOrganizationDto,
  UrlParams,
} from "@mio/helpers";

import {
  toInternalServerError,
  ZodValidationPipe,
  ApiKeyGuard,
  IDParamPipe,
  toNotFoundError,
} from "../shared";

import { OrganizationService } from "./organization.service";
import { JwtAuthGuard } from "../auth/jwt-auth.guard";
import { SlugParamPipe } from "./slug-param.pipe";
import { PermissionsGuard, RequiresPermissions } from "../permissions";

@Controller()
export class OrganizationController {
  constructor(private orgService: OrganizationService) {}

  /* creating a company requires an API_KEY, it's done by admins */
  @Post(apiUrls.createOrganization)
  @UseGuards(ApiKeyGuard)
  @UsePipes(new ZodValidationPipe(Organization.createDto))
  async createOrganization(@Body() dto: CreateOrganizationDto) {
    return this.orgService.createOrganization(dto).then((result) => {
      if (isError(result)) {
        switch (result.type) {
          case ErrorTypes.UnexpectedError: {
            throw toInternalServerError(result);
          }
          default:
            throw toInternalServerError(result);
        }
      }
      return result;
    });
  }

  @Post(apiUrls.demoOrganization)
  @UseGuards(ApiKeyGuard)
  async demoCreateOrganization(@Body() dto: CreateOrganizationDto) {
    return this.orgService.createOrganization(dto).then((result) => {
      if (isError(result)) {
        switch (result.type) {
          case ErrorTypes.UnexpectedError: {
            throw toInternalServerError(result);
          }
          default:
            throw toInternalServerError(result);
        }
      }
      return result;
    });
  }

  @Patch(apiUrls.joinOrganization)
  async joinOrganization(@Param(UrlParams.InviteId, IDParamPipe) inviteId: InviteId) {
    return this.orgService.joinOrganization(inviteId).then((result) => {
      if (isError(result)) {
        switch (result.type) {
          case ErrorTypes.DomainError: {
            if (result.message === ErrorMessages.EntityNotFound) {
              throw toNotFoundError(result);
            }
            throw toInternalServerError(result);
          }
          case ErrorTypes.UnexpectedError: {
            throw toInternalServerError(result);
          }
          case ErrorTypes.ParsingError: {
            throw toInternalServerError(result);
          }
          default:
            throw toInternalServerError(result);
        }
      }
      return result;
    });
  }

  @Patch(apiUrls.updateOrganization)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageOrganization])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @UsePipes(new ZodValidationPipe(Organization.updateDto))
  async updateOrganization(
    @Body() dto: UpdateOrganizationDto,
    @Param(UrlParams.OrganizationId, IDParamPipe) orgId: OrganizationId,
  ) {
    return this.orgService.updateOrganization(orgId, dto).then((result) => {
      if (isError(result)) {
        switch (result.type) {
          case ErrorTypes.DomainError: {
            if (result.message === ErrorMessages.EntityNotFound) {
              throw toNotFoundError(result);
            }
            throw toInternalServerError(result);
          }
          case ErrorTypes.UnexpectedError: {
            throw toInternalServerError(result);
          }
          case ErrorTypes.ParsingError: {
            throw toInternalServerError(result);
          }
          default:
            throw toInternalServerError(result);
        }
      }
      return result;
    });
  }

  @Get(apiUrls.getOrganization)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageOrganization])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async getOrganization(
    @Param(UrlParams.OrganizationId, IDParamPipe) orgId: OrganizationId,
  ): Promise<Organization | never> {
    return this.orgService.findById(orgId).then((result) => {
      if (isError(result)) {
        switch (result.type) {
          case ErrorTypes.UnexpectedError: {
            throw toInternalServerError(result);
          }
          case ErrorTypes.ParsingError: {
            throw toInternalServerError(result);
          }
          default:
            throw toInternalServerError(result);
        }
      }

      if (isNil(result)) {
        throw toNotFoundError(new DomainError(ErrorMessages.EntityNotFound));
      }

      return result;
    });
  }

  @Get(apiUrls.getOrganizationBySlug)
  async getOrganizationBySlug(
    @Param(UrlParams.OrganizationSlug, SlugParamPipe) slug: OrganizationSlug,
  ): Promise<PublicOrganization | never> {
    return this.orgService.findBySlug(slug).then((result) => {
      if (isError(result)) {
        switch (result.type) {
          case ErrorTypes.UnexpectedError: {
            throw toInternalServerError(result);
          }
          case ErrorTypes.ParsingError: {
            throw toInternalServerError(result);
          }
          default:
            throw toInternalServerError(result);
        }
      }

      if (isNil(result)) {
        throw toNotFoundError(new DomainError(ErrorMessages.EntityNotFound));
      }

      return Organization.toPublic(result);
    });
  }
}
