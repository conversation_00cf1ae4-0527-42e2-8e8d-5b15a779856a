import { Modu<PERSON> } from "@nestjs/common";

import { AppConfigModule } from "../config";
import { DatabaseModule } from "../database";
import { OrganizationExistsGuard } from "./organization-exists.guard";
import { OrganizationRepository } from "./organization.repository";
import { OrganizationSharedService } from "./organization.shared.service";

@Module({
  imports: [DatabaseModule, AppConfigModule],
  providers: [OrganizationRepository, OrganizationSharedService, OrganizationExistsGuard],
  controllers: [],
  exports: [OrganizationSharedService, OrganizationExistsGuard],
})
export class OrganizationSharedModule {}
