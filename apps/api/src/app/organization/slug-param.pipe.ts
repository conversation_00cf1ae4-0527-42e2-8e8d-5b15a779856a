import { PipeTransform, Injectable, ArgumentMetadata } from "@nestjs/common";

import { ErrorMessages, ErrorTypes, isError, Organization, StringErrors } from "@mio/helpers";
import { toBadRequest } from "../shared";

@Injectable()
export class SlugParamPipe implements PipeTransform {
  transform(value: unknown, metadata: ArgumentMetadata) {
    if (metadata.type === "param") {
      const result = Organization.parseSlug(value);

      if (!isError(result)) {
        return result;
      }

      throw toBadRequest({
        message: ErrorMessages.InvalidParam,
        type: ErrorTypes.ParsingError,
        errors: { slug: StringErrors.Required },
      });
    }

    return value;
  }
}
