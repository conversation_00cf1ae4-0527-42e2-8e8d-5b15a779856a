import { Injectable, CanActivate, ExecutionContext } from "@nestjs/common";
import { isNull } from "lodash/fp";

import { DomainError, ErrorMessages, isError, UnexpectedError, UrlParams } from "@mio/helpers";
import { toForbiddenError, toInternalServerError } from "../shared";
import { OrganizationSharedService } from "./organization.shared.service";

@Injectable()
export class OrganizationExistsGuard implements CanActivate {
  constructor(private orgService: OrganizationSharedService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const organizationId = request.params[UrlParams.OrganizationId];

    return this.orgService.findById(organizationId).then((result) => {
      if (isError(result)) {
        throw toInternalServerError(new UnexpectedError(result));
      }
      if (isNull(result)) {
        throw toForbiddenError(new DomainError(ErrorMessages.OrganizationNotFound));
      }

      return true;
    });
  }
}
