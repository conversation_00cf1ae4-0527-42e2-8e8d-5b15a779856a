import { <PERSON>du<PERSON> } from "@nestjs/common";

import { AppConfigModule } from "../config";
import { DatabaseModule } from "../database";
import { SharedInvitesModule } from "../invites/invites.shared.module";
import { PermissionsModule } from "../permissions";
import { ProfileModule } from "../profile/profile.module";
import { OrganizationController } from "./organization.controller";
import { OrganizationRepository } from "./organization.repository";
import { OrganizationService } from "./organization.service";

@Module({
  imports: [DatabaseModule, AppConfigModule, ProfileModule, PermissionsModule, SharedInvitesModule],
  providers: [OrganizationRepository, OrganizationService],
  controllers: [OrganizationController],
  exports: [OrganizationService, OrganizationRepository],
})
export class OrganizationModule {}
