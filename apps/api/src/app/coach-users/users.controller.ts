import { Controller, Get, Param, UseGuards } from "@nestjs/common";
import { isNull } from "lodash/fp";

import {
  apiUrls,
  DomainError,
  ErrorMessages,
  ErrorTypes,
  isError,
  OrganizationId,
  PermissionsModule,
  UnexpectedError,
  UrlParams,
  Coach<PERSON>ser,
  CoachUserId,
} from "@mio/helpers";
import { JwtAuthGuard } from "../auth/jwt-auth.guard";
import { CoachUsersService } from "./users.service";
import { WithCoachUserId } from "./with-user";
import {
  IDParamPipe,
  toBadRequest,
  toInternalServerError,
  toUnauthorizedException,
} from "../shared";
import { PermissionsGuard, RequiresPermissions } from "../permissions";
import { ProfileService } from "../profile/profile.service";

@Controller()
export class CoachUsersController {
  constructor(private usersService: CoachUsersService, private profileService: ProfileService) {}

  @Get(apiUrls.currentUser)
  @UseGuards(JwtAuthGuard)
  async getCurrentUser(@WithCoachUserId() userId: CoachUserId) {
    const result = await this.usersService.findById(userId);

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    if (isNull(result)) {
      throw toUnauthorizedException(new DomainError(ErrorMessages.InvalidUser));
    }

    const profile = await this.profileService.getByUserId(userId);

    if (isError(profile)) {
      throw toInternalServerError(profile);
    }

    if (isNull(profile)) {
      throw toInternalServerError(
        new DomainError(ErrorMessages.EntityNotFound, {
          message: "Profile not found.",
          userId,
        }),
      );
    }

    return CoachUser.publicFromUser(result, profile);
  }

  @Get(apiUrls.organizationUsers)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageUsers])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async getOrganizationUser(@Param(UrlParams.OrganizationId, IDParamPipe) orgId: OrganizationId) {
    const result = await this.usersService.getOrganizationUsers(orgId);

    if (isError(result)) {
      switch (result.type) {
        case ErrorTypes.DomainError:
          throw toBadRequest(result);
        case ErrorTypes.ParsingError:
          throw toInternalServerError(result);
        case ErrorTypes.UnexpectedError:
          throw toInternalServerError(result);
        default:
          throw new UnexpectedError(result);
      }
    }

    return result;
  }
}
