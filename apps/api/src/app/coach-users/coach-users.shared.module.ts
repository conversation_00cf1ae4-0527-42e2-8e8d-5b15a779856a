import { Module } from "@nestjs/common";

import { DatabaseModule } from "../database";
import { CoachUsersRepository } from "./users-repository";
import { SharedCoachUsersService } from "./users.shared.service";

@Module({
  imports: [DatabaseModule],
  providers: [CoachUsersRepository, SharedCoachUsersService],
  exports: [SharedCoachUsersService],
  controllers: [],
})
export class SharedCoachUsersModule {}
