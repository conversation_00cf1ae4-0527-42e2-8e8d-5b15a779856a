import { Injectable } from "@nestjs/common";
import { isNil, isNull } from "lodash/fp";

import {
  Email,
  CredentialsDto,
  UserTypes,
  CoachUser,
  isError,
  CoachUserId,
  UnexpectedError,
  DomainError,
  ErrorMessages,
  InviteRegistrationDto,
  Coach,
  ParsingError,
  OrganizationId,
  PasswordResetInitiationDto,
  PasswordResetActionDto,
  CustomDate,
  UUID,
} from "@mio/helpers";

import { CoachUsersRepository } from "./users-repository";
import { HashService } from "./hash.service";
import { EmailService } from "../email/email.service";
import { SharedInvitesService } from "../invites/invites.shared.service";
import { OrganizationService } from "../organization/organization.service";

@Injectable()
export class CoachUsersService {
  constructor(
    private userRepository: CoachUsersRepository,
    private hashService: HashService,
    private invitesService: SharedInvitesService,
    private orgService: OrganizationService,
    private emailService: EmailService,
  ) {}

  async registerInvitedUser(dto: InviteRegistrationDto) {
    const inviteResult = await this.invitesService.getInviteById(dto.invite);

    if (isError(inviteResult)) {
      return new UnexpectedError(inviteResult);
    }

    if (isNull(inviteResult)) {
      return new DomainError(ErrorMessages.InvalidInvite);
    }

    const userResult = await this.createCredentialsUser({
      type: UserTypes.Coach,
      email: inviteResult.email,
      password: dto.password,
    });

    if (isError(userResult)) {
      return userResult;
    }

    const addToOrganization = await this.orgService.addToFirstOrganization(dto, userResult.id);

    if (isError(addToOrganization)) {
      return addToOrganization;
    }

    return undefined;
  }

  private async createCredentialsUser(
    dto: CredentialsDto,
  ): Promise<UnexpectedError | DomainError | CoachUser> {
    const existingUser = await this.findByEmail(dto.email);

    if (isError(existingUser)) {
      return new UnexpectedError(existingUser, {
        service: CoachUsersService.name,
        method: this.createCredentialsUser.name,
        operation: this.findByEmail.name,
        dto,
      });
    }

    if (existingUser) {
      return new DomainError(ErrorMessages.EntityAlreadyExists, {
        service: CoachUsersService.name,
        method: this.createCredentialsUser.name,
        operation: this.findByEmail.name,
        dto,
        message: "User already exists",
      });
    }

    const hashResult = await this.hashService.hashPassword(dto.password);

    if (isError(hashResult)) {
      return hashResult;
    }

    const user = CoachUser.createUserWithCredentials(dto, hashResult);

    return this.userRepository.createUser(user).then((result) => (isError(result) ? result : user));
  }

  findByEmail(email: Email) {
    return this.userRepository.getUserByEmail(email);
  }

  findById(id: CoachUserId) {
    return this.userRepository.getUserById(id);
  }

  async resetPassword(dto: PasswordResetActionDto) {
    const user = await this.userRepository.getUserByEmail(dto.email);

    if (isError(user)) {
      return user;
    }

    if (isNil(user)) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    if (!user.authentication.passwordReset) {
      return new DomainError(ErrorMessages.InvalidUser);
    }

    const codeIsValid = await this.hashService.compare(
      user.authentication.passwordReset.code,
      dto.code,
    );

    if (isError(codeIsValid)) {
      return codeIsValid;
    }

    if (!codeIsValid) {
      return new DomainError(ErrorMessages.InvalidAction);
    }

    const codeHasExpired = CustomDate.isPastDate(user.authentication.passwordReset.expiration);

    if (codeHasExpired) {
      return new DomainError(ErrorMessages.InvalidAction);
    }

    const newPassword = await this.hashService.hashPassword(dto.newPassword);

    if (isError(newPassword)) {
      return newPassword;
    }

    const updatedUser = CoachUser.updatePassword(user, newPassword);

    const repoResult = await this.userRepository.update(updatedUser);

    if (isError(repoResult)) {
      return repoResult;
    }

    return repoResult;
  }

  async initiatePasswordReset(dto: PasswordResetInitiationDto) {
    const user = await this.findByEmail(dto.email);

    if (isError(user)) {
      return user;
    }

    if (isNil(user)) {
      return new DomainError(ErrorMessages.UserNotFound);
    }

    const code = UUID.generate();
    const hashedCode = await this.hashService.hash(code);

    if (isError(hashedCode)) {
      return hashedCode;
    }

    const updatedUser = CoachUser.setPasswordReset(user, code);

    const updateResult = await this.userRepository.update(updatedUser);

    if (isError(updateResult)) {
      return updateResult;
    }

    return this.emailService.sendPasswordResetEmail(dto.email, hashedCode, dto.type);
  }

  async getOrganizationUsers(
    orgId: OrganizationId,
  ): Promise<Coach<"populated">[] | ParsingError | UnexpectedError | DomainError> {
    const organization = await this.orgService.findById(orgId);

    if (isError(organization)) {
      organization.addContext({
        service: CoachUsersService.name,
        method: this.getOrganizationUsers.name,
        operation: this.orgService.findById.name,
        orgId,
      });

      return organization;
    }

    if (!organization) {
      return new DomainError(ErrorMessages.OrganizationNotFound, {
        service: CoachUsersService.name,
        method: this.getOrganizationUsers.name,
        operation: this.orgService.findById.name,
        orgId,
      });
    }

    const result = await this.userRepository.getOrganizationUsers(organization);

    if (isError(result)) {
      result.addContext({
        service: CoachUsersService.name,
        method: this.getOrganizationUsers.name,
        operation: this.userRepository.getOrganizationUsers.name,
        orgId,
      });
    }

    return result;
  }
}
