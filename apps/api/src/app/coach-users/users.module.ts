import { <PERSON><PERSON><PERSON> } from "@nestjs/common";

import { DatabaseModule } from "../database";
import { CoachUsersRepository } from "./users-repository";
import { CoachUsersService } from "./users.service";
import { HashService } from "./hash.service";
import { AppConfigModule } from "../config";
import { CoachUsersController } from "./users.controller";
import { PermissionsModule } from "../permissions";
import { EmailModule } from "../email/email.module";
import { OrganizationModule } from "../organization/organization.module";
import { SharedInvitesModule } from "../invites/invites.shared.module";
import { ProfileModule } from "../profile/profile.module";

@Module({
  imports: [
    DatabaseModule,
    AppConfigModule,
    OrganizationModule,
    PermissionsModule,
    EmailModule,
    SharedInvitesModule,
    ProfileModule,
  ],
  providers: [CoachUsersRepository, CoachUsersService, HashService],
  exports: [CoachUsersService, HashService, CoachUsersRepository],
  controllers: [CoachUsersController],
})
export class CoachUsersModule {}
