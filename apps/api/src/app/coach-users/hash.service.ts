import { Injectable } from "@nestjs/common";
import * as bcrypt from "bcrypt";

import { HashedPassword, Password, UnexpectedError } from "@mio/helpers";
import { AppConfigService } from "../config";

@Injectable()
export class HashService {
  constructor(private appConfig: AppConfigService) {}

  async hashPassword(password: Password, hashFunc = bcrypt.hash) {
    try {
      return hashFunc(password, this.appConfig.getSaltRounds()).catch(
        (err) => new UnexpectedError(err),
      ) as Promise<HashedPassword | UnexpectedError>;
    } catch (err) {
      return new UnexpectedError(err);
    }
  }

  async comparePasswords(
    password: Password,
    hashedPassword: HashedPassword,
    compare = bcrypt.compare,
  ) {
    try {
      return compare(password, hashedPassword).catch((err) => new UnexpectedError(err));
    } catch (err) {
      return new UnexpectedError(err);
    }
  }

  async hash(value: string, hashFunc = bcrypt.hash) {
    try {
      return hashFunc(value, this.appConfig.getSaltRounds()).catch(
        (err) => new UnexpectedError(err),
      ) as Promise<string | UnexpectedError>;
    } catch (err) {
      return new UnexpectedError(err);
    }
  }

  async compare(value: string, ecryptedValue: string, compare = bcrypt.compare) {
    try {
      return compare(value, ecryptedValue).catch((err) => new UnexpectedError(err));
    } catch (err) {
      return new UnexpectedError(err);
    }
  }
}
