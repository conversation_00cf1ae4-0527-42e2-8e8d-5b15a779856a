import { Test, TestingModule } from "@nestjs/testing";

import { Email, HashedPassword, CoachUser, CoachUserId, UserTypes, UUID } from "@mio/helpers";
import { CoachUsersService } from "./users.service";
import { mockDep } from "../../test";
import { CoachUsersRepository } from "./users-repository";
import { HashService } from "./hash.service";
import { ProfileService } from "../profile/profile.service";
import { EmailService } from "../email/email.service";
import { SharedInvitesService } from "../invites/invites.shared.service";
import { OrganizationService } from "../organization/organization.service";

describe(CoachUsersService.name, () => {
  let service: CoachUsersService;

  describe(CoachUsersService.prototype.findByEmail.name, () => {
    it("returns a user on success", async () => {
      const id = UUID.generate<CoachUserId>();
      const hashedPassword = "Totally hashed" as HashedPassword;

      const user = CoachUser.createUserWithCredentials(
        CoachUser.credentialsParser.parse({
          type: UserTypes.Coach,
          email: "<EMAIL>",
          password: "password1",
        }),
        hashedPassword,
        () => id,
      );

      const module: TestingModule = await Test.createTestingModule({
        providers: [
          CoachUsersService,
          mockDep(CoachUsersRepository, {
            getUserByEmail: () => Promise.resolve(user),
          }),
          mockDep(HashService, {
            hashPassword: async () => "a password" as HashedPassword,
          }),
          mockDep(SharedInvitesService, {}),
          mockDep(ProfileService, {}),
          mockDep(OrganizationService, {}),
          mockDep(EmailService, {}),
        ],
      }).compile();

      service = module.get<CoachUsersService>(CoachUsersService);
      const result = await service.findByEmail(Email.parser.parse("<EMAIL>"));
      expect(result).toEqual(user);
    });
  });

  describe(CoachUsersService.prototype.findById.name, () => {
    it("returns a user on success", async () => {
      const id = UUID.generate<CoachUserId>();
      const hashedPassword = "Totally hashed" as HashedPassword;

      const user = CoachUser.createUserWithCredentials(
        CoachUser.credentialsParser.parse({
          type: UserTypes.Coach,
          email: "<EMAIL>",
          password: "password1",
        }),
        hashedPassword,
        () => id,
      );

      const module: TestingModule = await Test.createTestingModule({
        providers: [
          CoachUsersService,
          mockDep(CoachUsersRepository, {
            getUserById: () => Promise.resolve(user),
          }),
          mockDep(HashService, {}),
          mockDep(SharedInvitesService, {}),
          mockDep(ProfileService, {}),
          mockDep(OrganizationService, {}),
          mockDep(EmailService, {}),
        ],
      }).compile();

      service = module.get<CoachUsersService>(CoachUsersService);
      const result = await service.findById(user.id);
      expect(result).toEqual(user);
    });
  });
});
