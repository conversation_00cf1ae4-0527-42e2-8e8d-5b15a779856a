import { ErrorMessages, ErrorTypes } from "@mio/helpers";
import { createParamDecorator, ExecutionContext } from "@nestjs/common";

import { AuthenticatedRequest } from "../auth/types";
import { toInternalServerError } from "../shared";

export const WithCoachUserId = createParamDecorator((_data: unknown, ctx: ExecutionContext) => {
  const request: AuthenticatedRequest = ctx.switchToHttp().getRequest();

  if (request.user) {
    return request.user.sub;
  } else {
    // TODO: log. We forgot to use AuthGuard
    throw toInternalServerError({
      type: ErrorTypes.UnexpectedError,
      message: ErrorMessages.InvalidUser,
    });
  }
});
