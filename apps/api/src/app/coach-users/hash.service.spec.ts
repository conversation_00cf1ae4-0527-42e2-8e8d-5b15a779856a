import { Test, TestingModule } from "@nestjs/testing";

import { breakTest, HashedPassword, isError, Password, UnexpectedError } from "@mio/helpers";
import { mockDep } from "../../test";
import { AppConfigService } from "../config";
import { HashService } from "./hash.service";

describe(HashService.name, () => {
  let service: HashService;

  describe(HashService.prototype.hashPassword.name, () => {
    it("hashes the password", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          HashService,
          mockDep(AppConfigService, {
            getSaltRounds: () => 1,
          }),
        ],
      }).compile();

      service = module.get<HashService>(HashService);

      const password = Password.parser.parse("password1");
      const hash = jest.fn(async (source) => source);

      const result = await service.hashPassword(password, hash);

      if (isError(result)) {
        return breakTest();
      }

      expect(result).toBe(password);
    });

    it("handles hashing failures", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          HashService,
          mockDep(AppConfigService, {
            getSaltRounds: () => 1,
          }),
        ],
      }).compile();

      service = module.get<HashService>(HashService);

      const password = Password.parser.parse("password1");
      const hash = jest.fn(async () => Promise.reject(new Error("Failed to hash")));

      const result = await service.hashPassword(password, hash);

      if (!isError(result)) {
        return breakTest();
      }

      expect(result).toBeInstanceOf(UnexpectedError);
    });

    it("handles unexpected errors", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          HashService,
          mockDep(AppConfigService, {
            getSaltRounds: () => 1,
          }),
        ],
      }).compile();

      service = module.get<HashService>(HashService);

      const password = Password.parser.parse("password1");
      const hash = jest.fn(async () => {
        throw new Error("something went wrong");
      });

      const result = await service.hashPassword(password, hash);

      if (!isError(result)) {
        return breakTest();
      }

      expect(result).toBeInstanceOf(UnexpectedError);
    });
  });

  describe(HashService.prototype.comparePasswords.name, () => {
    it("compares passwords", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          HashService,
          mockDep(AppConfigService, {
            getSaltRounds: () => 1,
          }),
        ],
      }).compile();

      service = module.get<HashService>(HashService);

      const password = Password.parser.parse("password1");
      const hashedPassword = HashedPassword.parser.parse("so hashed much lol");
      const compare = jest.fn(async () => true);

      const result = await service.comparePasswords(password, hashedPassword, compare);

      if (isError(result)) {
        return breakTest();
      }

      expect(result).toBe(true);
    });

    it("handles comparing failures", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          HashService,
          mockDep(AppConfigService, {
            getSaltRounds: () => 1,
          }),
        ],
      }).compile();

      service = module.get<HashService>(HashService);

      const password = Password.parser.parse("password1");
      const hashedPassword = HashedPassword.parser.parse("so hashed much lol");
      const compare = jest.fn(async () => Promise.reject(new Error("something went wrong")));

      const result = await service.comparePasswords(password, hashedPassword, compare);

      if (!isError(result)) {
        return breakTest();
      }
      expect(result).toBeInstanceOf(UnexpectedError);
    });

    it("handles unexpected errors", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          HashService,
          mockDep(AppConfigService, {
            getSaltRounds: () => 1,
          }),
        ],
      }).compile();

      service = module.get<HashService>(HashService);

      const password = Password.parser.parse("password1");
      const hashedPassword = HashedPassword.parser.parse("so hashed much lol");
      const compare = jest.fn(async () => {
        throw new Error("Something went wrong");
      });

      const result = await service.comparePasswords(password, hashedPassword, compare);

      if (!isError(result)) {
        return breakTest();
      }

      expect(result).toBeInstanceOf(UnexpectedError);
    });
  });
});
