import { Test, TestingModule } from "@nestjs/testing";
import { omit } from "lodash/fp";
import { InternalServerErrorException, UnauthorizedException } from "@nestjs/common";

import {
  breakTest,
  HashedPassword,
  UnexpectedError,
  CoachUser,
  CoachUserId,
  UserTypes,
  UUID,
  Profile,
} from "@mio/helpers";
import { CoachUsersController } from "./users.controller";
import { mockDep, passingGuard } from "../../test";
import { CoachUsersService } from "./users.service";
import { PermissionsGuard } from "../permissions";
import { ProfileService } from "../profile/profile.service";

describe("CoachUsersController", () => {
  let controller: CoachUsersController;

  describe(CoachUsersController.prototype.getCurrentUser.name, () => {
    const id = UUID.generate<CoachUserId>();
    const hashedPassword = "Totally hashed" as HashedPassword;

    const user = CoachUser.createUserWithCredentials(
      CoachUser.credentialsParser.parse({
        type: UserTypes.Coach,
        email: "<EMAIL>",
        password: "password1",
      }),
      hashedPassword,
      () => id,
    );

    const profile = Profile.create(
      Profile.createDto.parse({
        firstName: "John",
        lastName: "Doe",
      }),
      user.id,
    );

    it(`returns a public user`, async () => {
      const module: TestingModule = await Test.createTestingModule({
        controllers: [CoachUsersController],
        providers: [
          mockDep(CoachUsersService, {
            findById: async () => user,
          }),
          mockDep(ProfileService, {
            getByUserId: async () => profile,
          }),
        ],
      })
        .overrideGuard(PermissionsGuard)
        .useValue(passingGuard)
        .compile();

      controller = module.get<CoachUsersController>(CoachUsersController);

      const result = await controller.getCurrentUser(user.id);

      expect(result).toEqual(
        omit("authentication.password", {
          ...user,
          firstName: profile.firstName,
          lastName: profile.lastName,
        }),
      );
    });

    it(`throws InternalServerError when UserService fails`, async () => {
      const module: TestingModule = await Test.createTestingModule({
        controllers: [CoachUsersController],
        providers: [
          mockDep(CoachUsersService, {
            findById: async () => new UnexpectedError(),
          }),
          mockDep(ProfileService, {
            getByUserId: async () => profile,
          }),
        ],
      })
        .overrideGuard(PermissionsGuard)
        .useValue(passingGuard)
        .compile();

      controller = module.get<CoachUsersController>(CoachUsersController);

      try {
        await controller.getCurrentUser(user.id);
        breakTest();
      } catch (err) {
        expect(err).toBeInstanceOf(InternalServerErrorException);
      }
    });

    it(`denies access if the user is not found`, async () => {
      const module: TestingModule = await Test.createTestingModule({
        controllers: [CoachUsersController],
        providers: [
          mockDep(CoachUsersService, {
            findById: async () => null,
          }),
          mockDep(ProfileService, {
            getByUserId: async () => profile,
          }),
        ],
      })
        .overrideGuard(PermissionsGuard)
        .useValue(passingGuard)
        .compile();

      controller = module.get<CoachUsersController>(CoachUsersController);

      try {
        await controller.getCurrentUser(user.id);
        breakTest();
      } catch (err) {
        expect(err).toBeInstanceOf(UnauthorizedException);
      }
    });
  });
});
