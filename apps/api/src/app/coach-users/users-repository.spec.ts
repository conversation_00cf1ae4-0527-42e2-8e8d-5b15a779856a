import { Test, TestingModule } from "@nestjs/testing";

import { HashedPassword, ParsingError, UnexpectedError, CoachUser, UserTypes } from "@mio/helpers";
import { DATABASE_CONNECTION } from "../database";
import { CoachUsersRepository } from "./users-repository";

describe(CoachUsersRepository.name, () => {
  let service: CoachUsersRepository;

  describe(CoachUsersRepository.prototype.createUser.name, () => {
    const validUser = CoachUser.createUserWithCredentials(
      CoachUser.credentialsParser.parse({
        type: UserTypes.Coach,
        email: "<EMAIL>",
        password: "password1",
      }),
      HashedPassword.parser.parse("hashedPassword"),
    );

    it("returns undefined on success", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          CoachUsersRepository,
          {
            provide: DATABASE_CONNECTION,
            useValue: {
              collection: () => ({
                insertOne: async () => ({
                  type: "coach",
                }),
              }),
            },
          },
        ],
      }).compile();

      service = module.get<CoachUsersRepository>(CoachUsersRepository);

      const result = await service.createUser(validUser);
      expect(result).toBeUndefined();
    });

    it("returns UnexpectedError when the saving fails", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          CoachUsersRepository,
          {
            provide: DATABASE_CONNECTION,
            useValue: {
              collection: () => ({
                insertOne: async () => Promise.reject(new Error("something went wrong")),
              }),
            },
          },
        ],
      }).compile();

      service = module.get<CoachUsersRepository>(CoachUsersRepository);

      const result = await service.createUser(validUser);
      expect(result).toBeInstanceOf(UnexpectedError);
    });
  });

  describe(CoachUsersRepository.prototype.getUserByEmail.name, () => {
    const validUser = CoachUser.createUserWithCredentials(
      CoachUser.credentialsParser.parse({
        type: UserTypes.Coach,
        email: "<EMAIL>",
        password: "password1",
      }),
      HashedPassword.parser.parse("hashedPassword"),
    );

    it("returns the user when queried by email", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          CoachUsersRepository,
          {
            provide: DATABASE_CONNECTION,
            useValue: {
              collection: () => ({
                findOne: async () => validUser,
              }),
            },
          },
        ],
      }).compile();

      service = module.get<CoachUsersRepository>(CoachUsersRepository);

      const result = await service.getUserByEmail(validUser.authentication.email);
      expect(result).toEqual(validUser);
    });

    it("returns a parsing error when user has invalid fields", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          CoachUsersRepository,
          {
            provide: DATABASE_CONNECTION,
            useValue: {
              collection: () => ({
                findOne: async () => ({
                  ...validUser,
                  type: "invalid user type",
                }),
              }),
            },
          },
        ],
      }).compile();

      service = module.get<CoachUsersRepository>(CoachUsersRepository);

      const result = await service.getUserByEmail(validUser.authentication.email);
      expect(result).toBeInstanceOf(ParsingError);
    });

    it("returns null when user not found", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          CoachUsersRepository,
          {
            provide: DATABASE_CONNECTION,
            useValue: {
              collection: () => ({
                findOne: async () => null,
              }),
            },
          },
        ],
      }).compile();

      service = module.get<CoachUsersRepository>(CoachUsersRepository);

      const result = await service.getUserByEmail(validUser.authentication.email);
      expect(result).toBeNull();
    });

    it("handles unexpected errors", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          CoachUsersRepository,
          {
            provide: DATABASE_CONNECTION,
            useValue: {
              collection: () => ({
                findOne: async () => Promise.reject(new Error("oops")),
              }),
            },
          },
        ],
      }).compile();

      service = module.get<CoachUsersRepository>(CoachUsersRepository);

      const result = await service.getUserByEmail(validUser.authentication.email);
      expect(result).toBeInstanceOf(UnexpectedError);
    });
  });

  describe(CoachUsersRepository.prototype.getUserById.name, () => {
    const validUser = CoachUser.createUserWithCredentials(
      CoachUser.credentialsParser.parse({
        type: UserTypes.Coach,
        email: "<EMAIL>",
        password: "password1",
      }),
      HashedPassword.parser.parse("hashedPassword"),
    );

    it("returns the user when queried by id", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          CoachUsersRepository,
          {
            provide: DATABASE_CONNECTION,
            useValue: {
              collection: () => ({
                findOne: async () => validUser,
              }),
            },
          },
        ],
      }).compile();

      service = module.get<CoachUsersRepository>(CoachUsersRepository);

      const result = await service.getUserById(validUser.id);
      expect(result).toEqual(validUser);
    });

    it("returns a parsing error when user has invalid fields", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          CoachUsersRepository,
          {
            provide: DATABASE_CONNECTION,
            useValue: {
              collection: () => ({
                findOne: async () => ({
                  ...validUser,
                  type: "invalid user type",
                }),
              }),
            },
          },
        ],
      }).compile();

      service = module.get<CoachUsersRepository>(CoachUsersRepository);

      const result = await service.getUserById(validUser.id);
      expect(result).toBeInstanceOf(ParsingError);
    });

    it("returns null when user not found", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          CoachUsersRepository,
          {
            provide: DATABASE_CONNECTION,
            useValue: {
              collection: () => ({
                findOne: async () => null,
              }),
            },
          },
        ],
      }).compile();

      service = module.get<CoachUsersRepository>(CoachUsersRepository);

      const result = await service.getUserById(validUser.id);
      expect(result).toBeNull();
    });

    it("handles unexpected errors", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          CoachUsersRepository,
          {
            provide: DATABASE_CONNECTION,
            useValue: {
              collection: () => ({
                findOne: async () => Promise.reject(new Error("oops")),
              }),
            },
          },
        ],
      }).compile();

      service = module.get<CoachUsersRepository>(CoachUsersRepository);

      const result = await service.getUserById(validUser.id);
      expect(result).toBeInstanceOf(UnexpectedError);
    });
  });
});
