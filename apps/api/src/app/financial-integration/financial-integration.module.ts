import { Modu<PERSON> } from "@nestjs/common";

import { DatabaseModule } from "../database";
import { EncryptionModule } from "../encryption/encryption.module";
import { PermissionsModule } from "../permissions";

import { FinancialIntegrationController } from "./financial-integration.controller";
import { FinancialIntegrationRepository } from "./financial-integration.repository";
import { FinancialIntegrationService } from "./financial-integration.service";

@Module({
  imports: [DatabaseModule, PermissionsModule, EncryptionModule],
  providers: [FinancialIntegrationService, FinancialIntegrationRepository],
  controllers: [FinancialIntegrationController],
  exports: [FinancialIntegrationService],
})
export class FinancialIntegrationModule {}
