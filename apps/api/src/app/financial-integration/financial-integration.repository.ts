import { Inject, Injectable } from "@nestjs/common";

import {
  Primitive,
  UnexpectedError,
  isError,
  OrganizationId,
  DomainError,
  ErrorMessages,
  FinancialIntegration,
  FinancialIntegrationId,
  FinancialIntegrationType,
} from "@mio/helpers";

import { DATABASE_CONNECTION, DBClient, DBCollection } from "../database";

type FinancialIntegrationDocument = Primitive<FinancialIntegration>;

@Injectable()
export class FinancialIntegrationRepository {
  private collection: DBCollection<FinancialIntegrationDocument>;

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection("financial-integrations");
  }

  async create(integration: FinancialIntegration): Promise<UnexpectedError | void> {
    return this.collection
      .insertOne({ ...integration })
      .then(() => undefined)
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async update(integration: FinancialIntegration) {
    const result = await this.collection
      .updateOne({ id: integration.id }, { $set: integration })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });

    if (isError(result)) {
      return result;
    }

    if (result.matchedCount < 1) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    if (result.matchedCount > 1) {
      return new DomainError(ErrorMessages.MultipleEntitiesFound);
    }

    return undefined;
  }

  async delete(integrationId: FinancialIntegrationId) {
    return this.collection
      .deleteOne({
        id: integrationId,
      })
      .then(() => undefined)
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async findByOrganizationId(organizationId: OrganizationId) {
    return this.collection
      .find({ organizationId })
      .toArray()
      .then((mbFinancialIntegrations) => {
        if (mbFinancialIntegrations) {
          const asEntity = FinancialIntegration.toEntities(mbFinancialIntegrations);
          if (isError(asEntity)) {
            // TODO: log
          }

          return asEntity;
        }

        return [];
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async getByOrganizationIdAndType(organizationId: OrganizationId, type: FinancialIntegrationType) {
    return this.collection
      .findOne({ organizationId, type })
      .then((mbFinancialIntegration) => {
        if (mbFinancialIntegration) {
          const asEntity = FinancialIntegration.toEntity(mbFinancialIntegration);
          if (isError(asEntity)) {
            // TODO: log
          }

          return asEntity;
        }

        return null;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async getById(id: FinancialIntegrationId) {
    return this.collection
      .findOne({ id })
      .then((mbFinancialIntegration) => {
        if (mbFinancialIntegration) {
          const asEntity = FinancialIntegration.toEntity(mbFinancialIntegration);
          if (isError(asEntity)) {
            // TODO: log
          }

          return asEntity;
        }

        return null;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }
}
