import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  UseGuards,
  UsePipes,
} from "@nestjs/common";
import { isNil } from "lodash/fp";

import {
  apiUrls,
  CreateFinancialIntegrationDto,
  DomainError,
  ErrorMessages,
  ErrorTypes,
  FinancialIntegration,
  FinancialIntegrationId,
  isError,
  OrganizationId,
  PermissionsModule,
  UpdateFinancialIntegrationDto,
  UrlParams,
} from "@mio/helpers";

import {
  IDParamPipe,
  toBadRequest,
  toInternalServerError,
  toNotFoundError,
  ZodValidationPipe,
} from "../shared";
import { JwtAuthGuard } from "../auth/jwt-auth.guard";
import { PermissionsGuard, RequiresPermissions } from "../permissions";
import { FinancialIntegrationService } from "./financial-integration.service";

@Controller()
export class FinancialIntegrationController {
  constructor(private financialIntegrationService: FinancialIntegrationService) {}

  @Post(apiUrls.generalFinancialIntegration)
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  // TODO: Add specific permission rule for managing integrations
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageOrganization])
  @UsePipes(new ZodValidationPipe(FinancialIntegration.createDtoParser))
  async createFinancialIntegration(
    @Body() dto: CreateFinancialIntegrationDto,
  ): Promise<FinancialIntegration | never> {
    const result = await this.financialIntegrationService.create(dto);

    if (isError(result)) {
      switch (result.type) {
        case ErrorTypes.DomainError:
          throw toBadRequest(result);
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Patch(apiUrls.identifiedFinancialIntegration)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageOrganization])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @UsePipes(new ZodValidationPipe(FinancialIntegration.updateDtoParser))
  async updateFinancialIntegration(@Body() dto: UpdateFinancialIntegrationDto) {
    return this.financialIntegrationService.update(dto).then((result) => {
      if (isError(result)) {
        switch (result.type) {
          case ErrorTypes.DomainError: {
            if (result.message === ErrorMessages.EntityNotFound) {
              throw toNotFoundError(result);
            }
            throw toInternalServerError(result);
          }
          case ErrorTypes.UnexpectedError: {
            throw toInternalServerError(result);
          }
          case ErrorTypes.ParsingError: {
            throw toInternalServerError(result);
          }
          default:
            throw toInternalServerError(result);
        }
      }
      return result;
    });
  }

  @Delete(apiUrls.identifiedFinancialIntegration)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageOrganization])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async deleteFinancialIntegration(
    @Param(UrlParams.FinancialIntegrationId, IDParamPipe) id: FinancialIntegrationId,
  ) {
    return this.financialIntegrationService.delete(id).then((result) => {
      if (isError(result)) {
        switch (result.type) {
          case ErrorTypes.DomainError: {
            if (result.message === ErrorMessages.EntityNotFound) {
              throw toNotFoundError(result);
            }
            throw toInternalServerError(result);
          }
          case ErrorTypes.UnexpectedError: {
            throw toInternalServerError(result);
          }
          case ErrorTypes.ParsingError: {
            throw toInternalServerError(result);
          }
          default:
            throw toInternalServerError(result);
        }
      }
      return result;
    });
  }

  @Get(apiUrls.generalFinancialIntegration)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManagePlayers])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async findFinancialIntegrations(
    @Param(UrlParams.OrganizationId, IDParamPipe) orgId: OrganizationId,
  ) {
    const result = await this.financialIntegrationService.findByOrganizationId(orgId);

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return result;
  }

  @Get(apiUrls.identifiedFinancialIntegration)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageOrganization])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async getFinancialIntegration(
    @Param(UrlParams.FinancialIntegrationId, IDParamPipe) id: FinancialIntegrationId,
  ) {
    return this.financialIntegrationService.getById(id).then((result) => {
      if (isError(result)) {
        switch (result.type) {
          case ErrorTypes.UnexpectedError: {
            throw toInternalServerError(result);
          }
          case ErrorTypes.ParsingError: {
            throw toInternalServerError(result);
          }
          default:
            throw toInternalServerError(result);
        }
      }

      if (isNil(result)) {
        throw toNotFoundError(new DomainError(ErrorMessages.EntityNotFound));
      }

      return result;
    });
  }
}
