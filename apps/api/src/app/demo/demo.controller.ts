import { Body, Controller, Post, UseGuards } from "@nestjs/common";

import {
  apiUrls,
  CustomNumber,
  ErrorTypes,
  isError,
  OrganizationId,
  PositiveInteger,
  ProfileId,
  TeamId,
} from "@mio/helpers";

import { toInternalServerError, ApiKeyGuard } from "../shared";

import { DemoService } from "./demo.service";

@Controller()
export class DemoController {
  constructor(private demoService: DemoService) {}

  /* creating a complete club requires an API_KEY, it's done by admins */
  @Post(apiUrls.demoCompleteOrganization)
  @UseGuards(ApiKeyGuard)
  async createCompleteOrganization() {
    return this.demoService.createCompleteOrganization().then((result) => {
      if (isError(result)) {
        switch (result.type) {
          case ErrorTypes.UnexpectedError: {
            throw toInternalServerError(result);
          }
          default:
            throw toInternalServerError(result);
        }
      }

      return result;
    });
  }

  @Post(apiUrls.demoCompleteTeam)
  @UseGuards(ApiKeyGuard)
  async createCompleteTeam(
    @Body()
    dto: {
      organizationId: OrganizationId;
      profileId: ProfileId;
      numberOfPlayers: PositiveInteger;
    },
  ) {
    return this.demoService
      .createCompleteTeam(
        dto.organizationId,
        dto.profileId,
        CustomNumber.toPositiveInteger(dto.numberOfPlayers),
      )
      .then((result) => {
        if (isError(result)) {
          switch (result.type) {
            case ErrorTypes.UnexpectedError: {
              throw toInternalServerError(result);
            }
            default:
              throw toInternalServerError(result);
          }
        }

        return result;
      });
  }

  @Post(apiUrls.demoTeamPlayers)
  @UseGuards(ApiKeyGuard)
  async createTeamPlayers(
    @Body()
    dto: {
      organizationId: OrganizationId;
      profileId: ProfileId;
      numberOfPlayers: PositiveInteger;
      teamId: TeamId;
    },
  ) {
    return this.demoService
      .createTeamPlayers(
        dto.organizationId,
        dto.teamId,
        dto.profileId,
        CustomNumber.toPositiveInteger(dto.numberOfPlayers),
      )
      .then((result) => {
        if (isError(result)) {
          switch (result.type) {
            case ErrorTypes.UnexpectedError: {
              throw toInternalServerError(result);
            }
            default:
              throw toInternalServerError(result);
          }
        }

        return result;
      });
  }
}
