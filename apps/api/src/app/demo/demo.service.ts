import { Injectable } from "@nestjs/common";
import { isNil } from "lodash/fp";

import {
  isError,
  CreateTeamDto,
  Email,
  PermissionsModule,
  Password,
  StringOfLength,
  DomainError,
  ErrorMessages,
  OrganizationId,
  ProfileId,
  PositiveInteger,
  TeamId,
} from "@mio/helpers";

import { OrganizationService } from "../organization/organization.service";
import { TeamService } from "../team/team.service";
import { PlayerService } from "../player/player.service";
import { InvitesService } from "../invites/invites.service";
import { CoachUsersService } from "../coach-users/users.service";
import { ProfileService } from "../profile/profile.service";

@Injectable()
export class DemoService {
  constructor(
    private organizationService: OrganizationService,
    private teamService: TeamService,
    private playerService: PlayerService,
    private invitesService: InvitesService,
    private coachUserService: CoachUsersService,
    private profileService: ProfileService,
  ) {}

  async createCompleteOrganization() {
    const organization = await this.organizationService.demoCreateOrganization();
    const adminEmail = "<EMAIL>" as Email;

    if (isError(organization)) {
      organization.addContext({
        service: DemoService.name,
        method: this.createCompleteOrganization.name,
        operation: this.organizationService.demoCreateOrganization.name,
      });

      return organization;
    }

    const invite = await this.invitesService.createOrExtendInvite({
      email: adminEmail,
      invitedBy: "system",
      organization: organization.id,
      permissions: PermissionsModule.PermissionEntity.Type.Owner,
    });

    if (isError(invite)) {
      invite.addContext({
        service: DemoService.name,
        method: this.createCompleteOrganization.name,
        operation: this.invitesService.createOrExtendInvite.name,
      });

      return invite;
    }

    const coachUserRegistered = await this.coachUserService.registerInvitedUser({
      firstName: "chicho" as StringOfLength<1, 100>,
      lastName: "mitko" as StringOfLength<1, 100>,
      invite: invite.id,
      password: "kochkata442" as Password,
    });

    if (isError(coachUserRegistered)) {
      coachUserRegistered.addContext({
        service: DemoService.name,
        method: this.createCompleteOrganization.name,
        operation: this.coachUserService.registerInvitedUser.name,
      });

      return coachUserRegistered;
    }

    const coachUser = await this.coachUserService.findByEmail(adminEmail as Email);

    if (isError(coachUser)) {
      coachUser.addContext({
        service: DemoService.name,
        method: this.createCompleteOrganization.name,
        operation: this.coachUserService.findByEmail.name,
      });

      return coachUser;
    }

    if (isNil(coachUser)) {
      return new DomainError(ErrorMessages.UserNotFound);
    }

    const profile = await this.profileService.getByUserId(coachUser.id);

    if (isError(profile)) {
      profile.addContext({
        service: DemoService.name,
        method: this.createCompleteOrganization.name,
        operation: this.profileService.getByUserId.name,
      });

      return profile;
    }

    if (isNil(profile)) {
      return new DomainError(ErrorMessages.ProfileNotFound);
    }

    const team = await this.teamService.demoCreateTeam({
      organizationId: organization.id,
    } as CreateTeamDto);

    if (isError(team)) {
      team.addContext({
        service: DemoService.name,
        method: this.createCompleteOrganization.name,
        operation: this.teamService.demoCreateTeam.name,
      });

      return team;
    }

    const player = await this.playerService.demoCreateApplicant(organization.id);

    if (isError(player)) {
      player.addContext({
        service: DemoService.name,
        method: this.createCompleteOrganization.name,
        operation: this.playerService.demoCreateApplicant.name,
      });

      return player;
    }

    await this.playerService.assignNewTeam(
      {
        teamId: team.id,
        organizationId: organization.id,
        playerId: player.id,
      },
      profile.id,
    );

    const team2 = await this.teamService.demoCreateTeam({
      organizationId: organization.id,
    } as CreateTeamDto);

    if (isError(team2)) {
      team2.addContext({
        service: DemoService.name,
        method: this.createCompleteOrganization.name,
        operation: this.teamService.demoCreateTeam.name,
      });

      return team2;
    }

    const player2 = await this.playerService.demoCreateApplicant(organization.id);

    if (isError(player2)) {
      player2.addContext({
        service: DemoService.name,
        method: this.createCompleteOrganization.name,
        operation: this.playerService.demoCreateApplicant.name,
      });

      return player2;
    }

    const player3 = await this.playerService.demoCreateApplicant(organization.id);

    if (isError(player3)) {
      player3.addContext({
        service: DemoService.name,
        method: this.createCompleteOrganization.name,
        operation: this.playerService.demoCreateApplicant.name,
      });

      return player3;
    }

    await this.playerService.assignNewTeam(
      {
        teamId: team2.id,
        organizationId: organization.id,
        playerId: player2.id,
      },
      profile.id,
    );

    await this.playerService.assignNewTeam(
      {
        teamId: team2.id,
        organizationId: organization.id,
        playerId: player3.id,
      },
      profile.id,
    );

    return {
      organization: {
        name: organization.name,
        slug: organization.slug,
        id: organization.id,
      },
      coachUser: {
        email: coachUser.authentication.email,
        password: "kochkata442",
        id: coachUser.id,
      },
      profile: {
        id: profile.id,
        firstName: profile.firstName,
        lastName: profile.lastName,
      },
      team1: {
        id: team.id,
        name: team.name,
        playersBornBefore: team.playersBornBefore,
        playersBornAfter: team.playersBornAfter,

        players: [
          {
            player1: {
              id: player.id,
              dob: player.dob,
              firstName: player.firstName,
              lastName: player.lastName,
            },
          },
        ],
      },
      team2: {
        id: team2.id,
        name: team2.name,
        playersBornBefore: team2.playersBornBefore,
        playersBornAfter: team2.playersBornAfter,

        players: [
          {
            player2: {
              id: player2.id,
              dob: player2.dob,
              firstName: player2.firstName,
              lastName: player2.lastName,
            },
            player3: {
              id: player3.id,
              dob: player3.dob,
              firstName: player3.firstName,
              lastName: player3.lastName,
            },
          },
        ],
      },
    };
  }

  async createCompleteTeam(
    organizationId: OrganizationId,
    profileId: ProfileId,
    numberOfPlayers: PositiveInteger,
  ) {
    const team = await this.teamService.demoCreateTeam({
      organizationId,
    } as CreateTeamDto);

    if (isError(team)) {
      team.addContext({
        service: DemoService.name,
        method: this.createCompleteTeam.name,
        operation: this.teamService.demoCreateTeam.name,
      });

      return team;
    }

    const players = await this.createTeamPlayers(
      organizationId,
      team.id,
      profileId,
      numberOfPlayers,
    );

    if (isError(players)) {
      players.addContext({
        service: DemoService.name,
        method: this.createCompleteTeam.name,
        operation: this.createTeamPlayers.name,
      });

      return players;
    }

    return {
      id: team.id,
      name: team.name,
      playersBornBefore: team.playersBornBefore,
      playersBornAfter: team.playersBornAfter,
      players,
    };
  }

  async createTeamPlayers(
    organizationId: OrganizationId,
    teamId: TeamId,
    profileId: ProfileId,
    numberOfPlayers: PositiveInteger,
  ) {
    const players = await this.playerService.demoCreateApplicants(organizationId, numberOfPlayers);

    if (isError(players)) {
      players.addContext({
        service: DemoService.name,
        method: this.createCompleteTeam.name,
        operation: this.playerService.demoCreateApplicants.name,
      });

      return players;
    }

    const bulkAssign = await this.playerService.demoBulkAssignTeam(
      players.map((playah) => playah.id),
      teamId,
      organizationId,
      profileId,
    );

    if (isError(bulkAssign)) {
      bulkAssign.addContext({
        service: DemoService.name,
        method: this.createCompleteTeam.name,
        operation: this.playerService.demoBulkAssignTeam.name,
      });

      return bulkAssign;
    }

    return players;
  }
}
