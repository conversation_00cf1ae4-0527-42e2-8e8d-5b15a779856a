import { <PERSON>du<PERSON> } from "@nestjs/common";

import { OrganizationModule } from "../organization/organization.module";
import { DemoController } from "./demo.controller";
import { CoachUsersModule } from "../coach-users/users.module";
import { ProfileModule } from "../profile/profile.module";
import { TeamModule } from "../team/team.module";
import { PlayerModule } from "../player";
import { DemoService } from "./demo.service";
import { AppConfigModule } from "../config";
import { InvitesModule } from "../invites/invites.module";

@Module({
  imports: [
    CoachUsersModule,
    ProfileModule,
    TeamModule,
    PlayerModule,
    OrganizationModule,
    AppConfigModule,
    InvitesModule,
  ],
  providers: [DemoService],
  exports: [DemoService],
  controllers: [DemoController],
})
export class DemoModule {}
