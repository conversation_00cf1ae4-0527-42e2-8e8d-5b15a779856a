import { Injectable, NotImplementedException } from "@nestjs/common";
import { isNil } from "lodash/fp";

import { DomainError, ErrorMessages, isError, TrainingSessionPlan, TeamId } from "@mio/helpers";

import { TrainingSessionPlanRepository } from "./training-session-plan.repository";

@Injectable()
export class TrainingSessionPlanService {
  constructor(private repo: TrainingSessionPlanRepository) {}

  async create(dto: TrainingSessionPlan.CreateDto) {
    const trainingSessionPlan = TrainingSessionPlan.Entity.create(dto);

    const result = await this.repo.create(trainingSessionPlan);

    if (isError(result)) {
      result.addContext({
        service: TrainingSessionPlanService.name,
        method: this.create.name,
        operation: this.repo.create.name,
        dto,
      });

      return result;
    }

    return result;
  }

  async update(dto: TrainingSessionPlan.UpdateDto) {
    const trainingSessionPlan = await this.getById(dto.id);

    if (isError(trainingSessionPlan)) {
      return trainingSessionPlan;
    }

    if (isNil(trainingSessionPlan)) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: TrainingSessionPlanService.name,
        method: this.update.name,
        operation: this.repo.getById.name,
        dto,
      });
    }

    const updatedTrainingSessionPlan = TrainingSessionPlan.Entity.update(dto, trainingSessionPlan);

    const result = await this.repo.update(updatedTrainingSessionPlan);

    if (isError(result)) {
      result.addContext({
        service: TrainingSessionPlanService.name,
        method: this.update.name,
        operation: this.repo.update.name,
        dto,
      });

      return result;
    }

    return updatedTrainingSessionPlan;
  }

  async delete() {
    throw new NotImplementedException();
  }

  async getById(id: TrainingSessionPlan.PlanId) {
    const result = await this.repo.getById(id);

    if (isError(result)) {
      result.addContext({
        service: TrainingSessionPlanService.name,
        method: this.getById.name,
        operation: this.repo.getById.name,
        id,
      });
    }

    return result;
  }

  async find(teamId: TeamId) {
    const result = await this.repo.getByTeamId(teamId);

    if (isError(result)) {
      result.addContext({
        service: TrainingSessionPlanService.name,
        method: this.find.name,
        operation: this.repo.getByTeamId.name,
        teamId,
      });
    }

    return result;
  }
}
