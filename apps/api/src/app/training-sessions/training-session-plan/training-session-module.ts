import { Module } from "@nestjs/common";

import { AppConfigModule } from "../../config";
import { DatabaseModule } from "../../database";
import { PermissionsModule } from "../../permissions";
import { TrainingSessionPlanRepository } from "./training-session-plan.repository";
import { TrainingSessionPlanController } from "./training-session-plan.controller";
import { TrainingSessionPlanService } from "./training-session-plan.service";

@Module({
  imports: [DatabaseModule, AppConfigModule, PermissionsModule],
  providers: [TrainingSessionPlanRepository, TrainingSessionPlanService],
  controllers: [TrainingSessionPlanController],
  exports: [TrainingSessionPlanService],
})
export class TrainingSessionPlanModule {}
