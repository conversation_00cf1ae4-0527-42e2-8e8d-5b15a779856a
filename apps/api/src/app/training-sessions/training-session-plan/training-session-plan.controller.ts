import { Body, Controller, Get, Param, Patch, Post, UseGuards, UsePipes } from "@nestjs/common";
import { isNil } from "lodash/fp";

import {
  apiUrls,
  DomainError,
  ErrorMessages,
  ErrorTypes,
  isError,
  PermissionsModule,
  TeamId,
  TrainingSessionPlan,
  UrlParams,
} from "@mio/helpers";

import {
  IDParamPipe,
  toInternalServerError,
  toNotFoundError,
  ZodValidationPipe,
} from "../../shared";
import { JwtAuthGuard } from "../../auth/jwt-auth.guard";
import { PermissionsGuard, RequiresPermissions } from "../../permissions";
import { TrainingSessionPlanService } from "./training-session-plan.service";

@Controller()
export class TrainingSessionPlanController {
  constructor(private trainingSessionPlanService: TrainingSessionPlanService) {}

  @Post(apiUrls.trainingSessionPlans)
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UsePipes(new ZodValidationPipe(TrainingSessionPlan.Entity.createDto))
  async createTrainingSessionPlan(
    @Body() dto: TrainingSessionPlan.CreateDto,
  ): Promise<void | never> {
    const result = await this.trainingSessionPlanService.create(dto);

    if (isError(result)) {
      switch (result.type) {
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Patch(apiUrls.trainingSessionPlan)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @UsePipes(new ZodValidationPipe(TrainingSessionPlan.Entity.updateDto))
  async updateTrainingSessionPlan(@Body() dto: TrainingSessionPlan.UpdateDto) {
    return this.trainingSessionPlanService.update(dto).then((result) => {
      if (isError(result)) {
        switch (result.type) {
          case ErrorTypes.DomainError: {
            if (result.message === ErrorMessages.EntityNotFound) {
              throw toNotFoundError(result);
            }
            throw toInternalServerError(result);
          }
          case ErrorTypes.UnexpectedError: {
            throw toInternalServerError(result);
          }
          case ErrorTypes.ParsingError: {
            throw toInternalServerError(result);
          }
          default:
            throw toInternalServerError(result);
        }
      }
      return result;
    });
  }

  @Get(apiUrls.trainingSessionPlans)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async findTrainingSessionPlans(@Param(UrlParams.TeamId, IDParamPipe) teamId: TeamId) {
    const result = await this.trainingSessionPlanService.find(teamId);

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return result;
  }

  @Get(apiUrls.trainingSessionPlan)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async getTrainingSessionPlan(
    @Param(UrlParams.TrainingSessionPlanId, IDParamPipe)
    trainingSessionPlanId: TrainingSessionPlan.PlanId,
  ) {
    return this.trainingSessionPlanService.getById(trainingSessionPlanId).then((result) => {
      if (isError(result)) {
        throw toInternalServerError(result);
      }

      if (isNil(result)) {
        throw toNotFoundError(new DomainError(ErrorMessages.EntityNotFound));
      }

      return result;
    });
  }
}
