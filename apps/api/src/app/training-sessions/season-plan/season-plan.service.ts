import { Injectable, NotImplementedException } from "@nestjs/common";
import { isNil } from "lodash/fp";

import { DomainError, ErrorMessages, isError, SeasonPlan, TeamId } from "@mio/helpers";

import { SeasonPlanRepository } from "./season-plan.repository";

@Injectable()
export class SeasonPlanService {
  constructor(private repo: SeasonPlanRepository) {}

  async create(dto: SeasonPlan.CreateDto) {
    const seasonPlan = SeasonPlan.Entity.create(dto);

    const duplicateSeason = await this.findDuplicateSeasonPlan(seasonPlan);

    if (isError(duplicateSeason)) {
      return duplicateSeason;
    }

    const result = await this.repo.create(seasonPlan);

    if (isError(result)) {
      result.addContext({
        service: SeasonPlanService.name,
        method: this.create.name,
        operation: this.repo.create.name,
        dto,
      });

      return result;
    }

    return result;
  }

  async update(dto: SeasonPlan.UpdateDto) {
    const seasonPlan = await this.getById(dto.id);

    if (isError(seasonPlan)) {
      return seasonPlan;
    }

    if (isNil(seasonPlan)) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: SeasonPlanService.name,
        method: this.update.name,
        operation: this.repo.getById.name,
        dto,
      });
    }

    const updatedSeasonPlan = SeasonPlan.Entity.update(dto, seasonPlan);

    const result = await this.repo.update(updatedSeasonPlan);

    if (isError(result)) {
      result.addContext({
        service: SeasonPlanService.name,
        method: this.update.name,
        operation: this.repo.update.name,
        dto,
      });

      return result;
    }

    return updatedSeasonPlan;
  }

  async delete() {
    throw new NotImplementedException();
  }

  private async findDuplicateSeasonPlan(seasonPlan: SeasonPlan.SeasonPlan) {
    const seasonPlans = await this.repo.getByTeamId(seasonPlan.teamId);

    if (isError(seasonPlans)) {
      seasonPlans.addContext({
        service: SeasonPlanService.name,
        method: this.findDuplicateSeasonPlan.name,
        operation: this.repo.getByTeamId.name,
        seasonPlan,
      });

      return seasonPlans;
    }

    if (isNil(seasonPlans)) {
      return undefined;
    }

    if (SeasonPlan.Entity.duplicateSeasonPlanExists(seasonPlans, seasonPlan)) {
      return new DomainError(ErrorMessages.EntityAlreadyExists, {
        service: SeasonPlanService.name,
        method: this.findDuplicateSeasonPlan.name,
        operation: SeasonPlan.Entity.duplicateSeasonPlanExists.name,
        seasonPlan,
      });
    }
  }

  async getById(id: SeasonPlan.SeasonPlanId) {
    const result = await this.repo.getById(id);

    if (isError(result)) {
      result.addContext({
        service: SeasonPlanService.name,
        method: this.getById.name,
        operation: this.repo.getById.name,
        id,
      });
    }

    return result;
  }

  async find(teamId: TeamId) {
    const result = await this.repo.getByTeamId(teamId);

    if (isError(result)) {
      result.addContext({
        service: SeasonPlanService.name,
        method: this.find.name,
        operation: this.repo.getByTeamId.name,
        teamId,
      });
    }

    return result;
  }
}
