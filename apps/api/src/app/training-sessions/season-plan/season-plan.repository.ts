import { Inject, Injectable } from "@nestjs/common";

import {
  Primitive,
  UnexpectedError,
  TeamId,
  ParsingError,
  isError,
  DomainError,
  ErrorMessages,
  SeasonPlan,
} from "@mio/helpers";

import { DATABASE_CONNECTION, DBClient, DBCollection } from "../../database";

type PlayerTeamProfileDocument = Primitive<SeasonPlan.SeasonPlan>;

export const seasonPlanCollection = "season-plans";

@Injectable()
export class SeasonPlanRepository {
  private collection: DBCollection<PlayerTeamProfileDocument>;

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection(seasonPlanCollection);
  }

  //#region Commands

  async create(seasonPlan: SeasonPlan.SeasonPlan): Promise<UnexpectedError | void> {
    return this.collection
      .insertOne({
        ...seasonPlan,
      })
      .then(() => undefined)
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async update(seasonPlan: SeasonPlan.SeasonPlan) {
    const result = await this.collection
      .replaceOne({ id: seasonPlan.id }, seasonPlan)
      .then((res) => {
        if (res.modifiedCount < 1) {
          return new DomainError(ErrorMessages.EntityNotFound, {
            service: SeasonPlanRepository.name,
            method: this.update.name,
            operation: this.collection.replaceOne.name,
            seasonPlan,
          });
        }
        if (res.modifiedCount > 1) {
          return new DomainError(ErrorMessages.MultipleEntitiesFound, {
            service: SeasonPlanRepository.name,
            method: this.update.name,
            operation: this.collection.replaceOne.name,
            seasonPlan,
          });
        }

        return undefined;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: SeasonPlanRepository.name,
          method: this.update.name,
          operation: this.collection.replaceOne.name,
          seasonPlan,
        });
      });

    if (isError(result)) {
      result.addContext({
        service: SeasonPlanRepository.name,
        method: this.update.name,
        operation: this.collection.replaceOne,
        payload: result,
      });

      return result;
    }

    return undefined;
  }

  //#endregion

  //#region Queries

  async getById(
    id: SeasonPlan.SeasonPlanId,
  ): Promise<UnexpectedError | ParsingError | SeasonPlan.SeasonPlan | null> {
    return this.collection
      .findOne({ id })
      .then((seasonPlan) => {
        if (seasonPlan) {
          const parsed = SeasonPlan.Entity.toEntity(seasonPlan);
          if (isError(parsed)) {
            parsed.addContext({
              service: SeasonPlanRepository.name,
              method: this.getById.name,
              operation: SeasonPlan.Entity.toEntity.name,
              seasonPlanId: id,
            });

            return parsed;
          }

          return parsed;
        }
        return null;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err, {
          service: SeasonPlanRepository.name,
          method: this.getById.name,
          operation: this.collection.findOne.name,
        });
      });
  }

  getByTeamId(teamId: TeamId): Promise<UnexpectedError | ParsingError | SeasonPlan.SeasonPlan[]> {
    return this.collection
      .find({ teamId })
      .toArray()
      .then((seasonPlans) => {
        if (seasonPlans) {
          const parsed = SeasonPlan.Entity.toEntities(seasonPlans);
          if (isError(parsed)) {
            parsed.addContext({
              service: SeasonPlanRepository.name,
              method: this.getByTeamId.name,
              operation: SeasonPlan.Entity.toEntities.name,
              teamId,
            });

            return parsed;
          }

          return parsed;
        }

        return [];
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err, {
          service: SeasonPlanRepository.name,
          method: this.getByTeamId.name,
          operation: this.collection.find.name,
        });
      });
  }

  //#endregion
}
