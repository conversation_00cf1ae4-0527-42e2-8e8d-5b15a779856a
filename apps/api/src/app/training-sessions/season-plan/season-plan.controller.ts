import { Body, Controller, Get, Param, Patch, Post, UseGuards, UsePipes } from "@nestjs/common";
import { isNil } from "lodash/fp";

import {
  apiUrls,
  DomainError,
  ErrorMessages,
  ErrorTypes,
  isError,
  PermissionsModule,
  SeasonPlan,
  TeamId,
  UrlParams,
} from "@mio/helpers";

import {
  IDParamPipe,
  toBadRequest,
  toInternalServerError,
  toNotFoundError,
  ZodValidationPipe,
} from "../../shared";
import { JwtAuthGuard } from "../../auth/jwt-auth.guard";
import { PermissionsGuard, RequiresPermissions } from "../../permissions";
import { SeasonPlanService } from "./season-plan.service";

@Controller()
export class SeasonPlanController {
  constructor(private seasonPlanService: SeasonPlanService) {}

  @Post(apiUrls.seasonPlans)
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UsePipes(new ZodValidationPipe(SeasonPlan.Entity.createDto))
  async createSeasonPlan(@Body() dto: SeasonPlan.CreateDto): Promise<void | never> {
    const result = await this.seasonPlanService.create(dto);

    if (isError(result)) {
      switch (result.type) {
        case ErrorTypes.DomainError:
          throw toBadRequest(result);
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Patch(apiUrls.seasonPlan)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @UsePipes(new ZodValidationPipe(SeasonPlan.Entity.updateDto))
  async updateSeasonPlan(@Body() dto: SeasonPlan.UpdateDto) {
    return this.seasonPlanService.update(dto).then((result) => {
      if (isError(result)) {
        switch (result.type) {
          case ErrorTypes.DomainError: {
            if (result.message === ErrorMessages.EntityNotFound) {
              throw toNotFoundError(result);
            }
            throw toInternalServerError(result);
          }
          case ErrorTypes.UnexpectedError: {
            throw toInternalServerError(result);
          }
          case ErrorTypes.ParsingError: {
            throw toInternalServerError(result);
          }
          default:
            throw toInternalServerError(result);
        }
      }
      return result;
    });
  }

  @Get(apiUrls.seasonPlans)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async findSeasonPlans(@Param(UrlParams.TeamId, IDParamPipe) teamId: TeamId) {
    const result = await this.seasonPlanService.find(teamId);

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return result;
  }

  @Get(apiUrls.seasonPlan)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async getSeasonPlan(
    @Param(UrlParams.SeasonPlanId, IDParamPipe) seasonPlanId: SeasonPlan.SeasonPlanId,
  ) {
    return this.seasonPlanService.getById(seasonPlanId).then((result) => {
      if (isError(result)) {
        throw toInternalServerError(result);
      }

      if (isNil(result)) {
        throw toNotFoundError(new DomainError(ErrorMessages.EntityNotFound));
      }

      return result;
    });
  }
}
