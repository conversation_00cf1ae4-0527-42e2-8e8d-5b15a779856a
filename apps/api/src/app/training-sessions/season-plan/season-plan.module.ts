import { Module } from "@nestjs/common";

import { AppConfigModule } from "../../config";
import { DatabaseModule } from "../../database";
import { PermissionsModule } from "../../permissions";
import { SeasonPlanRepository } from "./season-plan.repository";
import { SeasonPlanService } from "./season-plan.service";
import { SeasonPlanController } from "./season-plan.controller";

@Module({
  imports: [DatabaseModule, AppConfigModule, PermissionsModule],
  providers: [SeasonPlanRepository, SeasonPlanService],
  controllers: [SeasonPlanController],
  exports: [SeasonPlanService],
})
export class SeasonPlanModule {}
