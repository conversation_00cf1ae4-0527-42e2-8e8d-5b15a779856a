import { Modu<PERSON> } from "@nestjs/common";

import { AppConfigModule } from "../../config";
import { DatabaseModule } from "../../database";
import { PermissionsModule } from "../../permissions";
import { TrainingSessionReviewRepository } from "./training-session-review.repository";
import { TrainingSessionReviewController } from "./training-session-review.controller";
import { TrainingSessionReviewService } from "./training-session-review.service";
import { TeamEventModule } from "../../team-events/instances/team-event.module";
import { PlayerModule } from "../../player";

@Module({
  imports: [DatabaseModule, AppConfigModule, PermissionsModule, TeamEventModule, PlayerModule],
  providers: [TrainingSessionReviewRepository, TrainingSessionReviewService],
  controllers: [TrainingSessionReviewController],
  exports: [TrainingSessionReviewService],
})
export class TrainingSessionReviewModule {}
