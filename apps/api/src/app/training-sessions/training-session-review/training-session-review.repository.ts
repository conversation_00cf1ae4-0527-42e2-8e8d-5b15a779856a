import { Inject, Injectable } from "@nestjs/common";

import {
  Primitive,
  UnexpectedError,
  TeamId,
  ParsingError,
  isError,
  DomainError,
  ErrorMessages,
  TrainingSessionReview,
  CustomError,
} from "@mio/helpers";

import { DATABASE_CONNECTION, DBClient, DBCollection } from "../../database";
import { trainingSessionPlayerReviewCollection } from "../training-session-player-review/training-session-player-review.repository";

type TrainingSessionReviewDocument = Primitive<TrainingSessionReview.Review>;

export const trainingSessionReviewCollection = "training-session-reviews";

@Injectable()
export class TrainingSessionReviewRepository {
  private collection: DBCollection<TrainingSessionReviewDocument>;

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection(trainingSessionReviewCollection);
  }

  //#region Commands

  async create(review: TrainingSessionReview.Review): Promise<UnexpectedError | void> {
    return this.collection
      .insertOne({
        ...review,
      })
      .then(() => undefined)
      .catch((err) => {
        return new UnexpectedError(err, {
          service: TrainingSessionReviewRepository.name,
          method: this.create.name,
          operation: this.collection.insertOne.name,
        });
      });
  }

  async update(review: TrainingSessionReview.Review) {
    const result = await this.collection
      .replaceOne({ id: review.id }, review)
      .then((res) => {
        if (res.modifiedCount < 1) {
          return new DomainError(ErrorMessages.EntityNotFound, {
            service: TrainingSessionReviewRepository.name,
            method: this.update.name,
            operation: this.collection.replaceOne.name,
            review: review,
          });
        }
        if (res.modifiedCount > 1) {
          return new DomainError(ErrorMessages.MultipleEntitiesFound, {
            service: TrainingSessionReviewRepository.name,
            method: this.update.name,
            operation: this.collection.replaceOne.name,
            review: review,
          });
        }

        return undefined;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: TrainingSessionReviewRepository.name,
          method: this.update.name,
          operation: this.collection.replaceOne.name,
          review: review,
        });
      });

    if (isError(result)) {
      result.addContext({
        service: TrainingSessionReviewRepository.name,
        method: this.update.name,
        operation: this.collection.replaceOne,
        payload: result,
      });

      return result;
    }

    return undefined;
  }

  //#endregion

  //#region Queries

  async getByIdWithPlayerReviews(
    id: TrainingSessionReview.ReviewId,
  ): Promise<CustomError | TrainingSessionReview.EntityWithPlayerReviews | null> {
    const stages = [
      {
        $match: { id },
      },
      {
        $lookup: {
          from: trainingSessionPlayerReviewCollection,
          localField: "id",
          foreignField: "trainingSessionReviewId",
          as: "playerReviews",
        },
      },
    ];

    return this.collection
      .aggregate(stages)
      .toArray()
      .then((result) => {
        if (result) {
          /* we expect only 1 TrainingSessionReview therefore we have the checks below */

          if (result.length === 0) {
            return null;
          }

          if (result.length > 1) {
            return new DomainError(ErrorMessages.MultipleEntitiesFound, {
              service: TrainingSessionReviewRepository.name,
              method: this.getByIdWithPlayerReviews.name,
              reviewId: id,
            });
          }

          const parsed = TrainingSessionReview.Entity.toEntityWithPlayerReviews(result[0]);

          if (isError(parsed)) {
            parsed.addContext({
              service: TrainingSessionReviewRepository.name,
              method: this.getByIdWithPlayerReviews.name,
              operation: TrainingSessionReview.Entity.toEntityWithPlayerReviews.name,
              data: result,
              reviewId: id,
            });

            return parsed;
          }

          return parsed;
        }

        return null;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: TrainingSessionReviewRepository.name,
          method: this.getByIdWithPlayerReviews.name,
          reviewId: id,
        });
      });
  }

  async getByTeamId(
    teamId: TeamId,
  ): Promise<UnexpectedError | ParsingError | TrainingSessionReview.Review[]> {
    return this.collection
      .find({ teamId })
      .toArray()
      .then((reviews) => {
        if (reviews) {
          const parsed = TrainingSessionReview.Entity.toEntities(reviews);
          if (isError(parsed)) {
            parsed.addContext({
              service: TrainingSessionReviewRepository.name,
              method: this.getByTeamId.name,
              operation: TrainingSessionReview.Entity.toEntities.name,
              teamId,
            });

            return parsed;
          }

          return parsed;
        }

        return [];
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: TrainingSessionReviewRepository.name,
          method: this.getByTeamId.name,
          operation: this.collection.find.name,
        });
      });
  }

  async getById(
    id: TrainingSessionReview.ReviewId,
  ): Promise<UnexpectedError | ParsingError | TrainingSessionReview.Review | null> {
    return this.collection
      .findOne({ id })
      .then((review) => {
        if (review) {
          const parsed = TrainingSessionReview.Entity.toEntity(review);

          if (isError(parsed)) {
            parsed.addContext({
              service: TrainingSessionReviewRepository.name,
              method: this.getById.name,
              operation: TrainingSessionReview.Entity.toEntity.name,
              playerReviewId: id,
            });

            return parsed;
          }

          return parsed;
        }
        return null;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err, {
          service: TrainingSessionReviewRepository.name,
          method: this.getById.name,
          operation: this.collection.findOne.name,
        });
      });
  }

  //#endregion
}
