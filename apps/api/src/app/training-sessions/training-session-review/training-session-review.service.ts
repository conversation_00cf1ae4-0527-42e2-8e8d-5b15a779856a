import { Injectable, NotImplementedException } from "@nestjs/common";
import { isNil } from "lodash/fp";

import {
  DomainError,
  ErrorMessages,
  isError,
  TrainingSessionReview,
  TeamId,
  CustomError,
  Player,
} from "@mio/helpers";

import { TrainingSessionReviewRepository } from "./training-session-review.repository";
import { TeamEventService } from "../../team-events/instances/team-event.service";
import { PlayerService } from "../../player/player.service";

@Injectable()
export class TrainingSessionReviewService {
  constructor(
    private repo: TrainingSessionReviewRepository,
    private teamEventService: TeamEventService,
    private playerService: PlayerService,
  ) {}

  async create(dto: TrainingSessionReview.CreateDto) {
    const trainingSessionReview = TrainingSessionReview.Entity.create(dto);

    const result = await this.repo.create(trainingSessionReview);

    if (isError(result)) {
      result.addContext({
        service: TrainingSessionReviewService.name,
        method: this.create.name,
        operation: this.repo.create.name,
        dto,
      });

      return result;
    }

    return trainingSessionReview;
  }

  async update(dto: TrainingSessionReview.UpdateDto) {
    const trainingSessionReview = await this.repo.getById(dto.id);

    if (isError(trainingSessionReview)) {
      trainingSessionReview.addContext({
        service: TrainingSessionReviewService.name,
        method: this.update.name,
        operation: this.repo.getById.name,
        dto,
      });

      return trainingSessionReview;
    }

    if (isNil(trainingSessionReview)) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: TrainingSessionReviewService.name,
        method: this.update.name,
        operation: this.repo.getById.name,
        dto,
      });
    }

    const updatedTrainingSessionReview = TrainingSessionReview.Entity.update(
      dto,
      trainingSessionReview,
    );

    const result = await this.repo.update(updatedTrainingSessionReview);

    if (isError(result)) {
      result.addContext({
        service: TrainingSessionReviewService.name,
        method: this.update.name,
        operation: this.repo.update.name,
        dto,
      });

      return result;
    }

    return updatedTrainingSessionReview;
  }

  async delete() {
    throw new NotImplementedException();
  }

  async getExtendedReview(
    id: TrainingSessionReview.ReviewId,
  ): Promise<CustomError | TrainingSessionReview.ExtendedReview> {
    const review = await this.repo.getByIdWithPlayerReviews(id);

    if (isError(review)) {
      review.addContext({
        service: TrainingSessionReviewService.name,
        method: this.getExtendedReview.name,
        operation: this.repo.getByIdWithPlayerReviews.name,
        id,
      });

      return review;
    }

    if (!review) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: TrainingSessionReviewService.name,
        method: this.getExtendedReview.name,
        operation: this.repo.getByIdWithPlayerReviews.name,
        id,
      });
    }

    const teamEvent = await this.teamEventService.getExtendedTeamEvent(review.teamEventId);

    if (isError(teamEvent)) {
      teamEvent.addContext({
        service: TrainingSessionReviewService.name,
        method: this.getExtendedReview.name,
        operation: this.teamEventService.getExtendedTeamEvent.name,
        id,
      });

      return teamEvent;
    }

    /*
    Querying players by only teamId is not enough, because some players might
    have a historic review but not be part of the team anymore. Therefore we
    also query by existingReview.playerId for each existing review.
    */
    const associatedPlayers = await this.playerService.getReviewPlayers(
      review.teamId,
      review.playerReviews.map((elem) => elem.playerId),
    );

    if (isError(associatedPlayers)) {
      associatedPlayers.addContext({
        service: TrainingSessionReviewService.name,
        method: this.getExtendedReview.name,
        operation: this.playerService.getReviewPlayers.name,
        id,
        teamId: review.teamId,
      });

      return associatedPlayers;
    }

    return TrainingSessionReview.Entity.addEvent(
      review,
      teamEvent,
      associatedPlayers.map(Player.toLean),
    );
  }

  async find(teamId: TeamId) {
    const result = await this.repo.getByTeamId(teamId);

    if (isError(result)) {
      result.addContext({
        service: TrainingSessionReviewService.name,
        method: this.find.name,
        operation: this.repo.getByTeamId.name,
        teamId,
      });
    }

    return result;
  }
}
