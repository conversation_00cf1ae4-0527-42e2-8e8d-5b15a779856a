import { Body, Controller, Get, Param, Patch, Post, UseGuards, UsePipes } from "@nestjs/common";
import { isNil } from "lodash/fp";

import {
  apiUrls,
  DomainError,
  ErrorMessages,
  ErrorTypes,
  isError,
  PermissionsModule,
  TrainingSessionReview,
  TeamId,
  UrlParams,
  UnexpectedError,
} from "@mio/helpers";

import {
  IDParamPipe,
  toBadRequest,
  toInternalServerError,
  toNotFoundError,
  ZodValidationPipe,
} from "../../shared";
import { JwtAuthGuard } from "../../auth/jwt-auth.guard";
import { PermissionsGuard, RequiresPermissions } from "../../permissions";
import { TrainingSessionReviewService } from "./training-session-review.service";

@Controller()
export class TrainingSessionReviewController {
  constructor(private trainingSessionReviewService: TrainingSessionReviewService) {}

  @Post(apiUrls.trainingSessionReviews)
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageReviews])
  @UsePipes(new ZodValidationPipe(TrainingSessionReview.Entity.createDto))
  async createTrainingSessionReview(
    @Body() dto: TrainingSessionReview.CreateDto,
  ): Promise<TrainingSessionReview.Review | never> {
    const result = await this.trainingSessionReviewService.create(dto);

    if (isError(result)) {
      switch (result.type) {
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Patch(apiUrls.trainingSessionReview)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageReviews])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @UsePipes(new ZodValidationPipe(TrainingSessionReview.Entity.updateDto))
  async updateTrainingSessionReview(@Body() dto: TrainingSessionReview.UpdateDto) {
    return this.trainingSessionReviewService.update(dto).then((result) => {
      if (isError(result)) {
        switch (result.type) {
          case ErrorTypes.DomainError: {
            if (result.message === ErrorMessages.EntityNotFound) {
              throw toNotFoundError(result);
            }
            throw toInternalServerError(result);
          }
          case ErrorTypes.UnexpectedError: {
            throw toInternalServerError(result);
          }
          case ErrorTypes.ParsingError: {
            throw toInternalServerError(result);
          }
          default:
            throw toInternalServerError(result);
        }
      }
      return result;
    });
  }

  @Get(apiUrls.trainingSessionReviews)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageReviews])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async findTrainingSessionReviews(@Param(UrlParams.TeamId, IDParamPipe) teamId: TeamId) {
    const result = await this.trainingSessionReviewService.find(teamId);

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return result;
  }

  @Get(apiUrls.trainingSessionReview)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageReviews])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async getTrainingSessionReview(
    @Param(UrlParams.TrainingSessionReviewId, IDParamPipe)
    trainingSessionReviewId: TrainingSessionReview.ReviewId,
  ): Promise<TrainingSessionReview.ExtendedReview> {
    const review = await this.trainingSessionReviewService.getExtendedReview(
      trainingSessionReviewId,
    );

    if (isError(review)) {
      switch (review.type) {
        case ErrorTypes.ParsingError:
        case ErrorTypes.UnexpectedError:
          throw toInternalServerError(review);
        case ErrorTypes.DomainError:
          throw toBadRequest(review);
        default:
          throw toInternalServerError(new UnexpectedError(review));
      }
    }

    if (isNil(review)) {
      throw toNotFoundError(new DomainError(ErrorMessages.EntityNotFound));
    }

    return review;
  }
}
