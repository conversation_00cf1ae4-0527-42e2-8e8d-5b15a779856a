import { Modu<PERSON> } from "@nestjs/common";

import { AppConfigModule } from "../../config";
import { DatabaseModule } from "../../database";
import { PermissionsModule } from "../../permissions";
import { TrainingSessionPlayerReviewRepository } from "./training-session-player-review.repository";
import { TrainingSessionPlayerReviewController } from "./training-session-player-review.controller";
import { TrainingSessionPlayerReviewService } from "./training-session-player-review.service";

@Module({
  imports: [DatabaseModule, AppConfigModule, PermissionsModule],
  providers: [TrainingSessionPlayerReviewRepository, TrainingSessionPlayerReviewService],
  controllers: [TrainingSessionPlayerReviewController],
  exports: [TrainingSessionPlayerReviewService],
})
export class TrainingSessionPlayerReviewModule {}
