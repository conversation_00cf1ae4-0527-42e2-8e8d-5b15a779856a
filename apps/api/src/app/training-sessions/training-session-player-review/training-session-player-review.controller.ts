import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
  UsePipes,
} from "@nestjs/common";
import { isNil } from "lodash/fp";

import {
  apiUrls,
  DomainError,
  ErrorMessages,
  ErrorTypes,
  isError,
  PermissionsModule,
  TrainingSessionPlayerReview,
  TeamId,
  UrlParams,
} from "@mio/helpers";

import {
  IDParamPipe,
  toInternalServerError,
  toNotFoundError,
  ZodValidationPipe,
} from "../../shared";
import { JwtAuthGuard } from "../../auth/jwt-auth.guard";
import { PermissionsGuard, RequiresPermissions } from "../../permissions";
import { TrainingSessionPlayerReviewService } from "./training-session-player-review.service";
import { PlayerReviewsQueryPipe } from "./query.pipe";

@Controller()
export class TrainingSessionPlayerReviewController {
  constructor(private trainingSessionPlayerReviewService: TrainingSessionPlayerReviewService) {}

  @Post(apiUrls.trainingSessionPlayerReviews)
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UsePipes(new ZodValidationPipe(TrainingSessionPlayerReview.Entity.createDto))
  async createTrainingSessionPlayerReview(
    @Body() dto: TrainingSessionPlayerReview.CreateDto,
  ): Promise<TrainingSessionPlayerReview.PlayerReview | never> {
    const result = await this.trainingSessionPlayerReviewService.create(dto);

    if (isError(result)) {
      switch (result.type) {
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Patch(apiUrls.trainingSessionPlayerReview)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @UsePipes(new ZodValidationPipe(TrainingSessionPlayerReview.Entity.updateDto))
  async updateTrainingSessionPlayerReview(
    @Body() dto: TrainingSessionPlayerReview.UpdateDto,
  ): Promise<TrainingSessionPlayerReview.PlayerReview> {
    return this.trainingSessionPlayerReviewService.update(dto).then((result) => {
      if (isError(result)) {
        switch (result.type) {
          case ErrorTypes.DomainError: {
            if (result.message === ErrorMessages.EntityNotFound) {
              throw toNotFoundError(result);
            }
            throw toInternalServerError(result);
          }
          case ErrorTypes.UnexpectedError: {
            throw toInternalServerError(result);
          }
          case ErrorTypes.ParsingError: {
            throw toInternalServerError(result);
          }
          default:
            throw toInternalServerError(result);
        }
      }
      return result;
    });
  }

  @Get(apiUrls.trainingSessionPlayerReviews)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async getExtendedReviews(
    @Param(UrlParams.TeamId, IDParamPipe) teamId: TeamId,
    @Query("", PlayerReviewsQueryPipe) query: TrainingSessionPlayerReview.QueryDto,
  ): Promise<TrainingSessionPlayerReview.ExtendedReview[]> {
    const result = await this.trainingSessionPlayerReviewService.getExtendedReviews(teamId, query);

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return result;
  }

  @Get(apiUrls.trainingSessionPlayerReview)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async getTrainingSessionPlayerReview(
    @Param(UrlParams.TrainingSessionPlayerReviewId, IDParamPipe)
    trainingSessionPlayerReviewId: TrainingSessionPlayerReview.PlayerReviewId,
  ) {
    return this.trainingSessionPlayerReviewService
      .getById(trainingSessionPlayerReviewId)
      .then((result) => {
        if (isError(result)) {
          throw toInternalServerError(result);
        }

        if (isNil(result)) {
          throw toNotFoundError(new DomainError(ErrorMessages.EntityNotFound));
        }

        return result;
      });
  }
}
