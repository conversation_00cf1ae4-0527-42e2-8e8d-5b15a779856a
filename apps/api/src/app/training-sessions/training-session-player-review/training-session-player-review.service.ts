import { Injectable, NotImplementedException } from "@nestjs/common";
import { isNil } from "lodash/fp";

import {
  DomainError,
  ErrorMessages,
  isError,
  TrainingSessionPlayerReview,
  TeamId,
  UnexpectedError,
  CustomError,
  ParsingError,
} from "@mio/helpers";

import { TrainingSessionPlayerReviewRepository } from "./training-session-player-review.repository";

@Injectable()
export class TrainingSessionPlayerReviewService {
  constructor(private repo: TrainingSessionPlayerReviewRepository) {}

  async create(
    dto: TrainingSessionPlayerReview.CreateDto,
  ): Promise<TrainingSessionPlayerReview.PlayerReview | UnexpectedError> {
    const trainingSessionPlayerReview = TrainingSessionPlayerReview.Entity.create(dto);

    const result = await this.repo.create(trainingSessionPlayerReview);

    if (isError(result)) {
      result.addContext({
        service: TrainingSessionPlayerReviewService.name,
        method: this.create.name,
        operation: this.repo.create.name,
        dto,
      });

      return result;
    }

    return trainingSessionPlayerReview;
  }

  async update(
    dto: TrainingSessionPlayerReview.UpdateDto,
  ): Promise<TrainingSessionPlayerReview.PlayerReview | CustomError> {
    const trainingSessionPlayerReview = await this.getById(dto.id);

    if (isError(trainingSessionPlayerReview)) {
      return trainingSessionPlayerReview;
    }

    if (isNil(trainingSessionPlayerReview)) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: TrainingSessionPlayerReviewService.name,
        method: this.update.name,
        operation: this.repo.getById.name,
        dto,
      });
    }

    const updatedTrainingSessionPlayerReview = TrainingSessionPlayerReview.Entity.update(
      dto,
      trainingSessionPlayerReview,
    );

    const result = await this.repo.update(updatedTrainingSessionPlayerReview);

    if (isError(result)) {
      result.addContext({
        service: TrainingSessionPlayerReviewService.name,
        method: this.update.name,
        operation: this.repo.update.name,
        dto,
      });

      return result;
    }

    return updatedTrainingSessionPlayerReview;
  }

  async delete() {
    throw new NotImplementedException();
  }

  async getById(id: TrainingSessionPlayerReview.PlayerReviewId) {
    const result = await this.repo.getById(id);

    if (isError(result)) {
      result.addContext({
        service: TrainingSessionPlayerReviewService.name,
        method: this.getById.name,
        operation: this.repo.getById.name,
        id,
      });
    }

    return result;
  }

  async getExtendedReviews(
    teamId: TeamId,
    query: TrainingSessionPlayerReview.QueryDto,
  ): Promise<UnexpectedError | ParsingError | TrainingSessionPlayerReview.ExtendedReview[]> {
    const reviews = await this.repo.getExtendedReviews(teamId, query);

    if (isError(reviews)) {
      reviews.addContext({
        service: TrainingSessionPlayerReviewService.name,
        method: this.getExtendedReviews.name,
        operation: this.repo.getExtendedReviews,
        query,
      });

      return reviews;
    }

    return reviews;
  }

  async find(teamId: TeamId) {
    const result = await this.repo.getByTeamId(teamId);

    if (isError(result)) {
      result.addContext({
        service: TrainingSessionPlayerReviewService.name,
        method: this.find.name,
        operation: this.repo.getByTeamId.name,
        teamId,
      });
    }

    return result;
  }
}
