import { PipeTransform, Injectable, ArgumentMetadata } from "@nestjs/common";

import { isError, TrainingSessionPlayerReview } from "@mio/helpers";

import { toBadRequest } from "../../shared";

@Injectable()
export class PlayerReviewsQueryPipe implements PipeTransform {
  transform(value: unknown, metadata: ArgumentMetadata) {
    if (metadata.type === "query") {
      const result = TrainingSessionPlayerReview.Entity.toQueryDto(value);

      if (isError(result)) {
        result.addContext({
          service: PlayerReviewsQueryPipe.name,
          value,
        });

        throw toBadRequest(result);
      }

      return result;
    }

    return value;
  }
}
