import { Injectable, NotImplementedException } from "@nestjs/common";
import { isNil } from "lodash/fp";

import { DomainError, ErrorMessages, isError, TrainingSessionPractice, TeamId } from "@mio/helpers";

import { TrainingSessionPracticeRepository } from "./practice.repository";

@Injectable()
export class TrainingSessionPracticeService {
  constructor(private repo: TrainingSessionPracticeRepository) {}

  async create(dto: TrainingSessionPractice.CreateDto) {
    const trainingSessionPractice = TrainingSessionPractice.Entity.create(dto);

    const result = await this.repo.create(trainingSessionPractice);

    if (isError(result)) {
      result.addContext({
        service: TrainingSessionPracticeService.name,
        method: this.create.name,
        operation: this.repo.create.name,
        dto,
      });

      return result;
    }

    return result;
  }

  async update(dto: TrainingSessionPractice.UpdateDto) {
    const trainingSessionPractice = await this.getById(dto.id);

    if (isError(trainingSessionPractice)) {
      return trainingSessionPractice;
    }

    if (isNil(trainingSessionPractice)) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: TrainingSessionPracticeService.name,
        method: this.update.name,
        operation: this.repo.getById.name,
        dto,
      });
    }

    const updatedTrainingSessionPractice = TrainingSessionPractice.Entity.update(
      dto,
      trainingSessionPractice,
    );

    const result = await this.repo.update(updatedTrainingSessionPractice);

    if (isError(result)) {
      result.addContext({
        service: TrainingSessionPracticeService.name,
        method: this.update.name,
        operation: this.repo.update.name,
        dto,
      });

      return result;
    }

    return updatedTrainingSessionPractice;
  }

  async delete() {
    throw new NotImplementedException();
  }

  async getById(id: TrainingSessionPractice.PracticeId) {
    const result = await this.repo.getById(id);

    if (isError(result)) {
      result.addContext({
        service: TrainingSessionPracticeService.name,
        method: this.getById.name,
        operation: this.repo.getById.name,
        id,
      });
    }

    return result;
  }

  async find(teamId: TeamId) {
    const result = await this.repo.getByTeamId(teamId);

    if (isError(result)) {
      result.addContext({
        service: TrainingSessionPracticeService.name,
        method: this.find.name,
        operation: this.repo.getByTeamId.name,
        teamId,
      });
    }

    return result;
  }
}
