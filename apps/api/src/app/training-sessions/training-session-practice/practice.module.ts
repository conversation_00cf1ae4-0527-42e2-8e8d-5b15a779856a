import { Modu<PERSON> } from "@nestjs/common";

import { AppConfigModule } from "../../config";
import { DatabaseModule } from "../../database";
import { PermissionsModule } from "../../permissions";
import { TrainingSessionPracticeRepository } from "./practice.repository";
import { TrainingSessionPracticeService } from "./practice.service";
import { TrainingSessionPracticeController } from "./practice.controller";

@Module({
  imports: [DatabaseModule, AppConfigModule, PermissionsModule],
  providers: [TrainingSessionPracticeRepository, TrainingSessionPracticeService],
  controllers: [TrainingSessionPracticeController],
  exports: [TrainingSessionPracticeService],
})
export class TrainingSessionPracticeModule {}
