import { Body, Controller, Get, Param, Patch, Post, UseGuards, UsePipes } from "@nestjs/common";
import { isNil } from "lodash/fp";

import {
  apiUrls,
  DomainError,
  ErrorMessages,
  ErrorTypes,
  isError,
  PermissionsModule,
  TrainingSessionPractice,
  TeamId,
  UrlParams,
} from "@mio/helpers";

import {
  IDParamPipe,
  toInternalServerError,
  toNotFoundError,
  ZodValidationPipe,
} from "../../shared";
import { JwtAuthGuard } from "../../auth/jwt-auth.guard";
import { PermissionsGuard, RequiresPermissions } from "../../permissions";
import { TrainingSessionPracticeService } from "./practice.service";

@Controller()
export class TrainingSessionPracticeController {
  constructor(private trainingSessionPracticeService: TrainingSessionPracticeService) {}

  @Post(apiUrls.trainingSessionPractices)
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UsePipes(new ZodValidationPipe(TrainingSessionPractice.Entity.createDto))
  async createTrainingSessionPractice(
    @Body() dto: TrainingSessionPractice.CreateDto,
  ): Promise<void | never> {
    const result = await this.trainingSessionPracticeService.create(dto);

    if (isError(result)) {
      switch (result.type) {
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Patch(apiUrls.trainingSessionPractice)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @UsePipes(new ZodValidationPipe(TrainingSessionPractice.Entity.updateDto))
  async updateTrainingSessionPractice(@Body() dto: TrainingSessionPractice.UpdateDto) {
    return this.trainingSessionPracticeService.update(dto).then((result) => {
      if (isError(result)) {
        switch (result.type) {
          case ErrorTypes.DomainError: {
            if (result.message === ErrorMessages.EntityNotFound) {
              throw toNotFoundError(result);
            }
            throw toInternalServerError(result);
          }
          case ErrorTypes.UnexpectedError: {
            throw toInternalServerError(result);
          }
          case ErrorTypes.ParsingError: {
            throw toInternalServerError(result);
          }
          default:
            throw toInternalServerError(result);
        }
      }
      return result;
    });
  }

  @Get(apiUrls.trainingSessionPractices)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async findTrainingSessionPractices(@Param(UrlParams.TeamId, IDParamPipe) teamId: TeamId) {
    const result = await this.trainingSessionPracticeService.find(teamId);

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return result;
  }

  @Get(apiUrls.trainingSessionPractice)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async getTrainingSessionPractice(
    @Param(UrlParams.TrainingSessionPracticeId, IDParamPipe)
    trainingSessionPracticeId: TrainingSessionPractice.PracticeId,
  ) {
    return this.trainingSessionPracticeService.getById(trainingSessionPracticeId).then((result) => {
      if (isError(result)) {
        throw toInternalServerError(result);
      }

      if (isNil(result)) {
        throw toNotFoundError(new DomainError(ErrorMessages.EntityNotFound));
      }

      return result;
    });
  }
}
