import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  UseGuards,
  UsePipes,
} from "@nestjs/common";

import {
  apiUrls,
  ErrorTypes,
  isError,
  OrganizationId,
  PermissionsModule,
  Profile,
  UrlParams,
} from "@mio/helpers";
import { JwtAuthGuard } from "../auth/jwt-auth.guard";
import {
  ApiKeyGuard,
  IDParamPipe,
  toBadRequest,
  toInternalServerError,
  ZodValidationPipe,
} from "../shared";
import { PermissionsService } from "./permissions.service";
import { RequiresPermissions } from "./requires-permissions";
import { PermissionsGuard } from "./permissions.guard";
import { ProfileGuard } from "../profile/profile.guard";
import { WithProfile } from "../profile/with-profile";

@Controller("")
export class PermissionsController {
  constructor(private permissionService: PermissionsService) {}

  @Post(apiUrls.ownerPermissions)
  @UsePipes(
    new ZodValidationPipe(PermissionsModule.PermissionEntity.Permission.createOwnerDtoParser),
  )
  @UseGuards(ApiKeyGuard)
  async createOwnerPermission(
    @Body() dto: PermissionsModule.PermissionEntity.CreateOwnerDto,
  ): Promise<PermissionsModule.PermissionEntity.Permission> {
    const result = await this.permissionService.createOwner(dto);

    if (isError(result)) {
      switch (result.type) {
        case ErrorTypes.UnexpectedError: {
          throw toInternalServerError(result);
        }
        case ErrorTypes.DomainError: {
          throw toBadRequest(result);
        }
        case ErrorTypes.ParsingError: {
          throw toInternalServerError(result);
        }
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Post(apiUrls.permissions)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageUsers])
  @UsePipes(
    new ZodValidationPipe(PermissionsModule.PermissionEntity.Permission.createCustomDtoParser),
  )
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async createCustomPermission(
    @Body() dto: PermissionsModule.PermissionEntity.CreateCustomDto,
  ): Promise<PermissionsModule.PermissionEntity.Permission> {
    const result = await this.permissionService.createCustom(dto);

    if (isError(result)) {
      switch (result.type) {
        case ErrorTypes.UnexpectedError: {
          throw toInternalServerError(result);
        }
        case ErrorTypes.DomainError: {
          throw toBadRequest(result);
        }
        case ErrorTypes.ParsingError: {
          throw toInternalServerError(result);
        }
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Patch(apiUrls.permission)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageUsers])
  @UsePipes(
    new ZodValidationPipe(PermissionsModule.PermissionEntity.Permission.updateCustomDtoParser),
  )
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async updatePermission(
    @Body() dto: PermissionsModule.PermissionEntity.UpdateCustomDto,
    @Param(UrlParams.PermissionId, IDParamPipe)
    permissionId: PermissionsModule.PermissionEntity.PermissionId,
  ): Promise<PermissionsModule.PermissionEntity.Permission> {
    const result = await this.permissionService.update(dto, permissionId);

    if (isError(result)) {
      switch (result.type) {
        case ErrorTypes.UnexpectedError: {
          throw toInternalServerError(result);
        }
        case ErrorTypes.DomainError: {
          throw toBadRequest(result);
        }
        case ErrorTypes.ParsingError: {
          throw toInternalServerError(result);
        }
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Delete(apiUrls.permission)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageUsers])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async deletePermission(
    @Param(UrlParams.PermissionId, IDParamPipe)
    permissionId: PermissionsModule.PermissionEntity.PermissionId,
  ): Promise<void> {
    const result = await this.permissionService.delete(permissionId);

    if (isError(result)) {
      switch (result.type) {
        case ErrorTypes.UnexpectedError: {
          throw toInternalServerError(result);
        }
        case ErrorTypes.DomainError: {
          throw toBadRequest(result);
        }
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Get(apiUrls.permissions)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageUsers])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async getPermissions(
    @Param(UrlParams.OrganizationId, IDParamPipe) orgId: OrganizationId,
  ): Promise<PermissionsModule.PermissionEntity.Permission[]> {
    const result = await this.permissionService.getByOrgId(orgId);

    if (isError(result)) {
      switch (result.type) {
        case ErrorTypes.UnexpectedError: {
          throw toInternalServerError(result);
        }
        case ErrorTypes.ParsingError: {
          throw toBadRequest(result);
        }
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Get(apiUrls.currentPermissions)
  @UseGuards(JwtAuthGuard, ProfileGuard)
  async getMyPermissions(
    @Param(UrlParams.OrganizationId, IDParamPipe) orgId: OrganizationId,
    @WithProfile() profile: Profile,
  ): Promise<PermissionsModule.PermissionEntity.PublicPermission[]> {
    const result = await this.permissionService.getPublicPermissions(profile.id, orgId);

    if (isError(result)) {
      switch (result.type) {
        case ErrorTypes.UnexpectedError: {
          throw toInternalServerError(result);
        }
        case ErrorTypes.ParsingError: {
          throw toBadRequest(result);
        }
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }
}
