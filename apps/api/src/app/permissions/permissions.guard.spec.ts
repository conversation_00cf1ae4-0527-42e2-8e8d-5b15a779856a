import { Test, TestingModule } from "@nestjs/testing";
import { Reflector } from "@nestjs/core";
import { ExecutionContext, ForbiddenException, InternalServerErrorException } from "@nestjs/common";

import {
  DomainError,
  ErrorMessages,
  PermissionsModule,
  UnexpectedError,
  CoachUserId,
  UUID,
} from "@mio/helpers";
import { mockDep } from "../../test";
import { PermissionsGuard } from "./permissions.guard";
import { AccessService } from "./access.service";

describe(PermissionsGuard.name, () => {
  let service: PermissionsGuard;

  describe(PermissionsGuard.prototype.canActivate.name, () => {
    const userId = UUID.generate<CoachUserId>();

    const context = {
      getHandler: () => undefined,
      switchToHttp: () => ({
        getRequest: () => ({
          user: { sub: userId },
          params: {},
        }),
      }),
    } as unknown as ExecutionContext;

    it("passes when data is correct", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          PermissionsGuard,
          mockDep(Reflector, {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            get: () => [PermissionsModule.Action.Actions.ManageOrganization] as any,
          }),
          mockDep(AccessService, {
            hasAccess: async () => true,
          }),
        ],
      }).compile();

      service = module.get<PermissionsGuard>(PermissionsGuard);

      const result = await service.canActivate(context);

      expect(result).toBe(true);
    });

    it("fails when permission params are missing", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          PermissionsGuard,
          mockDep(Reflector, {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            get: () => undefined as any,
          }),
          mockDep(AccessService, {}),
        ],
      }).compile();

      service = module.get<PermissionsGuard>(PermissionsGuard);

      const result = await service.canActivate(context).catch((err) => err);

      expect(result).toBeInstanceOf(InternalServerErrorException);
    });

    it("fails when permission params are invalid", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          PermissionsGuard,
          mockDep(Reflector, {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            get: () => ["not a real action"] as any,
          }),
          mockDep(AccessService, {}),
        ],
      }).compile();

      service = module.get<PermissionsGuard>(PermissionsGuard);

      const result = await service.canActivate(context).catch((err) => err);

      expect(result).toBeInstanceOf(InternalServerErrorException);
    });

    it("fails when permission params are invalid", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          PermissionsGuard,
          mockDep(Reflector, {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            get: () => ["not a real action"] as any,
          }),
          mockDep(AccessService, {}),
        ],
      }).compile();

      service = module.get<PermissionsGuard>(PermissionsGuard);

      const result = await service.canActivate(context).catch((err) => err);

      expect(result).toBeInstanceOf(InternalServerErrorException);
    });

    it(`fails when ${AccessService.name} fails with UnexpectedError`, async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          PermissionsGuard,
          mockDep(Reflector, {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            get: () => [PermissionsModule.Action.Actions.ManageOrganization] as any,
          }),
          mockDep(AccessService, {
            hasAccess: async () => new UnexpectedError(),
          }),
        ],
      }).compile();

      service = module.get<PermissionsGuard>(PermissionsGuard);

      const result = await service.canActivate(context).catch((err) => err);

      expect(result).toBeInstanceOf(InternalServerErrorException);
    });

    it(`fails when ${AccessService.name} fails with DomainError`, async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          PermissionsGuard,
          mockDep(Reflector, {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            get: () => [PermissionsModule.Action.Actions.ManageOrganization] as any,
          }),
          mockDep(AccessService, {
            hasAccess: async () => new DomainError(ErrorMessages.PermissionDenied),
          }),
        ],
      }).compile();

      service = module.get<PermissionsGuard>(PermissionsGuard);

      const result = await service.canActivate(context).catch((err) => err);

      expect(result).toBeInstanceOf(ForbiddenException);
    });

    it(`throws when permission is denied`, async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          PermissionsGuard,
          mockDep(Reflector, {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            get: () => [PermissionsModule.Action.Actions.ManageOrganization] as any,
          }),
          mockDep(AccessService, {
            hasAccess: async () => false,
          }),
        ],
      }).compile();

      service = module.get<PermissionsGuard>(PermissionsGuard);

      const result = await service.canActivate(context).catch((err) => err);

      expect(result).toBeInstanceOf(ForbiddenException);
    });
  });
});
