import { Injectable } from "@nestjs/common";
import { isNil } from "lodash/fp";

import {
  DomainError,
  ErrorMessages,
  isError,
  OrganizationId,
  PermissionsModule,
} from "@mio/helpers";
import { RolesRepository } from "./roles.repository";

@Injectable()
export class RolesService {
  constructor(private repo: RolesRepository) {}

  async create(dto: PermissionsModule.Role.SaveDto) {
    const existingWithSameName = await this.repo.getByName(dto.name);

    if (isError(existingWithSameName)) {
      return existingWithSameName;
    }

    if (existingWithSameName) {
      return new DomainError(ErrorMessages.EntityAlreadyExists);
    }

    const role = PermissionsModule.Role.Role.create(dto);

    const result = await this.repo.create(role);

    if (isError(result)) {
      return result;
    }

    return role;
  }

  async update(dto: PermissionsModule.Role.SaveDto, roleId: PermissionsModule.Role.RoleId) {
    const existingRole = await this.repo.getById(roleId);

    if (isError(existingRole)) {
      return existingRole;
    }

    if (isNil(existingRole)) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    const existingWithSameName = await this.repo.getByName(dto.name);

    if (isError(existingWithSameName)) {
      return existingWithSameName;
    }

    if (existingWithSameName && existingWithSameName.id !== existingRole.id) {
      return new DomainError(ErrorMessages.EntityAlreadyExists);
    }

    const role = PermissionsModule.Role.Role.update(existingRole, dto);

    const result = await this.repo.update(role);

    if (isError(result)) {
      return result;
    }

    return role;
  }

  async delete(roleId: PermissionsModule.Role.RoleId) {
    const result = await this.repo.deleteOne(roleId);

    if (isError(result)) {
      return result;
    }

    return result;
  }

  async getByOrgId(orgId: OrganizationId) {
    const result = await this.repo.getAllByOrgId(orgId);

    if (isError(result)) {
      return result;
    }

    return result;
  }
}
