import { Injectable } from "@nestjs/common";
import { isNil, keyBy } from "lodash/fp";

import {
  DomainError,
  ErrorMessages,
  isError,
  Organization,
  ParsingError,
  PermissionsModule,
  Team,
  TeamId,
  UnexpectedError,
  Unknown<PERSON><PERSON><PERSON>,
  <PERSON>rlParams,
  <PERSON><PERSON>ser,
  CoachUserId,
} from "@mio/helpers";
import { ProfileService } from "../profile/profile.service";
import { PermissionRepository } from "./permissions.repository";
import { RolesService } from "./roles.service";

type Params = {
  actions: PermissionsModule.Action.Actions[];
  urlParams: UnknownRecord;
  userId: CoachUserId;
};

@Injectable()
export class AccessService {
  constructor(
    private profileService: ProfileService,
    private repo: PermissionRepository,
    private roleService: RolesService,
  ) {}

  /* when undefined is returned = no restrictions ( done for owners ) */
  async fetchAvailableTeams(params: {
    orgId: unknown;
    userId: unknown;
  }): Promise<TeamId[] | undefined | UnexpectedError | ParsingError | DomainError> {
    const orgId = Organization.toOrganizationId(params.orgId);

    if (isError(orgId)) {
      return orgId;
    }

    const userId = CoachUser.toUserId(params.userId);

    if (isError(userId)) {
      return userId;
    }

    const profile = await this.profileService.getByUserId(userId);

    if (isError(profile)) {
      return profile;
    }

    if (isNil(profile)) {
      return new UnexpectedError(ErrorMessages.ProfileNotFound);
    }

    const permissions = await this.repo.getAllByProfileId(profile.id, orgId);

    if (isError(permissions)) {
      return permissions;
    }

    if (PermissionsModule.PermissionEntity.Permission.hasOwnerPermission(permissions)) {
      return undefined;
    }

    const allTeams = permissions.reduce((acc, item) => {
      if (!PermissionsModule.PermissionEntity.Permission.isOwnerType(item)) {
        acc = [...acc, ...(item.teamIds || [])];
      }
      return acc;
    }, [] as TeamId[]);

    return Array.from(new Set(allTeams));
  }

  async hasAccess(params: Params, passWithAny = false) {
    const orgId = Organization.toOrganizationId(params.urlParams[UrlParams.OrganizationId]);

    if (isError(orgId)) {
      return orgId;
    }

    const profile = await this.profileService.getByUserId(params.userId);

    if (isError(profile)) {
      return profile;
    }

    if (isNil(profile)) {
      return new UnexpectedError(ErrorMessages.ProfileNotFound);
    }

    const permissions = await this.repo.getAllByProfileId(profile.id, orgId);

    if (isError(permissions)) {
      return permissions;
    }

    const roles = await this.roleService.getByOrgId(orgId);

    if (isError(roles)) {
      return roles;
    }

    const teamId = Team.toTeamId(params.urlParams[UrlParams.TeamId]);

    if (params.urlParams[UrlParams.TeamId] && isError(teamId)) {
      return new DomainError(ErrorMessages.PermissionDenied);
    }

    const rolesById = keyBy("id", roles);

    const publicPermissions = permissions.map((permission) => {
      if (PermissionsModule.PermissionEntity.Permission.isOwnerType(permission)) {
        return permission;
      }

      const role = rolesById[permission.roleId];

      return PermissionsModule.PermissionEntity.Permission.toPublic(
        permission,
        role?.actions || [],
      );
    });

    return PermissionsModule.PermissionEntity.Permission.hasAccess(
      params.actions,
      publicPermissions,
      isError(teamId) ? undefined : [teamId],
      passWithAny,
    );
  }
}
