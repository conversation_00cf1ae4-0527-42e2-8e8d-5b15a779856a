import { ExecutionContext, Injectable } from "@nestjs/common";
import { Reflector } from "@nestjs/core";

import {
  DomainError,
  ErrorMessages,
  ErrorTypes,
  isError,
  PermissionsModule,
  UnexpectedError,
  UnknownR<PERSON><PERSON>,
  CoachUserId,
} from "@mio/helpers";

import { AuthenticatedRequest } from "../auth/types";
import { toForbiddenError, toInternalServerError } from "../shared";
import { PERMISSION_PARAMS } from "./constants";
import { AccessService } from "./access.service";

/* Uses "AND" logic for each required Action */
@Injectable()
export class PermissionsGuard {
  constructor(private reflector: Reflector, private accessService: AccessService) {}

  async canActivate(context: ExecutionContext): Promise<true | never> {
    /* this will extract the rules we have set via @RequiresPermissions() decorator*/
    const permissionParams = this.reflector.get(PERMISSION_PARAMS, context.getHandler());

    if (!permissionParams) {
      throw toInternalServerError(new UnexpectedError("The route is missing permission settings"));
    }

    const parsedPermissionParams =
      PermissionsModule.PermissionEntity.Permission.parseActions(permissionParams);

    if (isError(parsedPermissionParams)) {
      throw toInternalServerError(parsedPermissionParams);
    }

    const request: AuthenticatedRequest = context.switchToHttp().getRequest();
    const params = (request.params || {}) as UnknownRecord;

    const hasAccess = await this.accessService.hasAccess({
      urlParams: params,
      userId: request.user.sub as CoachUserId,
      actions: parsedPermissionParams,
    });

    if (isError(hasAccess)) {
      if (hasAccess.type === ErrorTypes.DomainError) {
        throw toForbiddenError(hasAccess);
      }
      throw toInternalServerError(hasAccess);
    }

    if (!hasAccess) {
      throw toForbiddenError(new DomainError(ErrorMessages.PermissionDenied));
    }

    return hasAccess;
  }
}

/* Uses "OR" logic for each required Action */
@Injectable()
export class OrPermissionsGuard {
  constructor(private reflector: Reflector, private accessService: AccessService) {}

  async canActivate(context: ExecutionContext): Promise<true | never> {
    /* this will extract the rules we have set via @RequiresPermissions() decorator*/
    const permissionParams = this.reflector.get(PERMISSION_PARAMS, context.getHandler());

    if (!permissionParams) {
      throw toInternalServerError(new UnexpectedError("The route is missing permission settings"));
    }

    const parsedPermissionParams =
      PermissionsModule.PermissionEntity.Permission.parseActions(permissionParams);

    if (isError(parsedPermissionParams)) {
      throw toInternalServerError(parsedPermissionParams);
    }

    const request: AuthenticatedRequest = context.switchToHttp().getRequest();
    const params = (request.params || {}) as UnknownRecord;

    const hasAccess = await this.accessService.hasAccess(
      {
        urlParams: params,
        userId: request.user.sub as CoachUserId,
        actions: parsedPermissionParams,
      },
      true,
    );

    if (isError(hasAccess)) {
      if (hasAccess.type === ErrorTypes.DomainError) {
        throw toForbiddenError(hasAccess);
      }
      throw toInternalServerError(hasAccess);
    }

    if (!hasAccess) {
      throw toForbiddenError(new DomainError(ErrorMessages.PermissionDenied));
    }

    return hasAccess;
  }
}
