import { Injectable } from "@nestjs/common";
import { isNil } from "lodash/fp";

import {
  CustomError,
  DomainError,
  ErrorMessages,
  isError,
  OrganizationId,
  ParsingError,
  PermissionsModule,
  ProfileId,
  UnexpectedError,
} from "@mio/helpers";
import { PermissionRepository } from "./permissions.repository";
import { RolesService } from "./roles.service";

@Injectable()
export class PermissionsService {
  constructor(private repo: PermissionRepository, private rolesService: RolesService) {}

  async createOwner(
    dto: PermissionsModule.PermissionEntity.CreateOwnerDto,
  ): Promise<PermissionsModule.PermissionEntity.OwnerPermission | CustomError> {
    const existingOwnerPermission = await this.repo.getOwnerPermission(
      dto.profileId,
      dto.organizationId,
    );

    if (isError(existingOwnerPermission)) {
      return existingOwnerPermission;
    }

    if (!isNil(existingOwnerPermission)) {
      return new DomainError(ErrorMessages.EntityAlreadyExists);
    }

    const ownerPermission = PermissionsModule.PermissionEntity.Permission.createOwner(dto);

    const saveResult = await this.repo.create(ownerPermission);

    if (isError(saveResult)) {
      return saveResult;
    }

    return ownerPermission;
  }

  async createCustom(
    dto: PermissionsModule.PermissionEntity.CreateCustomDto,
  ): Promise<PermissionsModule.PermissionEntity.CustomPermission | CustomError> {
    const existingPermission = await this.repo.getForProfileAndRole(dto.profileId, dto.roleId);

    if (isError(existingPermission)) {
      return existingPermission;
    }

    if (!isNil(existingPermission)) {
      return new DomainError(ErrorMessages.EntityAlreadyExists);
    }

    const customPermission = PermissionsModule.PermissionEntity.Permission.createCustom(dto);

    const saveResult = await this.repo.create(customPermission);

    if (isError(saveResult)) {
      return saveResult;
    }

    return customPermission;
  }

  async update(
    dto: PermissionsModule.PermissionEntity.UpdateCustomDto,
    id: PermissionsModule.PermissionEntity.PermissionId,
  ): Promise<PermissionsModule.PermissionEntity.Permission | CustomError> {
    const existingPermission = await this.repo.getById(id);

    if (isError(existingPermission)) {
      return existingPermission;
    }

    if (isNil(existingPermission)) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    if (PermissionsModule.PermissionEntity.Permission.isOwnerType(existingPermission)) {
      return new DomainError(ErrorMessages.InvalidAction);
    }

    const updatedPermission = PermissionsModule.PermissionEntity.Permission.updateTeams(
      existingPermission,
      dto,
    );

    const saveResult = await this.repo.update(updatedPermission);

    if (isError(saveResult)) {
      return saveResult;
    }

    return updatedPermission;
  }

  async delete(
    id: PermissionsModule.PermissionEntity.PermissionId,
  ): Promise<void | DomainError | UnexpectedError> {
    const result = await this.repo.deleteOne(id);

    if (isError(result)) {
      return result;
    }

    return result;
  }

  async getByOrgId(
    orgId: OrganizationId,
  ): Promise<PermissionsModule.PermissionEntity.Permission[] | ParsingError | UnexpectedError> {
    const result = await this.repo.getAllByOrgId(orgId);

    if (isError(result)) {
      return result;
    }

    return result;
  }

  async getPublicPermissions(
    profileId: ProfileId,
    orgId: OrganizationId,
  ): Promise<
    PermissionsModule.PermissionEntity.PublicPermission[] | ParsingError | UnexpectedError
  > {
    const permissions = await this.repo.getAllByProfileId(profileId, orgId);

    if (isError(permissions)) {
      return permissions;
    }

    const roles = await this.rolesService.getByOrgId(orgId);

    if (isError(roles)) {
      return roles;
    }

    return permissions.map((elem) => {
      if (PermissionsModule.PermissionEntity.Permission.isOwnerType(elem)) {
        return elem;
      }

      const relatedRole = roles.find((role) => role.id === elem.roleId);

      return PermissionsModule.PermissionEntity.Permission.toPublic(
        elem,
        relatedRole?.actions || [],
      );
    });
  }
}
