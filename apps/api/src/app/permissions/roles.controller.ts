import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  UseGuards,
  UsePipes,
} from "@nestjs/common";

import {
  apiUrls,
  ErrorTypes,
  isError,
  OrganizationId,
  PermissionsModule,
  UrlParams,
} from "@mio/helpers";
import { RolesService } from "./roles.service";
import { JwtAuthGuard } from "../auth/jwt-auth.guard";
import { IDParamPipe, toBadRequest, toInternalServerError, ZodValidationPipe } from "../shared";
import { RequiresPermissions } from "./requires-permissions";
import { PermissionsGuard } from "./permissions.guard";

@Controller("")
export class RolesController {
  constructor(private rolesService: RolesService) {}

  @Post(apiUrls.roles)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageUsers])
  @UsePipes(new ZodValidationPipe(PermissionsModule.Role.Role.saveDtoParser))
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async createRole(
    @Body() dto: PermissionsModule.Role.SaveDto,
  ): Promise<PermissionsModule.Role.Role> {
    const result = await this.rolesService.create(dto);

    if (isError(result)) {
      switch (result.type) {
        case ErrorTypes.DomainError: {
          throw toBadRequest(result);
        }
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Patch(apiUrls.role)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageUsers])
  @UsePipes(new ZodValidationPipe(PermissionsModule.Role.Role.saveDtoParser))
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async updateRole(
    @Body() dto: PermissionsModule.Role.SaveDto,
    @Param(UrlParams.RoleId, IDParamPipe) roleId: PermissionsModule.Role.RoleId,
  ): Promise<PermissionsModule.Role.Role> {
    const result = await this.rolesService.update(dto, roleId);

    if (isError(result)) {
      switch (result.type) {
        case ErrorTypes.UnexpectedError: {
          throw toInternalServerError(result);
        }
        case ErrorTypes.DomainError: {
          throw toBadRequest(result);
        }
        case ErrorTypes.ParsingError: {
          throw toInternalServerError(result);
        }
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Delete(apiUrls.role)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageUsers])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async deleteRole(
    @Param(UrlParams.RoleId, IDParamPipe) roleId: PermissionsModule.Role.RoleId,
  ): Promise<void> {
    const result = await this.rolesService.delete(roleId);

    if (isError(result)) {
      switch (result.type) {
        case ErrorTypes.UnexpectedError: {
          throw toInternalServerError(result);
        }
        case ErrorTypes.DomainError: {
          throw toBadRequest(result);
        }
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Get(apiUrls.roles)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageUsers])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async getRoles(
    @Param(UrlParams.OrganizationId, IDParamPipe) orgId: OrganizationId,
  ): Promise<PermissionsModule.Role.Role[]> {
    const result = await this.rolesService.getByOrgId(orgId);

    if (isError(result)) {
      switch (result.type) {
        case ErrorTypes.UnexpectedError: {
          throw toInternalServerError(result);
        }
        case ErrorTypes.ParsingError: {
          throw toBadRequest(result);
        }
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }
}
