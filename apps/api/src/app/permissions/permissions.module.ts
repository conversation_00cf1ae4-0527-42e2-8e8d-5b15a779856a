import { Modu<PERSON> } from "@nestjs/common";
import { AppConfigModule } from "../config";

import { DatabaseModule } from "../database";
import { ProfileModule } from "../profile/profile.module";
import { AccessService } from "./access.service";
import { PermissionsController } from "./permissions.controller";
import { PermissionsGuard } from "./permissions.guard";
import { PermissionRepository } from "./permissions.repository";
import { PermissionsService } from "./permissions.service";
import { RolesController } from "./roles.controller";
import { RolesRepository } from "./roles.repository";
import { RolesService } from "./roles.service";
import { VisibleTeamsGuard } from "./teams.guard";

@Module({
  imports: [ProfileModule, DatabaseModule, AppConfigModule],
  providers: [
    VisibleTeamsGuard,
    PermissionsService,
    AccessService,
    PermissionsGuard,
    PermissionRepository,
    RolesService,
    RolesRepository,
  ],
  exports: [PermissionsService, PermissionsGuard, VisibleTeamsGuard, RolesService, AccessService],
  controllers: [RolesController, PermissionsController],
})
export class PermissionsModule {}
