import { createParamDecorator, ExecutionContext, Injectable } from "@nestjs/common";
import { get, set } from "lodash";

import { isError, UnknownRecord, UrlParams } from "@mio/helpers";
import { AuthenticatedRequest } from "../auth/types";
import { toInternalServerError } from "../shared";
import { AccessService } from "./access.service";

const dataPath = ["locals", "teams"];

@Injectable()
export class VisibleTeamsGuard {
  constructor(private accessService: AccessService) {}

  async canActivate(context: ExecutionContext): Promise<true | never> {
    const request: AuthenticatedRequest = context.switchToHttp().getRequest();
    const params = (request.params || {}) as UnknownRecord;

    const teams = await this.accessService.fetchAvailableTeams({
      orgId: params[UrlParams.OrganizationId],
      userId: request.user.sub,
    });

    if (isError(teams)) {
      throw toInternalServerError(teams);
    }

    set(request, dataPath, teams);

    return true;
  }
}

export const WithVisibleTeams = createParamDecorator((_data: unknown, ctx: ExecutionContext) => {
  const request: AuthenticatedRequest = ctx.switchToHttp().getRequest();

  const teams = get(request, dataPath);

  return teams;
});
