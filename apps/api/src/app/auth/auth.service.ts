import { Injectable } from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";

import {
  DomainError,
  ErrorMessages,
  isError,
  CredentialsDto,
  UnexpectedError,
  CoachUser,
  AccessTokenDto,
  AccessToken,
  ParsingError,
  InviteRegistrationDto,
  PasswordResetInitiationDto,
  PasswordResetActionDto,
  PlayerUser,
  CustomError,
  LoginCodeDto,
} from "@mio/helpers";

import { AccessTokenPayload } from "./types";
import { CoachUsersService } from "../coach-users/users.service";
import { HashService } from "../coach-users/hash.service";
import { PlayerUsersService } from "../player-users/player-users.service";
import { EmailService } from "../email/email.service";

@Injectable()
export class AuthService {
  constructor(
    private usersService: CoachUsersService,
    private jwtService: JwtService,
    private crypto: HashService,
    private playerUserService: PlayerUsersService,
    private emailService: EmailService,
  ) {}

  registerInvitedUser(dto: InviteRegistrationDto) {
    return this.usersService.registerInvitedUser(dto);
  }

  initiatePasswordReset(dto: PasswordResetInitiationDto) {
    return this.usersService.initiatePasswordReset(dto);
  }

  resetPassword(dto: PasswordResetActionDto) {
    return this.usersService.resetPassword(dto);
  }

  async validateUser(
    dto: CredentialsDto,
  ): Promise<CoachUser | null | UnexpectedError | ParsingError> {
    const { email, password } = dto;

    const existingUser = await this.usersService.findByEmail(email);

    if (isError(existingUser)) {
      return existingUser;
    }

    if (!existingUser) {
      return null;
    }

    const validationResult = await this.crypto.comparePasswords(
      password,
      existingUser.authentication.password,
    );

    if (isError(validationResult)) {
      return validationResult;
    }

    return validationResult ? existingUser : null;
  }

  getAccessToken(user: CoachUser | PlayerUser.PlayerUser): AccessTokenDto | UnexpectedError {
    const payload: AccessTokenPayload = { sub: user.id };

    try {
      return {
        token: this.jwtService.sign(payload) as AccessToken,
      };
    } catch (err) {
      return new UnexpectedError(err);
    }
  }

  async login(dto: CredentialsDto) {
    const userResult = await this.validateUser(dto);

    if (isError(userResult)) {
      return userResult;
    }

    if (!userResult) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    return this.getAccessToken(userResult);
  }

  async requestLoginCode(dto: PlayerUser.SignInDto): Promise<void | CustomError> {
    const result = await this.playerUserService.addLoginCode(dto);

    if (isError(result)) {
      result.addContext({
        service: AuthService.name,
        method: this.requestLoginCode.name,
        operation: this.playerUserService.addLoginCode.name,
        dto,
      });

      return result;
    }

    return this.emailService.sendPlayerLoginCode(
      {
        code: result.authentication.code,
      },
      dto.email,
    );
  }

  async loginWithCode(dto: LoginCodeDto): Promise<AccessTokenDto | CustomError> {
    const targetUser = await this.playerUserService.verifyUser(dto);

    if (isError(targetUser)) {
      targetUser.addContext({
        service: AuthService.name,
        method: this.loginWithCode.name,
        operation: this.playerUserService.verifyUser.name,
        dto,
      });

      return targetUser;
    }

    if (PlayerUser.Entity.isVerified(targetUser)) {
      return new DomainError(ErrorMessages.InvalidAction, {
        service: AuthService.name,
        method: this.loginWithCode.name,
        operation: PlayerUser.Entity.isVerified.name,
        targetUser,
      });
    }

    const updateResult = await this.playerUserService.signIn(targetUser, dto);

    if (isError(updateResult)) {
      updateResult.addContext({
        service: AuthService.name,
        method: this.loginWithCode.name,
        operation: this.playerUserService.signIn.name,
        targetUser,
        dto,
      });

      return updateResult;
    }

    const tokenResponse = this.getAccessToken(updateResult);

    if (isError(tokenResponse)) {
      tokenResponse.addContext({
        service: AuthService.name,
        method: this.loginWithCode.name,
        operation: this.getAccessToken.name,
        data: updateResult,
      });

      return tokenResponse;
    }

    return tokenResponse;
  }
}
