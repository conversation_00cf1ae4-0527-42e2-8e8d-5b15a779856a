import {
  ForbiddenException,
  InternalServerErrorException,
  UnauthorizedException,
} from "@nestjs/common";
import { ZodError } from "zod";
import { Test, TestingModule } from "@nestjs/testing";

import {
  AccessToken,
  breakTest,
  DomainError,
  ErrorMessages,
  ParsingError,
  UnexpectedError,
  UUID,
  UserTypes,
  InviteRegistrationDto,
  CoachUser,
} from "@mio/helpers";

import { mockDep, passingGuard } from "../../test";
import { AuthController } from "./auth.controller";
import { AuthService } from "./auth.service";
import { ApiKeyGuard } from "../shared";

describe("AuthController", () => {
  let controller: AuthController;

  describe(AuthController.prototype.register.name, () => {
    const validPayload = {
      firstName: "John",
      lastName: "Doe",
      invite: UUID.generate(),
      password: "password1",
    } as InviteRegistrationDto;

    it(`returns the user when ${AuthService.prototype.registerInvitedUser.name} passes`, async () => {
      const module: TestingModule = await Test.createTestingModule({
        controllers: [AuthController],
        providers: [
          mockDep(AuthService, {
            registerInvitedUser: async () => undefined,
          }),
        ],
      })
        .overrideGuard(ApiKeyGuard)
        .useValue(passingGuard)
        .compile();

      controller = module.get<AuthController>(AuthController);

      const result = await controller.register(validPayload);

      expect(result).toBeUndefined();
    });

    it(`throws a ${ForbiddenException.name} when the user already exists`, async () => {
      const module: TestingModule = await Test.createTestingModule({
        controllers: [AuthController],
        providers: [
          mockDep(AuthService, {
            registerInvitedUser: async () => new DomainError(ErrorMessages.EntityAlreadyExists),
          }),
        ],
      })
        .overrideGuard(ApiKeyGuard)
        .useValue(passingGuard)
        .compile();

      controller = module.get<AuthController>(AuthController);

      try {
        await controller.register(validPayload);
        breakTest();
      } catch (err) {
        expect(err).toBeInstanceOf(ForbiddenException);
      }
    });

    it(`throws a ${InternalServerErrorException.name}`, async () => {
      const module: TestingModule = await Test.createTestingModule({
        controllers: [AuthController],
        providers: [
          mockDep(AuthService, {
            registerInvitedUser: async () => new UnexpectedError(),
          }),
        ],
      })
        .overrideGuard(ApiKeyGuard)
        .useValue(passingGuard)
        .compile();

      controller = module.get<AuthController>(AuthController);

      try {
        await controller.register(validPayload);
        breakTest();
      } catch (err) {
        expect(err).toBeInstanceOf(InternalServerErrorException);
      }
    });
  });

  describe(AuthController.prototype.login.name, () => {
    const validPayload = CoachUser.credentialsParser.parse({
      type: UserTypes.Coach,
      email: "<EMAIL>",
      password: "password1",
    });

    it(`returns an access token when login is successful`, async () => {
      const token = AccessToken.parser.parse("token");

      const module: TestingModule = await Test.createTestingModule({
        controllers: [AuthController],
        providers: [
          mockDep(AuthService, {
            login: async () => ({ token }),
          }),
        ],
      })
        .overrideGuard(ApiKeyGuard)
        .useValue(passingGuard)
        .compile();

      controller = module.get<AuthController>(AuthController);

      const result = await controller.login(validPayload);

      expect(result).toEqual({ token });
    });

    it(`throws ${UnauthorizedException.name} when credentials don't work`, async () => {
      const module: TestingModule = await Test.createTestingModule({
        controllers: [AuthController],
        providers: [
          mockDep(AuthService, {
            login: async () => new DomainError(ErrorMessages.EntityNotFound),
          }),
        ],
      })
        .overrideGuard(ApiKeyGuard)
        .useValue(passingGuard)
        .compile();

      controller = module.get<AuthController>(AuthController);

      try {
        await controller.login(validPayload);
        breakTest();
      } catch (err) {
        expect(err).toBeInstanceOf(UnauthorizedException);
      }
    });

    it(`throws ${InternalServerErrorException.name} when the user cannot be parsed`, async () => {
      const module: TestingModule = await Test.createTestingModule({
        controllers: [AuthController],
        providers: [
          mockDep(AuthService, {
            login: async () => new ParsingError(new ZodError([])),
          }),
        ],
      })
        .overrideGuard(ApiKeyGuard)
        .useValue(passingGuard)
        .compile();

      controller = module.get<AuthController>(AuthController);

      try {
        await controller.login(validPayload);
        breakTest();
      } catch (err) {
        expect(err).toBeInstanceOf(InternalServerErrorException);
      }
    });

    it(`throws ${InternalServerErrorException.name} when an UnexpectedError occurs`, async () => {
      const module: TestingModule = await Test.createTestingModule({
        controllers: [AuthController],
        providers: [
          mockDep(AuthService, {
            login: async () => new UnexpectedError(),
          }),
        ],
      })
        .overrideGuard(ApiKeyGuard)
        .useValue(passingGuard)
        .compile();

      controller = module.get<AuthController>(AuthController);

      try {
        await controller.login(validPayload);
        breakTest();
      } catch (err) {
        expect(err).toBeInstanceOf(InternalServerErrorException);
      }
    });
  });
});
