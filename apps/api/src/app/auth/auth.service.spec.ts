import { Test, TestingModule } from "@nestjs/testing";
import { JwtService } from "@nestjs/jwt";

import {
  DomainError,
  HashedPassword,
  UnexpectedError,
  CoachUser,
  CoachUserId,
  UserTypes,
  UUID,
} from "@mio/helpers";
import { mockDep } from "../../test";

import { AuthService } from "./auth.service";
import { CoachUsersService } from "../coach-users/users.service";
import { HashService } from "../coach-users/hash.service";
import { PlayerUsersService } from "../player-users/player-users.service";
import { EmailService } from "../email/email.service";

describe(AuthService.name, () => {
  let service: AuthService;

  describe(AuthService.prototype.validateUser.name, () => {
    const payload = CoachUser.credentialsParser.parse({
      type: UserTypes.Coach,
      email: "<EMAIL>",
      password: "password1",
    });

    const hashedPassword = HashedPassword.parser.parse("hashedPassword");
    const id = UUID.generate<CoachUserId>();

    const user = CoachUser.createUserWithCredentials(payload, hashedPassword, () => id);

    it("returns a user when validation succeeds", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          AuthService,
          mockDep(CoachUsersService, {
            findByEmail: async () => user,
          }),
          mockDep(HashService, {
            comparePasswords: async () => true,
          }),
          mockDep(JwtService, {}),
          mockDep(PlayerUsersService, {}),
          mockDep(EmailService, {}),
        ],
      }).compile();

      service = module.get<AuthService>(AuthService);

      const result = await service.validateUser(payload);

      expect(result).toEqual(user);
    });

    it("returns no user when validation fails", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          AuthService,
          mockDep(CoachUsersService, {
            findByEmail: async () => null,
          }),
          mockDep(HashService, {
            comparePasswords: async () => true,
          }),
          mockDep(JwtService, {}),
          mockDep(PlayerUsersService, {}),
          mockDep(EmailService, {}),
        ],
      }).compile();

      service = module.get<AuthService>(AuthService);

      const result = await service.validateUser(payload);

      expect(result).toBeNull();
    });

    it("returns UnexpectedError when UserService fails", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          AuthService,
          mockDep(CoachUsersService, {
            findByEmail: async () => new UnexpectedError(),
          }),
          mockDep(HashService, {
            comparePasswords: async () => true,
          }),
          mockDep(JwtService, {}),
          mockDep(PlayerUsersService, {}),
          mockDep(EmailService, {}),
        ],
      }).compile();

      service = module.get<AuthService>(AuthService);

      const result = await service.validateUser(payload);

      expect(result).toBeInstanceOf(UnexpectedError);
    });

    it("returns UnexpectedError when password compare unexpectedly fails", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          AuthService,
          mockDep(CoachUsersService, {
            findByEmail: async () => user,
          }),
          mockDep(HashService, {
            comparePasswords: async () => new UnexpectedError("oops"),
          }),
          mockDep(JwtService, {}),
          mockDep(PlayerUsersService, {}),
          mockDep(EmailService, {}),
        ],
      }).compile();

      service = module.get<AuthService>(AuthService);

      const result = await service.validateUser(payload);

      expect(result).toBeInstanceOf(UnexpectedError);
    });

    it("returns null when passwords do not match", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          AuthService,
          mockDep(CoachUsersService, {
            findByEmail: async () => user,
          }),
          mockDep(HashService, {
            comparePasswords: async () => false,
          }),
          mockDep(JwtService, {}),
          mockDep(PlayerUsersService, {}),
          mockDep(EmailService, {}),
        ],
      }).compile();

      service = module.get<AuthService>(AuthService);

      const result = await service.validateUser(payload);

      expect(result).toBeNull();
    });
  });

  describe(AuthService.prototype.login.name, () => {
    const payload = CoachUser.credentialsParser.parse({
      type: UserTypes.Coach,
      email: "<EMAIL>",
      password: "password1",
    });

    const hashedPassword = HashedPassword.parser.parse("hashedPassword");
    const id = UUID.generate<CoachUserId>();

    const user = CoachUser.createUserWithCredentials(payload, hashedPassword, () => id);

    it("returns a token when login succeeds", async () => {
      const token = "token";

      const module: TestingModule = await Test.createTestingModule({
        providers: [
          AuthService,
          mockDep(CoachUsersService, {
            findByEmail: async () => user,
          }),
          mockDep(HashService, {
            comparePasswords: async () => true,
          }),
          mockDep(JwtService, {
            sign: () => token,
          }),
          mockDep(PlayerUsersService, {}),
          mockDep(EmailService, {}),
        ],
      }).compile();

      service = module.get<AuthService>(AuthService);

      const result = await service.login(payload);

      expect(result).toEqual({ token });
    });

    it("return a DomainError when login fails because of validation", async () => {
      const token = "token";

      const module: TestingModule = await Test.createTestingModule({
        providers: [
          AuthService,
          mockDep(CoachUsersService, {
            findByEmail: async () => user,
          }),
          mockDep(HashService, {
            comparePasswords: async () => false,
          }),
          mockDep(JwtService, {
            sign: () => token,
          }),
          mockDep(PlayerUsersService, {}),
          mockDep(EmailService, {}),
        ],
      }).compile();

      service = module.get<AuthService>(AuthService);

      const result = await service.login(payload);

      expect(result).toBeInstanceOf(DomainError);
    });

    it("return an UnexpectedError when user login fails due to unexpected error in validation", async () => {
      const token = "token";

      const module: TestingModule = await Test.createTestingModule({
        providers: [
          AuthService,
          mockDep(CoachUsersService, {
            findByEmail: async () => new UnexpectedError(),
          }),
          mockDep(HashService, {
            comparePasswords: async () => true,
          }),
          mockDep(JwtService, {
            sign: () => token,
          }),
          mockDep(PlayerUsersService, {}),
          mockDep(EmailService, {}),
        ],
      }).compile();

      service = module.get<AuthService>(AuthService);

      const result = await service.login(payload);

      expect(result).toBeInstanceOf(UnexpectedError);
    });
  });

  describe(AuthService.prototype.getAccessToken.name, () => {
    const payload = CoachUser.credentialsParser.parse({
      type: UserTypes.Coach,
      email: "<EMAIL>",
      password: "password1",
    });

    const hashedPassword = HashedPassword.parser.parse("hashedPassword");
    const id = UUID.generate<CoachUserId>();

    const user = CoachUser.createUserWithCredentials(payload, hashedPassword, () => id);

    it("returns an Access token when user is successfully logged in", async () => {
      const token = "token";

      const module: TestingModule = await Test.createTestingModule({
        providers: [
          AuthService,
          mockDep(CoachUsersService, {}),
          mockDep(HashService, {}),
          mockDep(JwtService, {
            sign: () => token,
          }),
          mockDep(PlayerUsersService, {}),
          mockDep(EmailService, {}),
        ],
      }).compile();

      service = module.get<AuthService>(AuthService);

      const result = service.getAccessToken(user);
      expect(result).toEqual({ token });
    });

    it("returns UnexpectedError when JWT signing fails", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          AuthService,
          mockDep(CoachUsersService, {}),
          mockDep(HashService, {}),
          mockDep(JwtService, {
            sign: () => {
              throw new Error("oops");
            },
          }),
          mockDep(PlayerUsersService, {}),
          mockDep(EmailService, {}),
        ],
      }).compile();

      service = module.get<AuthService>(AuthService);

      const result = service.getAccessToken(user);
      expect(result).toBeInstanceOf(UnexpectedError);
    });
  });
});
