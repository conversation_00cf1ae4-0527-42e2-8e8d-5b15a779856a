import { Test, TestingModule } from "@nestjs/testing";
import { INestApplication } from "@nestjs/common";
import * as request from "supertest";

import { AppModule } from "../app.module";
import { apiUrls, ErrorMessages, ErrorTypes, Profile } from "@mio/helpers";
import { AppTestService } from "../test/test.service";
import { coachUserFactory } from "../../test/factories/coach-user";
import { DatabaseModule } from "../database/database.module";
import { createInMemoryMongoClient } from "../../test/memory-mongo";
import { DATABASE_CLIENT, DATABASE_CONNECTION, DBClient } from "../database";
import { inviteFactory } from "../../test/factories/invite";
import { organizationFactory } from "../../test/factories/organization";

describe("auth e2e", () => {
  let app: INestApplication;
  let testService: AppTestService;
  let mongoClient: DBClient;

  beforeAll(async () => {
    const fakeMongo = await createInMemoryMongoClient();
    mongoClient = fakeMongo.db;

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
      providers: [],
    })
      .overrideModule(DatabaseModule)
      .useModule({
        module: class TestDatabaseModule {},
        imports: [],
        providers: [
          {
            provide: DATABASE_CONNECTION,
            useFactory: async () => fakeMongo.db,
          },
          {
            provide: DATABASE_CLIENT,
            useFactory: async () => fakeMongo.mongoClient,
          },
        ],
        exports: [DATABASE_CONNECTION, DATABASE_CLIENT],
      })
      .compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    testService = new AppTestService(mongoClient);
  });

  afterEach(async () => {
    // Clear all collections between test cases
    const collections = await mongoClient.collections();
    for (const collection of collections) {
      await collection.deleteMany({});
    }
  });

  afterAll(async () => {
    await app.close();
  });

  it("lets you register", async () => {
    const user = coachUserFactory.create();

    const profile = Profile.create(
      Profile.createDto.parse({
        firstName: "John",
        lastName: "Doe",
      }),
      user.id,
    );

    const fixture = await testService.createOrgWithUserInvite({
      inviteStatus: "pending",
    });

    const response = await request
      .agent(app.getHttpServer())
      .post("/" + apiUrls.registerWithInvite)
      .send({
        invite: fixture.invite.id,
        ...profile,
        password: user.authentication.password,
      })
      .expect(204);

    expect(response.body).toEqual({});
  });

  it("forbids registration when invite is not found", async () => {
    const user = coachUserFactory.create();

    const profile = Profile.create(
      Profile.createDto.parse({
        firstName: "John",
        lastName: "Doe",
      }),
      user.id,
    );

    const fixture = await testService.createOrgWithUserInvite({
      inviteStatus: "pending",
    });
    const anotherInvite = inviteFactory.createPending(fixture.organization.id, {
      email: "<EMAIL>",
    });

    const response = await request
      .agent(app.getHttpServer())
      .post("/" + apiUrls.registerWithInvite)
      .send({
        invite: anotherInvite.id,
        ...profile,
        password: user.authentication.password,
      })
      .expect(500);

    expect(response.body).toEqual(
      expect.objectContaining({
        type: ErrorTypes.UnexpectedError,
        originalError: expect.objectContaining({
          message: ErrorMessages.EntityNotFound,
          type: ErrorTypes.DomainError,
        }),
      }),
    );
  });

  it("forbids registration when the invite has expired", async () => {
    const user = coachUserFactory.create();

    const profile = Profile.toCreateDtoOrThrow({
      firstName: "John",
      lastName: "Doe",
    });

    const fixture = await testService.createOrgWithUserInvite({
      inviteStatus: "expired",
    });

    const response = await request
      .agent(app.getHttpServer())
      .post("/" + apiUrls.registerWithInvite)
      .send({
        invite: fixture.invite.id,
        ...profile,
        password: user.authentication.password,
      })
      .expect(403);

    expect(response.body).toEqual(
      expect.objectContaining({
        type: ErrorTypes.DomainError,
        message: ErrorMessages.InviteExpired,
      }),
    );
  });

  it("forbids registration when the invite has been redeemed", async () => {
    const user = coachUserFactory.create();

    const profile = Profile.toCreateDtoOrThrow({
      firstName: "John",
      lastName: "Doe",
    });

    const fixture = await testService.createOrgWithUserInvite({
      inviteStatus: "redeemed",
    });

    const response = await request
      .agent(app.getHttpServer())
      .post("/" + apiUrls.registerWithInvite)
      .send({
        invite: fixture.invite.id,
        ...profile,
        password: user.authentication.password,
      })
      .expect(403);

    expect(response.body).toEqual(
      expect.objectContaining({
        type: ErrorTypes.DomainError,
        message: ErrorMessages.InviteAlreadyRedeemed,
      }),
    );
  });

  it("fails if the user already exists", async () => {
    const fixture = await testService.createOrgWithUserInvite({
      inviteStatus: "pending",
    });

    const user = coachUserFactory.create({
      authentication: {
        email: fixture.invite.email,
      },
    });
    await testService.saveCoachUser(user);

    const profile = Profile.toCreateDtoOrThrow({
      firstName: "John",
      lastName: "Doe",
    });

    const response = await request
      .agent(app.getHttpServer())
      .post("/" + apiUrls.registerWithInvite)
      .send({
        invite: fixture.invite.id,
        ...profile,
        password: user.authentication.password,
      })
      .expect(403);

    expect(response.body).toEqual(
      expect.objectContaining({
        type: ErrorTypes.DomainError,
        message: ErrorMessages.EntityAlreadyExists,
        context: expect.arrayContaining([
          expect.objectContaining({
            message: "User already exists",
          }),
        ]),
      }),
    );
  });

  it("fails if the organization doesn't exist", async () => {
    const unsavedOrg = organizationFactory.create();
    const unsavedUser = coachUserFactory.create({
      authentication: {
        email: "<EMAIL>",
      },
    });
    const pendingInvite = inviteFactory.createPending(unsavedOrg.id, {
      email: unsavedUser.authentication.email,
    });

    await testService.saveInvite(pendingInvite);

    const response = await request
      .agent(app.getHttpServer())
      .post("/" + apiUrls.registerWithInvite)
      .send({
        invite: pendingInvite.id,
        ...Profile.toCreateDtoOrThrow({
          firstName: "John",
          lastName: "Doe",
        }),
        password: unsavedUser.authentication.password,
      })
      .expect(403);

    expect(response.body).toEqual(
      expect.objectContaining({
        type: ErrorTypes.DomainError,
        message: ErrorMessages.EntityNotFound,
        context: expect.arrayContaining([
          expect.objectContaining({
            message: "Organization not found",
          }),
        ]),
      }),
    );
  });
});
