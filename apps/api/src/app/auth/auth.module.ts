import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { JwtModule } from "@nestjs/jwt";
import { PassportModule } from "@nestjs/passport";

import { AuthService } from "./auth.service";
import { AuthController } from "./auth.controller";
import { AppConfigModule, AppConfigService } from "../config";
import { JwtStrategy } from "./jwt.strategy";
import { ProfileModule } from "../profile/profile.module";
import { CoachUsersModule } from "../coach-users/users.module";
import { PlayerUsersModule } from "../player-users/player-users.module";
import { EmailModule } from "../email/email.module";

@Module({
  imports: [
    CoachUsersModule,
    PlayerUsersModule,
    AppConfigModule,
    PassportModule,
    ProfileModule,
    EmailModule,
    JwtModule.registerAsync({
      imports: [AppConfigModule],
      inject: [AppConfigService],
      useFactory: (configService: AppConfigService) => ({
        secret: configService.getJWTSecret(),
        signOptions: { expiresIn: configService.getJWTExpirationTime() },
      }),
    }),
  ],
  providers: [AuthService, JwtStrategy],
  controllers: [AuthController],
})
export class AuthModule {}
