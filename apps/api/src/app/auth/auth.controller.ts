import { toBadRequest } from "./../shared/error-response";
import { Controller, Post, UsePipes, Body, HttpCode, HttpStatus, Logger } from "@nestjs/common";

import {
  apiUrls,
  ErrorTypes,
  isError,
  CredentialsDto,
  CoachUser,
  InviteRegistrationDto,
  PasswordResetInitiationDto,
  PasswordResetActionDto,
  ErrorMessages,
  PlayerUser,
  UnexpectedError,
  AccessTokenDto,
  LoginCodeDto,
  loginCodeDtoParser,
} from "@mio/helpers";

import {
  toForbiddenError,
  toInternalServerError,
  toUnauthorizedException,
  ZodValidationPipe,
} from "../shared";
import { AuthService } from "./auth.service";

@Controller("")
export class AuthController {
  constructor(private authService: AuthService) {}

  private readonly logger = new Logger(AuthController.name);

  @Post(apiUrls.registerWithInvite)
  @HttpCode(HttpStatus.NO_CONTENT)
  @UsePipes(new ZodValidationPipe(CoachUser.registerWithInviteDto))
  async register(@Body() dto: InviteRegistrationDto) {
    return this.authService.registerInvitedUser(dto).then((result) => {
      if (isError(result)) {
        switch (result.type) {
          case ErrorTypes.DomainError: {
            throw toForbiddenError(result);
          }
          case ErrorTypes.UnexpectedError: {
            throw toInternalServerError(result);
          }
          default:
            throw toInternalServerError(result);
        }
      }
      return undefined;
    });
  }

  @Post(apiUrls.login)
  @UsePipes(new ZodValidationPipe(CoachUser.credentialsParser))
  async login(@Body() dto: CredentialsDto) {
    const result = await this.authService.login(dto);

    if (isError(result)) {
      switch (result.type) {
        case ErrorTypes.DomainError:
          // User entity not found in DB
          throw toUnauthorizedException();
        case ErrorTypes.ParsingError:
          // failing to parse the user from the DB is not something we expect
          throw toInternalServerError(result);
        case ErrorTypes.UnexpectedError:
          throw toInternalServerError(result);
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Post(apiUrls.passwordResetInitiation)
  @UsePipes(new ZodValidationPipe(CoachUser.passwordResetInitiationParser))
  async initiatePasswordReset(@Body() dto: PasswordResetInitiationDto) {
    const result = await this.authService.initiatePasswordReset(dto);

    if (isError(result)) {
      /* we don't have a User with the given email but it's a common security
      practice to not tell that to consumers */
      if (result.message === ErrorMessages.UserNotFound) {
        this.logger.log(
          "Trying to reset password on an Email we don`t know: " + dto.email + "/" + dto.type,
        );
        return undefined;
      }

      switch (result.type) {
        case ErrorTypes.DomainError:
          throw toForbiddenError(result);
        case ErrorTypes.ParsingError:
          throw toInternalServerError(result);
        case ErrorTypes.UnexpectedError:
          throw toInternalServerError(result);
        default:
          throw toInternalServerError(result);
      }
    }

    return undefined;
  }

  @Post(apiUrls.passwordResetAction)
  @UsePipes(new ZodValidationPipe(CoachUser.passwordResetActionParser))
  async resetPassword(@Body() dto: PasswordResetActionDto) {
    const result = await this.authService.resetPassword(dto);

    if (isError(result)) {
      switch (result.type) {
        case ErrorTypes.DomainError:
          throw toForbiddenError(result);
        case ErrorTypes.ParsingError:
          throw toInternalServerError(result);
        case ErrorTypes.UnexpectedError:
          throw toInternalServerError(result);
        default:
          throw toInternalServerError(result);
      }
    }

    return undefined;
  }

  @Post(apiUrls.playerCode)
  @UsePipes(new ZodValidationPipe(PlayerUser.Entity.signInDtoParser))
  async requestLoginCode(@Body() dto: PlayerUser.SignInDto): Promise<void | never> {
    const result = await this.authService.requestLoginCode(dto);

    if (isError(result)) {
      result.addContext({
        service: AuthController.name,
        method: this.requestLoginCode.name,
        operation: this.authService.requestLoginCode.name,
        dto,
      });

      this.logger.error(result);

      switch (result.type) {
        case ErrorTypes.DomainError: {
          if (result.message === ErrorMessages.PermissionDenied) {
            throw toForbiddenError(result);
          }
          throw toBadRequest(result);
        }
        case ErrorTypes.ParsingError:
          throw toInternalServerError(result);
        case ErrorTypes.UnexpectedError:
          throw toInternalServerError(result);
        default:
          throw toInternalServerError(new UnexpectedError(result));
      }
    }

    return result;
  }

  @Post(apiUrls.playerLogin)
  @UsePipes(new ZodValidationPipe(loginCodeDtoParser))
  async loginWithCode(@Body() dto: LoginCodeDto): Promise<AccessTokenDto | never> {
    const result = await this.authService.loginWithCode(dto);

    if (isError(result)) {
      result.addContext({
        service: AuthController.name,
        method: this.loginWithCode.name,
        operation: this.authService.loginWithCode.name,
        dto,
      });

      this.logger.error(result);

      switch (result.type) {
        case ErrorTypes.DomainError:
          throw toBadRequest(result);
        case ErrorTypes.ParsingError:
          throw toInternalServerError(result);
        case ErrorTypes.UnexpectedError:
          throw toInternalServerError(result);
        default:
          throw toInternalServerError(new UnexpectedError(result));
      }
    }

    return result;
  }
}
