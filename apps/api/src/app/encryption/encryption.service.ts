import { isError, UnexpectedError } from "@mio/helpers";
import { Injectable } from "@nestjs/common";
import * as crypto from "crypto";
import { AppConfigService } from "../config";

type EncryptedValues = {
  encryptedKey: string;
  iv: string;
};

@Injectable()
export class EncryptionService {
  constructor(private appConfigService: AppConfigService) {}

  private ALGORITHM = "aes-256-cbc";
  private IV_LENGTH = 16;
  private ENCODING = "hex";

  private generateIV() {
    try {
      const iv = crypto.randomBytes(this.IV_LENGTH);

      return iv;
    } catch (err) {
      return new UnexpectedError(err, {
        service: EncryptionService.name,
        method: this.generateIV.name,
        operation: this.generateIV.name,
      });
    }
  }

  encrypt(data: string): UnexpectedError | EncryptedValues {
    try {
      const bufferIv = this.generateIV();

      if (isError(bufferIv)) {
        bufferIv.addContext({
          service: EncryptionService.name,
          method: this.encrypt.name,
          operation: this.generateIV.name,
        });

        return bufferIv;
      }

      const cipher = crypto.createCipheriv(
        this.ALGORITHM,
        Buffer.from(this.appConfigService.getEncryptionKey()),
        bufferIv,
      );

      return {
        encryptedKey: Buffer.concat([cipher.update(data), cipher.final(), bufferIv]).toString(
          this.ENCODING as BufferEncoding,
        ),
        iv: bufferIv.toString(this.ENCODING as BufferEncoding),
      };
    } catch (err) {
      return new UnexpectedError(err, {
        service: EncryptionService.name,
        method: this.encrypt.name,
        operation: this.encrypt.name,
      });
    }
  }

  decrypt(data: string, iv: string) {
    try {
      const binaryData = Buffer.from(data, this.ENCODING as BufferEncoding);
      const encryptedData = binaryData.subarray(0, binaryData.length - this.IV_LENGTH);
      const decipher = crypto.createDecipheriv(
        this.ALGORITHM,
        Buffer.from(this.appConfigService.getEncryptionKey()),
        Buffer.from(iv, this.ENCODING as BufferEncoding),
      );

      return Buffer.concat([decipher.update(encryptedData), decipher.final()]).toString();
    } catch (err) {
      return new UnexpectedError(err, {
        service: EncryptionService.name,
        method: this.decrypt.name,
        operation: this.decrypt.name,
      });
    }
  }
}
