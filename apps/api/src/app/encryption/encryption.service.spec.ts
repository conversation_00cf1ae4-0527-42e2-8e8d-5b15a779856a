import { Test, TestingModule } from "@nestjs/testing";

import { breakTest, isError, UnexpectedError } from "@mio/helpers";
import { mockDep } from "../../test";
import { AppConfigService } from "../config";
import { EncryptionService } from "./encryption.service";

describe(EncryptionService.name, () => {
  let service: EncryptionService;

  describe(EncryptionService.prototype.encrypt.name, () => {
    it("encrypts a value", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          EncryptionService,
          mockDep(AppConfigService, {
            getEncryptionKey: () => "36733f62056282514c82312abc3925b6",
          }),
        ],
      }).compile();

      service = module.get<EncryptionService>(EncryptionService);

      const result = await service.encrypt("test encryption key");

      if (isError(result)) {
        return breakTest();
      }

      expect(result.encryptedKey).toHaveLength(96);
    });

    it("decrypts a value", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          EncryptionService,
          mockDep(AppConfigService, {
            getEncryptionKey: () => "36733f62056282514c82312abc3925b6",
          }),
        ],
      }).compile();

      service = module.get<EncryptionService>(EncryptionService);

      const valueToEncrypt = "test encryption key";

      const result = await service.encrypt(valueToEncrypt);

      if (isError(result)) {
        return breakTest();
      }

      expect(result.encryptedKey).toHaveLength(96);

      const decrypted = await service.decrypt(result.encryptedKey, result.iv);

      expect(decrypted).toBe(valueToEncrypt);
    });

    it("handles failures", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          EncryptionService,
          mockDep(AppConfigService, {
            getEncryptionKey: () => "should fail cause of length",
          }),
        ],
      }).compile();

      service = module.get<EncryptionService>(EncryptionService);

      const result = await service.encrypt("something");

      if (!isError(result)) {
        return breakTest();
      }

      expect(result).toBeInstanceOf(UnexpectedError);
    });
  });
});
