import { Injectable } from "@nestjs/common";
import { isNil } from "lodash/fp";

import {
  CreateSeasonDto,
  CustomDate,
  DomainError,
  ErrorMessages,
  GetSeasonByDatesDto,
  isError,
  OrganizationId,
  ParsingError,
  Season,
  SeasonId,
  UnexpectedError,
  UpdateSeasonDto,
} from "@mio/helpers";

import { SeasonRepository } from "./season.repository";

@Injectable()
export class SeasonService {
  constructor(private repo: SeasonRepository) {}

  async create(dto: CreateSeasonDto) {
    const findDuplicateSeasonDto = {
      organizationId: dto.organizationId,
      startDate: dto.startDate,
      endDate: dto.endDate,
    };

    if (isError(findDuplicateSeasonDto)) {
      return new UnexpectedError(findDuplicateSeasonDto);
    }

    const duplicateSeason = await this.findDuplicateSeason(findDuplicateSeasonDto);

    if (isError(duplicateSeason)) {
      return duplicateSeason;
    }

    const season = Season.create(dto);

    const seasonSaveResult = await this.repo.create(season);

    if (isError(seasonSaveResult)) {
      return seasonSaveResult;
    }
  }

  async findDuplicateSeason(findDupeSeasonDto: GetSeasonByDatesDto) {
    const seasons = await this.repo.findByOrganizationId(findDupeSeasonDto.organizationId);

    if (isError(seasons)) {
      return seasons;
    }

    if (isNil(seasons)) {
      return undefined;
    }

    if (Season.duplicateSeasonExists(seasons, findDupeSeasonDto)) {
      return new DomainError(ErrorMessages.EntityAlreadyExists);
    }
  }

  async updateSeason(seasonId: SeasonId, dto: UpdateSeasonDto) {
    const season = await this.repo.getById(seasonId);

    if (isError(season)) {
      return season;
    }

    if (isNil(season)) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    const updatedSeason = Season.update(dto, season);

    const updateResult = await this.repo.update(updatedSeason);

    if (isError(updateResult)) {
      return new UnexpectedError(updateResult);
    }

    return updatedSeason;
  }

  async deleteSeason() {
    // TODO: Implement
    // Deleting a season will delete all training seasons and matches attached to that season
  }

  async findSeasonsByOrganizationId(organizationId: OrganizationId) {
    return this.repo.findByOrganizationId(organizationId);
  }

  async getById(id: SeasonId) {
    return this.repo.getById(id);
  }

  async getCurrentSeason(
    date = CustomDate.now(),
  ): Promise<ParsingError | UnexpectedError | null | Season> {
    const season = await this.repo.findByDate(date);

    if (isError(season)) {
      season.addContext({
        service: SeasonService.name,
        method: this.getCurrentSeason.name,
        operation: this.repo.findByDate.name,
        date,
      });
    }

    return season;
  }
}
