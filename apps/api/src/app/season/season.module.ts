import { Module } from "@nestjs/common";

import { AppConfigModule } from "../config";
import { DatabaseModule } from "../database";
import { PermissionsModule } from "../permissions";
import { SeasonController } from "./season.controller";
import { SeasonRepository } from "./season.repository";
import { SeasonService } from "./season.service";

@Module({
  imports: [DatabaseModule, AppConfigModule, PermissionsModule],
  providers: [SeasonRepository, SeasonService],
  controllers: [SeasonController],
  exports: [SeasonService],
})
export class SeasonModule {}
