import { Body, Controller, Get, Param, Patch, Post, UseGuards, UsePipes } from "@nestjs/common";
import { isNil } from "lodash/fp";

import {
  apiUrls,
  CreateSeasonDto,
  DomainError,
  ErrorMessages,
  ErrorTypes,
  isError,
  OrganizationId,
  PermissionsModule,
  Season,
  SeasonId,
  UpdateSeasonDto,
  UrlParams,
} from "@mio/helpers";

import {
  IDParamPipe,
  toBadRequest,
  toInternalServerError,
  toNotFoundError,
  ZodValidationPipe,
} from "../shared";
import { JwtAuthGuard } from "../auth/jwt-auth.guard";
import { PermissionsGuard, RequiresPermissions } from "../permissions";
import { SeasonService } from "./season.service";

@Controller()
export class SeasonController {
  constructor(private seasonService: SeasonService) {}

  @Post(apiUrls.createSeason)
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageOrganization])
  @UsePipes(new ZodValidationPipe(Season.createDto))
  async createSeason(@Body() dto: CreateSeasonDto): Promise<void | never> {
    const result = await this.seasonService.create(dto);

    if (isError(result)) {
      switch (result.type) {
        case ErrorTypes.DomainError:
          throw toBadRequest(result);
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Patch(apiUrls.updateSeason)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageOrganization])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @UsePipes(new ZodValidationPipe(Season.updateDto))
  async updateSeason(
    @Body() dto: UpdateSeasonDto,
    @Param(UrlParams.SeasonId, IDParamPipe) seasonId: SeasonId,
  ) {
    return this.seasonService.updateSeason(seasonId, dto).then((result) => {
      if (isError(result)) {
        switch (result.type) {
          case ErrorTypes.DomainError: {
            if (result.message === ErrorMessages.EntityNotFound) {
              throw toNotFoundError(result);
            }
            throw toInternalServerError(result);
          }
          case ErrorTypes.UnexpectedError: {
            throw toInternalServerError(result);
          }
          case ErrorTypes.ParsingError: {
            throw toInternalServerError(result);
          }
          default:
            throw toInternalServerError(result);
        }
      }
      return result;
    });
  }

  @Get(apiUrls.findSeasons)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManagePlayers])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async findSeasons(@Param(UrlParams.OrganizationId, IDParamPipe) orgId: OrganizationId) {
    const result = await this.seasonService.findSeasonsByOrganizationId(orgId);

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return result;
  }

  @Get(apiUrls.getSeason)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageOrganization])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async getSeason(@Param(UrlParams.SeasonId, IDParamPipe) seasonId: SeasonId) {
    return this.seasonService.getById(seasonId).then((result) => {
      if (isError(result)) {
        throw toInternalServerError(result);
      }

      if (isNil(result)) {
        throw toNotFoundError(new DomainError(ErrorMessages.EntityNotFound));
      }

      return result;
    });
  }

  @Get(apiUrls.currentSeason)
  /* 
  For now it's used in the event-management forms and thus we use the "ManageEvents" permission.
   */
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageEvents])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async getCurrentSeason(): Promise<null | Season> {
    const result = await this.seasonService.getCurrentSeason();

    if (isError(result)) {
      switch (result.type) {
        case ErrorTypes.UnexpectedError: {
          throw toInternalServerError(result);
        }
        case ErrorTypes.ParsingError: {
          throw toInternalServerError(result);
        }
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }
}
