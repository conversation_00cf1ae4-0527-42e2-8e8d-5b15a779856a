import { Modu<PERSON> } from "@nestjs/common";

import { FootballMatchAttemptForModule } from "../match-centre/football-match-attempt-for/football-match-attempt-for.module";
import { MatchStatsController } from "./football-match-stats.controller";
import { AppConfigModule } from "../config";
import { PermissionsModule } from "../permissions";
import { PlayerTeamProfileModule } from "../player-team-profile";
import { FootballMatchAttemptAgainstModule } from "../match-centre/football-match-attempt-against/football-match-attempt-against.module";

@Module({
  imports: [
    FootballMatchAttemptForModule,
    FootballMatchAttemptAgainstModule,
    AppConfigModule,
    PermissionsModule,
    PlayerTeamProfileModule,
  ],
  providers: [],
  controllers: [MatchStatsController],
  exports: [],
})
export class FootballMatchStatsModule {}
