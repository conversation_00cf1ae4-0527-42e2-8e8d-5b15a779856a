import { Controller, Get, Param, Query, UseGuards } from "@nestjs/common";

import {
  apiUrls,
  isError,
  PermissionsModule,
  UrlParams,
  TeamId,
  StatsCategory,
} from "@mio/helpers";
import { JwtAuthGuard } from "../auth/jwt-auth.guard";
import { PermissionsGuard, RequiresPermissions } from "../permissions";
import { IDParamPipe, toInternalServerError } from "../shared";
import { PlayerTeamProfileService } from "../player-team-profile";
import { StatsCategoryQueryPipe } from "../shared/statsCategory.pipe";
import { FootballMatchAttemptForService } from "../match-centre/football-match-attempt-for/football-match-attempt-for.service";
import { FootballMatchAttemptAgainstService } from "../match-centre/football-match-attempt-against/football-match-attempt-against.service";

@Controller()
export class MatchStatsController {
  constructor(
    private playerTeamProfileService: PlayerTeamProfileService,
    private goalsScoredService: FootballMatchAttemptForService,
    private goalsConcededService: FootballMatchAttemptAgainstService,
  ) {}

  @Get(apiUrls.footballMatchPlayerStats)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async getAttemptsForStats(
    @Param(UrlParams.TeamId, IDParamPipe) teamId: TeamId,
    @Query("", StatsCategoryQueryPipe) category: StatsCategory,
  ) {
    const playerStats = await this.playerTeamProfileService.getMatchStatsByCategory(
      teamId,
      category,
    );

    if (isError(playerStats)) {
      throw toInternalServerError(playerStats);
    }

    return playerStats;
  }

  @Get(apiUrls.statsTeamAttemptsFor)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async getGoalsStats(@Param(UrlParams.TeamId, IDParamPipe) teamId: TeamId) {
    const result = await this.goalsScoredService.getAggregatedTeamGoals(teamId);

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return result;
  }

  @Get(apiUrls.statsTeamAttemptsAgainst)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageTeams])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async getAttemptsAgainstStats(@Param(UrlParams.TeamId, IDParamPipe) teamId: TeamId) {
    const result = await this.goalsConcededService.getAggregatedTeamGoals(teamId);

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return result;
  }
}
