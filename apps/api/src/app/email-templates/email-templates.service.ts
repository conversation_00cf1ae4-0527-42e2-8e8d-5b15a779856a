import { Injectable } from "@nestjs/common";

import {
  isError,
  DomainError,
  ErrorMessages,
  CreateEmailTemplateDto,
  EmailTemplate,
  CoachUserId,
  EmailTemplateId,
  OrganizationId,
} from "@mio/helpers";
import { EmailTemplateRepository } from "./email-templates.repository";
import { isNil } from "lodash";

@Injectable()
export class EmailTemplateService {
  constructor(private repo: EmailTemplateRepository) {}

  async create(dto: CreateEmailTemplateDto) {
    const emailTemplate = EmailTemplate.create(dto);

    const result = await this.repo.create(emailTemplate);

    if (isError(result)) {
      result.addContext({
        service: EmailTemplateService.name,
        method: this.create.name,
        operation: this.repo.create.name,
        dto,
      });
    }

    return emailTemplate;
  }

  async update(dto: EmailTemplate, userId: CoachUserId) {
    const emailTemplate = await this.repo.getById(dto.id);

    if (isError(emailTemplate)) {
      emailTemplate.addContext({
        service: EmailTemplateService.name,
        method: this.update.name,
        operation: this.repo.getById.name,
        dto,
      });

      return emailTemplate;
    }

    if (isNil(emailTemplate)) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    // todo: this is limiting update to only the owner, perhaps wrong?
    // todo: should we also check the EmailTemplateVisibility?
    if (emailTemplate.ownerId !== userId) {
      return new DomainError(ErrorMessages.PermissionDenied);
    }

    const updatedTemplate = EmailTemplate.update(emailTemplate, dto);

    const result = await this.repo.update(updatedTemplate);

    if (isError(result)) {
      result.addContext({
        service: EmailTemplateService.name,
        method: this.update.name,
        operation: this.repo.update.name,
        dto,
      });

      return result;
    }

    return updatedTemplate;
  }

  async delete(emailTemplateId: EmailTemplateId, userId: CoachUserId) {
    const emailTemplate = await this.repo.getById(emailTemplateId);

    if (isError(emailTemplate)) {
      emailTemplate.addContext({
        service: EmailTemplateService.name,
        method: this.delete.name,
        operation: this.repo.getById.name,
        emailTemplateId,
        userId,
      });

      return emailTemplate;
    }

    if (isNil(emailTemplate)) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    // todo: this is limiting delete to only the owner, perhaps wrong?
    // todo: should we also check the EmailTemplateVisibility?
    if (emailTemplate.ownerId !== userId) {
      return new DomainError(ErrorMessages.PermissionDenied);
    }

    const deleted = await this.repo.delete(emailTemplateId);

    if (isError(deleted)) {
      deleted.addContext({
        service: EmailTemplateService.name,
        method: this.delete.name,
        operation: this.repo.delete.name,
        emailTemplateId,
        userId,
      });

      return deleted;
    }

    return deleted;
  }

  async findByOwnerAndOrgId(userId: CoachUserId, organizationId: OrganizationId) {
    return this.repo.getByOwnerAndOrgId(userId, organizationId);
  }
}
