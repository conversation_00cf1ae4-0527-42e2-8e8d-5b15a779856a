import { Inject, Injectable } from "@nestjs/common";

import {
  CoachUserId,
  DomainError,
  EmailTemplate,
  EmailTemplateId,
  EmailTemplateVisibility,
  ErrorMessages,
  isError,
  OrganizationId,
  Primitive,
  UnexpectedError,
} from "@mio/helpers";
import { DATABASE_CONNECTION, DBClient, DBCollection } from "../database";

type EmailTemplateDocument = Primitive<EmailTemplate>;

@Injectable()
export class EmailTemplateRepository {
  private collection: DBCollection<EmailTemplateDocument>;

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection("email-templates");
  }

  async getByOwnerAndOrgId(userId: CoachUserId, organizationId: OrganizationId) {
    return this.collection
      .find({
        $or: [
          { organizationId, visibility: EmailTemplateVisibility.Company },
          { ownerId: userId, visibility: EmailTemplateVisibility.Private },
        ],
      })
      .toArray()
      .then((result) => {
        const parsed = EmailTemplate.toEntities(result);

        if (isError(parsed)) {
          parsed.addContext({
            service: EmailTemplateRepository.name,
            method: this.getByOwnerAndOrgId.name,
            operation: EmailTemplate.toEntities.name,
            organizationId,
            userId,
          });
        }

        return parsed;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: EmailTemplateRepository.name,
          method: this.getByOwnerAndOrgId.name,
          operation: this.collection.find.name,
          organizationId,
          userId,
        });
      });
  }

  async getByOwnerId(ownerId: CoachUserId) {
    return this.collection
      .find({ ownerId })
      .toArray()
      .then((result) => {
        const parsed = EmailTemplate.toEntities(result);

        if (isError(parsed)) {
          parsed.addContext({
            service: EmailTemplateRepository.name,
            method: this.getByOwnerId.name,
            operation: EmailTemplate.toEntities.name,
            ownerId,
          });
        }

        return parsed;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: EmailTemplateRepository.name,
          method: this.getByOwnerId.name,
          operation: this.collection.find.name,
          ownerId,
        });
      });
  }

  async getById(id: EmailTemplateId) {
    return this.collection
      .findOne({ id })
      .then((result) => {
        if (result) {
          const parsed = EmailTemplate.toEntity(result);

          if (isError(parsed)) {
            parsed.addContext({
              service: EmailTemplateRepository.name,
              method: this.getById.name,
              operation: EmailTemplate.toEntity.name,
              id,
            });
          }

          return parsed;
        }

        return null;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: EmailTemplateRepository.name,
          method: this.getById.name,
          operation: this.collection.findOne.name,
          id,
        });
      });
  }

  async findByName(name: string) {
    return this.collection
      .findOne({ name })
      .then((result) => {
        if (result) {
          const parsed = EmailTemplate.toEntity(result);

          if (isError(parsed)) {
            parsed.addContext({
              service: EmailTemplateRepository.name,
              method: this.findByName.name,
              operation: EmailTemplate.toEntity.name,
              name,
            });
          }

          return parsed;
        }

        return null;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: EmailTemplateRepository.name,
          method: this.findByName.name,
          operation: this.collection.findOne.name,
          name,
        });
      });
  }

  async update(entity: EmailTemplate) {
    return this.collection
      .replaceOne({ id: entity.id }, entity)
      .then(() => {
        return undefined;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: EmailTemplateRepository.name,
          method: this.update.name,
          operation: this.collection.replaceOne.name,
          entity,
        });
      });
  }

  async delete(id: EmailTemplateId) {
    return this.collection
      .deleteOne({ id })
      .then((result) => {
        if (result.deletedCount < 1) {
          return new DomainError(ErrorMessages.EntityNotFound, {
            service: EmailTemplateRepository.name,
            method: this.delete.name,
            operation: this.collection.deleteOne.name,
            id,
          });
        }
        if (result.deletedCount > 1) {
          return new DomainError(ErrorMessages.MultipleEntitiesFound, {
            service: EmailTemplateRepository.name,
            method: this.delete.name,
            operation: this.collection.deleteOne.name,
            id,
          });
        }

        return undefined;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: EmailTemplateRepository.name,
          method: this.delete.name,
          operation: this.collection.deleteOne.name,
          id,
        });
      });
  }

  async create(entity: EmailTemplate) {
    return this.collection
      .insertOne(entity)
      .then((result) => {
        if (result) {
          const parsed = EmailTemplate.toEntity(result);

          if (isError(parsed)) {
            parsed.addContext({
              service: EmailTemplateRepository.name,
              method: this.create.name,
              operation: EmailTemplate.toEntity.name,
              entity,
            });
          }

          return parsed;
        }

        return null;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: EmailTemplateRepository.name,
          method: this.create.name,
          operation: this.collection.insertOne.name,
          entity,
        });
      });
  }
}
