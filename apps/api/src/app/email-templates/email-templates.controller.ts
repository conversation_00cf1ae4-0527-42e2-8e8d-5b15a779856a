import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  UseGuards,
  UsePipes,
} from "@nestjs/common";
import {
  apiUrls,
  CoachUserId,
  CreateEmailTemplateDto,
  EmailTemplate,
  EmailTemplateId,
  isError,
  OrganizationId,
  PermissionsModule,
  UrlParams,
} from "@mio/helpers";
import { IDParamPipe, toInternalServerError, ZodValidationPipe } from "../shared";
import { EmailTemplateService } from "./email-templates.service";
import { WithCoachUserId } from "../coach-users/with-user";
import { JwtAuthGuard } from "../auth/jwt-auth.guard";
import { PermissionsGuard, RequiresPermissions } from "../permissions";

@Controller()
export class EmailTemplatesController {
  constructor(private emailTemplateService: EmailTemplateService) {}

  @Post(apiUrls.emailTemplates)
  // todo: maybe it's own permission?
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageOrganization])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @UsePipes(new ZodValidationPipe(EmailTemplate.createParser))
  async create(@Body() dto: CreateEmailTemplateDto, @WithCoachUserId() userId: CoachUserId) {
    const result = await this.emailTemplateService.create({ ...dto, ownerId: userId });

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return result;
  }

  @Put(apiUrls.emailTemplate)
  // todo: maybe it's own permission?
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageOrganization])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @UsePipes(new ZodValidationPipe(EmailTemplate.parser))
  async update(@Body() dto: EmailTemplate, @WithCoachUserId() userId: CoachUserId) {
    const result = await this.emailTemplateService.update(dto, userId);

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return result;
  }

  @Delete(apiUrls.emailTemplate)
  // todo: maybe it's own permission?
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageOrganization])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async delete(
    @Param(UrlParams.EmailTemplateId, IDParamPipe)
    emailTemplateId: EmailTemplateId,
    @WithCoachUserId() userId: CoachUserId,
  ) {
    const result = await this.emailTemplateService.delete(emailTemplateId, userId);

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return result;
  }

  @Get(apiUrls.emailTemplates)
  // todo: maybe it's own permission?
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageOrganization])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async find(
    @Param(UrlParams.OrganizationId, IDParamPipe) orgId: OrganizationId,
    @WithCoachUserId() userId: CoachUserId,
  ) {
    const result = await this.emailTemplateService.findByOwnerAndOrgId(userId, orgId);

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return result;
  }
}
