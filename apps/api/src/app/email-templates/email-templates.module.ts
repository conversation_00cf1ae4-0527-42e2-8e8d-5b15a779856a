import { <PERSON>du<PERSON> } from "@nestjs/common";

import { DatabaseModule } from "../database";
import { AppConfigModule } from "../config";
import { EmailTemplateRepository } from "./email-templates.repository";
import { EmailTemplateService } from "./email-templates.service";
import { EmailTemplatesController } from "./email-templates.controller";
import { PermissionsModule } from "../permissions";

@Module({
  imports: [DatabaseModule, AppConfigModule, PermissionsModule],
  providers: [EmailTemplateRepository, EmailTemplateService],
  controllers: [EmailTemplatesController],
  exports: [],
})
export class EmailTemplatesModule {}
