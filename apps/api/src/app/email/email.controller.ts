import { Body, Controller, Param, Post, UseGuards, UsePipes } from "@nestjs/common";

import {
  apiUrls,
  Email,
  EmailDto,
  isError,
  OrganizationId,
  MarketingEmailDto,
  UrlParams,
} from "@mio/helpers";

import { JwtAuthGuard } from "../auth/jwt-auth.guard";
import { EmailService } from "./email.service";
import { IDParamPipe, toInternalServerError, ZodValidationPipe } from "../shared";

@Controller("")
export class EmailController {
  constructor(private emailService: EmailService) {}

  @Post(apiUrls.sendEmail)
  @UseGuards(JwtAuthGuard)
  @UsePipes(new ZodValidationPipe(Email.emailDtoParser))
  async sendEmail(
    @Body() dto: EmailDto,
    @Param(UrlParams.OrganizationId, IDParamPipe) orgId: OrganizationId,
  ) {
    const result = await this.emailService.sendEmailByOrganization(dto, orgId);

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return undefined;
  }

  @Post(apiUrls.sendMarketingEmail)
  @UsePipes(new ZodValidationPipe(Email.marketingEmailDtoParser))
  async sendMarketingEmail(@Body() dto: MarketingEmailDto) {
    const result = await this.emailService.sendMarketingEmail(dto);

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return undefined;
  }
}
