import { Injectable } from "@nestjs/common";
import { OnEvent } from "@nestjs/event-emitter";

import { Events } from "../shared";
import { EmailService } from "./email.service";

@Injectable()
export class EmailListenerService {
  constructor(private mailSenderService: EmailService) {}

  @OnEvent(Events.Type.NewApplication)
  sendLoginCodeToApplicants(payload: Events.NewApplication) {
    payload.emails.forEach((email) => this.mailSenderService.greetNewApplicant(email));
  }
}
