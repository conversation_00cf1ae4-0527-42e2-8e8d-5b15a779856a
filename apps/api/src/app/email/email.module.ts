import { <PERSON><PERSON><PERSON> } from "@nestjs/common";

import { SentryModule } from "../../sentry/sentry.module";
import { AppConfigModule } from "../config";
import { OrganizationSharedModule } from "../organization/organization.shared.module";
import { Email<PERSON>ontroller } from "./email.controller";
import { EmailService } from "./email.service";
import { EmailListenerService } from "./email-listener.service";
import { TranslationsModule } from "../translations/translations.module";

@Module({
  imports: [OrganizationSharedModule, AppConfigModule, SentryModule, TranslationsModule],
  providers: [EmailService, EmailListenerService],
  controllers: [EmailController],
  exports: [EmailService],
})
export class EmailModule {}
