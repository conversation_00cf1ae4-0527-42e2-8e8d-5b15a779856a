import { Injectable, Logger } from "@nestjs/common";

import {
  DomainError,
  Email,
  EmailDto,
  ErrorMessages,
  isError,
  OrganizationId,
  MarketingEmailDto,
  sharedUrls,
  UnexpectedError,
  UrlQuery,
  UserTypes,
  LoginCodeDto,
} from "@mio/helpers";

import { SentryService } from "../../sentry/sentry.service";
import { AppConfigService } from "../config";
import { OrganizationSharedService } from "../organization/organization.shared.service";
import { TranslationsService, Locale } from "../translations/translations.service";

// eslint-disable-next-line @typescript-eslint/no-var-requires
const sgMail = require("@sendgrid/mail");

@Injectable()
export class EmailService {
  constructor(
    private appConfig: AppConfigService,
    private sentryService: SentryService,
    private orgService: OrganizationSharedService,
    private translationsService: TranslationsService,
  ) {
    const isSandboxed = this.appConfig.areEmailsDisabled();

    if (!isSandboxed) {
      sgMail.setApiKey(this.appConfig.getSendGridApiKey());
    }
  }

  private readonly logger = new Logger(EmailService.name);

  async greetNewApplicant(email: Email, locale: Locale = "en") {
    const clientUrl = this.appConfig.getPlayerPortalUrl();

    const mailBody = `
      <div>
        <h1>${this.translationsService.translate("emails.newApplicant.title", locale)}</h1>
        <p>${this.translationsService.translate("emails.newApplicant.body", locale, {
          url: clientUrl,
        })}</p>
        <p style="fontSize: 0.8em">${this.translationsService.translate(
          "emails.newApplicant.optOut",
          locale,
        )}</p>
      </div>
    `;

    const subject = this.translationsService.translate("emails.newApplicant.subject", locale);

    const msg = {
      to: email,
      from: this.appConfig.getDefaultEmail(),
      subject,
      html: mailBody,
      mailSettings: {
        sandboxMode: {
          enable: this.appConfig.areEmailsDisabled(),
        },
      },
    };

    try {
      await sgMail.send(msg);

      return undefined;
    } catch (error) {
      const exception = new UnexpectedError(error, {
        service: EmailService.name,
        method: this.greetNewApplicant.name,
        operation: sgMail.send.name,
        msg,
      });

      this.sentryService.logMessage(JSON.stringify(exception), exception);
      this.logger.error(JSON.stringify(exception));

      return exception;
    }
  }

  async sendPlayerLoginCode(params: LoginCodeDto, email: Email, locale: Locale = "en") {
    const clientUrl = this.appConfig.getPlayerPortalUrl();
    const url = `${clientUrl}${sharedUrls.playerLoginWithCode}?${UrlQuery.code}=${params.code}`;
    const baseUrl = `${clientUrl}${sharedUrls.playerLoginWithCode}`;

    const mailBody = `
      <p>
        <p>${this.translationsService.translate("emails.playerLogin.title", locale)}</p>
        <p>${this.translationsService.translate("emails.playerLogin.clickHere", locale, { url })}. 
           ${this.translationsService.translate("emails.playerLogin.validityNote", locale)}</p>
        <p>${this.translationsService.translate("emails.playerLogin.codeInstructions", locale, {
          code: params.code,
          baseUrl,
        })}</p>
      </p>
    `;

    const subject = this.translationsService.translate("emails.playerLogin.subject", locale);

    const msg = {
      to: email,
      from: this.appConfig.getDefaultEmail(),
      subject,
      html: mailBody,
      mailSettings: {
        sandboxMode: {
          enable: this.appConfig.areEmailsDisabled(),
        },
      },
    };

    try {
      await sgMail.send(msg);

      return undefined;
    } catch (error) {
      const exception = new UnexpectedError(error, {
        service: EmailService.name,
        method: this.sendPlayerLoginCode.name,
        operation: sgMail.send.name,
        msg,
      });
      this.sentryService.logMessage(JSON.stringify(exception), exception);
      this.logger.error(JSON.stringify(exception));

      return exception;
    }
  }

  async sendPasswordResetEmail(
    email: Email,
    code: string,
    userType: UserTypes,
    locale: Locale = "en",
  ) {
    const logMessage = `Sending password reset email to ${email}`;
    this.sentryService.logMessage(logMessage);
    this.logger.log(logMessage);

    const clientUrl = this.appConfig.getCoachPortalUrl();
    const url = `${clientUrl}/${sharedUrls.passwordReset}?code=${code}&email=${email}&type=${userType}`;

    const mailBody = `
      <p>
        <p>${this.translationsService.translate("emails.passwordReset.title", locale)}</p>
        <p>${this.translationsService.translate("emails.passwordReset.instructions", locale, {
          url,
        })}. 
           ${this.translationsService.translate("emails.passwordReset.validityNote", locale)}</p>
      </p>
    `;

    const subject = this.translationsService.translate("emails.passwordReset.subject", locale);

    const msg = {
      to: email,
      from: this.appConfig.getDefaultEmail(),
      subject,
      html: mailBody,
      mailSettings: {
        sandboxMode: {
          enable: this.appConfig.areEmailsDisabled(),
        },
      },
    };

    try {
      const result = await sgMail.send(msg);

      return result;
    } catch (error) {
      const exception = new UnexpectedError(error, {
        service: EmailService.name,
        method: this.sendPasswordResetEmail.name,
        operation: sgMail.send.name,
        msg,
      });

      this.sentryService.logMessage(JSON.stringify(exception), exception);
      this.logger.error(JSON.stringify(exception));

      return exception;
    }
  }

  async sendEmailByOrganization(dto: EmailDto, orgId: OrganizationId) {
    const organization = await this.orgService.findById(orgId);

    if (isError(organization)) {
      organization.addContext({
        service: EmailService.name,
        method: this.sendEmailByOrganization.name,
        operation: this.orgService.findById.name,
        orgId,
        dto,
      });

      this.sentryService.logMessage(JSON.stringify(organization), organization);
      this.logger.error(JSON.stringify(organization));

      return organization;
    }

    if (!organization) {
      const exception = new DomainError(ErrorMessages.EntityNotFound, {
        service: EmailService.name,
        method: this.sendEmailByOrganization.name,
        operation: this.orgService.findById.name,
        orgId,
        dto,
      });

      this.sentryService.logMessage(JSON.stringify(exception), exception);
      this.logger.error(JSON.stringify(exception));

      return exception;
    }

    return this.sendEmail(dto, organization.contactEmail);
  }

  async sendEmail(dto: EmailDto, forwardEmail: Email) {
    this.sentryService.logMessage(`Sending email to ${dto.to.join()}`);

    const isSandboxed = this.appConfig.areEmailsDisabled();

    const msg = {
      // to: "<EMAIL>",
      to: forwardEmail,
      bcc: dto.to,
      // TODO: check if this could be different
      from: "<EMAIL>",
      subject: dto.subject,
      html: dto.body,
      mailSettings: {
        sandboxMode: {
          // Even if `enable` is optional, Sendgrid needs a value, otherwise it will throw
          // "The sandbox_mode enable parameter is required."
          enable: isSandboxed,
        },
      },
    };

    try {
      const result = await sgMail.send(msg);

      return result;
    } catch (error) {
      const exception = new UnexpectedError(error, {
        service: EmailService.name,
        method: this.sendEmail.name,
        operation: sgMail.send.name,
        msg,
      });

      this.sentryService.logMessage(JSON.stringify(exception), exception);
      this.logger.error(JSON.stringify(exception));

      return exception;
    }
  }

  async sendMarketingEmail(dto: MarketingEmailDto, locale: Locale = "en") {
    const isSandboxed = this.appConfig.areEmailsDisabled();

    const mailBody = `
      <div class="container" style="margin-left: 20px;margin-right: 20px;">
        <h3>${this.translationsService.translate("emails.marketing.title", locale, {
          fullName: dto.fullName,
          email: dto.email,
        })}</h3>
        <div style="font-size: 16px;">
          <p>${this.translationsService.translate("emails.marketing.messageLabel", locale, {
            message: dto.message,
          })}</p>
          <br />
        </div>
      </div>
    `;

    const subject = this.translationsService.translate("emails.marketing.subject", locale, {
      subject: dto.subject,
    });

    const msg = {
      to: ["<EMAIL>", "<EMAIL>"], // TODO: get via config
      from: "<EMAIL>",
      subject,
      html: mailBody,
      mailSettings: {
        sandboxMode: {
          enable: isSandboxed,
        },
      },
    };

    try {
      const result = await sgMail.send(msg);

      return result;
    } catch (error) {
      return new UnexpectedError(error, {
        service: EmailService.name,
        method: this.sendMarketingEmail.name,
        operation: sgMail.send.name,
        dto,
      });
    }
  }
}
