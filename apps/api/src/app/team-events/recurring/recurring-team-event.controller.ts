import { Body, Controller, Param, Patch, Post, UseGuards, UsePipes } from "@nestjs/common";

import {
  apiUrls,
  ErrorMessages,
  ErrorTypes,
  isError,
  PermissionsModule,
  UrlParams,
  RecurringTeamEvent,
} from "@mio/helpers";

import {
  IDParamPipe,
  toInternalServerError,
  toNotFoundError,
  ZodValidationPipe,
} from "../../shared";
import { JwtAuthGuard } from "../../auth/jwt-auth.guard";
import { PermissionsGuard, RequiresPermissions } from "../../permissions";
import { RecurringTeamEventService } from "./recurring-team-event.service";

@Controller()
export class RecurringTeamEventController {
  constructor(private recurringTeamEventService: RecurringTeamEventService) {}

  @Post(apiUrls.recurringTeamEvents)
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageEvents])
  @UsePipes(new ZodValidationPipe(RecurringTeamEvent.Entity.createDto))
  async createEvent(
    @Body() dto: RecurringTeamEvent.CreateDto,
  ): Promise<void | RecurringTeamEvent.RecurringTeamEvent> {
    const result = await this.recurringTeamEventService.create(dto);

    if (isError(result)) {
      switch (result.type) {
        case ErrorTypes.UnexpectedError:
          throw toInternalServerError(result);
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Patch(apiUrls.recurringTeamEvent)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageEvents])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @UsePipes(new ZodValidationPipe(RecurringTeamEvent.Entity.updateDto))
  async updateEvent(
    @Body() dto: RecurringTeamEvent.UpdateDto,
    @Param(UrlParams.RecurringTeamEventId, IDParamPipe) eventId: RecurringTeamEvent.EventId,
  ) {
    return this.recurringTeamEventService.update(eventId, dto).then((result) => {
      if (isError(result)) {
        switch (result.type) {
          case ErrorTypes.DomainError: {
            if (result.message === ErrorMessages.EntityNotFound) {
              throw toNotFoundError(result);
            }
            throw toInternalServerError(result);
          }
          case ErrorTypes.UnexpectedError: {
            throw toInternalServerError(result);
          }
          case ErrorTypes.ParsingError: {
            throw toInternalServerError(result);
          }
          default:
            throw toInternalServerError(result);
        }
      }
      return result;
    });
  }
}
