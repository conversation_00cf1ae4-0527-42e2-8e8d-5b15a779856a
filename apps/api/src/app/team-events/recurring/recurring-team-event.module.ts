import { Modu<PERSON> } from "@nestjs/common";

import { DatabaseModule } from "../../database";
import { PermissionsModule } from "../../permissions";
import { TeamEventModule } from "../instances/team-event.module";
import { RecurringTeamEventController } from "./recurring-team-event.controller";
import { RecurringTeamEventRepository } from "./recurring-team-event.repository";
import { RecurringTeamEventService } from "./recurring-team-event.service";

@Module({
  imports: [DatabaseModule, PermissionsModule, TeamEventModule],
  providers: [RecurringTeamEventRepository, RecurringTeamEventService],
  controllers: [RecurringTeamEventController],
  exports: [],
})
export class RecurringTeamEventModule {}
