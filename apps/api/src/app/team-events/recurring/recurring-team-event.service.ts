import { Injectable } from "@nestjs/common";
import { isNil } from "lodash/fp";

import {
  DomainError,
  ErrorMessages,
  isError,
  UnexpectedError,
  RecurringTeamEvent,
} from "@mio/helpers";

import { RecurringTeamEventRepository } from "./recurring-team-event.repository";
import { TeamEventService } from "../instances/team-event.service";

@Injectable()
export class RecurringTeamEventService {
  constructor(
    private repo: RecurringTeamEventRepository,
    private teamEventService: TeamEventService,
  ) {}

  async create(dto: RecurringTeamEvent.CreateDto) {
    const event = RecurringTeamEvent.Entity.create(dto);

    const eventSaveResult = await this.repo.create(event);

    if (isError(eventSaveResult)) {
      eventSaveResult.addContext({
        service: RecurringTeamEventService.name,
        method: this.create.name,
        operation: this.repo.create.name,
        dto,
      });

      return eventSaveResult;
    }

    const teamEvents = await this.teamEventService.createEventsFromSchedule(event);

    if (isError(teamEvents)) {
      teamEvents.addContext({
        service: RecurringTeamEventService.name,
        method: this.create.name,
        operation: this.teamEventService.createEventsFromSchedule.name,
        event,
      });
    }

    return event;
  }

  async update(eventId: RecurringTeamEvent.EventId, dto: RecurringTeamEvent.UpdateDto) {
    const event = await this.repo.getById(eventId);

    if (isError(event)) {
      return event;
    }

    if (isNil(event)) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    const updatedEvent = RecurringTeamEvent.Entity.update(dto, event);

    const updateResult = await this.repo.update(updatedEvent);

    if (isError(updateResult)) {
      return new UnexpectedError(updateResult);
    }

    return updatedEvent;
  }

  async deleteEvent() {
    // TODO: Implement
    // Deleting an event will delete all review data associated with them, so it must be done carefully
  }
}
