import { PipeTransform, Injectable, ArgumentMetadata } from "@nestjs/common";

import { isError, TeamEvent } from "@mio/helpers";

import { toBadRequest } from "../../shared";

@Injectable()
export class TeamEventsQueryPipe implements PipeTransform {
  transform(value: unknown, metadata: ArgumentMetadata) {
    if (metadata.type === "query") {
      const result = TeamEvent.Entity.toQueryDto(value);

      if (isError(result)) {
        result.addContext({
          service: TeamEventsQueryPipe.name,
          value,
        });

        throw toBadRequest(result);
      }

      return result;
    }

    return value;
  }
}
