import { <PERSON>du<PERSON> } from "@nestjs/common";

import { DatabaseModule } from "../../database";
import { PermissionsModule } from "../../permissions";
import { SeasonModule } from "../../season/season.module";
import { TeamEventController } from "./team-event.controller";
import { TeamEventRepository } from "./team-event.repository";
import { TeamEventService } from "./team-event.service";
import { PlayerUsersModule } from "../../player-users/player-users.module";

@Module({
  imports: [DatabaseModule, PermissionsModule, SeasonModule, PlayerUsersModule],
  providers: [TeamEventRepository, TeamEventService],
  controllers: [TeamEventController],
  exports: [TeamEventService],
})
export class TeamEventModule {}
