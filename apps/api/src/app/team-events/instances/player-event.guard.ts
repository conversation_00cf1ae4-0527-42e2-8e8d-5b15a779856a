import { Injectable, CanActivate, ExecutionContext } from "@nestjs/common";

import {
  ErrorMessages,
  isError,
  UnknownR<PERSON><PERSON>,
  <PERSON>rlP<PERSON><PERSON>,
  Player,
  DomainError,
  TeamEvent,
} from "@mio/helpers";

import { AuthenticatedRequest } from "../../auth/types";
import { toForbiddenError, toInternalServerError } from "../../shared";
import { TeamEventService } from "./team-event.service";

/* 
Checks if a Player can access a TeamEvent, or in other words - whether that
Player is invited to the Event.
*/
@Injectable()
export class PlayerEventGuard implements CanActivate {
  constructor(private eventService: TeamEventService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request: AuthenticatedRequest = context.switchToHttp().getRequest();

    const params = (request.params || {}) as UnknownRecord;

    const playerId = params[UrlParams.PlayerId];

    const parsedPlayerId = Player.toPlayerId(playerId);

    if (isError(parsedPlayerId)) {
      parsedPlayerId.addContext({
        service: PlayerEventGuard.name,
        message: "Invalid PlayerId",
        playerId,
      });

      throw toInternalServerError(parsedPlayerId);
    }

    const eventId = params[UrlParams.TeamEventId];

    const parsedEventId = TeamEvent.Entity.toEventId(eventId);

    if (isError(parsedEventId)) {
      parsedEventId.addContext({
        service: PlayerEventGuard.name,
        message: "Invalid EventId",
        eventId,
      });

      throw toInternalServerError(parsedEventId);
    }

    const playerIsInvited = await this.eventService.isPlayerInvitedToEvent(
      parsedEventId,
      parsedPlayerId,
    );

    if (isError(playerIsInvited)) {
      playerIsInvited.addContext({
        service: PlayerEventGuard.name,
        operation: this.eventService.isPlayerInvitedToEvent.name,
        params: { eventId, playerId },
      });

      throw toInternalServerError(playerIsInvited);
    }

    if (!playerIsInvited) {
      throw toForbiddenError(
        new DomainError(ErrorMessages.PermissionDenied, {
          service: PlayerEventGuard.name,
        }),
      );
    }

    return true;
  }
}
