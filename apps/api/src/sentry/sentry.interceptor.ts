import { Call<PERSON><PERSON><PERSON>, ExecutionContext, Injectable, NestInterceptor, Scope } from "@nestjs/common";
import { Observable } from "rxjs";
import { catchError, finalize } from "rxjs/operators";
import { SentryService } from "./sentry.service";
import * as Sentry from "@sentry/node";
import { GenericRecord } from "@mio/helpers";

/**
 * We must be in Request scope as we inject SentryService
 */
@Injectable({ scope: Scope.REQUEST })
export class SentryInterceptor implements NestInterceptor {
  constructor(private sentryService: SentryService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<GenericRecord> {
    // start a child span for performance tracing
    const span = this.sentryService.startChild({ op: `route handler` });

    return next.handle().pipe(
      catchError((error) => {
        // capture the error, you can filter out some errors here
        Sentry.captureException(error, {
          contexts: {
            trace: this.sentryService.span?.getTraceContext(),
          },
        });

        // throw again the error
        throw error;
      }),
      finalize(() => {
        span?.end();
        this.sentryService.span?.end();
      }),
    );
  }
}
