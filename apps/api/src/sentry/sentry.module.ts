import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import * as <PERSON><PERSON> from "@sentry/node";
import { APP_INTERCEPTOR } from "@nestjs/core";
import { SentryService } from "./sentry.service";
import { SentryInterceptor } from "./sentry.interceptor";

export const SENTRY_OPTIONS = "SENTRY_OPTIONS";

@Module({
  providers: [SentryService],
  exports: [SentryService],
})
export class SentryModule {
  static forRoot(options: Sentry.NodeOptions) {
    // initialization of Sentry, this is where <PERSON><PERSON> will create a Hub
    Sentry.init(options);

    return {
      module: SentryModule,
      providers: [
        {
          provide: SENTRY_OPTIONS,
          useValue: options,
        },
        SentryService,
        {
          provide: APP_INTERCEPTOR,
          useClass: SentryInterceptor,
        },
      ],
      exports: [SentryService],
    };
  }
}
