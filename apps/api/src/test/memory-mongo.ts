import { MongoMemoryServer } from "mongodb-memory-server";
import { MongoClient } from "mongodb";

let mongoServer: MongoMemoryServer;
let mongoClient: MongoClient;

export const createInMemoryMongoClient = async () => {
  mongoServer = mongoServer ?? (await MongoMemoryServer.create());

  const uri = mongoServer.getUri();

  mongoClient = mongoClient ?? (await MongoClient.connect(uri));

  return {
    db: mongoClient.db(undefined, { ignoreUndefined: true }),
    mongoClient,
  };
};
