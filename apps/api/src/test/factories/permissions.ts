import { produce } from "immer";
import { merge } from "lodash";

import {
  Prettify,
  Primitive,
  UUID,
  isError,
  PermissionsModule,
  Organization,
  Profile,
  TeamId,
} from "@mio/helpers";

// Role Factory
type SimplifiedRole = Prettify<Primitive<PermissionsModule.Role.Role>>;

const createRole = (
  organizationId: Organization["id"] = UUID.generate(),
  overrides: Partial<SimplifiedRole> = {},
): PermissionsModule.Role.Role => {
  const defaultRole: SimplifiedRole = {
    id: UUID.generate<PermissionsModule.Role.RoleId>(),
    organizationId,
    name: "Test Role",
    description: "Test role description",
    actions: [
      PermissionsModule.Action.Actions.ManageOrganization,
      PermissionsModule.Action.Actions.ManageTeams,
      PermissionsModule.Action.Actions.ManageUsers,
    ],
  };

  const result = PermissionsModule.Role.Role.toEntity(
    produce(defaultRole, (draft) => {
      merge(draft, overrides);
    }),
  );

  if (isError(result)) {
    throw result;
  }

  return result;
};

const createCoachRole = (
  organizationId: Organization["id"] = UUID.generate(),
  overrides: Partial<SimplifiedRole> = {},
): PermissionsModule.Role.Role => {
  return createRole(organizationId, {
    name: "Coach",
    description: "Club coaches",
    actions: [
      PermissionsModule.Action.Actions.ManageTeams,
      PermissionsModule.Action.Actions.ManageEvents,
      PermissionsModule.Action.Actions.ManageReviews,
    ],
    ...overrides,
  });
};

const createAdminRole = (
  organizationId: Organization["id"] = UUID.generate(),
  overrides: Partial<SimplifiedRole> = {},
): PermissionsModule.Role.Role => {
  return createRole(organizationId, {
    name: "Club Admin",
    description: "Club administrators",
    actions: [
      PermissionsModule.Action.Actions.ManageOrganization,
      PermissionsModule.Action.Actions.ManageUsers,
      PermissionsModule.Action.Actions.ManagePlayers,
    ],
    ...overrides,
  });
};

// Permission Factory
type SimplifiedOwnerPermission = Prettify<
  Primitive<PermissionsModule.PermissionEntity.OwnerPermission>
>;
type SimplifiedCustomPermission = Prettify<
  Primitive<PermissionsModule.PermissionEntity.CustomPermission>
>;

const createOwnerPermission = (
  organizationId: Organization["id"] = UUID.generate(),
  profileId: Profile["id"] = UUID.generate(),
  overrides: Partial<SimplifiedOwnerPermission> = {},
): PermissionsModule.PermissionEntity.OwnerPermission => {
  const defaultPermission: SimplifiedOwnerPermission = {
    id: UUID.generate<PermissionsModule.PermissionEntity.PermissionId>(),
    organizationId,
    profileId,
    type: PermissionsModule.PermissionEntity.Type.Owner,
  };

  const result = PermissionsModule.PermissionEntity.Permission.toEntity(
    produce(defaultPermission, (draft) => {
      merge(draft, overrides);
    }),
  );

  if (isError(result)) {
    throw result;
  }

  return result as PermissionsModule.PermissionEntity.OwnerPermission;
};

const createCustomPermission = (
  organizationId: Organization["id"] = UUID.generate(),
  profileId: Profile["id"] = UUID.generate(),
  roleId: PermissionsModule.Role.RoleId = UUID.generate<PermissionsModule.Role.RoleId>(),
  teamIds: TeamId[] = [],
  overrides: Partial<SimplifiedCustomPermission> = {},
): PermissionsModule.PermissionEntity.CustomPermission => {
  const defaultPermission: SimplifiedCustomPermission = {
    id: UUID.generate<PermissionsModule.PermissionEntity.PermissionId>(),
    organizationId,
    profileId,
    type: PermissionsModule.PermissionEntity.Type.Custom,
    roleId,
    teamIds,
  };

  const result = PermissionsModule.PermissionEntity.Permission.toEntity(
    produce(defaultPermission, (draft) => {
      merge(draft, overrides);
    }),
  );

  if (isError(result)) {
    throw result;
  }

  return result as PermissionsModule.PermissionEntity.CustomPermission;
};

export const roleFactory = {
  create: createRole,
  createCoach: createCoachRole,
  createAdmin: createAdminRole,
};

export const permissionFactory = {
  createOwner: createOwnerPermission,
  createCustom: createCustomPermission,
};
