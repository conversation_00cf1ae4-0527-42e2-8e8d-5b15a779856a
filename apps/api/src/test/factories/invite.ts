import { produce } from "immer";
import { merge } from "lodash";

import {
  Prettify,
  Primitive,
  Invite,
  UUID,
  CustomDate,
  isError,
  CustomNumber,
  Organization,
  PendingInvite,
  ExpiredInvite,
  RedeemedInvite,
} from "@mio/helpers";

type SimplifiedInvite = Prettify<Primitive<Invite>>;

const createPending = (
  organizationId: Organization["id"],
  overrides: Partial<SimplifiedInvite> = {},
): PendingInvite => {
  const defaultInvite: SimplifiedInvite = {
    id: UUID.generate(),
    email: "<EMAIL>",
    organizationId: UUID.generate(),
    expires: CustomDate.addDays(CustomNumber.toPositiveInteger(7)),
    created: CustomDate.subDays(CustomNumber.toPositiveInteger(1)),
    invitedBy: "system",
    organization: organizationId,
  };

  const result = Invite.toEntity(
    produce(defaultInvite, (draft) => {
      merge(draft, overrides);
    }),
  );

  if (isError(result)) {
    throw result;
  }

  return result as PendingInvite;
};

const createExpired = (
  organizationId: Organization["id"],
  overrides: Partial<SimplifiedInvite> = {},
): ExpiredInvite => {
  const defaultInvite: SimplifiedInvite = {
    id: UUID.generate(),
    email: "<EMAIL>",
    organizationId: organizationId,
    expires: CustomDate.subDays(1),
    created: CustomDate.subDays(CustomNumber.toPositiveInteger(9)),
    invitedBy: "system",
    organization: organizationId,
  };

  const result = Invite.toEntity(
    produce(defaultInvite, (draft) => {
      merge(draft, overrides);
    }),
  );

  if (isError(result)) {
    throw result;
  }

  return result as ExpiredInvite;
};

const createRedeemed = (
  organizationId: Organization["id"],
  overrides: Partial<SimplifiedInvite> = {},
): RedeemedInvite => {
  const defaultInvite: SimplifiedInvite = {
    id: UUID.generate(),
    email: "<EMAIL>",
    organizationId: organizationId,
    expires: CustomDate.addDays(CustomNumber.toPositiveInteger(7)),
    created: CustomDate.subDays(CustomNumber.toPositiveInteger(10)),
    redeemed: CustomDate.now(),
    invitedBy: "system",
    organization: organizationId,
  };

  const result = Invite.toEntity(
    produce(defaultInvite, (draft) => {
      merge(draft, overrides);
    }),
  );

  if (isError(result)) {
    throw result;
  }

  return result as RedeemedInvite;
};

export const inviteFactory = {
  createPending,
  createExpired,
  createRedeemed,
};
