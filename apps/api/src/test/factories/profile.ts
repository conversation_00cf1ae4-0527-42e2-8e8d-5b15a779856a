import { produce } from "immer";
import { merge } from "lodash";

import { Prettify, Primitive, Profile, UUID, CoachUserId, isError } from "@mio/helpers";

type SimplifiedProfile = Prettify<Primitive<Profile>>;

const create = (userId: CoachUserId, overrides: Partial<SimplifiedProfile> = {}): Profile => {
  const defaultProfile: SimplifiedProfile = {
    id: UUID.generate(),
    user: userId,
    firstName: "<PERSON>",
    lastName: "Doe",
  };

  const result = Profile.toEntity(
    produce(defaultProfile, (draft) => {
      merge(draft, overrides);
    }),
  );

  if (isError(result)) {
    throw result;
  }

  return result;
};

export const profileFactory = {
  create,
};
