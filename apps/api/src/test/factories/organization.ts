import { produce } from "immer";
import { Prettify, Primitive, Organization, UUID, isError } from "@mio/helpers";
import { merge } from "lodash";

type SimplifiedOrganization = Prettify<Primitive<Organization>>;

const createOrganization = (overrides: Partial<SimplifiedOrganization> = {}): Organization => {
  const defaultOrg: SimplifiedOrganization = {
    id: UUID.generate(),
    name: "Test Organization",
    displayName: "Test Organization Display",
    slug: "test-organization",
    contactEmail: "<EMAIL>",
    senderEmail: "<EMAIL>",
    members: [],
    applications: {
      open: false,
      headline: "Join our organization",
      description: "We are looking for new members",
      closedMessage: "Applications are currently closed",
      successMessage: "Thank you for applying",
    },
  };

  const result = Organization.toEntity(
    produce(defaultOrg, (draft) => {
      merge(draft, overrides);
    }),
  );

  if (isError(result)) {
    throw result;
  }

  return result;
};

export const organizationFactory = {
  create: createOrganization,
};
