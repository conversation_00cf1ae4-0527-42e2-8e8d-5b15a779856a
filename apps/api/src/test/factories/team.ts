import { produce } from "immer";
import { merge } from "lodash";

import {
  Prettify,
  Primitive,
  UUID,
  isError,
  Organization,
  Team,
  TeamGenders,
  FeeInterval,
  CustomDate,
  PositiveInteger,
  FinancialIntegrationItemStatus,
  StripeEntities,
} from "@mio/helpers";

export type SimplifiedTeam = Prettify<Primitive<Team>>;

const createTeam = (
  organizationId: Organization["id"],
  overrides: Partial<SimplifiedTeam> = {},
): Team => {
  const defaultTeam: SimplifiedTeam = {
    id: UUID.generate(),
    organizationId,
    name: "Test Team",
    ageDescription: "Under 16s",
    slogan: "Test team slogan",
    playersBornAfter: CustomDate.subYears(16 as PositiveInteger),
    playersBornBefore: CustomDate.subYears(15 as PositiveInteger),
    gender: TeamGenders.Mixed,
    fee: 25,
    feeInterval: FeeInterval.Monthly,
    currency: StripeEntities.CurrencyLowerCase.GBP,
    financialIntegrationStatus: FinancialIntegrationItemStatus.Active,
  };

  const result = Team.toEntity(
    produce(defaultTeam, (draft) => {
      merge(draft, overrides);
    }),
  );

  if (isError(result)) {
    throw result;
  }

  return result;
};

const createYouthTeam = (
  organizationId: Organization["id"],
  overrides: Partial<SimplifiedTeam> = {},
): Team => {
  return createTeam(organizationId, {
    name: "Youth Team",
    ageDescription: "Under 12s",
    playersBornAfter: CustomDate.subYears(12 as PositiveInteger),
    playersBornBefore: CustomDate.subYears(11 as PositiveInteger),
    ...overrides,
  });
};

const createAdultTeam = (
  organizationId: Organization["id"],
  overrides: Partial<SimplifiedTeam> = {},
): Team => {
  return createTeam(organizationId, {
    name: "Adult Team",
    ageDescription: "Adults",
    playersBornAfter: CustomDate.subYears(100 as PositiveInteger),
    playersBornBefore: CustomDate.subYears(18 as PositiveInteger),
    ...overrides,
  });
};

export const teamFactory = {
  create: createTeam,
  createYouth: createYouthTeam,
  createAdult: createAdultTeam,
};
