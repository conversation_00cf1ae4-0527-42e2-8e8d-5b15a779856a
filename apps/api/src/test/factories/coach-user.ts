import { produce } from "immer";
import { merge } from "lodash";

import {
  Prettify,
  Primitive,
  CoachUser,
  UUID,
  HashedPassword,
  UserTypes,
  AuthTypes,
  isError,
  DeepPartial,
} from "@mio/helpers";

type SimplifiedCoachUser = Prettify<Primitive<CoachUser>>;

const create = (overrides: DeepPartial<SimplifiedCoachUser> = {}): CoachUser => {
  const defaultUser: SimplifiedCoachUser = {
    id: UUID.generate(),
    type: UserTypes.Coach,
    authentication: {
      type: AuthTypes.Credentials,
      email: "<EMAIL>",
      password: HashedPassword.parser.parse("Password1"),
    },
  };

  const result = CoachUser.toEntity(
    produce(defaultUser, (draft) => {
      merge(draft, overrides);
    }),
  );

  if (isError(result)) {
    throw result;
  }

  return result;
};

export const coachUserFactory = {
  create,
};
