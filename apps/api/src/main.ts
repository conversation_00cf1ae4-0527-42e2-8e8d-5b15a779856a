/**
 * This is not a production server yet!
 * This is only a minimal backend to get started.
 */
import { join } from "path";
import express from "express";
import { Logger } from "@nestjs/common";
import { NestFactory } from "@nestjs/core";

import { AppModule } from "./app/app.module";
import { environment } from "./environments/environment";

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  const globalPrefix = "api";

  app.setGlobalPrefix(globalPrefix);

  if (!environment.production) {
    app.enableCors({
      origin: ["http://localhost:4001", "http://localhost:4002", "http://localhost:4200"],
      credentials: true,
    });
  }

  if (environment.useDiskFileStorage) {
    const staticUrl = join(process.cwd(), "apps/api", environment.fileUploadFolder);

    app.use(`/${environment.fileUploadFolder}`, express.static(staticUrl));
  }

  const port = process.env.PORT || 3333;

  await app.listen(port, () => {
    Logger.log("Listening at http://localhost:" + port + "/" + globalPrefix);
  });
}

bootstrap();
