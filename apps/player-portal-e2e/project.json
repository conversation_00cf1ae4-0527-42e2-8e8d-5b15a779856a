{"name": "player-portal-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/player-portal-e2e/src", "projectType": "application", "tags": [], "implicitDependencies": ["player-portal"], "targets": {"e2e": {"executor": "@nx/cypress:cypress", "options": {"cypressConfig": "apps/player-portal-e2e/cypress.json", "devServerTarget": "player-portal:serve:development", "testingType": "e2e"}, "configurations": {"production": {"devServerTarget": "player-portal:serve:production"}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}}