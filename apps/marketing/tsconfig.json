{"extends": "../../tsconfig.base.json", "compilerOptions": {"jsx": "preserve", "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "plugins": [{"name": "next"}], "types": ["jest", "node"]}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "../../apps/marketing/.next/types/**/*.ts", "../../dist/apps/marketing/.next/types/**/*.ts", "next-env.d.ts"], "exclude": ["node_modules", "jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts"]}