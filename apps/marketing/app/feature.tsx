import { FC, ReactNode } from "react";

type Props = {
  icon: ReactNode;
  title: ReactNode;
  description: ReactNode;
};

const Feature: FC<Props> = ({ icon, title, description }) => {
  return (
    <div className="grid place-items-center gap-6">
      <div className="grid place-items-center gap-2">
        {icon}

        <div className="place-items-center font-bold text-center">
          <h4>{title}</h4>
        </div>
      </div>

      <p className="text-center text-lightGrayText">{description}</p>
    </div>
  );
};

export default Feature;
