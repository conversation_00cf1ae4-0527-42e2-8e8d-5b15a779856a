import { FC } from "react";

import { MarketingEmailSubjects } from "@mio/helpers";

import ExternalLinkButton from "../../components/buttons/external-link-button";
import ContactForm from "../../components/contact.form/form";

const DemoPage: FC = () => {
  return (
    <div className="min-h-screen text-black">
      <article className="mt-10 container mx-auto flex flex-col gap-12 p-6">
        <header className="flex flex-col gap-4 items-center">
          <h1 className="text-4xl">Team Assist Limited Demo</h1>
          <p>We provide a public limited demo version of our software.</p>
        </header>

        <section className="flex flex-col items-center">
          <ExternalLinkButton
            href="https://ambitious-grass-00586d403.3.azurestaticapps.net/d0be0e92-3e12-410e-aa2b-8a4ea26c78b0/teams"
            target="_blank"
          >
            Launch
          </ExternalLinkButton>
        </section>

        <hr className="h-0.5 border-t-0 bg-neutral-100 opacity-100" />

        <section className="flex flex-col gap-4 flex-wrap items-center w-full">
          <h2 className="text-4xl">Team Assist Full Demo</h2>
          <p>{`Please fill in the form below and we'll get in touch.`}</p>

          <div className="md:min-w-[500px] min-w-full">
            <ContactForm subject={MarketingEmailSubjects.Demo} heading="Send request" />
          </div>
        </section>
      </article>
    </div>
  );
};

export default DemoPage;
