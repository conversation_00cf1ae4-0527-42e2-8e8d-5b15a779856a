import { GiBabyfootPlayers, GiSoccerField, GiSoccer<PERSON>ick } from "react-icons/gi";
import { IoCalendarNumber } from "react-icons/io5";
import { TbPigMoney } from "react-icons/tb";

import Feature from "./feature";

export default function Features() {
  return (
    <div className="bg-lightestGray pb-12 pt-4 text-gray-700">
      <header className="grid mb-16 place-items-center gap-8 p-6">
        <h2 className="text-4xl font-bold">Features</h2>

        <p className="lg:w-2/3 text-lg text-center">
          {`Team-Assist is perfect for football clubs and coaches who want an end-to-end solution for
          managing everything related to the football process. Whether you're a small local team or
          a large professional club, Team-Assist has everything you need to take your football
          management to the next level.`}
        </p>
      </header>

      <div className="flex p-4 flex-wrap gap-5">
        <div className="flex-1 min-w-[300px] md:min-w-[0px]">
          <Feature
            icon={<GiBabyfootPlayers className="w-12" size="3rem" />}
            title="Player register"
            description={
              <p>
                Have all players register via team-assist player platform and allocate them to the
                right team.
              </p>
            }
          />
        </div>

        <div className="flex-1 min-w-[300px] md:min-w-[0px]">
          <Feature
            icon={<IoCalendarNumber className="w-12" size="3rem" />}
            title="Events management"
            description={
              <p>
                Invite players to your training sessions, matches or team meetings and track their
                availability and attendance.
              </p>
            }
          />
        </div>

        <div className="flex-1 min-w-[300px] md:min-w-[0px]">
          <Feature
            icon={<GiSoccerField className="w-12" size="3rem" />}
            title="Training session and Match day planning & reviews"
            description={
              <p>
                Create your session or match day plans and then review them with player specific
                data.
              </p>
            }
          />
        </div>

        <div className="flex-1 min-w-[300px] md:min-w-[0px]">
          <Feature
            icon={<GiSoccerKick className="w-12" size="3rem" />}
            title="Player profiles"
            description={
              <p>
                Our analytics will use the review data to build your player profiles for you with
                their strengths and weaknesses. We will show you trends, patterns and insights into
                their progress over time.
              </p>
            }
          />
        </div>

        <div className="flex-1 min-w-[300px] md:min-w-[0px]">
          <Feature
            icon={<TbPigMoney className="w-12" size="3rem" />}
            title="Finances"
            description={
              <p>
                Stay on top of your money balance with our payments integration. Players pay their
                fees via the app and you can track all payments and analytics within the platform.
              </p>
            }
          />
        </div>
      </div>
    </div>
  );
}
