import { <PERSON><PERSON><PERSON> } from "next/font/google";
import Link from "next/link";

import "./globals.css";
import Logo from "../components/logo";
import LinkButton from "../components/buttons/link-button";

const nunito = Nunito({ subsets: ["latin"] });

export const metadata = {
  title: "Team Assist",
  description: "Team Assist is a football management platform for teams and clubs.",
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body className={nunito.className}>
        <>
          <header className="p-6">
            <nav className="flex justify-between container mx-auto gap-2 flex-wrap">
              <Logo />

              <div className="flex justify-center items-center gap-12">
                <LinkButton href="/demo">Demo</LinkButton>
              </div>
            </nav>
          </header>

          <hr className="h-0.5 border-t-0 bg-neutral-100 opacity-100" />
        </>

        <main>{children}</main>

        <footer className="bg-blue-700 p-6">
          <section className="container mx-auto flex gap-6 flex-row-reverse">
            <Link href="/about-us" className="text-white">
              About us
            </Link>
          </section>
        </footer>
      </body>
    </html>
  );
}
