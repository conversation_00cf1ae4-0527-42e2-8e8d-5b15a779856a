import { FC } from "react";

const AboutUsPage: FC = () => {
  return (
    <article className="flex flex-wrap items-start gap-6 p-8 min-h-screen text-black">
      <section className="md:basis-[30%] basis-full grow shrink">
        <img
          src="/hero-illustration.png"
          alt="Image of kids climbing a tree where the football ball is stuck."
        />
      </section>

      <section className="flex flex-col gap-4 break-words grow shrink basis-[60%]">
        <h1 className="mb-6 text-4xl">
          Introducing Our Team and Our Passion for Football Coaching
        </h1>

        <p>
          Fueled by their personal journeys, our founders share a deep-rooted connection with
          football. <PERSON> and <PERSON><PERSON><PERSON><PERSON>, who once played alongside each other and officiated matches
          together, have etched a lasting legacy in the sport. <PERSON>{"'"}s evolution from a player to
          a coach, coupled with his pursuit of coaching certifications, exposed the gaps in existing
          software solutions, sparking his frustration. It was this shared sentiment among fellow
          coaches, along with the united efforts of <PERSON><PERSON><PERSON><PERSON>, that catalysed the birth of Team
          Assists — a platform meticulously crafted to cater to coaches{"'"} distinct needs.
        </p>

        <hr />

        <p>
          Soon, it became clear that the benefits of our solution extended beyond just coaches—it
          touched the entire club community, including players and parents. With our initial
          prototype in hand, the positive feedback from fellow coaches within the club gave us the
          motivation to push further. This drove us to refine and expand the product, making sure it
          truly served the specific needs of coaches at all levels of football.
        </p>

        <p>
          We craft everything while actively working in the field, drawing inspiration directly from
          coaches{"'"} insights.
        </p>

        <p>
          {`We take immense pride in what we do and approach our work with a sense of craftsmanship
          and unwavering dedication. Our commitment to quality is unwavering.`}
        </p>

        <p> Looking ahead, we have ambitious plans for the future.</p>
      </section>
    </article>
  );
};

export default AboutUsPage;
