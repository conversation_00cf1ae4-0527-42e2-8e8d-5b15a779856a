"use client";

import React, { useEffect } from "react";

import { MarketingEmailSubjects } from "@mio/helpers";

import { useSendEmail } from "./useForm";
import Button from "../buttons/button";

type Props = {
  heading: string;
  subject: MarketingEmailSubjects;
};

export default function ContactForm({ subject, heading }: Props) {
  const { register, onSubmit, isLoading, isSuccess, isError, errors, reset } =
    useSendEmail(subject);

  useEffect(() => {
    if (isSuccess) {
      /* show the success message for 3 secs */
      setTimeout(() => {
        reset();
      }, 3000);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isSuccess]);

  return (
    <form onSubmit={onSubmit} className="flex flex-col p-8 text-center">
      <h1 className="text-2xl font-bold dark:text-gray-50">{heading}</h1>

      <>
        <label htmlFor="fullName" className="text-gray-800 font-light mt-8">
          Full name<span className="text-red-500"> *</span>
        </label>
        <input
          type="text"
          className="bg-transparent border-b py-2 pl-4 focus:outline-none focus:rounded-md focus:ring-1 ring-green-500 font-light"
          {...register("fullName")}
        />
        {errors?.fullName && <p className="text-red-500">Full name cannot be empty.</p>}
      </>

      <>
        <label htmlFor="email" className="text-gray-800 font-light mt-4">
          E-mail<span className="text-red-500"> *</span>
        </label>
        <input
          type="email"
          className="bg-transparent border-b py-2 pl-4 focus:outline-none focus:rounded-md focus:ring-1 ring-green-500 font-light"
          {...register("email")}
        />
        {errors?.email && <p className="text-red-500">Email cannot be empty.</p>}
      </>

      <>
        <label htmlFor="message" className="text-gray-800 font-light mt-4">
          Message<span className="text-red-500"> *</span>
        </label>
        <textarea
          {...register("message")}
          className="bg-transparent border-b py-2 pl-4 focus:outline-none focus:rounded-md focus:ring-1 ring-green-500 font-light"
        ></textarea>
        {errors?.message && <p className="text-red-500">Message body cannot be empty.</p>}
      </>

      <div className="flex items-center justify-center">
        <Button type="submit" disabled={isLoading} className="px-10 mt-8 py-2 min-w-[150px]">
          {isLoading ? "Sending" : "Send"}
        </Button>
      </div>

      <div className="text-left">
        {isSuccess && (
          <p className="text-green-500 font-semibold text-xl my-2">
            Thank you! Your Message has been delivered.
          </p>
        )}
        {isError && <p className="text-red-500">Oops! Something went wrong, please try again.</p>}
      </div>
    </form>
  );
}
