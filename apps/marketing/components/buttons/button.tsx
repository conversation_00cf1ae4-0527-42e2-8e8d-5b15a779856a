import { ButtonHTMLAttributes, DetailedHTMLProps, ReactNode } from "react";

import { sharedButtonStyles } from "./button.shared-styles";

type Props = {
  children: ReactNode;
} & DetailedHTMLProps<ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement>;

export default function Button({ children, className, ...rest }: Props) {
  return (
    <button className={sharedButtonStyles + " " + className} {...rest}>
      {children}
    </button>
  );
}
