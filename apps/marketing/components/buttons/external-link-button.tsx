import { AnchorHTMLAttributes, DetailedHTMLProps, FC } from "react";

import { sharedButtonStyles } from "./button.shared-styles";

type Props = DetailedHTMLProps<AnchorHTMLAttributes<HTMLAnchorElement>, HTMLAnchorElement>;

const ExternalLinkButton: FC<Props> = ({ children, ...rest }) => {
  return (
    <a className={sharedButtonStyles} {...rest}>
      {children}
    </a>
  );
};

export default ExternalLinkButton;
