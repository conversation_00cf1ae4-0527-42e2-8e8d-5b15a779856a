{"name": "marketing", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/marketing", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/next:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"root": "apps/marketing", "outputPath": "dist/apps/marketing"}, "configurations": {"development": {"outputPath": "apps/marketing"}, "production": {"fileReplacements": [{"replace": "apps/marketing/environments/environment.ts", "with": "apps/marketing/environments/environment.prod.ts"}]}}}, "serve": {"executor": "@nx/next:server", "defaultConfiguration": "development", "options": {"buildTarget": "marketing:build", "dev": true}, "configurations": {"development": {"buildTarget": "marketing:build:development", "dev": true}, "production": {"buildTarget": "marketing:build:production", "dev": false}}}, "export": {"executor": "@nx/next:export", "options": {"buildTarget": "marketing:build:production"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/marketing/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}}