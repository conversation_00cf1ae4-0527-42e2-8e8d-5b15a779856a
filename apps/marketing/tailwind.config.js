const { createGlobPatternsForDependencies } = require("@nx/react/tailwind");
const { join } = require("path");

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    join(__dirname, "{src,pages,components,app}/**/*!(*.stories|*.spec).{ts,tsx,html}"),
    ...createGlobPatternsForDependencies(__dirname),
  ],
  theme: {
    extend: {
      maxWidth: {
        logo: "120px",
      },
      colors: {
        lightestGray: "#ededed",
        lightGrayText: "#4e4f4e",
      },
    },
  },
  plugins: [],
};
