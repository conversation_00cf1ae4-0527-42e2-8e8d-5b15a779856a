import "fake-indexeddb/auto";
// Add global.crypto polyfill if needed
import { webcrypto } from "node:crypto";

// Ensure crypto is available in the test environment
if (!global.crypto) {
  (global as any).crypto = webcrypto;
}

// Mock BroadcastChannel to prevent errors with MessageEvent
class MockBroadcastChannel implements BroadcastChannel {
  name: string;
  onmessage: ((this: BroadcastChannel, ev: MessageEvent) => any) | null = null;
  onmessageerror: ((this: BroadcastChannel, ev: MessageEvent) => any) | null = null;

  constructor(name: string) {
    this.name = name;
  }

  postMessage(message: any): void {
    // No-op implementation
  }

  close(): void {
    // No-op implementation
  }

  addEventListener(
    type: string,
    listener: EventListenerOrEventListenerObject,
    options?: boolean | AddEventListenerOptions,
  ): void {
    // No-op implementation
  }

  removeEventListener(
    type: string,
    listener: EventListenerOrEventListenerObject,
    options?: boolean | EventListenerOptions,
  ): void {
    // No-op implementation
  }

  dispatchEvent(event: Event): boolean {
    // No-op implementation
    return true;
  }
}

// Replace the global BroadcastChannel with our mock
if (typeof global.BroadcastChannel !== "undefined") {
  global.BroadcastChannel = MockBroadcastChannel as any;
}
