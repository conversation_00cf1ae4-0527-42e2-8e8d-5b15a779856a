import { render } from "@testing-library/react";
import { MemoryRouter } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

import { AppWithProviders } from "./root";
import { SDKContext, SDKProvider } from "./sdk/provider";
import { createDatabase } from "./database";
import { AccessToken, DomainError, ErrorMessages } from "@mio/helpers";

describe("App", () => {
  describe("when logged in", () => {
    const sdk = {
      authenticate: async () => {
        localStorage.setItem("token", "fake_token");

        return {
          id: "1",
          first_name: "<PERSON>",
          last_name: "<PERSON><PERSON>",
          email: "<EMAIL>",
        };
      },
      loginWithCredentials: async () => {
        return {
          user: {
            id: "1",
            first_name: "<PERSON>",
            last_name: "<PERSON><PERSON>",
            email: "<EMAIL>",
          },
          token: "fake_token" as AccessToken,
        };
      },
      db: createDatabase("MioTestDatabase"),
    } as const satisfies SDKContext;

    it("should render home view", async () => {
      const queryClient = new QueryClient();

      const screen = render(
        <MemoryRouter>
          <QueryClientProvider client={queryClient}>
            <SDKProvider sdk={sdk}>
              <AppWithProviders />
            </SDKProvider>
          </QueryClientProvider>
        </MemoryRouter>,
      );

      expect(await screen.findByText("Hello Jean")).toBeTruthy();
    });
  });

  describe("when not logged in", () => {
    const loggedOutSdk = {
      authenticate: async () => {
        throw new DomainError(ErrorMessages.Unauthorized);
      },
      loginWithCredentials: async () => {
        throw new DomainError(ErrorMessages.UserNotFound);
      },
      db: createDatabase("MioTestDatabase"),
    } as const satisfies SDKContext;

    it("should render login view", async () => {
      const queryClient = new QueryClient();

      const screen = render(
        <MemoryRouter>
          <QueryClientProvider client={queryClient}>
            <SDKProvider sdk={loggedOutSdk}>
              <AppWithProviders />
            </SDKProvider>
          </QueryClientProvider>
        </MemoryRouter>,
      );

      expect(await screen.findByText("Login to Team Assist")).toBeTruthy();
    });
  });
});
