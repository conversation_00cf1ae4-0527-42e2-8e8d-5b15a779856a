import { render } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { MemoryRouter } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

import { AppWithProviders } from "../../root";
import { SDKContext, SDKProvider } from "../../sdk/provider";
import { createDatabase } from "../../database";
import { AccessToken, DomainError, ErrorMessages, UnexpectedError } from "@mio/helpers";

describe("Login screen", () => {
  it("should show invalid credentials error", async () => {
    const withUnauthorizedError = {
      authenticate: async () => {
        throw new DomainError(ErrorMessages.Unauthorized);
      },
      loginWithCredentials: async () => {
        throw new DomainError(ErrorMessages.Unauthorized);
      },
      db: createDatabase("MioTestDatabase"),
    } as const satisfies SDKContext;

    const queryClient = new QueryClient();

    const screen = render(
      <MemoryRouter initialEntries={["/login"]}>
        <QueryClientProvider client={queryClient}>
          <SDKProvider sdk={withUnauthorizedError}>
            <AppWithProviders />
          </SDKProvider>
        </QueryClientProvider>
      </MemoryRouter>,
    );

    expect(await screen.findByText("Login to Team Assist")).toBeTruthy();

    await userEvent.type(await screen.findByLabelText("Email"), "<EMAIL>");
    await userEvent.type(await screen.findByLabelText("Password"), "password1");

    await userEvent.click(await screen.findByRole("button", { name: "Login" }));

    expect(await screen.findByText("Password or email is incorrect")).toBeTruthy();
  });

  it("should show a generic error", async () => {
    const withGenericError = {
      authenticate: async () => {
        throw new DomainError(ErrorMessages.Unauthorized);
      },
      loginWithCredentials: async () => {
        throw new UnexpectedError(ErrorMessages.UnexpectedError);
      },
      db: createDatabase("MioTestDatabase"),
    } as const satisfies SDKContext;

    const queryClient = new QueryClient();

    const screen = render(
      <MemoryRouter initialEntries={["/login"]}>
        <QueryClientProvider client={queryClient}>
          <SDKProvider sdk={withGenericError}>
            <AppWithProviders />
          </SDKProvider>
        </QueryClientProvider>
      </MemoryRouter>,
    );

    expect(await screen.findByText("Login to Team Assist")).toBeTruthy();

    await userEvent.type(await screen.findByLabelText("Email"), "<EMAIL>");
    await userEvent.type(await screen.findByLabelText("Password"), "password1");

    await userEvent.click(await screen.findByRole("button", { name: "Login" }));

    expect(await screen.findByText("Something went wrong")).toBeTruthy();
  });

  it("should let us login", async () => {
    const withLoginSuccess = {
      authenticate: async () => {
        // initially we're not logged in
        throw new DomainError(ErrorMessages.Unauthorized);
      },
      loginWithCredentials: async () => {
        return {
          token: "token" as AccessToken,
          user: {
            id: "1",
            first_name: "Jean",
            last_name: "Doe",
            email: "<EMAIL>",
          },
        };
      },
      db: createDatabase("MioTestDatabase"),
    } as const satisfies SDKContext;

    const queryClient = new QueryClient();

    const screen = render(
      <MemoryRouter initialEntries={["/login"]}>
        <QueryClientProvider client={queryClient}>
          <SDKProvider sdk={withLoginSuccess}>
            <AppWithProviders />
          </SDKProvider>
        </QueryClientProvider>
      </MemoryRouter>,
    );

    expect(await screen.findByText("Login to Team Assist")).toBeTruthy();

    await userEvent.type(await screen.findByLabelText("Email"), "<EMAIL>");
    await userEvent.type(await screen.findByLabelText("Password"), "password1");

    await userEvent.click(await screen.findByRole("button", { name: "Login" }));

    expect(await screen.findByText("Hello Jean")).toBeTruthy();
  });
});
