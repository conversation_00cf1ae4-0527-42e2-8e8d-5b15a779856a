import React from "react";
import { useQuery } from "@tanstack/react-query";

import { PageLoader } from "@mio/ui/lib/compositions/loaders";
import { useSdk } from "../sdk/provider";
import { useAccessToken, useLocalUserState } from "./state";
import { isError } from "@mio/helpers";

type Props = {
  children: React.ReactNode;
};

/**
 * AuthProvider is responsible for trying to authenticate the user and:
 * - storing the user
 * - making the UI wait for the authentication call to complete
 * - not responsible for auth redirections - for that check out [guards.tsx](./guards.tsx)
 * - not responsibe for any auth error visual indication - guards will just redirect to login
 */
export const AuthProvider: React.FC<Props> = ({ children }) => {
  const { authenticate } = useSdk();
  const { token, deleteToken } = useAccessToken();
  const { saveOne, deleteOne } = useLocalUserState();

  const authQuery = useQuery({
    queryKey: ["auth"],
    queryFn: () =>
      authenticate(token)
        .then(async (user) => {
          await saveOne(user);

          // we need to save the user synchronously thus the promise 'hack'
          await Promise.resolve();

          return user;
        })
        .catch(async (err) => {
          // we only invalidate our local auth data on unauthorized errors
          if (isError(err) && err.message === "Unauthorized") {
            deleteToken();
            await deleteOne("1");

            // we need to delete the token + user synchronously thus the promise 'hack'
            await Promise.resolve();
          }

          throw err;
        }),
    retry: false,
    staleTime: Infinity,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
  });

  if (authQuery.isLoading) {
    return <PageLoader message="Authenticating..." />;
  }

  return children;
};
