import React from "react";
import { Navigate } from "react-router-dom";

import { PageLoader } from "@mio/ui/lib/compositions/loaders";
import { useLocalUserState, UserContext } from "./state";

type Props = {
  children: React.ReactNode;
};

/**
 * If there's a user logged in, render the children.
 * If there's no user logged in, redirect to the login page.
 * Not responsible for actually fetching the user. For that check out [AuthProvider](./auth-provider.tsx)
 */
export const LoggedInGuard: React.FC<Props> = ({ children }) => {
  const { currentUser } = useLocalUserState();

  if (currentUser === "Loading") {
    return <PageLoader message="Loading user..." />;
  }

  if (!currentUser) {
    return <Navigate to="/login" />;
  }

  return <UserContext.Provider value={currentUser}>{children}</UserContext.Provider>;
};

/**
 * If there's no user logged in, render the children.
 * If there's a user logged in, redirect to the home page.
 * Not responsible for actually fetching the user. For that check out [AuthProvider](./auth-provider.tsx)
 */
export const LoggedOutGuard: React.FC<Props> = ({ children }) => {
  const { currentUser } = useLocalUserState();

  if (currentUser === "Loading") {
    return <PageLoader message="Loading user..." />;
  }

  if (currentUser) {
    return <Navigate to="/" />;
  }

  return children;
};
