import { useLocalStorage } from "react-use";
import { useLiveQuery } from "dexie-react-hooks";

import { AccessToken, DomainError, ErrorMessages } from "@mio/helpers";
import { useSdk } from "../sdk/provider";
import { LocalUser } from "./users.table";
import { createContext, useCallback, useContext } from "react";

export const useAccessToken = () => {
  const [token, setToken, deleteToken] = useLocalStorage<AccessToken | null>("token", null);

  const saveToken = useCallback(
    (token: AccessToken) => {
      setToken(token);
    },
    [setToken],
  );

  return { token, saveToken, deleteToken };
};

export const useLocalUserState = () => {
  const { db } = useSdk();

  const currentUser = useLiveQuery(
    () => db.users.get(localStorage.getItem("currentUserId") || ""),
    [],
    "Loading" as const,
  );

  return {
    currentUser,
    saveOne: async (user: LocalUser) => {
      db.users.put(user);
      localStorage.setItem("currentUserId", user.id);
    },
    deleteOne: async (id: string) => {
      db.users.delete(id);
      localStorage.removeItem("currentUserId");
    },
  };
};

export const UserContext = createContext<LocalUser | null>(null);

export const useLocalUser = () => {
  const user = useContext(UserContext);

  if (!user) {
    throw new DomainError(ErrorMessages.UserNotFound);
  }

  return user;
};
