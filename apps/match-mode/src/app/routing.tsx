import React from "react";
import { Route, Routes } from "react-router-dom";

import { HomeScreen } from "./home/<USER>";
import { LoginScreen } from "./login/login";
import { LoggedInGuard, LoggedOutGuard } from "./auth/guards";

export const Routing: React.FC = () => {
  return (
    <Routes>
      <Route
        path="/*"
        element={
          <LoggedInGuard>
            <Routes>
              <Route path="/" element={<HomeScreen />} />
            </Routes>
          </LoggedInGuard>
        }
      />
      <Route
        path="/login/*"
        element={
          <LoggedOutGuard>
            <Routes>
              <Route path="/" element={<LoginScreen />} />
            </Routes>
          </LoggedOutGuard>
        }
      />
    </Routes>
  );
};
