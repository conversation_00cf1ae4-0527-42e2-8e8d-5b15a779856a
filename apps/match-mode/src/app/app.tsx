import { <PERSON><PERSON>er<PERSON>outer } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

import { SDKProvider } from "./sdk/provider";
import { AppWithProviders } from "./root";

const queryClient = new QueryClient();

export function App() {
  return (
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        <SDKProvider>
          <AppWithProviders />
        </SDKProvider>
      </QueryClientProvider>
    </BrowserRouter>
  );
}

export default App;
