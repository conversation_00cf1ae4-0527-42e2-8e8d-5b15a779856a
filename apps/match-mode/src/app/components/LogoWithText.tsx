import { FC } from "react";

type Props = {
  variant: "light" | "dark";
};

export const LogoWithText: FC<Props> = ({ variant }) => {
  const textColor = variant === "light" ? "#fff" : "#1d1f1c";
  const bowColor = variant === "light" ? "#fff" : "#1d1f1c";
  const ballFragmentColor = "#1d1f1c";
  const ballFillColor = "#fff";

  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300.75 139.8">
      <g id="Logo">
        <g className="logo-bow-area">
          <polygon
            style={{ stroke: bowColor, strokeWidth: 4, fill: "none" }}
            className="logo-bow-left-fragment"
            points="164.35 80.12 146.6 71.99 164.35 63.86 164.35 80.12"
          />
          <polygon
            style={{ stroke: bowColor, strokeWidth: 4, fill: "none" }}
            className="logo-bow-right-fragment"
            points="131.59 80.12 149.34 71.99 131.59 63.86 131.59 80.12"
          />

          <rect
            className="logo-bow-inner-button"
            style={{ stroke: bowColor, fill: "none", strokeWidth: 5 }}
            x="146.12"
            y="69.23"
            width="3.71"
            height="5.52"
            rx="0.77"
            transform="translate(219.96 -75.98) rotate(90)"
          />

          <path
            className="logo-left-bow-line"
            style={{ stroke: bowColor, fill: bowColor, strokeWidth: 2 }}
            d="M118.06,73.49,30.92,72l87.14-1.5A1.49,1.49,0,0,1,119.59,72a1.51,1.51,0,0,1-1.47,1.53Z"
          />

          <path
            className="logo-right-bow-line"
            style={{ stroke: bowColor, fill: bowColor, strokeWidth: 2 }}
            d="M177.86,70.49,265,72l-87.15,1.5a1.5,1.5,0,0,1-.05-3Z"
          />
        </g>

        <g className="logo-ball-container" style={{ fill: ballFragmentColor }}>
          <circle
            className="logo-ball-base"
            style={{ stroke: ballFragmentColor, fill: ballFillColor, strokeWidth: 2 }}
            cx="147.97"
            cy="30.88"
            r="29.88"
          />

          <polygon
            className="logo-ball-fragment"
            points="148 18.41 135.57 27.44 140.31 41.91 155.63 41.91 160.38 27.4 148 18.41"
          />
          <polygon
            className="logo-ball-fragment"
            points="147.99 9.63 137.94 2.32 143.53 0.97 152.32 0.77 158.01 2.35 147.99 9.63"
          />
          <polygon
            className="logo-ball-fragment"
            points="128.03 24.3 117.83 31.41 118.39 25.69 121.08 17.31 124.43 12.45 128.03 24.3"
          />
          <polygon
            className="logo-ball-fragment"
            points="167.66 24.48 177.85 31.59 177.29 25.87 174.6 17.49 171.25 12.63 167.66 24.48"
          />
          <polygon
            className="logo-ball-fragment"
            points="159.69 48.13 155.76 59.92 161.09 57.76 168.36 52.81 172.07 48.22 159.69 48.13"
          />
          <polygon
            className="logo-ball-fragment"
            points="136.31 48.11 140.24 59.91 134.91 57.75 127.64 52.79 123.93 48.2 136.31 48.11"
          />
        </g>

        <g className="logo-text" style={{ fill: textColor }}>
          <path className="logo-text-letter" d="M0,101.15H24.94v7.17H16.22V139.8H8.56V108.32H0Z" />
          <path
            className="logo-text-letter"
            d="M49.76,101.15v7.17H29.48v-7.17Zm0,15.4v7.28H37.33v8.79H49.76v7.18H29.48V116.55Z"
          />
          <path
            className="logo-text-letter"
            d="M82.1,108.17a11.61,11.61,0,0,1,.86,4.4V139.8H75.41v-9.54h-5.7v-7.51h5.7V112.3a3.56,3.56,0,0,0-1.08-2.66,3.76,3.76,0,0,0-2.71-1H67.19a3.14,3.14,0,0,0-2.14,1,3.61,3.61,0,0,0-1,2.66v27.5H56.41V112.57a11.1,11.1,0,0,1,.9-4.4,12.28,12.28,0,0,1,2.52-3.68,11.46,11.46,0,0,1,3.6-2.48,11.22,11.22,0,0,1,4.17-.86h4.06A11.3,11.3,0,0,1,76,102a11.93,11.93,0,0,1,3.68,2.48A11.6,11.6,0,0,1,82.1,108.17Z"
          />
          <path
            className="logo-text-letter"
            d="M97.83,101.38l12.85,30.19H103l-2.89-6.72c-1-2.2-1.7-4-2.26-5.26l-.6-1.5V139.8H90.29V101.15h7.39S97.76,101.23,97.83,101.38Zm24-.23V139.8h-6.94V118.09l-.64,1.5-.9,2.14c-.38.88-.74,1.74-1.09,2.59l-3.65-10.14q1.17-2.62,2.39-5.44c.81-1.88,1.53-3.51,2.14-4.89s1-2.2,1.11-2.47.16-.23.19-.23Z"
          />
          <path
            className="logo-text-letter"
            d="M170,108.17a11.43,11.43,0,0,1,.87,4.4V139.8H163.3v-9.54h-5.71v-7.51h5.71V112.3a3.57,3.57,0,0,0-1.09-2.66,3.76,3.76,0,0,0-2.71-1h-4.43a3.16,3.16,0,0,0-2.14,1,3.61,3.61,0,0,0-1.05,2.66v27.5h-7.59V112.57a11.1,11.1,0,0,1,.9-4.4,12.28,12.28,0,0,1,2.52-3.68,11.51,11.51,0,0,1,3.61-2.48,11.18,11.18,0,0,1,4.17-.86h4a11.3,11.3,0,0,1,4.36.86,11.82,11.82,0,0,1,3.68,2.48A11.27,11.27,0,0,1,170,108.17Z"
          />
          <path
            className="logo-text-letter"
            d="M194.58,114.11v-2.18a3.34,3.34,0,0,0-1.09-2.56.13.13,0,0,0,0-.07s-.05,0-.08,0a3.83,3.83,0,0,0-2.18-.64h-2.85a3.71,3.71,0,0,0-1.77.75,3.07,3.07,0,0,0-1,2.56,3.56,3.56,0,0,0,.94,2.7,12.75,12.75,0,0,0,3.68,2l4.88,1.92a11.88,11.88,0,0,1,3.23,2,13.06,13.06,0,0,1,2.78,3.38,8.63,8.63,0,0,1,1.2,4.54,11,11,0,0,1-3.49,8.23,10.83,10.83,0,0,1-3.68,2.29,12.88,12.88,0,0,1-4.51.79h-1.76a11.68,11.68,0,0,1-7.25-2.63,3.88,3.88,0,0,0-.38-.34,12,12,0,0,1-2.51-3.57,11.15,11.15,0,0,1-.83-2.81,13.41,13.41,0,0,1-.08-1.39v-2.22h7.7v2.22a3.34,3.34,0,0,0,1,2.51l.12.11a4.18,4.18,0,0,0,2.18.64h2.85a3.47,3.47,0,0,0,1.77-.75,2.92,2.92,0,0,0,1-2.51,3.68,3.68,0,0,0-.94-2.75,13.64,13.64,0,0,0-3.72-2.06L185,122.41a13.5,13.5,0,0,1-3.26-2A15,15,0,0,1,179,117a8.73,8.73,0,0,1-1.17-4.5,11,11,0,0,1,3.5-8.23,9.9,9.9,0,0,1,3.68-2.29,12.6,12.6,0,0,1,4.5-.79h1.73a12.05,12.05,0,0,1,4.28.86,11.35,11.35,0,0,1,3,1.77,2.27,2.27,0,0,1,.38.38,10.68,10.68,0,0,1,2.51,3.53,9.69,9.69,0,0,1,.83,2.85,8.61,8.61,0,0,1,.11,1.39v2.18Z"
          />
          <path
            className="logo-text-letter"
            d="M224.25,114.11v-2.18a3.34,3.34,0,0,0-1.09-2.56s0,0,0-.07a.1.1,0,0,0-.07,0,3.83,3.83,0,0,0-2.18-.64H218a3.71,3.71,0,0,0-1.77.75,3.07,3.07,0,0,0-1,2.56,3.56,3.56,0,0,0,.94,2.7,12.75,12.75,0,0,0,3.68,2l4.88,1.92a11.88,11.88,0,0,1,3.23,2,13.06,13.06,0,0,1,2.78,3.38,8.63,8.63,0,0,1,1.2,4.54,11,11,0,0,1-3.49,8.23,10.83,10.83,0,0,1-3.68,2.29,12.88,12.88,0,0,1-4.51.79h-1.76a11.68,11.68,0,0,1-7.25-2.63,3.88,3.88,0,0,0-.38-.34,12,12,0,0,1-2.51-3.57,11.15,11.15,0,0,1-.83-2.81,13.41,13.41,0,0,1-.08-1.39v-2.22h7.7v2.22a3.34,3.34,0,0,0,1.05,2.51l.12.11a4.18,4.18,0,0,0,2.18.64h2.85a3.4,3.4,0,0,0,1.76-.75,2.9,2.9,0,0,0,1.06-2.51,3.68,3.68,0,0,0-.94-2.75,13.64,13.64,0,0,0-3.72-2.06l-4.85-1.84a13.5,13.5,0,0,1-3.26-2,15,15,0,0,1-2.78-3.42,8.73,8.73,0,0,1-1.17-4.5,11,11,0,0,1,3.5-8.23,9.9,9.9,0,0,1,3.68-2.29,12.6,12.6,0,0,1,4.5-.79h1.73a12.05,12.05,0,0,1,4.28.86,11.35,11.35,0,0,1,3,1.77,2.27,2.27,0,0,1,.38.38,10.68,10.68,0,0,1,2.51,3.53,9.69,9.69,0,0,1,.83,2.85,8.61,8.61,0,0,1,.11,1.39v2.18Z"
          />
          <path className="logo-text-letter" d="M237,101.15h7.73V139.8H237Z" />
          <path
            className="logo-text-letter"
            d="M267.07,114.11v-2.18a3.38,3.38,0,0,0-1.09-2.56s0,0,0-.07-.05,0-.08,0a3.8,3.8,0,0,0-2.17-.64h-2.86a3.72,3.72,0,0,0-1.76.75,3,3,0,0,0-1,2.56,3.56,3.56,0,0,0,.94,2.7,12.9,12.9,0,0,0,3.68,2l4.88,1.92a11.73,11.73,0,0,1,3.23,2,12.86,12.86,0,0,1,2.78,3.38,8.63,8.63,0,0,1,1.2,4.54,11,11,0,0,1-3.49,8.23,10.83,10.83,0,0,1-3.68,2.29,12.84,12.84,0,0,1-4.51.79h-1.76a11.75,11.75,0,0,1-7.25-2.63,3.81,3.81,0,0,0-.37-.34,11.8,11.8,0,0,1-2.52-3.57,11.57,11.57,0,0,1-.83-2.81,13.34,13.34,0,0,1-.07-1.39v-2.22H258v2.22a3.34,3.34,0,0,0,1,2.51l.11.11a4.21,4.21,0,0,0,2.18.64h2.85a3.47,3.47,0,0,0,1.77-.75,2.92,2.92,0,0,0,1.05-2.51,3.64,3.64,0,0,0-.94-2.75,13.33,13.33,0,0,0-3.72-2.06l-4.84-1.84a13.38,13.38,0,0,1-3.27-2,15,15,0,0,1-2.78-3.42,8.82,8.82,0,0,1-1.16-4.5,11,11,0,0,1,3.49-8.23,10,10,0,0,1,3.68-2.29,12.66,12.66,0,0,1,4.51-.79h1.73A12.14,12.14,0,0,1,268,102a11.13,11.13,0,0,1,3,1.77,2.27,2.27,0,0,1,.38.38,10.71,10.71,0,0,1,2.52,3.53,9.67,9.67,0,0,1,.82,2.85,7.61,7.61,0,0,1,.11,1.39v2.18Z"
          />
          <path
            className="logo-text-letter"
            d="M275.82,101.15h24.93v7.17H292V139.8h-7.66V108.32h-8.56Z"
          />
        </g>
      </g>
    </svg>
  );
};
