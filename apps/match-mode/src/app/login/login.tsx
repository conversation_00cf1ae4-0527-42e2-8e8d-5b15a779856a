import React from "react";
import { useNavigate } from "react-router-dom";
import { useMutation } from "@tanstack/react-query";

import { LogoWithText } from "../components/LogoWithText";
import { CoachUser, ErrorMessages, isError, UserTypes } from "@mio/helpers";
import { Login, Box, Card, Container, Stack, Typography } from "@mio/ui";
import { useAccessToken, useLocalUserState } from "../auth/state";
import { useSdk } from "../sdk/provider";

export const LoginScreen: React.FC = () => {
  const { saveToken } = useAccessToken();
  const navigate = useNavigate();
  const { saveOne } = useLocalUserState();
  const { loginWithCredentials } = useSdk();

  const loginHandler = useMutation({
    mutationFn: loginWithCredentials,
    onSuccess: async (data) => {
      saveToken(data.token);
      saveOne(data.user);

      await Promise.resolve();

      navigate("/");
    },
  });

  return (
    <Container
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "flex-start",
        flexDirection: "column",
        gap: { xs: 2, md: 5 },
        height: "100%",
      }}
    >
      <Typography component="h1" variant="h3">
        Login to Team Assist
      </Typography>

      <Card
        sx={{
          minWidth: { md: "50%", xs: "100%" },
        }}
      >
        <Stack
          sx={{ minWidth: [1, 1, 1 / 2], padding: 2 }}
          gap={{ md: 5, xs: 2 }}
          alignItems="center"
        >
          <Box width="200px">
            <LogoWithText variant="dark" />
          </Box>

          <Box sx={{ width: "100%" }}>
            <Login.Box
              type={UserTypes.Coach}
              isLoading={loginHandler.isLoading}
              serverError={mapError(loginHandler.error)}
              onLogin={(data) => {
                const parsedCredentials = CoachUser.toLoginDto(data, UserTypes.Coach);

                if (isError(parsedCredentials)) {
                  return;
                }

                loginHandler.mutate(parsedCredentials);
              }}
            />
          </Box>
        </Stack>
      </Card>
    </Container>
  );
};

const mapError = (error: unknown) => {
  if (!error) {
    return "";
  }

  if (isError(error) && error.message === ErrorMessages.Unauthorized) {
    return "Password or email is incorrect";
  }

  return "Something went wrong";
};
