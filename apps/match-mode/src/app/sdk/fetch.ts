import { DomainError, ErrorMessages, UnexpectedError } from "@mio/helpers";
import { environment } from "../../environments/environment";

export const fetchRequest = async (
  path: string,
  options?: RequestInit & { token?: string },
): Promise<unknown> => {
  const fullUrl = `${environment.BASE_URL}/${path}`;

  return fetch(fullUrl, {
    ...{
      ...options,
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
        Authorization: `Bearer ${options?.token}`,
        ...options?.headers,
      },
    },
  }).then((res) => {
    if (res.status === 401) {
      throw new DomainError(ErrorMessages.Unauthorized);
    }

    if (res.status > 400 && res.status < 500) {
      throw new DomainError(ErrorMessages.BadRequest);
    }

    if (res.status >= 500) {
      throw new UnexpectedError(ErrorMessages.UnexpectedError);
    }

    return res.json();
  });
};
