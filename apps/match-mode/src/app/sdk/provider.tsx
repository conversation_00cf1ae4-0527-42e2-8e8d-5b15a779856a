import React, { createContext, useContext } from "react";

import { createDatabase } from "../database";
import { authenticate, loginWithCredentials } from "./login";

const defaults = {
  authenticate,
  loginWithCredentials,
  db: createDatabase(),
} as const;

export type SDKContext = typeof defaults;

type Props = {
  children: React.ReactNode;
  sdk?: Partial<typeof defaults>;
};

const SDKContext = createContext<SDKContext>(defaults);

export const SDKProvider: React.FC<Props> = ({ children, sdk }) => {
  return <SDKContext.Provider value={{ ...defaults, ...sdk }}>{children}</SDKContext.Provider>;
};

export const useSdk = () => {
  return useContext(SDKContext);
};
