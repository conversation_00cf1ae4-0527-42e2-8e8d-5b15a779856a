import {
  AccessToken,
  apiUrls,
  CoachUser,
  CredentialsDto,
  DomainError,
  ErrorMessages,
  isError,
} from "@mio/helpers";
import { applyGenericErrorHandling } from "@mio/ui/lib/sdk/genericErrorHandling";
import { LocalUser } from "../auth/users.table";
import { fetchRequest } from "./fetch";

type AuthResponse = { token: AccessToken; user: LocalUser };

export const loginWithCredentials = async (dto: CredentialsDto): Promise<AuthResponse> => {
  try {
    const tokenResponse = await fetchRequest(apiUrls.login, {
      body: JSON.stringify(dto),
      method: "POST",
    }).then(CoachUser.toAccessTokenDto);

    if (isError(tokenResponse)) {
      throw tokenResponse;
    }

    const userResponse = await fetchRequest(apiUrls.currentUser, {
      token: tokenResponse.token,
    }).then(CoachUser.publicFromUnknown);

    if (isError(userResponse)) {
      throw userResponse;
    }

    return {
      user: {
        email: userResponse.authentication.email,
        id: userResponse.id,
        first_name: userResponse.firstName,
        last_name: userResponse.lastName,
      },
      token: tokenResponse.token,
    };
  } catch (err) {
    throw applyGenericErrorHandling(err);
  }
};

export const authenticate = async (token: AccessToken | undefined | null): Promise<LocalUser> => {
  if (!token) {
    throw new DomainError(ErrorMessages.Unauthorized);
  }

  const userResponse = await fetchRequest(apiUrls.currentUser, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  }).then(CoachUser.publicFromUnknown);

  if (isError(userResponse)) {
    throw userResponse;
  }

  return {
    email: userResponse.authentication.email,
    id: userResponse.id,
    first_name: userResponse.firstName,
    last_name: userResponse.lastName,
  };
};
